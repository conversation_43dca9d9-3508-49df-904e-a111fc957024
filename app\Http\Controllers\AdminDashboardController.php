<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Feedback;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class AdminDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display admin dashboard
     */
    public function index()
    {
        // Basic statistics
        $stats = [
            'total_users' => User::count(),
            'total_events' => Event::count(),
            'total_orders' => Order::count(),
            'total_tickets' => Ticket::count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_orders' => Order::where('payment_status', 'pending')->count(),
            'active_events' => Event::where('status', 'published')->where('end_date', '>', now())->count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
        ];

        // Revenue chart data (last 30 days)
        $revenueData = Order::where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // User registration chart data (last 30 days)
        $userRegistrationData = User::where('created_at', '>=', now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Recent activities
        $recentOrders = Order::with(['user', 'event'])
            ->latest()
            ->limit(10)
            ->get();

        $recentUsers = User::latest()
            ->limit(10)
            ->get();

        // Top events by revenue
        $topEvents = Event::withSum(['orders' => function($query) {
                $query->where('payment_status', 'paid');
            }], 'total_amount')
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(10)
            ->get();

        // Recent feedback
        $recentFeedback = collect(); // Initialize as empty collection
        try {
            if (class_exists('App\Models\Feedback')) {
                $recentFeedback = Feedback::with(['user', 'event'])
                    ->latest()
                    ->limit(5)
                    ->get();
            }
        } catch (\Exception $e) {
            // If feedback table doesn't exist or has issues, use empty collection
            $recentFeedback = collect();
        }

        // Recent activities for compatibility
        $recentActivities = collect();

        // Add recent orders as activities
        foreach ($recentOrders as $order) {
            $recentActivities->push([
                'type' => 'order_created',
                'title' => 'New Order',
                'description' => "Order #{$order->order_number} for {$order->event->title}",
                'user' => $order->user->name,
                'event' => $order->event->title,
                'created_at' => $order->created_at,
                'amount' => $order->total_amount
            ]);
        }

        // Add recent users as activities
        foreach ($recentUsers as $user) {
            $recentActivities->push([
                'type' => 'user_registration',
                'title' => 'New User Registration',
                'description' => "New {$user->role} registered",
                'user' => $user->name,
                'created_at' => $user->created_at
            ]);
        }

        // Sort activities by date
        $recentActivities = $recentActivities->sortByDesc('created_at')->take(15);

        // Category performance
        $categoryPerformance = collect();

        // Geographic data
        $geographicData = collect();

        // System health
        $systemHealth = [
            'overall_status' => 'healthy',
            'database' => ['status' => 'healthy', 'message' => 'Connected', 'response_time' => '< 1ms'],
            'storage' => ['status' => 'healthy', 'message' => '75% used', 'free_space' => '2.5GB'],
            'cache' => ['status' => 'healthy', 'message' => 'Working', 'response_time' => '< 1ms'],
            'api' => ['status' => 'healthy', 'message' => 'Online', 'response_time' => '< 100ms'],
            'queue' => ['status' => 'healthy', 'message' => '0 pending', 'failed_jobs' => 0],
            'memory_usage' => ['current' => '128MB'],
            'uptime' => '7 days',
            'last_updated' => now()->format('H:i:s')
        ];

        return view('pages.admin.dashboard', compact(
            'stats',
            'revenueData',
            'userRegistrationData',
            'recentOrders',
            'recentUsers',
            'topEvents',
            'recentFeedback',
            'recentActivities',
            'categoryPerformance',
            'geographicData',
            'systemHealth'
        ));
    }

    /**
     * Generate and export reports
     */
    public function exportReport(Request $request)
    {
        $type = $request->get('type', 'revenue');
        $format = $request->get('format', 'csv');
        $startDate = $request->get('start_date', now()->subMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        switch ($type) {
            case 'revenue':
                return $this->exportRevenueReport($startDate, $endDate, $format);
            case 'users':
                return $this->exportUsersReport($startDate, $endDate, $format);
            case 'events':
                return $this->exportEventsReport($startDate, $endDate, $format);
            case 'orders':
                return $this->exportOrdersReport($startDate, $endDate, $format);
            case 'feedback':
                return $this->exportFeedbackReport($startDate, $endDate, $format);
            default:
                return back()->with('error', 'Invalid report type');
        }
    }

    /**
     * Generate specific report type
     */
    public function generateReport(Request $request, $type)
    {
        $startDate = $request->get('start_date', now()->subMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));

        switch ($type) {
            case 'revenue':
                $data = $this->getRevenueReportData($startDate, $endDate);
                break;
            case 'users':
                $data = $this->getUsersReportData($startDate, $endDate);
                break;
            case 'events':
                $data = $this->getEventsReportData($startDate, $endDate);
                break;
            case 'orders':
                $data = $this->getOrdersReportData($startDate, $endDate);
                break;
            case 'feedback':
                $data = $this->getFeedbackReportData($startDate, $endDate);
                break;
            default:
                return response()->json(['error' => 'Invalid report type'], 400);
        }

        return response()->json($data);
    }

    /**
     * Get analytics data
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days

        $analytics = [
            'revenue_trend' => $this->getRevenueTrend($period),
            'user_growth' => $this->getUserGrowth($period),
            'event_performance' => $this->getEventPerformance($period),
            'geographic_data' => $this->getGeographicData($period),
            'payment_methods' => $this->getPaymentMethodsData($period),
            'device_analytics' => $this->getDeviceAnalytics($period),
        ];

        return response()->json($analytics);
    }

    // Private helper methods for report generation

    private function exportRevenueReport($startDate, $endDate, $format)
    {
        $data = Order::with(['user', 'event'])
            ->where('payment_status', 'paid')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $headers = ['Order ID', 'User', 'Event', 'Amount', 'Date', 'Payment Method'];
        $rows = $data->map(function ($order) {
            return [
                $order->order_number,
                $order->user->name,
                $order->event->title,
                $order->total_amount,
                $order->created_at->format('Y-m-d H:i:s'),
                $order->payment_method ?? 'N/A'
            ];
        });

        return $this->generateFileResponse($headers, $rows, "revenue_report_{$startDate}_{$endDate}", $format);
    }

    private function exportUsersReport($startDate, $endDate, $format)
    {
        $data = User::whereBetween('created_at', [$startDate, $endDate])->get();

        $headers = ['ID', 'Name', 'Email', 'Role', 'Registration Date', 'Email Verified'];
        $rows = $data->map(function ($user) {
            return [
                $user->id,
                $user->name,
                $user->email,
                $user->role,
                $user->created_at->format('Y-m-d H:i:s'),
                $user->email_verified_at ? 'Yes' : 'No'
            ];
        });

        return $this->generateFileResponse($headers, $rows, "users_report_{$startDate}_{$endDate}", $format);
    }

    private function exportEventsReport($startDate, $endDate, $format)
    {
        $data = Event::with(['organizer'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $headers = ['ID', 'Title', 'Organizer', 'Status', 'Start Date', 'End Date', 'Capacity', 'Created Date'];
        $rows = $data->map(function ($event) {
            return [
                $event->id,
                $event->title,
                $event->organizer->name,
                $event->status,
                $event->start_date->format('Y-m-d H:i:s'),
                $event->end_date->format('Y-m-d H:i:s'),
                $event->total_capacity,
                $event->created_at->format('Y-m-d H:i:s')
            ];
        });

        return $this->generateFileResponse($headers, $rows, "events_report_{$startDate}_{$endDate}", $format);
    }

    private function exportOrdersReport($startDate, $endDate, $format)
    {
        $data = Order::with(['user', 'event'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $headers = ['Order Number', 'User', 'Event', 'Quantity', 'Amount', 'Status', 'Payment Status', 'Date'];
        $rows = $data->map(function ($order) {
            return [
                $order->order_number,
                $order->user->name,
                $order->event->title,
                $order->quantity,
                $order->total_amount,
                $order->status,
                $order->payment_status,
                $order->created_at->format('Y-m-d H:i:s')
            ];
        });

        return $this->generateFileResponse($headers, $rows, "orders_report_{$startDate}_{$endDate}", $format);
    }

    private function exportFeedbackReport($startDate, $endDate, $format)
    {
        $data = Feedback::with(['user', 'event'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        $headers = ['ID', 'User', 'Event', 'Rating', 'Comment', 'Date'];
        $rows = $data->map(function ($feedback) {
            return [
                $feedback->id,
                $feedback->user->name,
                $feedback->event->title,
                $feedback->rating,
                $feedback->comment,
                $feedback->created_at->format('Y-m-d H:i:s')
            ];
        });

        return $this->generateFileResponse($headers, $rows, "feedback_report_{$startDate}_{$endDate}", $format);
    }

    private function generateFileResponse($headers, $rows, $filename, $format)
    {
        if ($format === 'csv') {
            $content = implode(',', $headers) . "\n";
            foreach ($rows as $row) {
                $content .= implode(',', array_map(function($field) {
                    return '"' . str_replace('"', '""', $field) . '"';
                }, $row)) . "\n";
            }

            return Response::make($content, 200, [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}.csv\"",
            ]);
        }

        // Add other formats (Excel, PDF) here if needed
        return back()->with('error', 'Unsupported format');
    }

    // Analytics helper methods
    private function getRevenueTrend($days)
    {
        return Order::where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    private function getUserGrowth($days)
    {
        return User::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    private function getEventPerformance($days)
    {
        return Event::with(['orders' => function($query) {
                $query->where('payment_status', 'paid');
            }])
            ->where('created_at', '>=', now()->subDays($days))
            ->get()
            ->map(function($event) {
                return [
                    'title' => $event->title,
                    'revenue' => $event->orders->sum('total_amount'),
                    'tickets_sold' => $event->orders->sum('quantity')
                ];
            });
    }

    private function getGeographicData($days)
    {
        // This would require storing user location data
        // For now, return sample data
        return [
            ['city' => 'Jakarta', 'users' => 150, 'revenue' => 15000000],
            ['city' => 'Surabaya', 'users' => 80, 'revenue' => 8000000],
            ['city' => 'Bandung', 'users' => 60, 'revenue' => 6000000],
        ];
    }

    private function getPaymentMethodsData($days)
    {
        return Order::where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('payment_method, COUNT(*) as count, SUM(total_amount) as revenue')
            ->groupBy('payment_method')
            ->get();
    }

    private function getDeviceAnalytics($days)
    {
        // This would require storing device/browser data
        // For now, return sample data
        return [
            ['device' => 'Mobile', 'percentage' => 65],
            ['device' => 'Desktop', 'percentage' => 30],
            ['device' => 'Tablet', 'percentage' => 5],
        ];
    }
}

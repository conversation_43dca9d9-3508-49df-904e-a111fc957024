<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // JSON column to store assigned organizer IDs for staff
            $table->json('assigned_organizers')->nullable()->after('role');
            
            // Index for better performance when querying assigned organizers
            $table->index(['role']);
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('assigned_organizers');
            $table->dropIndex(['role']);
        });
    }
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classic Template Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .ticket {
            width: 400px;
            height: 200px;
            background: {{ request('background_color', '#ffffff') }};
            border: 2px dashed {{ request('secondary_color', '#D1D5DB') }};
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ticket::before,
        .ticket::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: #f8fafc;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
        }

        .ticket::before {
            left: -10px;
        }

        .ticket::after {
            right: -10px;
        }

        .ticket-header {
            background: linear-gradient(135deg, {{ request('primary_color', '#059669') }}, {{ request('secondary_color', '#D1D5DB') }});
            color: white;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 14px;
        }

        .ticket-body {
            padding: 16px 20px;
            color: {{ request('text_color', '#1F2937') }};
        }

        .event-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
            color: {{ request('primary_color', '#059669') }};
        }

        .event-details {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 16px;
            align-items: center;
        }

        .event-info {
            font-size: 12px;
            line-height: 1.4;
        }

        .event-info div {
            margin-bottom: 4px;
        }

        .event-info strong {
            color: {{ request('primary_color', '#059669') }};
        }

        .qr-code {
            width: 60px;
            height: 60px;
            background: {{ request('secondary_color', '#D1D5DB') }};
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: {{ request('text_color', '#1F2937') }};
            text-align: center;
            border: 1px solid {{ request('primary_color', '#059669') }};
        }

        .ticket-number {
            position: absolute;
            bottom: 8px;
            left: 20px;
            font-size: 10px;
            color: {{ request('secondary_color', '#6B7280') }};
            font-weight: 500;
        }

        .price {
            position: absolute;
            bottom: 8px;
            right: 20px;
            font-size: 12px;
            font-weight: 700;
            color: {{ request('primary_color', '#059669') }};
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 24px;
            font-weight: 900;
            color: rgba(0, 0, 0, 0.05);
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="watermark">TIXARA</div>
        
        <div class="ticket-header">
            BOARDING PASS
        </div>
        
        <div class="ticket-body">
            <div class="event-title">{{ request('event_title', 'Sample Event') }}</div>
            
            <div class="event-details">
                <div class="event-info">
                    <div><strong>Date:</strong> {{ request('start_date', now()->addDays(7)->format('M d, Y')) }}</div>
                    <div><strong>Time:</strong> {{ request('start_date', now()->addDays(7)->format('H:i')) }}</div>
                    <div><strong>Venue:</strong> {{ request('venue_name', 'Sample Venue') }}</div>
                    <div><strong>Attendee:</strong> {{ request('attendee_name', 'John Doe') }}</div>
                </div>
                
                <div class="qr-code">
                    QR<br>CODE
                </div>
            </div>
        </div>
        
        <div class="ticket-number">{{ request('ticket_number', 'TIK-PREVIEW-001') }}</div>
        <div class="price">{{ request('price', 'Rp 150.000') }}</div>
    </div>
</body>
</html>

<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class WebhookController extends Controller
{
    /**
     * Handle Xendit webhook
     */
    public function xendit(Request $request)
    {
        try {
            $payload = $request->all();
            Log::info('Xendit webhook received', $payload);

            // Verify webhook token
            $webhookToken = $request->header('x-callback-token');
            if ($webhookToken !== config('services.xendit.webhook_token')) {
                Log::warning('Invalid Xendit webhook token');
                return response()->json(['status' => 'error'], 401);
            }

            // Handle different event types
            $eventType = $payload['event'] ?? null;

            switch ($eventType) {
                case 'payment.paid':
                case 'qr_code.payment.completed':
                case 'virtual_account.payment.completed':
                case 'ewallet.charge.completed':
                    return $this->handleXenditPaymentSuccess($payload);

                case 'payment.failed':
                case 'ewallet.charge.failed':
                    return $this->handleXenditPaymentFailed($payload);

                default:
                    Log::info('Unhandled Xendit event type: ' . $eventType);
                    return response()->json(['status' => 'ok']);
            }

        } catch (\Exception $e) {
            Log::error('Xendit webhook error: ' . $e->getMessage(), [
                'payload' => $request->all()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle Midtrans webhook
     */
    public function midtrans(Request $request)
    {
        try {
            $payload = $request->all();
            Log::info('Midtrans webhook received', $payload);

            // Verify signature
            $serverKey = config('services.midtrans.server_key');
            $orderId = $payload['order_id'];
            $statusCode = $payload['status_code'];
            $grossAmount = $payload['gross_amount'];
            $signatureKey = $payload['signature_key'];

            $expectedSignature = hash('sha512', $orderId . $statusCode . $grossAmount . $serverKey);

            if ($signatureKey !== $expectedSignature) {
                Log::warning('Invalid Midtrans signature');
                return response()->json(['status' => 'error'], 401);
            }

            $transactionStatus = $payload['transaction_status'];
            $fraudStatus = $payload['fraud_status'] ?? null;

            if ($transactionStatus === 'capture') {
                if ($fraudStatus === 'challenge') {
                    return $this->handleMidtransPaymentChallenge($payload);
                } else if ($fraudStatus === 'accept') {
                    return $this->handleMidtransPaymentSuccess($payload);
                }
            } else if ($transactionStatus === 'settlement') {
                return $this->handleMidtransPaymentSuccess($payload);
            } else if (in_array($transactionStatus, ['cancel', 'deny', 'expire'])) {
                return $this->handleMidtransPaymentFailed($payload);
            } else if ($transactionStatus === 'pending') {
                return $this->handleMidtransPaymentPending($payload);
            }

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            Log::error('Midtrans webhook error: ' . $e->getMessage(), [
                'payload' => $request->all()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle Tripay webhook
     */
    public function tripay(Request $request)
    {
        try {
            $payload = $request->all();
            Log::info('Tripay webhook received', $payload);

            // Verify signature
            $privateKey = config('services.tripay.private_key');
            $receivedSignature = $request->header('X-Callback-Signature');
            $expectedSignature = hash_hmac('sha256', json_encode($payload), $privateKey);

            if ($receivedSignature !== $expectedSignature) {
                Log::warning('Invalid Tripay signature');
                return response()->json(['status' => 'error'], 401);
            }

            $status = $payload['status'];

            switch ($status) {
                case 'PAID':
                    return $this->handleTripayPaymentSuccess($payload);

                case 'EXPIRED':
                case 'FAILED':
                    return $this->handleTripayPaymentFailed($payload);

                default:
                    Log::info('Unhandled Tripay status: ' . $status);
                    return response()->json(['status' => 'ok']);
            }

        } catch (\Exception $e) {
            Log::error('Tripay webhook error: ' . $e->getMessage(), [
                'payload' => $request->all()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle successful Xendit payment
     */
    private function handleXenditPaymentSuccess(array $payload)
    {
        $externalId = $payload['external_id'] ?? $payload['data']['external_id'] ?? null;
        $order = Order::where('order_number', $externalId)->first();

        if (!$order) {
            Log::warning('Order not found for Xendit payment', ['external_id' => $externalId]);
            return response()->json(['status' => 'error'], 404);
        }

        if ($order->payment_status === 'paid') {
            return response()->json(['status' => 'ok']);
        }

        DB::beginTransaction();
        try {
            $order->update([
                'payment_status' => 'paid',
                'payment_reference' => $payload['id'] ?? $payload['data']['id'],
                'paid_at' => now(),
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'payment_data' => $payload,
            ]);

            // Send confirmation notification
            $this->sendPaymentConfirmation($order);

            // Broadcast payment success event for real-time updates
            $this->broadcastPaymentSuccess($order);

            DB::commit();
            Log::info('Xendit payment confirmed', ['order_id' => $order->id]);

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update order for Xendit payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle successful Midtrans payment
     */
    private function handleMidtransPaymentSuccess(array $payload)
    {
        $orderId = $payload['order_id'];
        $order = Order::where('order_number', $orderId)->first();

        if (!$order) {
            Log::warning('Order not found for Midtrans payment', ['order_id' => $orderId]);
            return response()->json(['status' => 'error'], 404);
        }

        if ($order->payment_status === 'paid') {
            return response()->json(['status' => 'ok']);
        }

        DB::beginTransaction();
        try {
            $order->update([
                'payment_status' => 'paid',
                'payment_reference' => $payload['transaction_id'],
                'paid_at' => now(),
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'payment_data' => $payload,
            ]);

            $this->sendPaymentConfirmation($order);
            $this->broadcastPaymentSuccess($order);

            DB::commit();
            Log::info('Midtrans payment confirmed', ['order_id' => $order->id]);

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update order for Midtrans payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle successful Tripay payment
     */
    private function handleTripayPaymentSuccess(array $payload)
    {
        $merchantRef = $payload['merchant_ref'];
        $order = Order::where('order_number', $merchantRef)->first();

        if (!$order) {
            Log::warning('Order not found for Tripay payment', ['merchant_ref' => $merchantRef]);
            return response()->json(['status' => 'error'], 404);
        }

        if ($order->payment_status === 'paid') {
            return response()->json(['status' => 'ok']);
        }

        DB::beginTransaction();
        try {
            $order->update([
                'payment_status' => 'paid',
                'payment_reference' => $payload['reference'],
                'paid_at' => now(),
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'payment_data' => $payload,
            ]);

            $this->sendPaymentConfirmation($order);
            $this->broadcastPaymentSuccess($order);

            DB::commit();
            Log::info('Tripay payment confirmed', ['order_id' => $order->id]);

            return response()->json(['status' => 'ok']);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Failed to update order for Tripay payment', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Handle failed payments
     */
    private function handleXenditPaymentFailed(array $payload)
    {
        $externalId = $payload['external_id'] ?? $payload['data']['external_id'] ?? null;
        $order = Order::where('order_number', $externalId)->first();

        if ($order && $order->payment_status === 'pending') {
            $order->update([
                'payment_status' => 'failed',
                'payment_data' => $payload,
            ]);
            Log::info('Xendit payment failed', ['order_id' => $order->id]);
        }

        return response()->json(['status' => 'ok']);
    }

    private function handleMidtransPaymentFailed(array $payload)
    {
        $orderId = $payload['order_id'];
        $order = Order::where('order_number', $orderId)->first();

        if ($order && $order->payment_status === 'pending') {
            $order->update([
                'payment_status' => 'failed',
                'payment_data' => $payload,
            ]);
            Log::info('Midtrans payment failed', ['order_id' => $order->id]);
        }

        return response()->json(['status' => 'ok']);
    }

    private function handleTripayPaymentFailed(array $payload)
    {
        $merchantRef = $payload['merchant_ref'];
        $order = Order::where('order_number', $merchantRef)->first();

        if ($order && $order->payment_status === 'pending') {
            $order->update([
                'payment_status' => 'failed',
                'payment_data' => $payload,
            ]);
            Log::info('Tripay payment failed', ['order_id' => $order->id]);
        }

        return response()->json(['status' => 'ok']);
    }

    /**
     * Handle pending payments
     */
    private function handleMidtransPaymentPending(array $payload)
    {
        Log::info('Midtrans payment pending', $payload);
        return response()->json(['status' => 'ok']);
    }

    private function handleMidtransPaymentChallenge(array $payload)
    {
        Log::info('Midtrans payment challenge', $payload);
        return response()->json(['status' => 'ok']);
    }

    /**
     * Send payment confirmation and generate E-Tickets
     */
    private function sendPaymentConfirmation(Order $order)
    {
        try {
            // Load necessary relationships
            $order->load(['event', 'user', 'tickets']);

            // Update tickets status to active
            $order->tickets()->update(['status' => 'active']);

            // Check if event has auto-generate tickets enabled
            if ($order->event->auto_generate_tickets ?? true) {
                // Generate E-Tickets automatically using event's selected template
                $ticketGenerator = new \App\Services\TicketGeneratorService();
                $generatedTickets = [];

                foreach ($order->tickets as $ticket) {
                    try {
                        // Generate boarding pass ID if not exists
                        if (empty($ticket->boarding_pass_id)) {
                            $boardingPassService = app(\App\Services\BoardingPassService::class);
                            $boardingPassId = $boardingPassService->generateBoardingPassId($order->event->boarding_pass_template ?? 'unix');
                            $ticket->update(['boarding_pass_id' => $boardingPassId]);
                        }

                        // Generate PDF using selected template
                        $pdfPath = $ticketGenerator->generateTicketPdfFromEvent($ticket);

                        // Update ticket with PDF path and generation timestamp
                        $ticket->update([
                            'pdf_path' => $pdfPath,
                            'pdf_generated_at' => now(),
                            'template_used' => $order->event->boarding_pass_template ?? 'unix'
                        ]);

                        $generatedTickets[] = [
                            'ticket_id' => $ticket->id,
                            'ticket_number' => $ticket->ticket_number,
                            'boarding_pass_id' => $ticket->boarding_pass_id,
                            'pdf_path' => $pdfPath,
                            'template' => $order->event->boarding_pass_template ?? 'unix',
                            'success' => true
                        ];
                    } catch (\Exception $e) {
                        Log::error('Failed to generate ticket PDF', [
                            'ticket_id' => $ticket->id,
                            'order_id' => $order->id,
                            'error' => $e->getMessage()
                        ]);

                        $generatedTickets[] = [
                            'ticket_id' => $ticket->id,
                            'ticket_number' => $ticket->ticket_number,
                            'error' => $e->getMessage(),
                            'success' => false
                        ];
                    }
                }

                // Log generation results
                Log::info('E-Tickets auto-generation completed', [
                    'order_id' => $order->id,
                    'total_tickets' => count($generatedTickets),
                    'successful' => count(array_filter($generatedTickets, fn($t) => $t['success'])),
                    'failed' => count(array_filter($generatedTickets, fn($t) => !$t['success'])),
                    'template_used' => $order->event->boarding_pass_template ?? 'unix'
                ]);
            }

            // Check if event has email tickets enabled
            if ($order->event->email_tickets_to_buyers ?? true) {
                // Send email with E-Tickets
                \Mail::to($order->user->email)->send(new \App\Mail\TicketPurchasedMail($order));
            }

            // Create notification for user
            \App\Models\Notification::create([
                'user_id' => $order->user_id,
                'title' => 'E-Tiket Siap Diunduh!',
                'message' => "Pembayaran untuk pesanan #{$order->order_number} berhasil. E-Tiket Anda sudah siap diunduh.",
                'type' => 'ticket',
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'event_title' => $order->event->title,
                    'tickets_count' => $order->tickets->count(),
                    'template' => $order->event->boarding_pass_template ?? 'unix',
                    'auto_generated' => $order->event->auto_generate_tickets ?? true,
                    'email_sent' => $order->event->email_tickets_to_buyers ?? true
                ]
            ]);

            Log::info('Payment confirmation sent and E-Tickets processed', [
                'order_id' => $order->id,
                'tickets_count' => $order->tickets->count(),
                'template_used' => $order->event->boarding_pass_template ?? 'unix',
                'auto_generate' => $order->event->auto_generate_tickets ?? true,
                'email_enabled' => $order->event->email_tickets_to_buyers ?? true
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send payment confirmation or generate tickets', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Broadcast payment success for real-time updates
     */
    private function broadcastPaymentSuccess(Order $order)
    {
        try {
            // Create a simple notification file that can be checked by frontend
            $notificationData = [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'status' => 'paid',
                'total_amount' => $order->total_amount,
                'customer_name' => $order->customer_name,
                'event_title' => $order->event->title,
                'timestamp' => now()->toISOString(),
                'success_url' => route('orders.success', $order),
            ];

            // Store notification in cache for real-time checking
            cache()->put("payment_success_{$order->id}", $notificationData, now()->addMinutes(10));

            // Log for debugging
            Log::info('Payment success broadcasted', [
                'order_id' => $order->id,
                'notification_data' => $notificationData
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast payment success', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }


}

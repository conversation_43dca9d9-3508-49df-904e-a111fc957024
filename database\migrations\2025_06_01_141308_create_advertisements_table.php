<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('advertisements')) {
            Schema::create('advertisements', function (Blueprint $table) {
                $table->id();
                $table->string('title');
                $table->text('description')->nullable();
                $table->string('type')->default('banner'); // banner, sponsored_event, popup, sidebar
                $table->string('position')->default('top'); // top, bottom, sidebar, popup, between_events
                $table->string('image_url')->nullable();
                $table->string('click_url')->nullable();
                $table->json('targeting')->nullable(); // JSON for targeting criteria
                $table->decimal('cost_per_click', 10, 2)->default(0);
                $table->decimal('cost_per_impression', 10, 2)->default(0);
                $table->decimal('daily_budget', 10, 2)->default(0);
                $table->decimal('total_budget', 10, 2)->default(0);
                $table->decimal('spent_amount', 10, 2)->default(0);
                $table->integer('impressions')->default(0);
                $table->integer('clicks')->default(0);
                $table->integer('priority')->default(1); // 1-10, higher = more priority
                $table->boolean('is_active')->default(true);
                $table->boolean('is_approved')->default(false);
                $table->datetime('start_date')->nullable();
                $table->datetime('end_date')->nullable();
                $table->foreignId('advertiser_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('event_id')->nullable()->constrained('events')->onDelete('cascade');
                $table->string('status')->default('pending'); // pending, approved, rejected, paused, completed
                $table->text('admin_notes')->nullable();
                $table->timestamps();

                $table->index(['is_active', 'is_approved', 'start_date', 'end_date']);
                $table->index(['type', 'position']);
                $table->index(['advertiser_id']);
                $table->index(['status']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('advertisements');
    }
};

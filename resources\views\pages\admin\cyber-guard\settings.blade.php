@extends('layouts.admin')

@section('title', 'CyberGuard Settings')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-cog text-primary me-2"></i>CyberGuard Settings
            </h1>
            <p class="mb-0 text-muted">Configure security settings and protection level</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary" onclick="testConfiguration()">
                <i class="fas fa-vial me-2"></i>Test Config
            </button>
            <button type="button" class="btn btn-success" onclick="saveSettings()">
                <i class="fas fa-save me-2"></i>Save Settings
            </button>
        </div>
    </div>

    <!-- Status Overview -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Protection Status
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $settings['protection_enabled'] ?? true ? 'Active' : 'Disabled' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shield-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Protection Level
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ ucfirst($settings['protection_level'] ?? 'medium') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-layer-group fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Active Rules
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['active_rules'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Threats Today
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['threats_today'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form id="settingsForm">
        <div class="row">
            <!-- General Settings -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">General Protection</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="protection_enabled" 
                                       name="protection_enabled" {{ ($settings['protection_enabled'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="protection_enabled">
                                    Enable CyberGuard Protection
                                </label>
                            </div>
                            <small class="form-text text-muted">Master switch for all security features</small>
                        </div>

                        <div class="form-group">
                            <label for="protection_level">Protection Level</label>
                            <select class="form-control" id="protection_level" name="protection_level">
                                <option value="low" {{ ($settings['protection_level'] ?? 'medium') == 'low' ? 'selected' : '' }}>
                                    Low - Basic protection
                                </option>
                                <option value="medium" {{ ($settings['protection_level'] ?? 'medium') == 'medium' ? 'selected' : '' }}>
                                    Medium - Balanced protection
                                </option>
                                <option value="high" {{ ($settings['protection_level'] ?? 'medium') == 'high' ? 'selected' : '' }}>
                                    High - Maximum protection
                                </option>
                            </select>
                            <small class="form-text text-muted">Higher level may impact performance</small>
                        </div>

                        <div class="form-group">
                            <label for="log_retention_days">Log Retention (Days)</label>
                            <input type="number" class="form-control" id="log_retention_days" 
                                   name="log_retention_days" value="{{ $settings['log_retention_days'] ?? 30 }}" 
                                   min="1" max="365">
                            <small class="form-text text-muted">How long to keep security logs</small>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_block_threats" 
                                       name="auto_block_threats" {{ ($settings['auto_block_threats'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="auto_block_threats">
                                    Auto-block detected threats
                                </label>
                            </div>
                            <small class="form-text text-muted">Automatically block malicious IPs</small>
                        </div>
                    </div>
                </div>

                <!-- Rate Limiting -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Rate Limiting</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="rate_limiting_enabled" 
                                       name="rate_limiting_enabled" {{ ($settings['rate_limiting_enabled'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="rate_limiting_enabled">
                                    Enable Rate Limiting
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="max_requests_per_minute">Max Requests per Minute</label>
                            <input type="number" class="form-control" id="max_requests_per_minute" 
                                   name="max_requests_per_minute" value="{{ $settings['max_requests_per_minute'] ?? 60 }}" 
                                   min="1" max="1000">
                        </div>

                        <div class="form-group">
                            <label for="rate_limit_window">Rate Limit Window (seconds)</label>
                            <input type="number" class="form-control" id="rate_limit_window" 
                                   name="rate_limit_window" value="{{ $settings['rate_limit_window'] ?? 60 }}" 
                                   min="1" max="3600">
                        </div>

                        <div class="form-group">
                            <label for="rate_limit_action">Action on Rate Limit</label>
                            <select class="form-control" id="rate_limit_action" name="rate_limit_action">
                                <option value="block" {{ ($settings['rate_limit_action'] ?? 'block') == 'block' ? 'selected' : '' }}>
                                    Block Request
                                </option>
                                <option value="delay" {{ ($settings['rate_limit_action'] ?? 'block') == 'delay' ? 'selected' : '' }}>
                                    Delay Response
                                </option>
                                <option value="captcha" {{ ($settings['rate_limit_action'] ?? 'block') == 'captcha' ? 'selected' : '' }}>
                                    Show Captcha
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Threat Detection -->
            <div class="col-lg-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Threat Detection</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sql_injection_protection" 
                                       name="sql_injection_protection" {{ ($settings['sql_injection_protection'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="sql_injection_protection">
                                    SQL Injection Protection
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="xss_protection" 
                                       name="xss_protection" {{ ($settings['xss_protection'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="xss_protection">
                                    XSS Protection
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="brute_force_protection" 
                                       name="brute_force_protection" {{ ($settings['brute_force_protection'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="brute_force_protection">
                                    Brute Force Protection
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="ddos_protection" 
                                       name="ddos_protection" {{ ($settings['ddos_protection'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="ddos_protection">
                                    DDoS Protection
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="malware_scanning" 
                                       name="malware_scanning" {{ ($settings['malware_scanning'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="malware_scanning">
                                    Malware Scanning
                                </label>
                            </div>
                            <small class="form-text text-muted">Scan uploaded files for malware</small>
                        </div>

                        <div class="form-group">
                            <label for="threat_sensitivity">Threat Detection Sensitivity</label>
                            <select class="form-control" id="threat_sensitivity" name="threat_sensitivity">
                                <option value="low" {{ ($settings['threat_sensitivity'] ?? 'medium') == 'low' ? 'selected' : '' }}>
                                    Low - Fewer false positives
                                </option>
                                <option value="medium" {{ ($settings['threat_sensitivity'] ?? 'medium') == 'medium' ? 'selected' : '' }}>
                                    Medium - Balanced detection
                                </option>
                                <option value="high" {{ ($settings['threat_sensitivity'] ?? 'medium') == 'high' ? 'selected' : '' }}>
                                    High - Maximum detection
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Notifications</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="email_notifications" 
                                       name="email_notifications" {{ ($settings['email_notifications'] ?? true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="email_notifications">
                                    Email Notifications
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notification_email">Notification Email</label>
                            <input type="email" class="form-control" id="notification_email" 
                                   name="notification_email" value="{{ $settings['notification_email'] ?? '' }}" 
                                   placeholder="<EMAIL>">
                        </div>

                        <div class="form-group">
                            <label for="notification_threshold">Notification Threshold</label>
                            <select class="form-control" id="notification_threshold" name="notification_threshold">
                                <option value="all" {{ ($settings['notification_threshold'] ?? 'medium') == 'all' ? 'selected' : '' }}>
                                    All Events
                                </option>
                                <option value="medium" {{ ($settings['notification_threshold'] ?? 'medium') == 'medium' ? 'selected' : '' }}>
                                    Medium and Above
                                </option>
                                <option value="high" {{ ($settings['notification_threshold'] ?? 'medium') == 'high' ? 'selected' : '' }}>
                                    High and Critical Only
                                </option>
                                <option value="critical" {{ ($settings['notification_threshold'] ?? 'medium') == 'critical' ? 'selected' : '' }}>
                                    Critical Only
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="slack_notifications" 
                                       name="slack_notifications" {{ ($settings['slack_notifications'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="slack_notifications">
                                    Slack Notifications
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="slack_webhook">Slack Webhook URL</label>
                            <input type="url" class="form-control" id="slack_webhook" 
                                   name="slack_webhook" value="{{ $settings['slack_webhook'] ?? '' }}" 
                                   placeholder="https://hooks.slack.com/...">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Settings -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Advanced Settings</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="whitelist_ips">Whitelisted IPs</label>
                            <textarea class="form-control" id="whitelist_ips" name="whitelist_ips" rows="4" 
                                      placeholder="127.0.0.1&#10;***********/24">{{ $settings['whitelist_ips'] ?? '' }}</textarea>
                            <small class="form-text text-muted">One IP or CIDR per line</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="blacklist_ips">Blacklisted IPs</label>
                            <textarea class="form-control" id="blacklist_ips" name="blacklist_ips" rows="4" 
                                      placeholder="*******&#10;*******/24">{{ $settings['blacklist_ips'] ?? '' }}</textarea>
                            <small class="form-text text-muted">One IP or CIDR per line</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="blocked_countries">Blocked Countries</label>
                            <textarea class="form-control" id="blocked_countries" name="blocked_countries" rows="4" 
                                      placeholder="CN&#10;RU&#10;KP">{{ $settings['blocked_countries'] ?? '' }}</textarea>
                            <small class="form-text text-muted">ISO country codes, one per line</small>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="maintenance_mode" 
                                       name="maintenance_mode" {{ ($settings['maintenance_mode'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="maintenance_mode">
                                    Maintenance Mode
                                </label>
                            </div>
                            <small class="form-text text-muted">Block all traffic except whitelisted IPs</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="debug_mode" 
                                       name="debug_mode" {{ ($settings['debug_mode'] ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="debug_mode">
                                    Debug Mode
                                </label>
                            </div>
                            <small class="form-text text-muted">Enable detailed logging (performance impact)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <button type="button" class="btn btn-secondary me-3" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>Reset to Defaults
                </button>
                <button type="button" class="btn btn-outline-primary me-3" onclick="testConfiguration()">
                    <i class="fas fa-vial me-2"></i>Test Configuration
                </button>
                <button type="button" class="btn btn-success" onclick="saveSettings()">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
function saveSettings() {
    const formData = new FormData(document.getElementById('settingsForm'));
    
    // Convert FormData to JSON
    const settings = {};
    for (let [key, value] of formData.entries()) {
        if (document.querySelector(`[name="${key}"]`).type === 'checkbox') {
            settings[key] = document.querySelector(`[name="${key}"]`).checked;
        } else {
            settings[key] = value;
        }
    }
    
    // Add unchecked checkboxes as false
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        if (!formData.has(checkbox.name)) {
            settings[checkbox.name] = false;
        }
    });
    
    fetch('/admin/cyber-guard/settings', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Settings saved successfully!');
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving settings.');
    });
}

function testConfiguration() {
    fetch('/admin/cyber-guard/test-config', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Configuration test passed!\n\n' + data.message);
        } else {
            alert('Configuration test failed!\n\n' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while testing configuration.');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults? This will overwrite your current configuration.')) {
        fetch('/admin/cyber-guard/reset-defaults', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while resetting settings.');
        });
    }
}

// Protection level change handler
document.getElementById('protection_level').addEventListener('change', function() {
    const level = this.value;
    const presets = {
        'low': {
            'max_requests_per_minute': 120,
            'threat_sensitivity': 'low',
            'auto_block_threats': false
        },
        'medium': {
            'max_requests_per_minute': 60,
            'threat_sensitivity': 'medium',
            'auto_block_threats': true
        },
        'high': {
            'max_requests_per_minute': 30,
            'threat_sensitivity': 'high',
            'auto_block_threats': true
        }
    };
    
    if (presets[level]) {
        Object.keys(presets[level]).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = presets[level][key];
                } else {
                    element.value = presets[level][key];
                }
            }
        });
    }
});
</script>
@endpush
@endsection

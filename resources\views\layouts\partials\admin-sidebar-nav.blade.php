<!-- Home -->
<a href="{{ route('home') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('home') ? 'bg-violet-50 dark:bg-violet-900/20 text-violet-700 dark:text-violet-300 border border-violet-200 dark:border-violet-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('home') ? 'text-violet-600 dark:text-violet-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="home" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Home</span>
    @if(request()->routeIs('home'))
        <div class="ml-auto w-2 h-2 bg-violet-600 dark:bg-violet-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Dashboard -->
<a href="{{ route('admin.dashboard') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.dashboard') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.dashboard') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="layout-dashboard" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Dashboard</span>
    @if(request()->routeIs('admin.dashboard'))
        <div class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Events Management -->
<a href="{{ route('admin.tickets.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.tickets.*') ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.tickets.*') ? 'text-purple-600 dark:text-purple-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="calendar" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Events</span>
    @if(request()->routeIs('admin.tickets.*'))
        <div class="ml-auto w-2 h-2 bg-purple-600 dark:bg-purple-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Users Management -->
<a href="{{ route('admin.users.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.users.*') ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.users.*') ? 'text-green-600 dark:text-green-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="users" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Users</span>
    @if(request()->routeIs('admin.users.*'))
        <div class="ml-auto w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Staff Management -->
<a href="{{ route('admin.staff-management.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.staff-management.*') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.staff-management.*') ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="user-check" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Staff Management</span>
    @if(request()->routeIs('admin.staff-management.*'))
        <div class="ml-auto w-2 h-2 bg-indigo-600 dark:bg-indigo-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Orders Management -->
<a href="{{ route('admin.orders.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.orders.*') ? 'bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.orders.*') ? 'text-orange-600 dark:text-orange-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="shopping-cart" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Orders</span>
    @if(request()->routeIs('admin.orders.*'))
        <div class="ml-auto w-2 h-2 bg-orange-600 dark:bg-orange-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- UangTix Management -->
<a href="{{ route('admin.uangtix.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.uangtix.*') ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.uangtix.*') ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="coins" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">UangTix</span>
    @if(request()->routeIs('admin.uangtix.*'))
        <div class="ml-auto w-2 h-2 bg-yellow-600 dark:bg-yellow-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Payment Methods (Admin Only) -->
@if(auth()->user()->role === 'admin')
<a href="{{ route('admin.payment-methods.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.payment-methods.*') ? 'bg-indigo-50 dark:bg-indigo-900/20 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.payment-methods.*') ? 'text-indigo-600 dark:text-indigo-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="credit-card" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Payment Methods</span>
    @if(request()->routeIs('admin.payment-methods.*'))
        <div class="ml-auto w-2 h-2 bg-indigo-600 dark:bg-indigo-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>
@endif

<!-- Payments -->
<a href="{{ route('admin.payments.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.payments.*') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.payments.*') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="receipt" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Payments</span>
    @if(request()->routeIs('admin.payments.*'))
        <div class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Badge Level (Admin Only) -->
@if(auth()->user()->role === 'admin')
<a href="{{ route('admin.badge-level.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.badge-level.*') ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border border-amber-200 dark:border-amber-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.badge-level.*') ? 'text-amber-600 dark:text-amber-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="award" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Badge Level</span>
    @if(request()->routeIs('admin.badge-level.*'))
        <div class="ml-auto w-2 h-2 bg-amber-600 dark:bg-amber-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>
@endif

<!-- Ads Management (Admin Only) -->
@if(auth()->user()->role === 'admin')
<a href="{{ route('admin.ads.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.ads.*') ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.ads.*') ? 'text-emerald-600 dark:text-emerald-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="megaphone" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Ads Management</span>
    @if(request()->routeIs('admin.ads.*'))
        <div class="ml-auto w-2 h-2 bg-emerald-600 dark:bg-emerald-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>
@endif

<!-- Vouchers -->
<a href="{{ route('admin.vouchers.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.vouchers.*') ? 'bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border border-emerald-200 dark:border-emerald-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.vouchers.*') ? 'text-emerald-600 dark:text-emerald-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="tag" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Vouchers</span>
    @if(request()->routeIs('admin.vouchers.*'))
        <div class="ml-auto w-2 h-2 bg-emerald-600 dark:bg-emerald-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Categories -->
<a href="{{ route('admin.categories.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.categories.*') ? 'bg-cyan-50 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300 border border-cyan-200 dark:border-cyan-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.categories.*') ? 'text-cyan-600 dark:text-cyan-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="folder" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Categories</span>
    @if(request()->routeIs('admin.categories.*'))
        <div class="ml-auto w-2 h-2 bg-cyan-600 dark:bg-cyan-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Notifications -->
<a href="{{ route('admin.notifications.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.notifications.*') ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.notifications.*') ? 'text-red-600 dark:text-red-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="bell" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Notifications</span>
    @if(request()->routeIs('admin.notifications.*'))
        <div class="ml-auto w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Organizers -->
<a href="{{ route('admin.organizers.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.organizers.*') ? 'bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 border border-teal-200 dark:border-teal-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.organizers.*') ? 'text-teal-600 dark:text-teal-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="building" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Organizers</span>
    @if(request()->routeIs('admin.organizers.*'))
        <div class="ml-auto w-2 h-2 bg-teal-600 dark:bg-teal-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Divider -->
<div class="my-4 border-t border-gray-200 dark:border-gray-700"></div>

<!-- Analytics -->
<a href="{{ route('admin.analytics.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.analytics.*') ? 'bg-pink-50 dark:bg-pink-900/20 text-pink-700 dark:text-pink-300 border border-pink-200 dark:border-pink-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.analytics.*') ? 'text-pink-600 dark:text-pink-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="bar-chart-3" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Analytics</span>
    @if(request()->routeIs('admin.analytics.*'))
        <div class="ml-auto w-2 h-2 bg-pink-600 dark:bg-pink-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Content Management Section -->
@if(auth()->user()->role === 'admin')
<!-- Section Header -->
<div class="px-3 py-2">
    <h3 class="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider"
        :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Content Management</h3>
</div>

<!-- Banner Management -->
<a href="{{ route('admin.banners.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.banners.*') ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.banners.*') ? 'text-green-600 dark:text-green-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="image" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Banner Management</span>
    @if(request()->routeIs('admin.banners.*'))
        <div class="ml-auto w-2 h-2 bg-green-600 dark:bg-green-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Reports Management -->
<a href="{{ route('admin.reports.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.reports.*') ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200 dark:border-blue-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.reports.*') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="file-text" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Reports Management</span>
    @if(request()->routeIs('admin.reports.*'))
        <div class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Feedback Management -->
<a href="{{ route('admin.feedback.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.feedback.*') ? 'bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.feedback.*') ? 'text-purple-600 dark:text-purple-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="message-circle" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Feedback Management</span>
    @if(request()->routeIs('admin.feedback.*'))
        <div class="ml-auto w-2 h-2 bg-purple-600 dark:bg-purple-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

<!-- Divider -->
<div class="my-4 border-t border-gray-200 dark:border-gray-700"></div>

<!-- CyberGuard Security (Admin Only) -->
<a href="{{ route('admin.cyber-guard.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.cyber-guard.*') ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.cyber-guard.*') ? 'text-red-600 dark:text-red-400' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="shield" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">CyberGuard</span>
    @if(request()->routeIs('admin.cyber-guard.*'))
        <div class="ml-auto w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>
@endif

<!-- Settings -->
<a href="{{ route('admin.settings.index') }}"
   class="group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 {{ request()->routeIs('admin.settings.*') ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 hover:text-gray-900 dark:hover:text-white' }}"
   :class="sidebarCollapsed ? 'justify-center px-2' : ''">
    <div class="flex items-center justify-center w-6 h-6 {{ request()->routeIs('admin.settings.*') ? 'text-gray-700 dark:text-gray-300' : 'text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300' }}"
         :class="sidebarCollapsed ? 'mr-0' : 'mr-3'">
        <i data-lucide="settings" class="w-5 h-5"></i>
    </div>
    <span class="truncate transition-opacity duration-300"
          :class="sidebarCollapsed ? 'opacity-0 w-0 overflow-hidden' : 'opacity-100'">Settings</span>
    @if(request()->routeIs('admin.settings.*'))
        <div class="ml-auto w-2 h-2 bg-gray-600 dark:bg-gray-400 rounded-full transition-opacity duration-300"
             :class="sidebarCollapsed ? 'opacity-0' : 'opacity-100'"></div>
    @endif
</a>

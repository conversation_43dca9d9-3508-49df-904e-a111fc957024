<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TicketTemplate;

class TicketTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating ticket templates...');

        $templates = [
            [
                'name' => 'Modern Boarding Pass',
                'slug' => 'modern-boarding-pass',
                'description' => 'Template modern dengan desain boarding pass yang elegan dan profesional',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#3B82F6',
                    'secondary_color' => '#E5E7EB',
                    'text_color' => '#1F2937',
                    'layout' => 'modern',
                    'elements' => [
                        'header' => ['show' => true, 'style' => 'gradient'],
                        'qr_code' => ['show' => true, 'size' => '120px'],
                        'perforated_edge' => true,
                        'corner_cuts' => true
                    ]
                ],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'Classic Ticket',
                'slug' => 'classic-ticket',
                'description' => 'Template klasik dengan desain tiket tradisional yang timeless',
                'template_data' => [
                    'background_color' => '#F9FAFB',
                    'primary_color' => '#059669',
                    'secondary_color' => '#D1D5DB',
                    'text_color' => '#1F2937',
                    'layout' => 'classic',
                    'elements' => [
                        'border' => ['show' => true, 'style' => 'dashed'],
                        'corner_decorations' => true,
                        'vintage_style' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Minimal Design',
                'slug' => 'minimal-design',
                'description' => 'Template minimalis dengan fokus pada informasi penting dan clean design',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1F2937',
                    'secondary_color' => '#9CA3AF',
                    'text_color' => '#1F2937',
                    'layout' => 'minimal',
                    'elements' => [
                        'shadows' => false,
                        'borders' => ['style' => 'thin'],
                        'clean_typography' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Elegant Style',
                'slug' => 'elegant-style',
                'description' => 'Template elegan dengan sentuhan mewah dan sophisticated',
                'template_data' => [
                    'background_color' => '#1F2937',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#374151',
                    'text_color' => '#ffffff',
                    'layout' => 'elegant',
                    'elements' => [
                        'gradient_background' => true,
                        'gold_accents' => true,
                        'luxury_typography' => true,
                        'decorative_elements' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Festival Theme',
                'slug' => 'festival-theme',
                'description' => 'Template colorful dan vibrant untuk event festival dan musik',
                'template_data' => [
                    'background_color' => '#7C3AED',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#EC4899',
                    'text_color' => '#ffffff',
                    'layout' => 'festival',
                    'elements' => [
                        'colorful_gradient' => true,
                        'music_icons' => true,
                        'vibrant_colors' => true,
                        'party_atmosphere' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Corporate Style',
                'slug' => 'corporate-style',
                'description' => 'Template profesional untuk event corporate dan business',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1E40AF',
                    'secondary_color' => '#E5E7EB',
                    'text_color' => '#1F2937',
                    'layout' => 'corporate',
                    'elements' => [
                        'professional_layout' => true,
                        'company_branding' => true,
                        'formal_typography' => true,
                        'business_style' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($templates as $templateData) {
            $template = TicketTemplate::firstOrCreate(
                ['slug' => $templateData['slug']],
                $templateData
            );

            if ($template->wasRecentlyCreated) {
                $this->command->line("✓ Created template: {$template->name}");
            } else {
                $this->command->line("- Template already exists: {$template->name}");
            }
        }

        // Update existing events to use default template
        $defaultTemplate = TicketTemplate::where('is_default', true)->first();
        if ($defaultTemplate) {
            $eventsUpdated = \App\Models\Event::whereNull('ticket_template_id')
                ->update(['ticket_template_id' => $defaultTemplate->id]);
            
            if ($eventsUpdated > 0) {
                $this->command->info("✓ Updated {$eventsUpdated} events to use default template");
            }
        }

        $this->command->info('✅ Ticket templates seeding completed!');
    }
}

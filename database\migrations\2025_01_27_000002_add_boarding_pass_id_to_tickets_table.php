<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $columns = Schema::getColumnListing('tickets');
            
            // Add boarding pass ID field if it doesn't exist
            if (!in_array('boarding_pass_id', $columns)) {
                $table->string('boarding_pass_id')->nullable()->unique()->after('ticket_number');
            }
            
            // Add template used field if it doesn't exist
            if (!in_array('template_used', $columns)) {
                $table->string('template_used')->default('unix')->after('boarding_pass_id');
            }
            
            // Add generation timestamp if it doesn't exist
            if (!in_array('generated_at', $columns)) {
                $table->timestamp('generated_at')->nullable()->after('template_used');
            }
        });
        
        // Add index for better performance
        try {
            Schema::table('tickets', function (Blueprint $table) {
                $table->index(['boarding_pass_id', 'template_used']);
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore the error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropIndex(['boarding_pass_id', 'template_used']);
            $table->dropColumn([
                'boarding_pass_id',
                'template_used',
                'generated_at'
            ]);
        });
    }
};

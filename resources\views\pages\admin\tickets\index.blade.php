@extends('layouts.admin')

@section('title', 'Tickets Management - Admin Dashboard')

@push('styles')
<style>
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-card-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stats-card-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.stats-card-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.ticket-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.ticket-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-left-color: #3B82F6;
}

.ticket-card.active {
    border-left-color: #10B981;
}

.ticket-card.used {
    border-left-color: #6B7280;
}

.ticket-card.cancelled {
    border-left-color: #EF4444;
}

.filter-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8 slide-in-up">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div>
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <i class="fas fa-ticket-alt mr-3"></i>
                        Tickets Management
                    </h1>
                    <p class="mt-2 text-gray-600 text-lg">Comprehensive ticket management and analytics dashboard</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <button onclick="refreshData()" class="flex items-center justify-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                    <a href="{{ route('admin.tickets.create') }}"
                       class="flex items-center justify-center px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-plus mr-2"></i>
                        Create Ticket
                    </a>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" data-aos="fade-up">
            <!-- Total Tickets -->
            <div class="stats-card rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Total Tickets</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $tickets->total() ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-chart-line text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">All time</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-ticket-alt text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Active Tickets -->
            <div class="stats-card-success rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Active</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $tickets->where('status', 'active')->count() ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-check-circle text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">Valid tickets</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Used Tickets -->
            <div class="stats-card-warning rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Used</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $tickets->where('status', 'used')->count() ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-clock text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">Validated</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-history text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Revenue -->
            <div class="stats-card-secondary rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Revenue</p>
                        <p class="text-3xl font-bold text-white mt-2">
                            Rp {{ number_format($tickets->sum('price') ?? 0, 0, ',', '.') }}
                        </p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-money-bill-wave text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">Total earnings</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-2xl text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="filter-card rounded-xl p-6 shadow-lg mb-8" data-aos="fade-up" data-aos-delay="100">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4 flex-1">
                    <!-- Search -->
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               id="searchInput"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search tickets by number, user, or event..."
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Status Filter -->
                    <div class="relative">
                        <select id="statusFilter"
                                name="status"
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="used" {{ request('status') == 'used' ? 'selected' : '' }}>Used</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                        </select>
                    </div>

                    <!-- Event Filter -->
                    <div class="relative">
                        <select id="eventFilter"
                                name="event_id"
                                class="block w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Events</option>
                            @if(isset($events))
                                @foreach($events as $event)
                                    <option value="{{ $event->id }}" {{ request('event_id') == $event->id ? 'selected' : '' }}>
                                        {{ $event->title }}
                                    </option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button onclick="exportTickets()" class="flex items-center px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-lg transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </button>
                    <button onclick="clearFilters()" class="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Tickets Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
            @forelse($tickets as $ticket)
            <div class="ticket-card {{ strtolower($ticket->status ?? 'active') }} bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300">
                <!-- Ticket Header -->
                <div class="relative bg-gradient-to-br from-blue-500 to-purple-600 p-6 text-white">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-bold mb-1">{{ $ticket->ticket_number ?? 'N/A' }}</h3>
                            <p class="text-blue-100 text-sm">{{ $ticket->event->title ?? 'No Event' }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full
                                @if(($ticket->status ?? 'active') === 'active') bg-green-100 text-green-800
                                @elseif(($ticket->status ?? 'active') === 'used') bg-gray-100 text-gray-800
                                @elseif(($ticket->status ?? 'active') === 'cancelled') bg-red-100 text-red-800
                                @else bg-yellow-100 text-yellow-800
                                @endif">
                                {{ ucfirst($ticket->status ?? 'active') }}
                            </span>
                        </div>
                    </div>

                    <!-- QR Code Icon -->
                    <div class="absolute bottom-4 right-4 w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-qrcode text-xl"></i>
                    </div>
                </div>

                <!-- Ticket Content -->
                <div class="p-6">
                    <!-- User Info -->
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-user text-gray-400 mr-2"></i>
                            <span class="font-medium text-gray-900">{{ $ticket->attendee_name ?? $ticket->user->name ?? 'N/A' }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-envelope text-gray-400 mr-2"></i>
                            <span>{{ $ticket->order->customer_email ?? $ticket->user->email ?? 'N/A' }}</span>
                        </div>
                    </div>

                    <!-- Event Details -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-calendar-alt text-gray-400 mr-2 w-4"></i>
                            <span>{{ $ticket->event->start_date ? $ticket->event->start_date->format('d M Y, H:i') : 'No Date' }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-map-marker-alt text-gray-400 mr-2 w-4"></i>
                            <span class="line-clamp-1">{{ $ticket->event->venue_name ?? 'No Venue' }}</span>
                        </div>
                        @if($ticket->seat_number)
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-chair text-gray-400 mr-2 w-4"></i>
                            <span>Seat: {{ $ticket->seat_number }}</span>
                        </div>
                        @endif
                    </div>

                    <!-- Price and Date -->
                    <div class="flex justify-between items-center mb-4 p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="text-lg font-bold text-gray-900">
                                Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-gray-500">Price</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-gray-900">
                                {{ $ticket->created_at ? $ticket->created_at->format('d M Y') : 'N/A' }}
                            </div>
                            <div class="text-xs text-gray-500">Created</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-2">
                        <a href="{{ route('admin.tickets.show', $ticket->id) }}"
                           class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-eye mr-1"></i>
                            View
                        </a>
                        <a href="{{ route('admin.tickets.edit', $ticket->id) }}"
                           class="flex-1 bg-yellow-100 hover:bg-yellow-200 text-yellow-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        <button onclick="downloadTicket({{ $ticket->id }})"
                                class="flex-1 bg-green-100 hover:bg-green-200 text-green-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-download mr-1"></i>
                            PDF
                        </button>
                    </div>
                </div>
            </div>
            @empty
            <!-- Empty State -->
            <div class="col-span-full">
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-ticket-alt text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Tickets Found</h3>
                    <p class="text-gray-600 mb-6">
                        @if(request('search') || request('status') || request('event_id'))
                            No tickets match your current filters. Try adjusting your search criteria.
                        @else
                            No tickets have been created yet. Start by creating your first ticket!
                        @endif
                    </p>
                    <a href="{{ route('admin.tickets.create') }}"
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-plus mr-2"></i>
                        Create Your First Ticket
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($tickets->hasPages())
        <div class="mt-8 flex justify-center" data-aos="fade-up" data-aos-delay="300">
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4">
                {{ $tickets->links() }}
            </div>
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 600,
        easing: 'ease-out-cubic',
        once: true
    });

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const eventFilter = document.getElementById('eventFilter');

    let searchTimeout;

    function performSearch() {
        const searchValue = searchInput.value;
        const statusValue = statusFilter.value;
        const eventValue = eventFilter.value;

        const url = new URL(window.location.href);
        url.searchParams.set('search', searchValue);
        url.searchParams.set('status', statusValue);
        url.searchParams.set('event_id', eventValue);

        if (!searchValue) url.searchParams.delete('search');
        if (!statusValue) url.searchParams.delete('status');
        if (!eventValue) url.searchParams.delete('event_id');

        window.location.href = url.toString();
    }

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 500);
    });

    statusFilter.addEventListener('change', performSearch);
    eventFilter.addEventListener('change', performSearch);
});

// Utility functions
function refreshData() {
    window.location.reload();
}

function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('search');
    url.searchParams.delete('status');
    url.searchParams.delete('event_id');
    window.location.href = url.toString();
}

function exportTickets() {
    const url = new URL('{{ route("admin.tickets.export") }}', window.location.origin);

    // Add current filters to export
    const searchParams = new URLSearchParams(window.location.search);
    for (const [key, value] of searchParams) {
        url.searchParams.set(key, value);
    }

    window.open(url.toString(), '_blank');
}

function downloadTicket(ticketId) {
    window.open(`/admin/tickets/${ticketId}/download`, '_blank');
}

// Bulk actions
function selectAllTickets() {
    const checkboxes = document.querySelectorAll('input[name="ticket_ids[]"]');
    const selectAll = document.getElementById('selectAll');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const checkedBoxes = document.querySelectorAll('input[name="ticket_ids[]"]:checked');
    const bulkActions = document.getElementById('bulkActions');

    if (checkedBoxes.length > 0) {
        bulkActions.classList.remove('hidden');
    } else {
        bulkActions.classList.add('hidden');
    }
}

function performBulkAction(action) {
    const checkedBoxes = document.querySelectorAll('input[name="ticket_ids[]"]:checked');
    const ticketIds = Array.from(checkedBoxes).map(cb => cb.value);

    if (ticketIds.length === 0) {
        alert('Please select at least one ticket');
        return;
    }

    if (confirm(`Are you sure you want to ${action} ${ticketIds.length} ticket(s)?`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ route("admin.tickets.bulk-action") }}';

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        form.appendChild(actionInput);

        // Add ticket IDs
        ticketIds.forEach(id => {
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'ticket_ids[]';
            idInput.value = id;
            form.appendChild(idInput);
        });

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_badge_level', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#6b7280'); // Hex color
            $table->string('icon')->nullable();
            $table->string('badge_image')->nullable();
            $table->decimal('min_spent_amount', 15, 2)->default(0);
            $table->decimal('min_uangtix_balance', 15, 2)->default(0);
            $table->integer('min_transactions')->default(0);
            $table->integer('min_events_attended')->default(0);
            $table->decimal('discount_percentage', 5, 2)->default(0);
            $table->decimal('cashback_percentage', 5, 2)->default(0);
            $table->json('benefits')->nullable(); // Store benefits as JSON
            $table->json('requirements')->nullable(); // Store requirements as JSON
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            // Indexes
            $table->index('is_active');
            $table->index('is_default');
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_badge_level');
    }
};

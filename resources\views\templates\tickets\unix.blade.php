<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unix Terminal E-Ticket</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background: #0d1117;
            color: #c9d1d9;
            padding: 20px;
        }

        .terminal-ticket {
            max-width: 800px;
            margin: 0 auto;
            background: #161b22;
            border: 2px solid #30363d;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.4);
        }

        .terminal-header {
            background: #21262d;
            padding: 12px 16px;
            border-bottom: 1px solid #30363d;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-buttons {
            display: flex;
            gap: 6px;
        }

        .terminal-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .close { background: #ff5f56; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #27ca3f; }

        .terminal-title {
            color: #8b949e;
            font-size: 12px;
            margin-left: 12px;
        }

        .terminal-content {
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
        }

        .prompt {
            color: #7c3aed;
            font-weight: 500;
        }

        .command {
            color: #58a6ff;
        }

        .output {
            color: #c9d1d9;
            margin-left: 20px;
        }

        .success {
            color: #3fb950;
        }

        .warning {
            color: #d29922;
        }

        .error {
            color: #f85149;
        }

        .ticket-info {
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
        }

        .ascii-art {
            color: #58a6ff;
            font-size: 10px;
            line-height: 1.2;
            text-align: center;
            margin: 16px 0;
        }

        .qr-section {
            text-align: center;
            margin: 20px 0;
            padding: 16px;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
        }

        .qr-code {
            display: inline-block;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        .footer-info {
            border-top: 1px solid #30363d;
            padding-top: 16px;
            margin-top: 20px;
            font-size: 12px;
            color: #8b949e;
        }

        @media print {
            body { background: white; color: black; }
            .terminal-ticket { border-color: #ccc; }
            .terminal-header { background: #f6f8fa; }
        }
    </style>
</head>
<body>
    <div class="terminal-ticket">
        <!-- Terminal Header -->
        <div class="terminal-header">
            <div class="terminal-buttons">
                <div class="terminal-button close"></div>
                <div class="terminal-button minimize"></div>
                <div class="terminal-button maximize"></div>
            </div>
            <div class="terminal-title">tixara@terminal: ~/e-tickets</div>
        </div>

        <!-- Terminal Content -->
        <div class="terminal-content">
            <div class="ascii-art">
╔══════════════════════════════════════════════════════════════════════════════╗
║  ████████╗██╗██╗  ██╗ █████╗ ██████╗  █████╗     ███████╗      ████████╗██╗ ║
║  ╚══██╔══╝██║╚██╗██╔╝██╔══██╗██╔══██╗██╔══██╗    ██╔════╝      ╚══██╔══╝██║ ║
║     ██║   ██║ ╚███╔╝ ███████║██████╔╝███████║    █████╗           ██║   ██║ ║
║     ██║   ██║ ██╔██╗ ██╔══██║██╔══██╗██╔══██║    ██╔══╝           ██║   ╚═╝ ║
║     ██║   ██║██╔╝ ██╗██║  ██║██║  ██║██║  ██║    ███████╗         ██║   ██╗ ║
║     ╚═╝   ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝    ╚══════╝         ╚═╝   ╚═╝ ║
╚══════════════════════════════════════════════════════════════════════════════╝
            </div>

            <div><span class="prompt">user@tixara:~$</span> <span class="command">cat ticket_info.txt</span></div>
            <div class="ticket-info">
                <div class="output">
                    <div><span class="success">✓</span> TICKET VALIDATED SUCCESSFULLY</div>
                    <div>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</div>
                    <div><strong>EVENT:</strong> {{ $ticket['event_title'] ?? 'Sample Event' }}</div>
                    <div><strong>VENUE:</strong> {{ $ticket['venue_name'] ?? 'Sample Venue' }}</div>
                    <div><strong>DATE:</strong> {{ $ticket['event_date'] ?? '2024-12-31' }}</div>
                    <div><strong>TIME:</strong> {{ $ticket['event_time'] ?? '19:00 WIB' }}</div>
                    <div>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</div>
                    <div><strong>TICKET ID:</strong> <span class="warning">{{ $ticket['ticket_number'] ?? 'TIX-UNIX-001' }}</span></div>
                    <div><strong>BOARDING PASS:</strong> <span class="success">{{ $ticket['boarding_pass_id'] ?? 'BP250127ABC123' }}</span></div>
                    <div><strong>HOLDER:</strong> {{ $ticket['buyer_name'] ?? 'John Doe' }}</div>
                    <div><strong>EMAIL:</strong> {{ $ticket['buyer_email'] ?? '<EMAIL>' }}</div>
                    <div><strong>PRICE:</strong> {{ $ticket['formatted_price'] ?? 'Rp 150.000' }}</div>
                    <div>━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━</div>
                </div>
            </div>

            <div><span class="prompt">user@tixara:~$</span> <span class="command">generate_qr_code --ticket-id {{ $ticket['ticket_number'] ?? 'TIX-UNIX-001' }}</span></div>
            <div class="qr-section">
                <div class="output success">QR Code generated successfully!</div>
                <div class="qr-code">
                    {!! QrCode::size(120)->generate($ticket['qr_code'] ?? 'SAMPLE-QR-CODE') !!}
                </div>
                <div class="output" style="margin-top: 10px;">
                    <small>Scan this QR code at venue entrance</small>
                </div>
            </div>

            <div><span class="prompt">user@tixara:~$</span> <span class="command">verify_ticket_status</span></div>
            <div class="output">
                <div><span class="success">STATUS:</span> ACTIVE</div>
                <div><span class="success">VALIDATION:</span> READY FOR SCAN</div>
                <div><span class="warning">USAGE:</span> SINGLE USE ONLY</div>
            </div>

            <div class="footer-info">
                <div><span class="prompt">user@tixara:~$</span> <span class="command">cat system_info.txt</span></div>
                <div class="output">
                    <div>Generated: {{ now()->format('Y-m-d H:i:s T') }}</div>
                    <div>System: TiXara E-Ticket v2.0</div>
                    <div>Terminal: Unix Boarding Pass Template</div>
                    <div>Support: <EMAIL> | +62 812-3456-7890</div>
                    <div>Website: https://tixara.my.id</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

# UangTix - Digital Wallet (GoPay/OVO Style)

## 📱 Overview
UangTix adalah dompet digital yang terintegrasi dengan platform TiXara, dirancang dengan gaya modern mirip GoPay/OVO. Aplikasi ini mendukung PWA (Progressive Web App) dan responsive design untuk desktop dan mobile.

## ✨ Features

### 🎨 Design Features
- **GoPay/OVO Style Interface** - Modern, clean, dan user-friendly
- **Responsive Design** - Optimal untuk desktop, tablet, dan mobile
- **PWA Support** - Dapat diinstall sebagai aplikasi mobile
- **Floating Footer** - Navigasi mudah untuk mobile
- **Dark/Light Theme** - Mendukung tema yang sudah ada

### 💰 Wallet Features
- **Balance Display** - Tampilan saldo UTX dan konversi IDR
- **Top Up/Deposit** - Isi saldo dengan berbagai metode pembayaran
- **Withdrawal** - <PERSON><PERSON> dana ke rekening bank
- **Transfer** - <PERSON><PERSON> UangTix ke pengguna lain
- **Transaction History** - Riwayat transaksi lengkap
- **Real-time Updates** - Saldo dan transaksi update otomatis

### 🔧 Technical Features
- **Real-time Calculations** - Kalkulasi fee dan konversi otomatis
- **Form Validation** - Validasi input yang komprehensif
- **Toast Notifications** - Notifikasi yang elegan
- **Loading States** - Feedback visual untuk semua aksi
- **Error Handling** - Penanganan error yang baik

## 🚀 Installation & Setup

### Prerequisites
- PHP 8.1+
- Laravel 10+
- MySQL/PostgreSQL
- Node.js & NPM

### Database Setup
Pastikan tabel UangTix sudah ada:
```bash
php artisan migrate
php artisan db:seed --class=UangTixSeeder
```

### Frontend Assets
```bash
npm install
npm run build
```

## 📱 Mobile PWA Features

### Installation
- Aplikasi dapat diinstall sebagai PWA
- Banner install otomatis muncul di mobile
- Support offline mode dengan service worker

### Mobile Optimizations
- Touch-friendly buttons (min 48px)
- Floating footer navigation
- Swipe gestures support
- Safe area handling untuk notch devices

## 🎯 Usage

### Accessing UangTix
1. Login ke aplikasi TiXara
2. Klik menu "UangTix" di floating footer (mobile) atau navigasi (desktop)
3. Dashboard UangTix akan terbuka dengan tampilan GoPay/OVO style

### Top Up Process
1. Klik tombol "Top Up" di balance card atau quick actions
2. Masukkan jumlah deposit (minimum Rp 10,000)
3. Pilih metode pembayaran (Bank Transfer, E-Wallet, QRIS)
4. Review ringkasan dan konfirmasi
5. Ikuti instruksi pembayaran

### Withdrawal Process
1. Klik tombol "Tarik" atau "Tarik Dana"
2. Masukkan jumlah penarikan dalam UTX
3. Pilih bank dan masukkan detail rekening
4. Review fee dan jumlah yang diterima
5. Submit permintaan (perlu approval admin)

### Transfer Process
1. Klik tombol "Transfer"
2. Masukkan email penerima
3. Masukkan jumlah transfer
4. Tambahkan catatan (opsional)
5. Konfirmasi transfer

## 🔧 Technical Implementation

### Frontend Stack
- **CSS Framework**: Custom CSS dengan CSS Variables
- **Icons**: FontAwesome 6.4.0
- **Modals**: Bootstrap 5.3.0
- **Animations**: CSS Transitions & Transforms
- **PWA**: Service Worker + Web App Manifest

### Backend Integration
- **Controller**: `UangTixController`
- **Models**: `UangTixBalance`, `UangTixTransaction`, `UangTixRequest`
- **Routes**: `/uangtix/*` dengan middleware auth
- **API Endpoints**: RESTful API untuk AJAX calls

### Security Features
- CSRF Protection
- Authentication Required
- Input Validation
- XSS Prevention
- SQL Injection Protection

## 📊 API Endpoints

### Main Routes
- `GET /uangtix` - Dashboard
- `GET /uangtix/balance` - Get balance info
- `GET /uangtix/transactions` - Transaction history
- `POST /uangtix/deposit` - Create deposit request
- `POST /uangtix/withdraw` - Create withdrawal request
- `POST /uangtix/transfer` - Transfer to another user

### Response Format
```json
{
    "success": true,
    "message": "Operation successful",
    "data": {
        "balance": 100000,
        "balance_idr": 100000
    }
}
```

## 🎨 Styling Guide

### Color Scheme
```css
:root {
    --primary-blue: #0066CC;    /* GoPay Blue */
    --primary-green: #00AA5B;   /* OVO Green */
    --secondary-blue: #E6F3FF;  /* Light Blue */
    --text-dark: #1A1A1A;       /* Dark Text */
    --text-gray: #666666;       /* Gray Text */
    --bg-light: #F8FAFC;        /* Light Background */
}
```

### Responsive Breakpoints
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px

### Component Structure
- Header dengan gradient background
- Balance card dengan glassmorphism effect
- Quick actions grid (4 columns)
- Statistics cards (2x2 grid mobile, 4x1 desktop)
- Transaction list dengan hover effects

## 🔄 Real-time Features

### Auto-refresh
- Balance refresh setiap 30 detik
- Transaction list update otomatis
- Real-time calculation untuk fee

### Live Updates
- Toast notifications untuk feedback
- Loading states untuk semua aksi
- Progressive enhancement

## 📱 PWA Configuration

### Manifest.json
```json
{
    "name": "UangTix - Digital Wallet",
    "short_name": "UangTix",
    "display": "standalone",
    "theme_color": "#0066CC",
    "background_color": "#F8FAFC"
}
```

### Service Worker
- Cache static assets
- Offline fallback pages
- Background sync untuk transaksi

## 🐛 Troubleshooting

### Common Issues
1. **CSRF Token Error**: Pastikan meta tag csrf-token ada di layout
2. **Modal Not Opening**: Periksa Bootstrap JS sudah loaded
3. **Icons Not Showing**: Pastikan FontAwesome CSS loaded
4. **PWA Not Installing**: Periksa manifest.json dan HTTPS

### Debug Mode
Aktifkan console.log untuk debugging:
```javascript
// Uncomment untuk debugging
// console.log('Debug info:', data);
```

## 📈 Performance Optimization

### Frontend
- CSS minification
- Image optimization
- Lazy loading untuk transaksi
- Debounced input untuk kalkulasi

### Backend
- Database indexing
- Query optimization
- Caching untuk exchange rates
- Pagination untuk transaction history

## 🔐 Security Considerations

### Frontend Security
- XSS prevention dengan proper escaping
- CSRF token untuk semua form
- Input sanitization
- Secure cookie handling

### Backend Security
- Rate limiting untuk API
- Input validation
- SQL injection prevention
- Proper error handling

## 📞 Support

Untuk bantuan teknis atau bug report, silakan hubungi:
- Developer: Dhafa Nazula P
- Instagram: @seehai.dhafa
- Email: [contact information]

## 📄 License

Copyright (c) 2024 BintangCode - Sub Holding CV Bintang Gumilang Group
All rights reserved.

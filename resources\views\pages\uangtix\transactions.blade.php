@extends('layouts.app')

@section('title', 'Riwayat Transaksi UangTix')

@push('styles')
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- UangTix Transactions CSS -->
<link rel="stylesheet" href="{{ asset('css/uangtix.css') }}?v={{ time() }}">
<style>
/* UangTix Transactions Page Styles */
.transactions-page-container {
    --uangtix-primary-blue: #0066CC;
    --uangtix-primary-green: #00AA5B;
    --uangtix-secondary-blue: #E6F3FF;
    --uangtix-text-dark: #1A1A1A;
    --uangtix-text-gray: #666666;
    --uangtix-bg-light: #F8FAFC;
    --uangtix-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --uangtix-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --uangtix-border-radius: 16px;

    background: var(--uangtix-bg-light);
    min-height: 100vh;
    padding-bottom: 100px;
    font-family: 'Poppins', 'DM Sans', sans-serif;
}

/* Header Section */
.transactions-header {
    background: linear-gradient(135deg, var(--uangtix-primary-blue) 0%, var(--uangtix-primary-green) 100%);
    padding: 24px 16px;
    border-radius: var(--uangtix-border-radius);
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.transactions-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(60px);
}

.transactions-header-content {
    position: relative;
    z-index: 2;
}

.transactions-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.transactions-header-title {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.transactions-header-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 4px 0 0 0;
}

.transactions-header-actions {
    display: flex;
    gap: 12px;
}

.transactions-header-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
}

.transactions-header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Balance Summary Card */
.balance-summary-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.balance-summary-info h3 {
    color: white;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 4px 0;
}

.balance-summary-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0 0 4px 0;
}

.balance-summary-info small {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.balance-summary-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

/* Filter Section */
.filter-section {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
    margin-bottom: 24px;
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.filter-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-toggle {
    background: var(--uangtix-secondary-blue);
    color: var(--uangtix-primary-blue);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-toggle:hover {
    background: var(--uangtix-primary-blue);
    color: white;
}

.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--uangtix-text-gray);
    margin-bottom: 8px;
}

.filter-input {
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-input:focus {
    border-color: var(--uangtix-primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.filter-actions {
    display: flex;
    gap: 8px;
}

.filter-btn {
    background: var(--uangtix-primary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-btn:hover {
    background: #0052A3;
    transform: translateY(-1px);
}

.filter-btn.secondary {
    background: #6B7280;
}

.filter-btn.secondary:hover {
    background: #4B5563;
}

/* Transactions List */
.transactions-list {
    background: white;
    border-radius: var(--uangtix-border-radius);
    box-shadow: var(--uangtix-shadow-light);
    overflow: hidden;
}

.transactions-list-header {
    background: var(--uangtix-secondary-blue);
    padding: 20px 24px;
    border-bottom: 1px solid #E5E7EB;
}

.transactions-list-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin: 0;
}

.transaction-item {
    padding: 20px 24px;
    border-bottom: 1px solid #F3F4F6;
    transition: all 0.3s ease;
    cursor: pointer;
}

.transaction-item:hover {
    background: var(--uangtix-bg-light);
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-content {
    display: flex;
    align-items: center;
    gap: 16px;
}

.transaction-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.transaction-icon.deposit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.transaction-icon.withdrawal {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.transaction-icon.transfer_in {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

.transaction-icon.transfer_out {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
}

.transaction-icon.earning {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.transaction-icon.spending {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.transaction-details {
    flex: 1;
    min-width: 0;
}

.transaction-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--uangtix-text-dark);
    margin: 0 0 4px 0;
}

.transaction-subtitle {
    font-size: 14px;
    color: var(--uangtix-text-gray);
    margin: 0 0 4px 0;
}

.transaction-meta {
    font-size: 12px;
    color: var(--uangtix-text-gray);
    display: flex;
    align-items: center;
    gap: 12px;
}

.transaction-amount-section {
    text-align: right;
    flex-shrink: 0;
}

.transaction-amount {
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 4px 0;
}

.transaction-amount.positive {
    color: #10B981;
}

.transaction-amount.negative {
    color: #EF4444;
}

.transaction-amount-idr {
    font-size: 12px;
    color: var(--uangtix-text-gray);
    margin: 0 0 4px 0;
}

.transaction-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.transaction-status.completed {
    background: #D1FAE5;
    color: #065F46;
}

.transaction-status.pending {
    background: #FEF3C7;
    color: #92400E;
}

.transaction-status.failed {
    background: #FEE2E2;
    color: #991B1B;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--uangtix-text-gray);
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: var(--uangtix-secondary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: var(--uangtix-primary-blue);
}

.empty-state-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin: 0 0 8px 0;
}

.empty-state-subtitle {
    font-size: 14px;
    color: var(--uangtix-text-gray);
    margin: 0 0 24px 0;
}

.empty-state-action {
    background: var(--uangtix-primary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.empty-state-action:hover {
    background: #0052A3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Pagination */
.pagination-wrapper {
    padding: 24px;
    display: flex;
    justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .transactions-page-container {
        padding: 16px;
    }

    .transactions-header {
        margin: 0 -16px 24px;
        border-radius: 0;
    }

    .transactions-header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .transactions-header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .balance-summary-card {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .filter-form {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        grid-column: 1 / -1;
        justify-content: stretch;
    }

    .filter-btn {
        flex: 1;
        justify-content: center;
    }

    .transaction-content {
        gap: 12px;
    }

    .transaction-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .transaction-amount-section {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .transactions-header-actions {
        flex-direction: column;
        gap: 8px;
    }

    .transactions-header-btn {
        width: 100%;
        justify-content: center;
    }

    .transaction-item {
        padding: 16px;
    }

    .transaction-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .transaction-amount-section {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
</style>
@endpush

@section('content')
<div class="transactions-page-container">
    <!-- Header -->
    <div class="transactions-header">
        <div class="transactions-header-content">
            <div class="transactions-header-top">
                <div>
                    <h1 class="transactions-header-title">Riwayat Transaksi UangTix</h1>
                    <p class="transactions-header-subtitle">Lihat semua transaksi UangTix Anda</p>
                </div>
                <div class="transactions-header-actions">
                    <a href="{{ route('uangtix.index') }}" class="transactions-header-btn">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <a href="{{ route('uangtix.balance') }}" class="transactions-header-btn">
                        <i class="fas fa-chart-line"></i> Detail Saldo
                    </a>
                </div>
            </div>

            <!-- Balance Summary -->
            <div class="balance-summary-card">
                <div class="balance-summary-info">
                    <h3>{{ number_format($balance->balance ?? 0, 0, ',', '.') }} UTX</h3>
                    <p>Saldo UangTix Saat Ini</p>
                    <small>≈ Rp {{ number_format(($balance->balance ?? 0) * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}</small>
                </div>
                <div class="balance-summary-icon">
                    <i class="fas fa-wallet"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="filter-section">
        <div class="filter-header">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                Filter Transaksi
            </div>
            <button type="button" class="filter-toggle" onclick="toggleFilters()">
                <i class="fas fa-chevron-down" id="filterToggleIcon"></i>
                Tampilkan Filter
            </button>
        </div>

        <div id="filterForm" style="display: none;">
            <form method="GET" action="{{ route('uangtix.transactions') }}" class="filter-form">
                <div class="filter-group">
                    <label class="filter-label">Pencarian</label>
                    <input type="text" name="search" class="filter-input" value="{{ request('search') }}" placeholder="No. transaksi atau deskripsi...">
                </div>

                <div class="filter-group">
                    <label class="filter-label">Jenis Transaksi</label>
                    <select name="type" class="filter-input">
                        <option value="">Semua Jenis</option>
                        @if(isset($transactionTypes))
                            @foreach($transactionTypes as $key => $label)
                                <option value="{{ $key }}" {{ request('type') == $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        @else
                            <option value="deposit" {{ request('type') == 'deposit' ? 'selected' : '' }}>Deposit</option>
                            <option value="withdrawal" {{ request('type') == 'withdrawal' ? 'selected' : '' }}>Penarikan</option>
                            <option value="transfer_in" {{ request('type') == 'transfer_in' ? 'selected' : '' }}>Transfer Masuk</option>
                            <option value="transfer_out" {{ request('type') == 'transfer_out' ? 'selected' : '' }}>Transfer Keluar</option>
                            <option value="earning" {{ request('type') == 'earning' ? 'selected' : '' }}>Pendapatan</option>
                            <option value="spending" {{ request('type') == 'spending' ? 'selected' : '' }}>Pengeluaran</option>
                        @endif
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Status</label>
                    <select name="status" class="filter-input">
                        <option value="">Semua Status</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>Gagal</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Dari Tanggal</label>
                    <input type="date" name="date_from" class="filter-input" value="{{ request('date_from') }}">
                </div>

                <div class="filter-group">
                    <label class="filter-label">Sampai Tanggal</label>
                    <input type="date" name="date_to" class="filter-input" value="{{ request('date_to') }}">
                </div>

                <div class="filter-actions">
                    <button type="submit" class="filter-btn">
                        <i class="fas fa-search"></i> Terapkan Filter
                    </button>
                    <a href="{{ route('uangtix.transactions') }}" class="filter-btn secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                    <button type="button" class="filter-btn secondary" onclick="exportTransactions()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </form>
        </div>
    </div>



    <!-- Transactions List -->
    <div class="transactions-list">
        <div class="transactions-list-header">
            <h3 class="transactions-list-title">
                <i class="fas fa-list"></i>
                Daftar Transaksi
            </h3>
        </div>

        @if(isset($transactions) && $transactions->count() > 0)
            @foreach($transactions as $transaction)
            <div class="transaction-item" onclick="showTransactionDetail('{{ $transaction->id }}')">
                <div class="transaction-content">
                    <div class="transaction-icon {{ $transaction->type ?? 'deposit' }}">
                        @php
                            $iconMap = [
                                'deposit' => 'fas fa-arrow-down',
                                'withdrawal' => 'fas fa-arrow-up',
                                'transfer_in' => 'fas fa-arrow-down',
                                'transfer_out' => 'fas fa-arrow-up',
                                'earning' => 'fas fa-coins',
                                'spending' => 'fas fa-shopping-cart'
                            ];
                            $icon = $iconMap[$transaction->type ?? 'deposit'] ?? 'fas fa-exchange-alt';
                        @endphp
                        <i class="{{ $icon }}"></i>
                    </div>

                    <div class="transaction-details">
                        <div class="transaction-title">
                            {{ $transaction->description ?? 'Transaksi UangTix' }}
                        </div>
                        <div class="transaction-subtitle">
                            @if(isset($transaction->fromUser))
                                Dari: {{ $transaction->fromUser->name }}
                            @elseif(isset($transaction->toUser))
                                Ke: {{ $transaction->toUser->name }}
                            @else
                                {{ ucfirst($transaction->type ?? 'deposit') }}
                            @endif
                        </div>
                        <div class="transaction-meta">
                            <span>
                                <i class="fas fa-calendar"></i>
                                {{ $transaction->created_at->format('d/m/Y H:i') }}
                            </span>
                            <span>
                                <i class="fas fa-hashtag"></i>
                                #{{ $transaction->id }}
                            </span>
                        </div>
                    </div>

                    <div class="transaction-amount-section">
                        <div class="transaction-amount {{ ($transaction->amount ?? 0) > 0 ? 'positive' : 'negative' }}">
                            {{ ($transaction->amount ?? 0) > 0 ? '+' : '' }}{{ number_format($transaction->amount ?? 0, 0, ',', '.') }} UTX
                        </div>
                        <div class="transaction-amount-idr">
                            ≈ Rp {{ number_format(($transaction->amount ?? 0) * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}
                        </div>
                        <div class="transaction-status {{ $transaction->status ?? 'completed' }}">
                            @php
                                $statusMap = [
                                    'completed' => 'Selesai',
                                    'pending' => 'Pending',
                                    'failed' => 'Gagal'
                                ];
                                $statusLabel = $statusMap[$transaction->status ?? 'completed'] ?? 'Selesai';
                            @endphp
                            <i class="fas fa-{{ ($transaction->status ?? 'completed') == 'completed' ? 'check' : (($transaction->status ?? 'completed') == 'pending' ? 'clock' : 'times') }}"></i>
                            {{ $statusLabel }}
                        </div>
                    </div>
                </div>
            </div>
            @endforeach

            <!-- Pagination -->
            @if(isset($transactions) && $transactions->hasPages())
                <div class="pagination-wrapper">
                    {{ $transactions->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <!-- Empty State -->
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-history"></i>
                </div>
                <h3 class="empty-state-title">Belum Ada Transaksi</h3>
                <p class="empty-state-subtitle">Transaksi UangTix Anda akan muncul di sini setelah Anda melakukan aktivitas</p>
                <a href="{{ route('uangtix.index') }}" class="empty-state-action">
                    <i class="fas fa-plus"></i>
                    Mulai Transaksi Pertama
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// Transactions Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeFilters();
    setupTransactionInteractions();
    setupDateFilters();
});

function initializeFilters() {
    // Set max date to today for date inputs
    const today = new Date().toISOString().split('T')[0];
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.setAttribute('max', today);
    });
}

function toggleFilters() {
    const filterForm = document.getElementById('filterForm');
    const toggleIcon = document.getElementById('filterToggleIcon');
    const toggleBtn = document.querySelector('.filter-toggle');

    if (filterForm.style.display === 'none') {
        filterForm.style.display = 'block';
        toggleIcon.className = 'fas fa-chevron-up';
        toggleBtn.innerHTML = '<i class="fas fa-chevron-up" id="filterToggleIcon"></i> Sembunyikan Filter';

        // Animate show
        filterForm.style.opacity = '0';
        filterForm.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            filterForm.style.transition = 'all 0.3s ease';
            filterForm.style.opacity = '1';
            filterForm.style.transform = 'translateY(0)';
        }, 10);
    } else {
        filterForm.style.transition = 'all 0.3s ease';
        filterForm.style.opacity = '0';
        filterForm.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            filterForm.style.display = 'none';
            toggleIcon.className = 'fas fa-chevron-down';
            toggleBtn.innerHTML = '<i class="fas fa-chevron-down" id="filterToggleIcon"></i> Tampilkan Filter';
        }, 300);
    }
}

function setupTransactionInteractions() {
    // Add hover effects to transaction items
    document.querySelectorAll('.transaction-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = 'var(--uangtix-shadow-medium)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

function setupDateFilters() {
    // Auto-set date_to when date_from is selected
    const dateFromInputs = document.querySelectorAll('input[name="date_from"]');
    const dateToInputs = document.querySelectorAll('input[name="date_to"]');

    dateFromInputs.forEach((dateFrom, index) => {
        dateFrom.addEventListener('change', function() {
            const dateTo = dateToInputs[index];
            if (this.value && dateTo && !dateTo.value) {
                dateTo.value = this.value;
            }
        });
    });
}

function showTransactionDetail(transactionId) {
    // Show transaction detail modal or navigate to detail page
    showToast('Detail transaksi #' + transactionId, 'info');

    // In real app, this would open a modal or navigate to detail page
    console.log('Show transaction detail for ID:', transactionId);
}

function exportTransactions() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    btn.disabled = true;

    // Simulate export process
    setTimeout(() => {
        // In real app, this would trigger actual export
        showToast('Export berhasil! File akan diunduh sebentar lagi.', 'success');

        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;

        // Simulate file download
        const link = document.createElement('a');
        link.href = 'data:text/plain;charset=utf-8,Sample UangTix Transaction Export';
        link.download = 'uangtix-transactions-' + new Date().toISOString().split('T')[0] + '.csv';
        link.click();
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Real-time search functionality
function setupRealTimeSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 3 || query.length === 0) {
                searchTimeout = setTimeout(() => {
                    // In real app, this would make AJAX request to filter transactions
                    console.log('Search for:', query);
                    showToast('Mencari: ' + (query || 'Semua transaksi'), 'info');
                }, 500);
            }
        });
    }
}

// Initialize real-time search
setupRealTimeSearch();

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .transaction-item {
        transition: all 0.3s ease !important;
    }

    .filter-form {
        transition: all 0.3s ease !important;
    }
`;
document.head.appendChild(style);
</script>
@endpush

<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Event;
use App\Models\TicketTemplate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;

class TicketGeneratorService
{
    /**
     * Generate E-Ticket PDF for a ticket
     */
    public function generateTicketPdf(Ticket $ticket): string
    {
        // Get event and template
        $event = $ticket->event;
        $template = $event->ticketTemplate ?? TicketTemplate::where('is_default', true)->first();

        if (!$template) {
            throw new \Exception('No ticket template found');
        }

        // Prepare ticket data
        $ticketData = $this->prepareTicketData($ticket);

        // Generate HTML from template
        $html = $this->generateTicketHtml($ticketData, $template);

        // Generate PDF
        $pdf = Pdf::loadHTML($html)
            ->setPaper('A4', 'landscape')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
            ]);

        // Save PDF to storage
        $filename = $this->generateFilename($ticket);
        $pdfPath = "tickets/{$filename}";

        Storage::disk('public')->put($pdfPath, $pdf->output());

        // Update ticket with PDF path
        $ticket->update([
            'pdf_path' => $pdfPath,
            'pdf_generated_at' => now()
        ]);

        return $pdfPath;
    }

    /**
     * Generate ticket HTML for preview
     */
    public function generateTicketHtml(array $ticketData, TicketTemplate $template = null): string
    {
        // If no template provided, get from event or use default
        if (!$template && isset($ticketData['event'])) {
            $event = $ticketData['event'];
            $templateName = $event->boarding_pass_template ?? 'unix';
            $config = $event->template_config ?? [];
        } else {
            $templateName = $template ? $template->slug : 'unix';
            $config = $template ? $template->config : [];
        }

        try {
            return view("templates.tickets.{$templateName}", [
                'ticket' => $ticketData,
                'config' => $config,
                'template' => $template
            ])->render();
        } catch (\Exception $e) {
            // Fallback to unix template
            return view('templates.tickets.unix', [
                'ticket' => $ticketData,
                'config' => [],
                'template' => $template
            ])->render();
        }
    }

    /**
     * Generate E-Ticket PDF using event's selected template
     */
    public function generateTicketPdfFromEvent(Ticket $ticket): string
    {
        $event = $ticket->event;
        $templateName = $event->boarding_pass_template ?? 'unix';
        $config = $event->template_config ?? [];

        // Prepare ticket data
        $ticketData = $this->prepareTicketData($ticket);
        $ticketData['event'] = $event;

        // Generate HTML from selected template
        $html = view("templates.tickets.{$templateName}", [
            'ticket' => $ticketData,
            'config' => $config
        ])->render();

        // Generate PDF
        $pdf = Pdf::loadHTML($html)
            ->setPaper('A4', 'landscape')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
            ]);

        // Save PDF
        $filename = "ticket-{$ticket->ticket_number}.pdf";
        $path = "tickets/{$filename}";

        Storage::disk('public')->put($path, $pdf->output());

        // Update ticket with PDF path
        $ticket->update(['pdf_path' => $path]);

        return $path;
    }

    /**
     * Generate random boarding pass ID
     */
    private function generateBoardingPassId(): string
    {
        $prefix = 'BP';
        $timestamp = now()->format('ymd');
        $random = strtoupper(Str::random(6));
        return "{$prefix}{$timestamp}{$random}";
    }

    /**
     * Prepare ticket data for template
     */
    private function prepareTicketData(Ticket $ticket): array
    {
        $event = $ticket->event;
        $buyer = $ticket->buyer;

        // Generate boarding pass ID if not exists
        if (!$ticket->boarding_pass_id) {
            $ticket->update([
                'boarding_pass_id' => $this->generateBoardingPassId(),
                'template_used' => $event->boarding_pass_template ?? 'unix',
                'generated_at' => now()
            ]);
        }

        return [
            'id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'boarding_pass_id' => $ticket->boarding_pass_id,
            'qr_code' => $ticket->qr_code,
            'price' => $ticket->price,
            'total_paid' => $ticket->total_paid,
            'formatted_price' => 'Rp ' . number_format($ticket->total_paid, 0, ',', '.'),
            'status' => $ticket->status,
            'attendee_name' => $ticket->attendee_name,
            'attendee_email' => $ticket->attendee_email,
            'attendee_phone' => $ticket->attendee_phone,
            'created_at' => $ticket->created_at,
            'generated_at' => $ticket->generated_at ? $ticket->generated_at->format('d M Y H:i') : now()->format('d M Y H:i'),
            'template_used' => $ticket->template_used ?? 'unix',

            // Flattened event data for easier template access
            'event_title' => $event->title,
            'event_date' => $event->start_date->format('d M Y'),
            'event_time' => $event->start_date->format('H:i') . ' WIB',
            'venue_name' => $event->venue_name,
            'venue_address' => $event->venue_address,
            'city' => $event->city,
            'category' => $event->category->name ?? 'General',

            // Flattened buyer data for easier template access
            'buyer_name' => $buyer->name,
            'buyer_email' => $buyer->email,
            'buyer_phone' => $buyer->phone ?? 'N/A',

            'event' => [
                'id' => $event->id,
                'title' => $event->title,
                'description' => $event->description,
                'venue_name' => $event->venue_name,
                'venue_address' => $event->venue_address,
                'city' => $event->city,
                'province' => $event->province,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'poster' => $event->poster,
                'organizer_name' => $event->organizer->name ?? 'Unknown'
            ],
            'buyer' => [
                'id' => $buyer->id,
                'name' => $buyer->name,
                'email' => $buyer->email,
                'phone' => $buyer->phone ?? 'N/A'
            ],
            'template' => [
                'name' => $event->ticketTemplate->name ?? 'Default',
                'slug' => $event->ticketTemplate->slug ?? 'modern-boarding-pass'
            ]
        ];
    }

    /**
     * Generate filename for ticket PDF
     */
    private function generateFilename(Ticket $ticket): string
    {
        $eventSlug = Str::slug($ticket->event->title);
        $ticketNumber = $ticket->ticket_number;
        $timestamp = now()->format('Ymd_His');

        return "ticket_{$eventSlug}_{$ticketNumber}_{$timestamp}.pdf";
    }

    /**
     * Generate multiple tickets for an order
     */
    public function generateOrderTickets($order): array
    {
        $generatedTickets = [];

        foreach ($order->tickets as $ticket) {
            try {
                $pdfPath = $this->generateTicketPdf($ticket);
                $generatedTickets[] = [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'pdf_path' => $pdfPath,
                    'success' => true
                ];
            } catch (\Exception $e) {
                $generatedTickets[] = [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }
        }

        return $generatedTickets;
    }

    /**
     * Get ticket download URL
     */
    public function getTicketDownloadUrl(Ticket $ticket): ?string
    {
        if (!$ticket->pdf_path || !Storage::disk('public')->exists($ticket->pdf_path)) {
            return null;
        }

        return Storage::disk('public')->url($ticket->pdf_path);
    }

    /**
     * Generate ticket preview (HTML only)
     */
    public function generateTicketPreview(Event $event, array $sampleData = []): string
    {
        $template = $event->ticketTemplate ?? TicketTemplate::where('is_default', true)->first();

        if (!$template) {
            throw new \Exception('No ticket template found');
        }

        // Prepare sample ticket data
        $ticketData = array_merge([
            'ticket_number' => 'TIX-PREVIEW-001',
            'qr_code' => 'PREVIEW-QR-CODE',
            'price' => $event->price,
            'total_paid' => $event->price,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '+62812345678',
            'event' => [
                'title' => $event->title,
                'venue_name' => $event->venue_name,
                'city' => $event->city,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'poster' => $event->poster,
                'organizer_name' => $event->organizer->name ?? 'Organizer'
            ],
            'buyer' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '+62812345678'
            ]
        ], $sampleData);

        return $this->generateTicketHtml($ticketData, $template);
    }

    /**
     * Regenerate ticket PDF (for updates)
     */
    public function regenerateTicketPdf(Ticket $ticket): string
    {
        // Delete old PDF if exists
        if ($ticket->pdf_path && Storage::disk('public')->exists($ticket->pdf_path)) {
            Storage::disk('public')->delete($ticket->pdf_path);
        }

        // Generate new PDF
        return $this->generateTicketPdf($ticket);
    }

    /**
     * Bulk generate tickets for multiple tickets
     */
    public function bulkGenerateTickets(array $ticketIds): array
    {
        $results = [];

        foreach ($ticketIds as $ticketId) {
            $ticket = Ticket::find($ticketId);

            if (!$ticket) {
                $results[] = [
                    'ticket_id' => $ticketId,
                    'success' => false,
                    'error' => 'Ticket not found'
                ];
                continue;
            }

            try {
                $pdfPath = $this->generateTicketPdf($ticket);
                $results[] = [
                    'ticket_id' => $ticketId,
                    'ticket_number' => $ticket->ticket_number,
                    'pdf_path' => $pdfPath,
                    'success' => true
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'ticket_id' => $ticketId,
                    'ticket_number' => $ticket->ticket_number ?? 'Unknown',
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }
}

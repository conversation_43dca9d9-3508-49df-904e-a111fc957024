# 🎯 Demo UangTix - Digital Wallet GoPay/OVO Style

## 🚀 Cara Menjalankan Demo

### 1. Persiapan Environment
```bash
# Clone atau pastikan di direktori project
cd c:\laragon\www\Project-tixara.my.id

# Install dependencies
composer install
npm install

# Setup database
php artisan migrate
php artisan db:seed

# Build assets
npm run build
```

### 2. Jalankan Server
```bash
# Windows
run-uangtix.bat

# Linux/Mac
./run-uangtix.sh

# Manual
php artisan serve --host=0.0.0.0 --port=8000
```

### 3. Akses UangTix
- **URL**: http://localhost:8000/uangtix
- **Login**: Gunakan akun yang sudah ada atau register baru
- **Mobile**: Buka di browser mobile atau gunakan DevTools (F12) → Toggle Device Toolbar

## 📱 Fitur Demo yang Dapat Dicoba

### 🎨 **Design & UI/UX**
- ✅ **GoPay/OVO Style Interface** - Modern, clean, professional
- ✅ **Responsive Design** - Optimal di desktop, tablet, mobile
- ✅ **PWA Support** - Install sebagai aplikasi mobile
- ✅ **Floating Footer** - Navigasi mudah untuk mobile
- ✅ **Smooth Animations** - Transisi dan hover effects
- ✅ **Color Scheme** - Blue & Green gradient seperti GoPay/OVO

### 💰 **Wallet Features**
- ✅ **Balance Display** - Tampilan saldo UTX dan konversi IDR
- ✅ **Real-time Updates** - Auto refresh balance setiap 30 detik
- ✅ **Quick Actions** - 4 tombol utama (Top Up, Tarik, Transfer, Riwayat)
- ✅ **Statistics Cards** - Total earned, deposited, withdrawn, transaksi
- ✅ **Recent Transactions** - 5 transaksi terbaru dengan icon dan status

### 🔧 **Interactive Features**
- ✅ **Top Up Modal** - Form deposit dengan kalkulasi real-time
- ✅ **Withdrawal Modal** - Form penarikan dengan validasi saldo
- ✅ **Transfer Modal** - Transfer antar user dengan email
- ✅ **Toast Notifications** - Feedback visual untuk semua aksi
- ✅ **Loading States** - Spinner dan disabled state saat proses

### 📱 **PWA Features**
- ✅ **Install Prompt** - Banner install otomatis di mobile
- ✅ **Offline Support** - Halaman offline dengan retry button
- ✅ **Service Worker** - Cache static assets dan API responses
- ✅ **App Shortcuts** - Quick access dari home screen
- ✅ **Standalone Mode** - Fullscreen app experience

## 🎮 Skenario Demo

### **Skenario 1: First Time User**
1. Buka http://localhost:8000/uangtix
2. Login atau register akun baru
3. Lihat dashboard dengan saldo 0
4. Coba klik "Top Up" → Modal terbuka
5. Masukkan jumlah deposit (min Rp 10,000)
6. Pilih metode pembayaran
7. Lihat kalkulasi real-time (fee, UTX yang diterima)
8. Submit form → Toast notification muncul

### **Skenario 2: Mobile PWA Experience**
1. Buka di browser mobile atau DevTools mobile view
2. Scroll dan lihat responsive design
3. Banner install PWA muncul di bottom
4. Klik "Install" → App terinstall di home screen
5. Buka app dari home screen → Standalone mode
6. Test floating footer navigation
7. Test touch-friendly buttons (min 48px)

### **Skenario 3: Transfer Between Users**
1. Pastikan ada 2 user dengan saldo
2. User A: Klik "Transfer"
3. Masukkan email User B
4. Masukkan jumlah transfer
5. Tambahkan catatan (opsional)
6. Submit → Saldo User A berkurang, User B bertambah
7. Lihat notifikasi di kedua user

### **Skenario 4: Offline Mode**
1. Buka UangTix di browser
2. Disconnect internet (DevTools → Network → Offline)
3. Refresh halaman → Offline page muncul
4. Klik "Coba Lagi" → Retry connection
5. Connect internet kembali → Normal operation

### **Skenario 5: Real-time Features**
1. Buka UangTix di 2 tab/browser
2. Di tab 1: Lakukan transfer
3. Di tab 2: Lihat balance auto-refresh (30 detik)
4. Test form calculations saat input berubah
5. Test toast notifications untuk feedback

## 🎯 Testing Checklist

### **✅ Desktop Testing (1024px+)**
- [ ] Layout 2-column dengan sidebar
- [ ] Hover effects pada buttons dan cards
- [ ] Modal responsive dan centered
- [ ] Typography readable dan consistent
- [ ] Color scheme sesuai GoPay/OVO

### **✅ Tablet Testing (768px-1024px)**
- [ ] Layout single column
- [ ] Touch-friendly button sizes
- [ ] Modal full-width dengan padding
- [ ] Grid responsive (2x2 → 4x1)
- [ ] Navigation accessible

### **✅ Mobile Testing (<768px)**
- [ ] Floating footer visible
- [ ] PWA install banner muncul
- [ ] Touch gestures responsive
- [ ] Text readable tanpa zoom
- [ ] Forms easy to fill

### **✅ PWA Testing**
- [ ] Manifest.json valid
- [ ] Service worker registered
- [ ] Install prompt works
- [ ] Offline page accessible
- [ ] App shortcuts functional
- [ ] Standalone mode works

### **✅ Functionality Testing**
- [ ] All buttons clickable dan functional
- [ ] Forms submit dengan validation
- [ ] AJAX requests work
- [ ] Error handling proper
- [ ] Loading states visible
- [ ] Toast notifications appear

## 🐛 Known Issues & Solutions

### **Issue 1: CSRF Token Error**
**Problem**: Form submission gagal dengan 419 error
**Solution**: Pastikan meta tag csrf-token ada di layout
```html
<meta name="csrf-token" content="{{ csrf_token() }}">
```

### **Issue 2: FontAwesome Icons Not Loading**
**Problem**: Icons tidak muncul
**Solution**: Pastikan CDN FontAwesome loaded
```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
```

### **Issue 3: PWA Install Not Working**
**Problem**: Install banner tidak muncul
**Solution**: 
- Pastikan HTTPS (atau localhost)
- Manifest.json valid
- Service worker registered
- Icons tersedia

### **Issue 4: Balance Not Updating**
**Problem**: Saldo tidak update real-time
**Solution**: Check route uangtix.balance returns JSON untuk AJAX

### **Issue 5: Mobile Layout Broken**
**Problem**: Layout tidak responsive
**Solution**: Check viewport meta tag dan CSS media queries

## 📊 Performance Metrics

### **Target Metrics**
- **First Contentful Paint**: < 2s
- **Largest Contentful Paint**: < 3s
- **Time to Interactive**: < 4s
- **Cumulative Layout Shift**: < 0.1
- **PWA Score**: > 90

### **Optimization Tips**
1. **Images**: Compress dan gunakan WebP
2. **CSS**: Minify dan critical CSS inline
3. **JavaScript**: Code splitting dan lazy loading
4. **Fonts**: Preload critical fonts
5. **Service Worker**: Cache strategy optimal

## 🎉 Demo Success Criteria

### **✅ Visual Excellence**
- Design mirip GoPay/OVO (modern, clean, professional)
- Color scheme consistent (blue-green gradient)
- Typography readable dan hierarchy jelas
- Icons dan imagery berkualitas tinggi

### **✅ Functional Excellence**
- Semua button dan form berfungsi
- Real-time calculations accurate
- Error handling graceful
- Loading states informative

### **✅ Technical Excellence**
- Responsive design perfect di semua device
- PWA installable dan functional
- Offline mode works
- Performance optimal

### **✅ User Experience Excellence**
- Navigation intuitive
- Feedback immediate dan clear
- Accessibility considerations
- Mobile-first approach

## 📞 Support & Documentation

**Developer**: Dhafa Nazula P  
**Instagram**: @seehai.dhafa  
**Company**: BintangCode - Sub Holding CV Bintang Gumilang Group  

**Files Modified**:
- `resources/views/pages/uangtix/index.blade.php` - Main UangTix page
- `app/Http/Controllers/UangTixController.php` - Balance API endpoint
- `public/manifest.json` - PWA manifest
- `public/sw.js` - Service worker with UangTix support

**Additional Files**:
- `UANGTIX_README.md` - Technical documentation
- `run-uangtix.bat` / `run-uangtix.sh` - Setup scripts
- `DEMO_UANGTIX.md` - This demo guide

---

🎯 **Ready to Demo!** Jalankan server dan akses http://localhost:8000/uangtix untuk melihat UangTix dengan gaya GoPay/OVO yang responsive dan PWA-ready!

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('events', function (Blueprint $table) {
            $columns = Schema::getColumnListing('events');

            // Enhanced ticket template fields - only add if they don't exist
            if (!in_array('boarding_pass_template', $columns)) {
                $table->string('boarding_pass_template')->default('unix')->after('ticket_template');
            }
            if (!in_array('template_config', $columns)) {
                $table->json('template_config')->nullable()->after('boarding_pass_template');
            }
            if (!in_array('auto_generate_tickets', $columns)) {
                $table->boolean('auto_generate_tickets')->default(true)->after('template_config');
            }
            if (!in_array('email_tickets_to_buyers', $columns)) {
                $table->boolean('email_tickets_to_buyers')->default(true)->after('auto_generate_tickets');
            }
            if (!in_array('custom_template_id', $columns)) {
                $table->string('custom_template_id')->nullable()->after('email_tickets_to_buyers');
            }
        });

        // Add index for better performance (separate from table modification)
        try {
            Schema::table('events', function (Blueprint $table) {
                $table->index(['boarding_pass_template', 'auto_generate_tickets']);
            });
        } catch (\Exception $e) {
            // Index might already exist, ignore the error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('events', function (Blueprint $table) {
            $table->dropIndex(['boarding_pass_template', 'auto_generate_tickets']);
            $table->dropColumn([
                'boarding_pass_template',
                'template_config',
                'auto_generate_tickets',
                'email_tickets_to_buyers',
                'custom_template_id'
            ]);
        });
    }
};

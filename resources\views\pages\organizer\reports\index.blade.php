@extends('layouts.organizer')

@section('title', 'Reports & Analytics')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Reports & Analytics</h1>
            <p class="mb-0 text-muted">Comprehensive insights into your event performance</p>
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-calendar me-2"></i>{{ $dateRange }} Days
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="?range=7">Last 7 Days</a>
                    <a class="dropdown-item" href="?range=30">Last 30 Days</a>
                    <a class="dropdown-item" href="?range=90">Last 90 Days</a>
                    <a class="dropdown-item" href="?range=365">Last Year</a>
                </div>
            </div>
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>Export
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="{{ route('organizer.reports.export', ['type' => 'revenue', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Revenue Report (CSV)
                    </a>
                    <a class="dropdown-item" href="{{ route('organizer.reports.export', ['type' => 'sales', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Sales Report (CSV)
                    </a>
                    <a class="dropdown-item" href="{{ route('organizer.reports.export', ['type' => 'events', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Events Report (CSV)
                    </a>
                    <a class="dropdown-item" href="{{ route('organizer.reports.export', ['type' => 'customers', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Customers Report (CSV)
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row mb-4">
        <!-- Revenue Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($revenueData['total_revenue'], 0, ',', '.') }}
                            </div>
                            @if($revenueData['growth_percentage'] != 0)
                                <div class="text-xs {{ $revenueData['growth_percentage'] > 0 ? 'text-success' : 'text-danger' }}">
                                    <i class="fas fa-arrow-{{ $revenueData['growth_percentage'] > 0 ? 'up' : 'down' }} me-1"></i>
                                    {{ abs($revenueData['growth_percentage']) }}% from previous period
                                </div>
                            @endif
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Orders
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($salesData['total_orders']) }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($salesData['total_tickets']) }} tickets sold
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Events Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Events
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($eventPerformance['total_events']) }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($eventPerformance['published_events']) }} published
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Customers
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($customerData['total_customers']) }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($customerData['new_customers']) }} new customers
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Detailed Reports</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('organizer.reports.revenue') }}" class="text-decoration-none">
                                <div class="card border-left-primary h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Revenue Report</h5>
                                        <p class="card-text text-muted">Detailed revenue analysis and trends</p>
                                        <div class="mt-3">
                                            <span class="badge badge-primary">
                                                Avg Order: Rp {{ number_format($revenueData['average_order_value'], 0, ',', '.') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ route('organizer.reports.sales') }}" class="text-decoration-none">
                                <div class="card border-left-success h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-bar fa-3x text-success mb-3"></i>
                                        <h5 class="card-title">Sales Report</h5>
                                        <p class="card-text text-muted">Sales performance and conversion metrics</p>
                                        <div class="mt-3">
                                            <span class="badge badge-success">
                                                Conversion: {{ $salesData['conversion_rate'] }}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ route('organizer.reports.events') }}" class="text-decoration-none">
                                <div class="card border-left-info h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calendar-check fa-3x text-info mb-3"></i>
                                        <h5 class="card-title">Event Performance</h5>
                                        <p class="card-text text-muted">Individual event analytics and metrics</p>
                                        <div class="mt-3">
                                            <span class="badge badge-info">
                                                Avg Attendance: {{ number_format($eventPerformance['average_attendance']) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-3 mb-3">
                            <a href="{{ route('organizer.reports.customers') }}" class="text-decoration-none">
                                <div class="card border-left-warning h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-friends fa-3x text-warning mb-3"></i>
                                        <h5 class="card-title">Customer Analytics</h5>
                                        <p class="card-text text-muted">Customer behavior and retention analysis</p>
                                        <div class="mt-3">
                                            <span class="badge badge-warning">
                                                Retention: {{ $customerData['customer_retention_rate'] }}%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Insights -->
    <div class="row">
        <!-- Revenue Trend Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue Trend</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="{{ route('organizer.reports.revenue') }}">View Details</a>
                            <a class="dropdown-item" href="{{ route('organizer.reports.export', ['type' => 'revenue']) }}">Export Data</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Events -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performing Events</h6>
                    <a href="{{ route('organizer.reports.events') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @forelse($eventPerformance['top_events'] ?? [] as $event)
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div>
                                    <h6 class="mb-1">{{ Str::limit($event->title, 25) }}</h6>
                                    <small class="text-muted">{{ $event->tickets_sold }} tickets sold</small>
                                </div>
                                <div class="text-right">
                                    <span class="font-weight-bold">Rp {{ number_format($event->revenue, 0, ',', '.') }}</span>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No event data available</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Key Performance Indicators</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 border-right">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-primary">
                                    Rp {{ number_format($revenueData['average_order_value'], 0, ',', '.') }}
                                </div>
                                <div class="text-muted">Average Order Value</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-success">
                                    {{ $salesData['conversion_rate'] }}%
                                </div>
                                <div class="text-muted">Conversion Rate</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6 border-right">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-info">
                                    {{ number_format($eventPerformance['average_attendance']) }}
                                </div>
                                <div class="text-muted">Avg Attendance</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-warning">
                                    {{ $customerData['customer_retention_rate'] }}%
                                </div>
                                <div class="text-muted">Customer Retention</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activity</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">New Order Received</h6>
                                <p class="mb-0 text-muted small">Customer purchased tickets for upcoming event</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Event Published</h6>
                                <p class="mb-0 text-muted small">New event went live and is accepting bookings</p>
                                <small class="text-muted">1 day ago</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Payment Processed</h6>
                                <p class="mb-0 text-muted small">Payment confirmation received from gateway</p>
                                <small class="text-muted">2 days ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #e3e6f0;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue',
            data: [12000000, 19000000, 15000000, 25000000, 22000000, 30000000],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + (value / 1000000) + 'M';
                    }
                }
            }
        }
    }
});
</script>
@endpush
@endsection

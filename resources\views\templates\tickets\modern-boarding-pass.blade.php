<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #164e63;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 850px;
            width: 100%;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(8, 145, 178, 0.1), 0 10px 10px -5px rgba(8, 145, 178, 0.04);
            position: relative;
        }

        .modern-header {
            background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
            color: white;
            padding: 32px;
            position: relative;
            overflow: hidden;
        }

        .header-content {
            position: relative;
            z-index: 2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            flex: 1;
        }

        .header-title {
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 4px;
            letter-spacing: 1px;
        }

        .header-subtitle {
            font-size: 14px;
            font-weight: 300;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .header-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 900;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .boarding-body {
            padding: 40px;
            background: linear-gradient(135deg, #fefefe 0%, #f0f9ff 100%);
        }

        .event-card {
            background: white;
            border-radius: 20px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.1);
            border: 1px solid #e0f2fe;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .event-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0891b2, #67e8f9, #0891b2);
        }

        .event-title {
            font-size: 28px;
            font-weight: 700;
            color: #164e63;
            margin-bottom: 12px;
            line-height: 1.2;
        }

        .event-category {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #0891b2, #67e8f9);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 1.8fr 1fr;
            gap: 32px;
            align-items: start;
        }

        .info-panel {
            background: white;
            border-radius: 20px;
            padding: 32px;
            box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.1);
            border: 1px solid #e0f2fe;
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e0f2fe;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 700;
            color: #0891b2;
        }

        .info-grid {
            display: grid;
            gap: 20px;
        }

        .info-pair {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .info-item {
            background: #f0f9ff;
            border-radius: 12px;
            padding: 16px;
            border-left: 4px solid #0891b2;
            transition: all 0.3s ease;
        }

        .info-item:hover {
            background: #e0f2fe;
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.1);
        }

        .info-label {
            font-size: 11px;
            font-weight: 700;
            color: #0891b2;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .info-value {
            font-size: 15px;
            font-weight: 500;
            color: #164e63;
            line-height: 1.3;
        }

        .info-value.primary {
            font-size: 17px;
            font-weight: 700;
            color: #0891b2;
        }

        .qr-panel {
            background: white;
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.1);
            border: 1px solid #e0f2fe;
            position: relative;
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #0891b2, #67e8f9, #0891b2);
            border-radius: 20px 20px 0 0;
        }

        .qr-header {
            margin-bottom: 24px;
        }

        .qr-title {
            font-size: 16px;
            font-weight: 700;
            color: #0891b2;
            margin-bottom: 4px;
        }

        .qr-subtitle {
            font-size: 12px;
            color: #67e8f9;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #164e63, #0891b2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(8, 145, 178, 0.3);
            border: 3px solid #67e8f9;
        }

        .boarding-id {
            background: #f0f9ff;
            border: 2px solid #67e8f9;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 700;
            color: #0891b2;
            font-family: 'Roboto Mono', monospace;
            margin-top: 16px;
        }

        .modern-footer {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            padding: 24px 40px;
            border-top: 1px solid #b3e5fc;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            align-items: center;
        }

        .footer-item {
            text-align: center;
        }

        .footer-label {
            font-size: 10px;
            color: #0891b2;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .footer-value {
            font-size: 12px;
            font-weight: 500;
            color: #164e63;
        }

        .tixara-modern {
            font-size: 18px;
            font-weight: 900;
            color: #0891b2;
            letter-spacing: 1px;
            text-align: center;
            grid-column: 1 / -1;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #b3e5fc;
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .info-pair {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .modern-footer {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 2px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="modern-header">
            <div class="header-content">
                <div class="header-left">
                    <div class="header-title">MODERN</div>
                    <div class="header-subtitle">Contemporary Boarding Pass</div>
                </div>
                <div class="header-icon">M</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-card">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">
                    {{ $ticket->event->category->name ?? 'Modern Event' }}
                </div>
            </div>

            <div class="boarding-content">
                <div class="info-panel">
                    <div class="panel-header">
                        <div class="panel-title">Event Information</div>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-pair">
                            <div class="info-item">
                                <div class="info-label">Event Date</div>
                                <div class="info-value">{{ $ticket->event->start_date->format('d M Y') }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Event Time</div>
                                <div class="info-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Venue Location</div>
                            <div class="info-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">Attendee Name</div>
                            <div class="info-value primary">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="info-pair">
                            <div class="info-item">
                                <div class="info-label">Ticket Number</div>
                                <div class="info-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Seat/Zone</div>
                                <div class="info-value">{{ $ticket->seat_number ?? 'General' }}</div>
                            </div>
                        </div>

                        <div class="info-pair">
                            <div class="info-item">
                                <div class="info-label">Order ID</div>
                                <div class="info-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Price</div>
                                <div class="info-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-header">
                        <div class="qr-title">QR Validation</div>
                        <div class="qr-subtitle">Modern Security</div>
                    </div>
                    
                    <div class="qr-code">
                        MODERN<br>
                        QR CODE<br>
                        SCAN ME
                    </div>
                    
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-MOD-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="modern-footer">
            <div class="footer-item">
                <div class="footer-label">Issue Date</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y') ?? now()->format('d M Y') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Category</div>
                <div class="footer-value">{{ $ticket->event->category->name ?? 'Modern' }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Version</div>
                <div class="footer-value">v2.0</div>
            </div>
            <div class="tixara-modern">TiXara Modern</div>
        </div>
    </div>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('payment_methods')) {
            Schema::create('payment_methods', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('code')->unique();
                $table->string('type'); // manual, xendit, midtrans, tripay
                $table->string('category'); // bank_transfer, e_wallet, virtual_account, etc.
                $table->text('description')->nullable();
                $table->string('icon')->nullable();
                $table->string('logo')->nullable();
                $table->decimal('fee_percentage', 5, 2)->default(0);
                $table->integer('fee_fixed')->default(0);
                $table->integer('min_amount')->default(0);
                $table->integer('max_amount')->default(*********);
                $table->boolean('is_manual')->default(false);
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->json('manual_config')->nullable();
                $table->text('instructions')->nullable();
                $table->timestamps();

                $table->index(['is_active', 'sort_order']);
                $table->index('type');
                $table->index('category');
        });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retro Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Courier+New:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
            color: #9a3412;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: #fffbeb;
            border: 4px solid #ea580c;
            position: relative;
            box-shadow: 
                8px 8px 0px #fb923c,
                16px 16px 0px #fdba74;
            transform: rotate(-1deg);
        }

        .boarding-container::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px dashed #fb923c;
        }

        .retro-header {
            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
            color: #fffbeb;
            padding: 32px;
            text-align: center;
            position: relative;
            border-bottom: 4px solid #fb923c;
            transform: rotate(1deg);
        }

        .retro-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 40px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: 4px;
            text-shadow: 4px 4px 0px #dc2626;
            transform: perspective(500px) rotateX(15deg);
        }

        .header-subtitle {
            font-size: 16px;
            font-weight: 400;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 2px 2px 0px #dc2626;
        }

        .retro-stripes {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: repeating-linear-gradient(
                90deg,
                #fbbf24 0px,
                #fbbf24 20px,
                #f59e0b 20px,
                #f59e0b 40px,
                #ea580c 40px,
                #ea580c 60px
            );
        }

        .boarding-body {
            padding: 40px;
            background: #fffbeb;
            position: relative;
            transform: rotate(1deg);
        }

        .boarding-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40"><circle cx="20" cy="20" r="2" fill="rgba(251, 146, 60, 0.1)"/></svg>');
            opacity: 0.3;
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 40px;
            padding: 32px;
            background: #fef3c7;
            border: 4px solid #fb923c;
            position: relative;
            transform: rotate(-0.5deg);
            box-shadow: 4px 4px 0px #fdba74;
        }

        .event-showcase::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px dashed #f59e0b;
        }

        .event-title {
            font-size: 32px;
            font-weight: 700;
            color: #9a3412;
            margin-bottom: 16px;
            line-height: 1.2;
            text-shadow: 2px 2px 0px #fed7aa;
            position: relative;
            z-index: 2;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(135deg, #ea580c, #dc2626);
            color: #fffbeb;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            border: 3px solid #fb923c;
            box-shadow: 3px 3px 0px #fdba74;
            position: relative;
            z-index: 2;
            transform: rotate(1deg);
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
            position: relative;
            z-index: 2;
        }

        .details-panel {
            background: #fef3c7;
            border: 4px solid #fb923c;
            padding: 32px;
            position: relative;
            transform: rotate(0.5deg);
            box-shadow: 4px 4px 0px #fdba74;
        }

        .details-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px dashed #f59e0b;
        }

        .panel-title {
            font-size: 24px;
            font-weight: 700;
            color: #ea580c;
            margin-bottom: 24px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 2px 2px 0px #fed7aa;
            position: relative;
            z-index: 2;
        }

        .details-grid {
            display: grid;
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            background: #fffbeb;
            border: 3px solid #f59e0b;
            padding: 16px;
            text-align: center;
            position: relative;
            transform: rotate(-0.5deg);
            box-shadow: 2px 2px 0px #fdba74;
        }

        .detail-item:nth-child(even) {
            transform: rotate(0.5deg);
        }

        .detail-item::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px dashed #fbbf24;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 700;
            color: #ea580c;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 700;
            color: #9a3412;
            line-height: 1.3;
            position: relative;
            z-index: 2;
        }

        .detail-value.highlight {
            font-size: 16px;
            color: #ea580c;
            text-shadow: 1px 1px 0px #fed7aa;
        }

        .qr-panel {
            background: #fef3c7;
            border: 4px solid #fb923c;
            padding: 32px;
            text-align: center;
            position: relative;
            transform: rotate(-0.5deg);
            box-shadow: 4px 4px 0px #fdba74;
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 2px dashed #f59e0b;
        }

        .qr-title {
            font-size: 20px;
            font-weight: 700;
            color: #ea580c;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 2px 2px 0px #fed7aa;
            position: relative;
            z-index: 2;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #9a3412, #ea580c);
            border: 4px solid #fb923c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fffbeb;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            position: relative;
            z-index: 2;
            box-shadow: 3px 3px 0px #fdba74;
            transform: rotate(2deg);
        }

        .qr-code::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 2px dashed #fbbf24;
        }

        .boarding-id {
            background: #fffbeb;
            border: 3px solid #f59e0b;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 700;
            color: #ea580c;
            margin-top: 16px;
            position: relative;
            z-index: 2;
            box-shadow: 2px 2px 0px #fdba74;
            transform: rotate(-1deg);
        }

        .boarding-id::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px dashed #fbbf24;
        }

        .retro-footer {
            background: linear-gradient(135deg, #fed7aa, #fef3c7);
            padding: 24px 40px;
            border-top: 4px solid #fb923c;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            position: relative;
            transform: rotate(1deg);
        }

        .retro-footer::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: repeating-linear-gradient(
                90deg,
                #ea580c 0px,
                #ea580c 20px,
                #f59e0b 20px,
                #f59e0b 40px,
                #fbbf24 40px,
                #fbbf24 60px
            );
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
            position: relative;
            z-index: 2;
        }

        .footer-label {
            font-size: 11px;
            color: #ea580c;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
        }

        .footer-value {
            font-size: 13px;
            font-weight: 700;
            color: #9a3412;
        }

        .tixara-retro {
            font-size: 22px;
            font-weight: 700;
            color: #ea580c;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-shadow: 3px 3px 0px #fed7aa;
            position: relative;
            z-index: 2;
            transform: rotate(-1deg);
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .retro-footer {
                flex-direction: column;
                text-align: center;
                padding: 24px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 4px solid #000;
                transform: none;
            }
            
            .retro-header,
            .boarding-body,
            .event-showcase,
            .details-panel,
            .qr-panel,
            .retro-footer {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="retro-stripes"></div>
        
        <div class="retro-header">
            <div class="header-content">
                <div class="header-title">RETRO</div>
                <div class="header-subtitle">Vintage 80s Boarding Pass</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Retro Event' }}</div>
            </div>

            <div class="boarding-content">
                <div class="details-panel">
                    <div class="panel-title">Event Info</div>
                    <div class="details-grid">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('d/m/Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }}</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Venue</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Guest Name</div>
                            <div class="detail-value highlight">{{ strtoupper($ticket->attendee_name) }}</div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Ticket #</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Seat</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'GENERAL' }}</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Order</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Price</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-title">Scan Code</div>
                    <div class="qr-code">
                        RETRO<br>
                        QR CODE<br>
                        SCAN ME
                    </div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-RET-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="retro-footer">
            <div class="footer-info">
                <div class="footer-label">Issued</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d/m/Y H:i') ?? now()->format('d/m/Y H:i') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ strtoupper($ticket->status ?? 'ACTIVE') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Type</div>
                <div class="footer-value">{{ strtoupper($ticket->event->category->name ?? 'RETRO') }}</div>
            </div>
            <div class="tixara-retro">TiXara Retro</div>
        </div>
    </div>
</body>
</html>

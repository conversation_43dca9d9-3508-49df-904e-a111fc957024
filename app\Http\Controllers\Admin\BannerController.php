<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class BannerController extends Controller
{
    public function index()
    {
        $banners = Banner::orderBy('sort_order')->orderBy('created_at', 'desc')->paginate(10);
        
        return view('admin.banners.index', compact('banners'));
    }

    public function create()
    {
        return view('admin.banners.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'subtitle' => 'nullable|string|max:255',
            'image' => 'required|image|mimes:jpeg,png,jpg,webp|max:5120', // 5MB
            'mobile_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120',
            'link_url' => 'nullable|url',
            'button_text' => 'required|string|max:50',
            'button_color' => 'required|in:primary,secondary,success,warning,danger,pastel-green,pastel-pink,pastel-purple',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'sort_order' => 'required|integer|min:0',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'show_overlay' => 'boolean',
            'overlay_color' => 'required|in:dark,light,gradient',
            'overlay_opacity' => 'required|numeric|min:0|max:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle main image upload
        if ($request->hasFile('image')) {
            $data['image_path'] = $this->uploadImage($request->file('image'), 'desktop');
        }

        // Handle mobile image upload
        if ($request->hasFile('mobile_image')) {
            $data['mobile_image_path'] = $this->uploadImage($request->file('mobile_image'), 'mobile');
        }

        // Handle animation settings
        $data['animation_settings'] = [
            'entrance' => $request->input('entrance_animation', 'fadeIn'),
            'duration' => $request->input('animation_duration', 1000),
            'delay' => $request->input('animation_delay', 0)
        ];

        Banner::create($data);

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner berhasil ditambahkan!');
    }

    public function show(Banner $banner)
    {
        return view('admin.banners.show', compact('banner'));
    }

    public function edit(Banner $banner)
    {
        return view('admin.banners.edit', compact('banner'));
    }

    public function update(Request $request, Banner $banner)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'subtitle' => 'nullable|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120',
            'mobile_image' => 'nullable|image|mimes:jpeg,png,jpg,webp|max:5120',
            'link_url' => 'nullable|url',
            'button_text' => 'required|string|max:50',
            'button_color' => 'required|in:primary,secondary,success,warning,danger,pastel-green,pastel-pink,pastel-purple',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'sort_order' => 'required|integer|min:0',
            'text_position' => 'required|in:left,center,right',
            'text_color' => 'required|in:white,dark',
            'show_overlay' => 'boolean',
            'overlay_color' => 'required|in:dark,light,gradient',
            'overlay_opacity' => 'required|numeric|min:0|max:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $data = $request->all();

        // Handle main image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($banner->image_path && !filter_var($banner->image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($banner->image_path);
            }
            $data['image_path'] = $this->uploadImage($request->file('image'), 'desktop');
        }

        // Handle mobile image upload
        if ($request->hasFile('mobile_image')) {
            // Delete old mobile image
            if ($banner->mobile_image_path && !filter_var($banner->mobile_image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($banner->mobile_image_path);
            }
            $data['mobile_image_path'] = $this->uploadImage($request->file('mobile_image'), 'mobile');
        }

        // Handle animation settings
        $data['animation_settings'] = [
            'entrance' => $request->input('entrance_animation', 'fadeIn'),
            'duration' => $request->input('animation_duration', 1000),
            'delay' => $request->input('animation_delay', 0)
        ];

        $banner->update($data);

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner berhasil diperbarui!');
    }

    public function destroy(Banner $banner)
    {
        $banner->delete();

        return redirect()->route('admin.banners.index')
            ->with('success', 'Banner berhasil dihapus!');
    }

    public function toggleStatus(Banner $banner)
    {
        $banner->update(['is_active' => !$banner->is_active]);

        $status = $banner->is_active ? 'diaktifkan' : 'dinonaktifkan';
        
        return redirect()->back()
            ->with('success', "Banner berhasil {$status}!");
    }

    public function updateOrder(Request $request)
    {
        $request->validate([
            'banners' => 'required|array',
            'banners.*.id' => 'required|exists:banners,id',
            'banners.*.sort_order' => 'required|integer|min:0'
        ]);

        foreach ($request->banners as $bannerData) {
            Banner::where('id', $bannerData['id'])
                ->update(['sort_order' => $bannerData['sort_order']]);
        }

        return response()->json(['success' => true]);
    }

    private function uploadImage($file, $type = 'desktop')
    {
        $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();
        $path = "banners/{$filename}";

        // Resize image based on type
        $image = Image::make($file);
        
        if ($type === 'mobile') {
            // Mobile: 768x1024 (3:4 ratio)
            $image->fit(768, 1024, function ($constraint) {
                $constraint->upsize();
            });
        } else {
            // Desktop: 1920x800 (12:5 ratio)
            $image->fit(1920, 800, function ($constraint) {
                $constraint->upsize();
            });
        }

        // Optimize image quality
        $image->encode($file->getClientOriginalExtension(), 85);

        // Store image
        Storage::put($path, $image->stream());

        return $path;
    }

    public function preview(Request $request)
    {
        $data = $request->all();
        
        // Handle temporary image upload for preview
        if ($request->hasFile('image')) {
            $tempPath = $request->file('image')->store('temp');
            $data['image_url'] = Storage::url($tempPath);
        }

        return response()->json($data);
    }
}

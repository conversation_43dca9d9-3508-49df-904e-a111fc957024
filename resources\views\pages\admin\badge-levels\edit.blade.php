@extends('layouts.admin')

@section('title', 'Edit Badge Level - ' . $badgeLevel->name)

@push('styles')
<style>
.form-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #E5E7EB;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #F3F4F6;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    margin-left: 12px;
}

.icon-preview {
    width: 40px;
    height: 40px;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #6B7280;
    margin-left: 12px;
}

.current-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
    border: 2px solid #E5E7EB;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #F9FAFB;
    border-radius: 8px;
    margin-bottom: 8px;
}

.requirement-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #FEF3C7;
    border-radius: 8px;
    margin-bottom: 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.users-info {
    background: linear-gradient(135deg, #EBF8FF 0%, #E0F2FE 100%);
    border: 1px solid #BAE6FD;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Badge Level</h1>
            <p class="text-gray-600 dark:text-gray-400">Update badge level information and settings</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('admin.badge-level.show', $badgelevel) }}" class="btn btn-secondary">
                <i data-lucide="eye" class="w-4 h-4"></i>
                View Badge Level
            </a>
            <a href="{{ route('admin.badge-level.index') }}" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4"></i>
                Back to Badge Level
            </a>
        </div>
    </div>

    <!-- Users Info -->
    @if($badgeLevel->users->count() > 0)
        <div class="users-info">
            <div class="flex items-center gap-3">
                <i data-lucide="info" class="w-5 h-5 text-blue-600"></i>
                <div>
                    <p class="font-medium text-blue-900">
                        This badge level is currently assigned to {{ $badgeLevel->users->count() }} user(s).
                    </p>
                    <p class="text-sm text-blue-700">
                        Changes to requirements may affect existing users. Consider running auto-upgrade after saving.
                    </p>
                </div>
            </div>
        </div>
    @endif

    <form action="{{ route('admin.badge-level.update', $badgelevel) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="form-section-title">Basic Information</h3>
            
            <div class="form-grid">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Badge Name *</label>
                    <input type="text" name="name" value="{{ old('name', $badgeLevel->name) }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="e.g., Gold Member">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order *</label>
                    <input type="number" name="sort_order" value="{{ old('sort_order', $badgeLevel->sort_order) }}" required min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="1">
                    @error('sort_order')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Describe this badge level...">{{ old('description', $badgeLevel->description) }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div class="form-grid mt-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Badge Color *</label>
                    <div class="flex items-center">
                        <input type="color" name="color" value="{{ old('color', $badgeLevel->color) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               onchange="updateColorPreview(this.value)">
                        <div id="colorPreview" class="color-preview" style="background: {{ old('color', $badgeLevel->color) }};">
                            <i class="{{ $badgeLevel->icon ?? 'fas fa-medal' }}"></i>
                        </div>
                    </div>
                    @error('color')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Icon Class</label>
                    <div class="flex items-center">
                        <input type="text" name="icon" value="{{ old('icon', $badgeLevel->icon) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="fas fa-medal"
                               onchange="updateIconPreview(this.value)">
                        <div id="iconPreview" class="icon-preview">
                            <i class="{{ old('icon', $badgeLevel->icon) }}"></i>
                        </div>
                    </div>
                    @error('icon')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Badge Image (Optional)</label>
                @if($badgeLevel->badge_image)
                    <div class="mb-3">
                        <p class="text-sm text-gray-600 mb-2">Current image:</p>
                        <img src="{{ asset($badgeLevel->badge_image) }}" alt="Current badge image" class="current-image">
                    </div>
                @endif
                <input type="file" name="badge_image" accept="image/*"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <p class="text-sm text-gray-500 mt-1">Upload a new badge image to replace the current one (JPEG, PNG, GIF, max 2MB)</p>
                @error('badge_image')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Requirements -->
        <div class="form-section">
            <h3 class="form-section-title">Requirements</h3>
            
            <div class="form-grid">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Spent Amount (Rp) *</label>
                    <input type="number" name="min_spent_amount" value="{{ old('min_spent_amount', $badgeLevel->min_spent_amount) }}" required min="0" step="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('min_spent_amount')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum UangTix Balance (Rp) *</label>
                    <input type="number" name="min_uangtix_balance" value="{{ old('min_uangtix_balance', $badgeLevel->min_uangtix_balance) }}" required min="0" step="1000"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('min_uangtix_balance')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Transactions *</label>
                    <input type="number" name="min_transactions" value="{{ old('min_transactions', $badgeLevel->min_transactions) }}" required min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('min_transactions')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Events Attended *</label>
                    <input type="number" name="min_events_attended" value="{{ old('min_events_attended', $badgeLevel->min_events_attended) }}" required min="0"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('min_events_attended')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Additional Requirements -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Additional Requirements</label>
                <div class="space-y-2">
                    <div class="requirement-item">
                        <input type="checkbox" name="requirements[verified_email]" value="1" 
                               {{ (old('requirements.verified_email') ?? ($badgeLevel->requirements['verified_email'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Email must be verified</label>
                    </div>
                    <div class="requirement-item">
                        <input type="checkbox" name="requirements[verified_phone]" value="1"
                               {{ (old('requirements.verified_phone') ?? ($badgeLevel->requirements['verified_phone'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Phone must be verified</label>
                    </div>
                    <div class="requirement-item">
                        <input type="checkbox" name="requirements[kyc_verified]" value="1"
                               {{ (old('requirements.kyc_verified') ?? ($badgeLevel->requirements['kyc_verified'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">KYC must be completed</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits -->
        <div class="form-section">
            <h3 class="form-section-title">Benefits</h3>
            
            <div class="form-grid">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Discount Percentage (%) *</label>
                    <input type="number" name="discount_percentage" value="{{ old('discount_percentage', $badgeLevel->discount_percentage) }}" required min="0" max="100" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('discount_percentage')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Cashback Percentage (%) *</label>
                    <input type="number" name="cashback_percentage" value="{{ old('cashback_percentage', $badgeLevel->cashback_percentage) }}" required min="0" max="100" step="0.1"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="0">
                    @error('cashback_percentage')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Additional Benefits -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Additional Benefits</label>
                <div class="space-y-2">
                    <div class="benefit-item">
                        <input type="checkbox" name="benefits[priority_support]" value="1"
                               {{ (old('benefits.priority_support') ?? ($badgeLevel->benefits['priority_support'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Priority customer support</label>
                    </div>
                    <div class="benefit-item">
                        <input type="checkbox" name="benefits[early_access]" value="1"
                               {{ (old('benefits.early_access') ?? ($badgeLevel->benefits['early_access'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Early access to events</label>
                    </div>
                    <div class="benefit-item">
                        <input type="checkbox" name="benefits[free_shipping]" value="1"
                               {{ (old('benefits.free_shipping') ?? ($badgeLevel->benefits['free_shipping'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Free shipping</label>
                    </div>
                    <div class="benefit-item">
                        <input type="checkbox" name="benefits[exclusive_events]" value="1"
                               {{ (old('benefits.exclusive_events') ?? ($badgeLevel->benefits['exclusive_events'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Access to exclusive events</label>
                    </div>
                    <div class="benefit-item">
                        <input type="checkbox" name="benefits[personal_manager]" value="1"
                               {{ (old('benefits.personal_manager') ?? ($badgeLevel->benefits['personal_manager'] ?? false)) ? 'checked' : '' }}
                               class="rounded border-gray-300">
                        <label class="text-sm text-gray-700">Personal account manager</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class="form-section">
            <h3 class="form-section-title">Status</h3>
            
            <div class="flex items-center">
                <input type="checkbox" name="is_active" value="1" {{ old('is_active', $badgeLevel->is_active) ? 'checked' : '' }}
                       class="rounded border-gray-300">
                <label class="ml-2 text-sm text-gray-700">Active (users can achieve this badge level)</label>
            </div>
            
            @if($badgeLevel->users->count() > 0 && !$badgeLevel->is_active)
                <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p class="text-sm text-yellow-800">
                        <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                        Warning: This badge level has {{ $badgeLevel->users->count() }} user(s) assigned. 
                        Deactivating it may affect their benefits.
                    </p>
                </div>
            @endif
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-between">
            <div>
                @if($badgeLevel->users->count() > 0)
                    <button type="button" onclick="runAutoUpgrade()" class="btn btn-warning">
                        <i data-lucide="zap" class="w-4 h-4"></i>
                        Run Auto-Upgrade After Save
                    </button>
                @endif
            </div>
            <div class="flex gap-3">
                <a href="{{ route('admin.badge-level.show', $badgelevel) }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i data-lucide="save" class="w-4 h-4"></i>
                    Update Badge Level
                </button>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
function updateColorPreview(color) {
    document.getElementById('colorPreview').style.background = color;
}

function updateIconPreview(iconClass) {
    const preview = document.getElementById('iconPreview');
    preview.innerHTML = `<i class="${iconClass}"></i>`;
}

function runAutoUpgrade() {
    if (confirm('This will automatically upgrade all eligible users after saving. Continue?')) {
        // Add a hidden input to trigger auto-upgrade
        const form = document.querySelector('form');
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'run_auto_upgrade';
        input.value = '1';
        form.appendChild(input);
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value;
    const minSpent = parseFloat(document.querySelector('input[name="min_spent_amount"]').value);
    const minUangTix = parseFloat(document.querySelector('input[name="min_uangtix_balance"]').value);
    const minTransactions = parseInt(document.querySelector('input[name="min_transactions"]').value);
    const minEvents = parseInt(document.querySelector('input[name="min_events_attended"]').value);
    const discount = parseFloat(document.querySelector('input[name="discount_percentage"]').value);
    const cashback = parseFloat(document.querySelector('input[name="cashback_percentage"]').value);

    if (!name.trim()) {
        alert('Badge name is required');
        e.preventDefault();
        return;
    }

    if (discount < 0 || discount > 100) {
        alert('Discount percentage must be between 0 and 100');
        e.preventDefault();
        return;
    }

    if (cashback < 0 || cashback > 100) {
        alert('Cashback percentage must be between 0 and 100');
        e.preventDefault();
        return;
    }

    if (minSpent < 0 || minUangTix < 0 || minTransactions < 0 || minEvents < 0) {
        alert('All minimum requirements must be 0 or greater');
        e.preventDefault();
        return;
    }
});
</script>
@endpush

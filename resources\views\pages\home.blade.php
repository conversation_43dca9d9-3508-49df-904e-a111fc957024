@extends('layouts.main')

@section('content')
<div class="min-h-screen">
    {{-- ENHANCED GOPAY/OVO-STYLE HERO SECTION WITH INTEGRATED BANNER & FLOATING TICKETS --}}
    <div class="hero-container pwa-safe-area relative z-10 overflow-hidden">
        {{-- Dynamic GoPay/OVO Style Gradient Background --}}
        <div class="gopay-gradient-bg absolute inset-0 z-0"></div>

        {{-- Enhanced Floating Tickets Background Animation --}}
        <div class="floating-tickets-container absolute inset-0 pointer-events-none z-5">
            {{-- Primary Floating Tickets --}}
            <div class="floating-ticket ticket-1" data-speed="2" data-type="konser">
                <div class="ticket-content">
                    <div class="ticket-header">🎵</div>
                    <div class="ticket-title">Konser</div>
                    <div class="ticket-price">Rp 150K</div>
                    <div class="ticket-category">Musik</div>
                </div>
            </div>
            <div class="floating-ticket ticket-2" data-speed="3" data-type="theater">
                <div class="ticket-content">
                    <div class="ticket-header">🎭</div>
                    <div class="ticket-title">Theater</div>
                    <div class="ticket-price">Rp 75K</div>
                    <div class="ticket-category">Seni</div>
                </div>
            </div>
            <div class="floating-ticket ticket-3" data-speed="1.5" data-type="olahraga">
                <div class="ticket-content">
                    <div class="ticket-header">🏃</div>
                    <div class="ticket-title">Marathon</div>
                    <div class="ticket-price">Rp 200K</div>
                    <div class="ticket-category">Olahraga</div>
                </div>
            </div>
            <div class="floating-ticket ticket-4" data-speed="2.5" data-type="workshop">
                <div class="ticket-content">
                    <div class="ticket-header">🎨</div>
                    <div class="ticket-title">Workshop</div>
                    <div class="ticket-price">Rp 100K</div>
                    <div class="ticket-category">Edukasi</div>
                </div>
            </div>
            <div class="floating-ticket ticket-5" data-speed="1.8" data-type="kuliner">
                <div class="ticket-content">
                    <div class="ticket-header">🍔</div>
                    <div class="ticket-title">Food Fest</div>
                    <div class="ticket-price">Rp 50K</div>
                    <div class="ticket-category">Kuliner</div>
                </div>
            </div>
            <div class="floating-ticket ticket-6" data-speed="2.2" data-type="bisnis">
                <div class="ticket-content">
                    <div class="ticket-header">💼</div>
                    <div class="ticket-title">Seminar</div>
                    <div class="ticket-price">Rp 125K</div>
                    <div class="ticket-category">Bisnis</div>
                </div>
            </div>

            {{-- Additional Dynamic Tickets --}}
            <div class="floating-ticket ticket-7" data-speed="2.8" data-type="teknologi">
                <div class="ticket-content">
                    <div class="ticket-header">💻</div>
                    <div class="ticket-title">Tech Talk</div>
                    <div class="ticket-price">Rp 80K</div>
                    <div class="ticket-category">Teknologi</div>
                </div>
            </div>
            <div class="floating-ticket ticket-8" data-speed="1.3" data-type="fashion">
                <div class="ticket-content">
                    <div class="ticket-header">👗</div>
                    <div class="ticket-title">Fashion Show</div>
                    <div class="ticket-price">Rp 300K</div>
                    <div class="ticket-category">Fashion</div>
                </div>
            </div>
        </div>

        {{-- TiXara Maskot 3D Ticket Animation --}}
        <div class="tixara-mascot-container absolute inset-0 pointer-events-auto z-6" id="tixaraMascotContainer">
            {{-- Main TiXara Mascot Ticket --}}
            <div class="tixara-mascot-ticket" id="mainMascotTicket">
                <div class="tixara-ticket-inner">
                    {{-- Front Side --}}
                    <div class="tixara-ticket-front">
                        <div class="tixara-ticket-content">
                            {{-- TiXara Logo/Mascot --}}
                            <div class="tixara-mascot-logo">
                                <div class="mascot-character">
                                    <div class="mascot-head">
                                        <div class="mascot-eyes">
                                            <div class="eye left-eye"></div>
                                            <div class="eye right-eye"></div>
                                        </div>
                                        <div class="mascot-smile"></div>
                                    </div>
                                    <div class="mascot-body">
                                        <div class="mascot-arms">
                                            <div class="arm left-arm"></div>
                                            <div class="arm right-arm"></div>
                                        </div>
                                        <div class="mascot-ticket-hold">🎫</div>
                                    </div>
                                </div>
                            </div>

                            {{-- TiXara Branding --}}
                            <div class="tixara-branding">
                                <h2 class="tixara-title">TiXara</h2>
                                <p class="tixara-subtitle">Event Ticket Platform</p>
                            </div>

                            {{-- Features Icons --}}
                            <div class="tixara-features">
                                <div class="feature-icon">🎵</div>
                                <div class="feature-icon">🎭</div>
                                <div class="feature-icon">🏃</div>
                                <div class="feature-icon">🍔</div>
                            </div>

                            {{-- Stats --}}
                            <div class="tixara-stats">
                                <div class="stat-item">
                                    <div class="stat-number">1000+</div>
                                    <div class="stat-label">Events</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">50K+</div>
                                    <div class="stat-label">Users</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Back Side --}}
                    <div class="tixara-ticket-back">
                        <div class="tixara-ticket-content">
                            {{-- QR Code Representation --}}
                            <div class="tixara-qr-section">
                                <div class="qr-code-graphic">
                                    <div class="qr-pattern">
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                        <div class="qr-dot"></div>
                                    </div>
                                </div>
                                <p class="qr-label">Scan untuk akses</p>
                            </div>

                            {{-- Platform Info --}}
                            <div class="platform-info">
                                <h3 class="platform-title">Platform #1</h3>
                                <p class="platform-desc">Tiket Event Indonesia</p>
                            </div>

                            {{-- Security Features --}}
                            <div class="security-features">
                                <div class="security-item">
                                    <span class="security-icon">🔒</span>
                                    <span class="security-text">Secure</span>
                                </div>
                                <div class="security-item">
                                    <span class="security-icon">✅</span>
                                    <span class="security-text">Verified</span>
                                </div>
                                <div class="security-item">
                                    <span class="security-icon">⚡</span>
                                    <span class="security-text">Instant</span>
                                </div>
                            </div>

                            {{-- Footer --}}
                            <div class="ticket-footer">
                                <p class="footer-text">tixara.my.id</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Floating Mini Tickets --}}
            <div class="mini-ticket mini-ticket-1">
                <div class="mini-content">
                    <div class="mini-icon">🎵</div>
                    <div class="mini-text">Musik</div>
                </div>
            </div>

            <div class="mini-ticket mini-ticket-2">
                <div class="mini-content">
                    <div class="mini-icon">🎭</div>
                    <div class="mini-text">Seni</div>
                </div>
            </div>

            <div class="mini-ticket mini-ticket-3">
                <div class="mini-content">
                    <div class="mini-icon">💻</div>
                    <div class="mini-text">Tech</div>
                </div>
            </div>

            <div class="mini-ticket mini-ticket-4">
                <div class="mini-content">
                    <div class="mini-icon">🏃</div>
                    <div class="mini-text">Sport</div>
                </div>
            </div>

            <div class="mini-ticket mini-ticket-5">
                <div class="mini-content">
                    <div class="mini-icon">🍔</div>
                    <div class="mini-text">Food</div>
                </div>
            </div>
        </div>

        {{-- Floating Particles Effect --}}
        <div class="particles-container absolute inset-0 pointer-events-none z-4">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
        </div>

        @if($banners && $banners->count() > 0)
            {{-- Enhanced GoPay/OVO-Style Banner Carousel --}}
            <div id="bannerCarousel" class="banner-carousel gpu-accelerated relative z-10">
                @foreach($banners as $index => $banner)
                    <div class="banner-slide transition-optimized gpu-accelerated no-select {{ $index === 0 ? 'opacity-100 z-10' : 'opacity-0 z-0' }}"
                        data-banner-id="{{ $banner->id }}"
                        data-animation="{{ json_encode($banner->animation_settings ?? ['entrance' => 'fadeIn', 'duration' => 1000, 'delay' => 0]) }}"
                        role="img"
                        aria-label="{{ $banner->title }}"
                        tabindex="{{ $index === 0 ? '0' : '-1' }}">

                        {{-- Background Image with GoPay/OVO-style overlay --}}
                        <div class="absolute inset-0">
                            <img src="{{ $banner->image_url }}"
                                 alt="{{ $banner->title }}"
                                 class="hidden md:block w-full h-full object-cover"
                                 loading="{{ $index === 0 ? 'eager' : 'lazy' }}">
                            <img src="{{ $banner->mobile_image_url }}"
                                 alt="{{ $banner->title }}"
                                 class="md:hidden w-full h-full object-cover"
                                 loading="{{ $index === 0 ? 'eager' : 'lazy' }}">

                            {{-- GoPay/OVO-style gradient overlay --}}
                            <div class="absolute inset-0 bg-gradient-to-br from-black/60 via-transparent to-black/40"></div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent"></div>
                        </div>

                        {{-- Custom overlay if enabled --}}
                        @if($banner->show_overlay)
                            <div class="absolute inset-0" style="{{ $banner->overlay_style }}"></div>
                        @endif

                        {{-- GoPay/OVO-style status header --}}
                        <div class="absolute top-4 sm:top-6 left-4 sm:left-6 right-4 sm:right-6 z-30">
                            <div class="gopay-card p-3 sm:p-4 text-sm">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                        <span class="text-green-600 font-bold text-xs sm:text-sm">LIVE EVENT</span>
                                    </div>
                                    <div class="flex items-center space-x-1 text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="text-xs">TiXara Platform</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Main Content with GoPay/OVO Design --}}
                        <div class="relative z-20 h-full flex items-center">
                            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                    {{-- Left Content --}}
                                    <div class="space-y-6">
                                        {{-- GoPay/OVO-style status badge --}}
                                        @if($banner->subtitle)
                                            <div class="inline-flex items-center px-4 py-2 gopay-card text-sm font-bold"
                                                 data-aos="fade-up" data-aos-delay="{{ ($banner->animation_settings['delay'] ?? 0) + 100 }}">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                                                <span class="text-blue-600">{{ $banner->subtitle }}</span>
                                            </div>
                                        @endif

                                        {{-- Main title with modern typography --}}
                                        <h1 class="hero-title font-bold leading-tight text-white mb-4"
                                            data-aos="fade-up" data-aos-delay="{{ ($banner->animation_settings['delay'] ?? 0) + 200 }}">
                                            {{ strip_tags($banner->title) }}
                                        </h1>

                                        {{-- Description with GoPay/OVO-style card --}}
                                        @if($banner->description)
                                            <div class="gopay-card p-4 max-w-lg"
                                                 data-aos="fade-up" data-aos-delay="{{ ($banner->animation_settings['delay'] ?? 0) + 300 }}">
                                                <div class="text-gray-700 leading-relaxed">{{ $banner->description }}</div>
                                            </div>
                                        @endif

                                        {{-- GoPay/OVO-style action buttons --}}
                                        <div class="flex flex-col sm:flex-row gap-4"
                                             data-aos="fade-up" data-aos-delay="{{ ($banner->animation_settings['delay'] ?? 0) + 400 }}">
                                            @if($banner->link_url && $banner->button_text)
                                                <a href="{{ $banner->link_url }}"
                                                   class="gopay-btn px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                                    <span class="flex items-center justify-center">
                                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                                        </svg>
                                                        {{ $banner->button_text }}
                                                    </span>
                                                </a>
                                            @endif
                                            <button class="ovo-btn px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                                <span class="flex items-center justify-center">
                                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                                    </svg>
                                                    Simpan Event
                                                </span>
                                            </button>
                                        </div>
                                    </div>

                                    {{-- Right Content - Interactive Card --}}
                                    <div class="hidden lg:block" data-aos="fade-left" data-aos-delay="{{ ($banner->animation_settings['delay'] ?? 0) + 500 }}">
                                        <div class="gopay-card p-6 space-y-4">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-lg font-bold text-gray-800">Event Quick Info</h3>
                                                <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="grid grid-cols-2 gap-4">
                                                <div class="text-center p-3 bg-green-50 rounded-xl">
                                                    <div class="text-2xl font-bold text-green-600">1000+</div>
                                                    <div class="text-sm text-gray-600">Events</div>
                                                </div>
                                                <div class="text-center p-3 bg-blue-50 rounded-xl">
                                                    <div class="text-2xl font-bold text-blue-600">50K+</div>
                                                    <div class="text-sm text-gray-600">Users</div>
                                                </div>
                                            </div>
                                            <div class="flex items-center justify-center space-x-2 text-sm text-gray-600">
                                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                                <span>Trusted Platform</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach

                {{-- GoPay/OVO-style Navigation --}}
                @if($banners->count() > 1)
                    {{-- Modern dots indicator --}}
                    <div class="banner-dots absolute bottom-6 sm:bottom-8 left-1/2 transform -translate-x-1/2 z-30">
                        <div class="gopay-card px-4 py-2">
                            <div class="flex items-center space-x-3">
                                @foreach($banners as $index => $banner)
                                    <button class="banner-dot touch-target w-3 h-3 rounded-full transition-all duration-300 {{ $index === 0 ? 'bg-green-500 scale-125' : 'bg-gray-300 hover:bg-green-400' }}"
                                            data-slide="{{ $index }}"
                                            aria-label="Go to slide {{ $index + 1 }}"
                                            tabindex="0"></button>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    {{-- Modern navigation buttons --}}
                    <button class="banner-nav-prev banner-nav-btn absolute left-4 sm:left-6 top-1/2 transform -translate-y-1/2 z-30 w-12 h-12 sm:w-14 sm:h-14 gopay-card hover:scale-110 flex items-center justify-center transition-all duration-300 group"
                            aria-label="Previous slide"
                            tabindex="0">
                        <svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-600 group-hover:text-green-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                    </button>
                    <button class="banner-nav-next banner-nav-btn absolute right-4 sm:right-6 top-1/2 transform -translate-y-1/2 z-30 w-12 h-12 sm:w-14 sm:h-14 gopay-card hover:scale-110 flex items-center justify-center transition-all duration-300 group"
                            aria-label="Next slide"
                            tabindex="0">
                        <svg class="w-5 h-5 sm:w-6 sm:h-6 text-gray-600 group-hover:text-green-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </button>
                @endif
            </div>
        @else
            {{-- GoPay/OVO-Style Default Hero Section --}}
            <div class="relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-green-900">
                {{-- Matrix-style background --}}
                <div class="absolute inset-0">
                    <div class="h-full w-full opacity-30" style="background-image:
                        linear-gradient(rgba(0,255,0,0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(0,255,0,0.1) 1px, transparent 1px);
                        background-size: 20px 20px;"></div>
                </div>

                {{-- Animated code rain effect --}}
                <div class="absolute inset-0 overflow-hidden">
                    <div class="code-rain"></div>
                </div>

                {{-- Terminal header --}}
                <div class="absolute top-4 left-4 right-4 z-30">
                    <div class="bg-gray-900/90 backdrop-blur-sm rounded-lg border border-green-500/30 p-3 font-mono text-sm">
                        <div class="flex items-center space-x-2 mb-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-green-400 ml-4">tixara@events:~$</span>
                        </div>
                        <div class="text-green-300 text-xs">
                            <span class="text-gray-400"># </span>Initializing TiXara Event Platform...
                        </div>
                    </div>
                </div>
                <div class="relative z-10 flex items-center min-h-screen">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                            <div class="text-white space-y-8">
                                {{-- Unix-style status badge --}}
                                <div class="inline-flex items-center px-4 py-2 bg-gray-900/80 backdrop-blur-sm rounded-lg border border-green-500/50 text-sm font-mono" data-aos="fade-up">
                                    <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                                    <span class="text-green-400">[ONLINE]</span>
                                    <span class="text-white ml-2">Platform Tiket Event #1 Indonesia</span>
                                </div>

                                {{-- Terminal-style main title --}}
                                <div data-aos="fade-up" data-aos-delay="100">
                                    <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight text-white font-mono">
                                        <span class="text-green-400">></span>
                                        <span class="block">./find</span>
                                        <span class="block text-green-300 typing-text">event_impian</span>
                                        <span class="block">--execute</span>
                                        <span class="cursor-blink text-green-400">|</span>
                                    </h1>
                                </div>

                                {{-- Terminal output description --}}
                                <div class="bg-gray-900/60 backdrop-blur-sm rounded-lg border border-green-500/30 p-4 font-mono text-sm max-w-2xl" data-aos="fade-up" data-aos-delay="200">
                                    <div class="text-green-400 mb-2">$ cat platform_info.txt</div>
                                    <div class="text-white/90 leading-relaxed">
                                        Jelajahi ribuan event menarik: konser musik, seminar bisnis, workshop kreatif, festival kuliner.
                                        <span class="text-green-300 font-semibold">Semua dalam satu platform!</span>
                                    </div>
                                </div>
                                {{-- GoPay/OVO-style command buttons --}}
                                <div class="flex flex-col sm:flex-row gap-4" data-aos="fade-up" data-aos-delay="300">
                                    <button onclick="document.getElementById('heroSearch').focus()"
                                            class="gopay-btn group px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                        <span class="flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                            </svg>
                                            <span>Cari Event</span>
                                        </span>
                                    </button>
                                    <a href="{{ route('categories.index') }}"
                                       class="ovo-btn group px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                        <span class="flex items-center justify-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                            </svg>
                                            <span>Kategori</span>
                                        </span>
                                    </a>
                                </div>
                                {{-- GoPay/OVO-style UangTix wallet --}}
                                @auth
                                <div class="mt-6" data-aos="fade-up" data-aos-delay="350">
                                    <a href="{{ route('uangtix.index') }}"
                                       class="gopay-card group inline-flex items-center px-6 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-105">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-3">
                                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="text-gray-800 font-bold">UangTix Wallet</div>
                                                <div class="text-green-600 font-bold text-lg">
                                                    UTX {{ number_format(auth()->user()->getUangTixBalanceAmount(), 0) }}
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                    <div class="gopay-card mt-3 p-4 text-sm">
                                        <div class="text-gray-600 flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            1 UangTix = 1 Rupiah • Digital wallet untuk transaksi mudah
                                        </div>
                                    </div>
                                </div>
                                @endauth

                                {{-- Unix-style system stats --}}
                                <div class="flex items-center space-x-6 pt-4" data-aos="fade-up" data-aos-delay="400">
                                    <div class="bg-gray-900/60 backdrop-blur-sm rounded-lg border border-green-500/30 p-3 font-mono text-xs">
                                        <div class="text-green-400 mb-1">$ ps aux | grep users</div>
                                        <div class="text-white/80">50K+ active users</div>
                                    </div>
                                    <div class="bg-gray-900/60 backdrop-blur-sm rounded-lg border border-green-500/30 p-3 font-mono text-xs">
                                        <div class="text-green-400 mb-1">$ cat rating.log</div>
                                        <div class="text-white/80">★★★★★ 4.9/5</div>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-8">
                                {{-- Unix-style search terminal --}}
                                <div class="bg-gray-900/90 backdrop-blur-lg rounded-lg border border-green-500/30 p-6 shadow-xl" data-aos="fade-up" data-aos-delay="300">
                                    {{-- Terminal header --}}
                                    <div class="flex items-center space-x-2 mb-4">
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-green-400 font-mono text-sm ml-4">search@tixara:~$</span>
                                    </div>

                                    {{-- Search input --}}
                                    <div class="relative mb-6">
                                        <div class="flex items-center bg-black/50 rounded-lg border border-green-500/50 p-3 font-mono">
                                            <span class="text-green-400 mr-2">$</span>
                                            <span class="text-green-300 mr-2">grep -i</span>
                                            <input type="text"
                                                   id="heroSearch"
                                                   class="flex-1 bg-transparent text-white placeholder-gray-400 focus:outline-none font-mono"
                                                   placeholder="'konser|seminar|workshop'">
                                            <button id="heroSearchBtn" class="ml-2 px-3 py-1 bg-green-600 hover:bg-green-500 text-black rounded font-mono text-sm transition-colors">
                                                exec
                                            </button>
                                        </div>
                                    </div>

                                    {{-- Popular commands --}}
                                    <div class="mb-4">
                                        <div class="text-green-400 font-mono text-sm mb-2">$ history | tail -3</div>
                                        <div class="space-y-1">
                                            <button class="block w-full text-left px-3 py-1 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-mono text-sm rounded transition-colors border border-gray-600/50">
                                                grep "konser" events.db
                                            </button>
                                            <button class="block w-full text-left px-3 py-1 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-mono text-sm rounded transition-colors border border-gray-600/50">
                                                find . -name "*workshop*"
                                            </button>
                                            <button class="block w-full text-left px-3 py-1 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-mono text-sm rounded transition-colors border border-gray-600/50">
                                                cat seminar_list.txt
                                            </button>
                                        </div>
                                    </div>
                                    <div id="searchSuggestions" class="hidden space-y-2"></div>
                                </div>
                                {{-- Unix-style system monitor --}}
                                <div class="bg-gray-900/90 backdrop-blur-lg rounded-lg border border-green-500/30 p-6" data-aos="fade-up" data-aos-delay="400">
                                    <div class="flex items-center space-x-2 mb-4">
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <span class="text-green-400 font-mono text-sm ml-4">monitor@tixara:~$</span>
                                    </div>

                                    <div class="space-y-3 font-mono text-sm">
                                        <div class="bg-black/50 rounded border border-green-500/30 p-3">
                                            <div class="text-green-400 mb-1">$ wc -l events.db</div>
                                            <div class="text-white flex items-center justify-between">
                                                <span>events_available:</span>
                                                <span class="text-green-300 font-bold" data-counter="1000">0</span>
                                            </div>
                                        </div>

                                        <div class="bg-black/50 rounded border border-green-500/30 p-3">
                                            <div class="text-green-400 mb-1">$ grep -c "sold" tickets.log</div>
                                            <div class="text-white flex items-center justify-between">
                                                <span>tickets_sold:</span>
                                                <span class="text-green-300 font-bold" data-counter="50000">0</span>
                                            </div>
                                        </div>

                                        <div class="grid grid-cols-2 gap-3">
                                            <div class="bg-black/50 rounded border border-green-500/30 p-3">
                                                <div class="text-green-400 mb-1 text-xs">$ cities.count</div>
                                                <div class="text-white text-center">
                                                    <div class="text-lg font-bold text-green-300" data-counter="25">0</div>
                                                    <div class="text-xs">cities</div>
                                                </div>
                                            </div>

                                            <div class="bg-black/50 rounded border border-green-500/30 p-3">
                                                <div class="text-green-400 mb-1 text-xs">$ avg rating.log</div>
                                                <div class="text-white text-center">
                                                    <div class="text-lg font-bold text-green-300" data-counter="4.9">0</div>
                                                    <div class="text-xs">rating</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Unix-style scroll indicator --}}
                <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2" data-aos="fade-up" data-aos-delay="600">
                    <div class="bg-gray-900/80 backdrop-blur-sm rounded-lg border border-green-500/30 p-3 font-mono text-sm">
                        <div class="text-green-400 text-center">
                            <div class="animate-bounce">↓</div>
                            <div class="text-xs mt-1">scroll down</div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>

    {{-- Enhanced Promotional Banner Carousel --}}
    <section class="py-16 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 relative overflow-hidden">
        {{-- Animated Background Elements --}}
        <div class="absolute inset-0">
            <div class="absolute top-0 left-0 w-full h-full opacity-10">
                <div class="absolute top-10 left-10 w-32 h-32 bg-blue-400 rounded-full blur-3xl animate-pulse"></div>
                <div class="absolute top-32 right-20 w-24 h-24 bg-purple-400 rounded-full blur-2xl animate-pulse" style="animation-delay: 1s;"></div>
                <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-green-400 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
                <div class="absolute bottom-10 right-10 w-28 h-28 bg-pink-400 rounded-full blur-2xl animate-pulse" style="animation-delay: 3s;"></div>
            </div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {{-- Section Header --}}
            <div class="text-center mb-12">
                <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-4" data-aos="fade-up">
                    <span class="w-2 h-2 bg-yellow-400 rounded-full mr-2 animate-pulse"></span>
                    Promo Spesial
                </div>
                <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4" data-aos="fade-up" data-aos-delay="100">
                    🎉 Banner Promosi
                </h2>
                <p class="text-gray-300 max-w-2xl mx-auto text-lg" data-aos="fade-up" data-aos-delay="200">
                    Jangan lewatkan penawaran menarik dan event eksklusif dari partner kami
                </p>
            </div>

            {{-- Banner Carousel Container --}}
            <div class="relative" data-aos="fade-up" data-aos-delay="300">
                <div id="promoBannerCarousel" class="relative overflow-hidden rounded-2xl shadow-2xl">
                    {{-- Banner Slides --}}
                    <div class="banner-slides-container relative">
                        {{-- Sample Banner 1 --}}
                        <div class="banner-slide active relative h-64 md:h-80 lg:h-96 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center">
                            <div class="absolute inset-0 bg-black/20"></div>
                            <div class="relative z-10 w-full px-8 md:px-12">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                    <div class="text-white space-y-4">
                                        <div class="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                                            <span class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></span>
                                            Diskon 50%
                                        </div>
                                        <h3 class="text-2xl md:text-4xl font-bold">Festival Musik Indonesia</h3>
                                        <p class="text-lg opacity-90">Nikmati pertunjukan musik terbaik dengan harga spesial. Berlaku hingga akhir bulan!</p>
                                        <button class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                            Dapatkan Tiket
                                        </button>
                                    </div>
                                    <div class="hidden lg:flex justify-center">
                                        <div class="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <div class="text-6xl">🎵</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Sample Banner 2 --}}
                        <div class="banner-slide relative h-64 md:h-80 lg:h-96 bg-gradient-to-r from-green-600 to-teal-600 flex items-center">
                            <div class="absolute inset-0 bg-black/20"></div>
                            <div class="relative z-10 w-full px-8 md:px-12">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                    <div class="text-white space-y-4">
                                        <div class="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                                            <span class="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                                            Early Bird
                                        </div>
                                        <h3 class="text-2xl md:text-4xl font-bold">Tech Conference 2024</h3>
                                        <p class="text-lg opacity-90">Bergabunglah dengan para ahli teknologi terdepan. Daftar sekarang dan hemat 30%!</p>
                                        <button class="bg-white text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                            Daftar Sekarang
                                        </button>
                                    </div>
                                    <div class="hidden lg:flex justify-center">
                                        <div class="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <div class="text-6xl">💻</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Sample Banner 3 --}}
                        <div class="banner-slide relative h-64 md:h-80 lg:h-96 bg-gradient-to-r from-purple-600 to-pink-600 flex items-center">
                            <div class="absolute inset-0 bg-black/20"></div>
                            <div class="relative z-10 w-full px-8 md:px-12">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                    <div class="text-white space-y-4">
                                        <div class="inline-flex items-center px-3 py-1 bg-white/20 rounded-full text-sm font-medium">
                                            <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                            Limited Time
                                        </div>
                                        <h3 class="text-2xl md:text-4xl font-bold">Art Exhibition</h3>
                                        <p class="text-lg opacity-90">Pameran seni kontemporer dengan karya-karya eksklusif dari seniman ternama Indonesia.</p>
                                        <button class="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                            Lihat Koleksi
                                        </button>
                                    </div>
                                    <div class="hidden lg:flex justify-center">
                                        <div class="w-64 h-48 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center">
                                            <div class="text-6xl">🎨</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Navigation Arrows --}}
                    <button class="promo-banner-prev absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-300 group">
                        <svg class="w-6 h-6 text-white group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                        </svg>
                    </button>
                    <button class="promo-banner-next absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full flex items-center justify-center transition-all duration-300 group">
                        <svg class="w-6 h-6 text-white group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </button>

                    {{-- Dots Indicator --}}
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                        <button class="promo-dot active w-3 h-3 bg-white rounded-full transition-all duration-300" data-slide="0"></button>
                        <button class="promo-dot w-3 h-3 bg-white/50 rounded-full transition-all duration-300" data-slide="1"></button>
                        <button class="promo-dot w-3 h-3 bg-white/50 rounded-full transition-all duration-300" data-slide="2"></button>
                    </div>
                </div>

                {{-- Additional Info Cards --}}
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center text-white" data-aos="fade-up" data-aos-delay="400">
                        <div class="text-3xl mb-3">🎫</div>
                        <h4 class="text-lg font-semibold mb-2">Tiket Terjamin</h4>
                        <p class="text-sm opacity-80">Semua tiket dijamin asli dan terverifikasi</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center text-white" data-aos="fade-up" data-aos-delay="500">
                        <div class="text-3xl mb-3">💳</div>
                        <h4 class="text-lg font-semibold mb-2">Pembayaran Aman</h4>
                        <p class="text-sm opacity-80">Berbagai metode pembayaran yang aman</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center text-white" data-aos="fade-up" data-aos-delay="600">
                        <div class="text-3xl mb-3">📱</div>
                        <h4 class="text-lg font-semibold mb-2">E-Ticket Digital</h4>
                        <p class="text-sm opacity-80">Tiket digital yang mudah digunakan</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- Trending Categories --}}
    <x-trending-categories :categories="$trendingCategories" />
    {{-- All Categories Grid --}}
    <x-category-grid
        :categories="$categories"
        title="Jelajahi Kategori Event"
        subtitle="Temukan event sesuai minat dan passion Anda dari berbagai kategori menarik"
        :show-all="true" />

    {{-- Featured Events --}}
    <section class="py-16 bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium mb-4" data-aos="fade-up">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                    Event Pilihan Terbaik
                </div>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-aos-delay="100">
                    ⭐ Event Unggulan
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto text-lg" data-aos="fade-up" data-aos-delay="200">
                    Event pilihan terbaik yang direkomendasikan khusus untuk Anda
                </p>
            </div>
            <div id="featured-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
        </div>
    </section>

    {{-- Latest Events --}}
    <section class="py-16 bg-gradient-to-br from-pink-50 via-rose-50 to-red-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="inline-flex items-center px-4 py-2 bg-pink-100 text-pink-800 rounded-full text-sm font-medium mb-4" data-aos="fade-up">
                    <span class="w-2 h-2 bg-pink-500 rounded-full mr-2 animate-pulse"></span>
                    Baru Ditambahkan
                </div>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-aos-delay="100">
                    🆕 Event Terbaru
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto text-lg" data-aos="fade-up" data-aos-delay="200">
                    Event-event terbaru yang baru saja ditambahkan ke platform kami
                </p>
            </div>
            <div id="latest-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
        </div>
    </section>

    {{-- Popular Events --}}
    <section class="py-16 bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <div class="inline-flex items-center px-4 py-2 bg-purple-100 text-purple-800 rounded-full text-sm font-medium mb-4" data-aos="fade-up">
                    <span class="w-2 h-2 bg-purple-500 rounded-full mr-2 animate-pulse"></span>
                    Paling Diminati
                </div>
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up" data-aos-delay="100">
                    🔥 Event Populer
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto text-lg" data-aos="fade-up" data-aos-delay="200">
                    Event dengan penjualan tiket terbanyak dan rating tertinggi
                </p>
            </div>
            <div id="popular-tickets" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
        </div>
    </section>


</div>

{{-- Install PWA Prompt --}}
<div id="installPrompt" class="hidden fixed bottom-20 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:w-96 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl shadow-2xl p-6 border border-green-200/50">
    <div class="flex items-center mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-400 rounded-xl flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
            </svg>
        </div>
        <h4 class="text-lg font-bold text-gray-800">Install TiXara App</h4>
    </div>
    <p class="text-gray-600 mb-6">Install aplikasi TiXara untuk pengalaman yang lebih baik dan akses tiket offline.</p>
    <div class="flex justify-end space-x-4">
        <button onclick="this.parentElement.parentElement.classList.add('hidden')" class="px-4 py-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100 transition-colors">
            Nanti
        </button>
        <button id="installButton" class="px-6 py-2 bg-gradient-to-r from-green-400 to-emerald-400 text-white rounded-lg hover:from-green-500 hover:to-emerald-500 transition-all duration-300 shadow-lg hover:shadow-xl">
            Install
        </button>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Enhanced GoPay/OVO Style Animations and Floating Tickets */

/* Dynamic GoPay/OVO Gradient Background */
.gopay-gradient-bg {
    background: linear-gradient(135deg,
        #00AA5B 0%,     /* GoPay Green */
        #0066CC 15%,    /* OVO Blue */
        #4F46E5 30%,    /* Modern Purple */
        #06B6D4 45%,    /* Cyan */
        #10B981 60%,    /* Emerald */
        #F59E0B 75%,    /* Amber */
        #EF4444 90%,    /* Red */
        #00AA5B 100%    /* Back to GoPay Green */
    );
    background-size: 400% 400%;
    animation: gopayGradient 20s ease infinite;
    opacity: 0.15;
}

@keyframes gopayGradient {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 50%; }
}

/* Floating Particles Effect */
.particles-container {
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: floatParticle 15s linear infinite;
}

.particle-1 {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 12s;
}

.particle-2 {
    left: 30%;
    animation-delay: 3s;
    animation-duration: 18s;
}

.particle-3 {
    left: 50%;
    animation-delay: 6s;
    animation-duration: 15s;
}

.particle-4 {
    left: 70%;
    animation-delay: 9s;
    animation-duration: 20s;
}

.particle-5 {
    left: 90%;
    animation-delay: 12s;
    animation-duration: 14s;
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) scale(1);
    }
    100% {
        transform: translateY(-10vh) scale(0);
        opacity: 0;
    }
}

/* Floating Tickets Container */
.floating-tickets-container {
    overflow: hidden;
}

/* Individual Floating Tickets */
.floating-ticket {
    position: absolute;
    width: 120px;
    height: 80px;
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.3);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    animation: floatTicket 20s linear infinite;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.floating-ticket:hover {
    transform: scale(1.1) !important;
    opacity: 1;
    box-shadow: 0 12px 40px rgba(0,0,0,0.2);
}

.ticket-content {
    padding: 8px 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
}

.ticket-header {
    font-size: 20px;
    line-height: 1;
}

.ticket-title {
    font-size: 10px;
    font-weight: 600;
    color: #1f2937;
    margin: 2px 0;
}

.ticket-price {
    font-size: 9px;
    font-weight: 700;
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
}

/* Floating Animation */
@keyframes floatTicket {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.8;
    }
    90% {
        opacity: 0.8;
    }
    100% {
        transform: translateY(-120px) translateX(50px) rotate(360deg);
        opacity: 0;
    }
}

/* Individual Ticket Positions and Animations */
.ticket-1 {
    left: 10%;
    animation-delay: 0s;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(16, 185, 129, 0.7));
}

.ticket-2 {
    left: 25%;
    animation-delay: 3s;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(59, 130, 246, 0.7));
}

.ticket-3 {
    left: 40%;
    animation-delay: 6s;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(245, 158, 11, 0.7));
}

.ticket-4 {
    left: 55%;
    animation-delay: 9s;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.9), rgba(139, 92, 246, 0.7));
}

.ticket-5 {
    left: 70%;
    animation-delay: 12s;
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.9), rgba(236, 72, 153, 0.7));
}

.ticket-6 {
    left: 85%;
    animation-delay: 15s;
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.9), rgba(6, 182, 212, 0.7));
}

.ticket-7 {
    left: 15%;
    animation-delay: 18s;
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.9), rgba(168, 85, 247, 0.7));
}

.ticket-8 {
    left: 75%;
    animation-delay: 21s;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(34, 197, 94, 0.7));
}

/* Enhanced Ticket Content */
.ticket-category {
    font-size: 8px;
    font-weight: 500;
    color: #374151;
    background: rgba(255, 255, 255, 0.8);
    padding: 1px 4px;
    border-radius: 4px;
    margin-top: 2px;
}

/* TiXara Maskot 3D Ticket Animation Styling */
.tixara-mascot-container {
    position: absolute;
    inset: 0;
    pointer-events: none;
    perspective: 1000px;
}

.ticket-3d {
    position: absolute;
    width: 160px;
    height: 100px;
    cursor: pointer;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    animation: float3D 8s ease-in-out infinite;
}

.ticket-3d-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.ticket-3d:hover .ticket-3d-inner {
    transform: rotateY(180deg);
}

.ticket-3d-front,
.ticket-3d-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.ticket-3d-back {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.95) 0%,
        rgba(6, 182, 212, 0.95) 100%
    );
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: rotateY(180deg);
}

.ticket-3d-content {
    padding: 12px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
    position: relative;
}

.ticket-3d-header {
    font-size: 24px;
    line-height: 1;
    margin-bottom: 4px;
}

.ticket-3d-title {
    font-size: 12px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 2px;
}

.ticket-3d-price {
    font-size: 11px;
    font-weight: 600;
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
    margin-bottom: 2px;
}

.ticket-3d-date {
    font-size: 9px;
    color: #6b7280;
    font-weight: 500;
}

/* Back side styling */
.ticket-3d-back .ticket-3d-content {
    color: white;
}

.ticket-3d-qr {
    font-size: 28px;
    margin-bottom: 4px;
}

.ticket-3d-code {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
    letter-spacing: 1px;
}

.ticket-3d-venue {
    font-size: 10px;
    opacity: 0.9;
}

/* Individual 3D Ticket Positions and Animations */
.ticket-3d-1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.ticket-3d-2 {
    top: 25%;
    right: 15%;
    animation-delay: 1.5s;
}

.ticket-3d-3 {
    top: 45%;
    left: 20%;
    animation-delay: 3s;
}

.ticket-3d-4 {
    top: 35%;
    right: 25%;
    animation-delay: 4.5s;
}

.ticket-3d-5 {
    top: 65%;
    left: 15%;
    animation-delay: 6s;
}

.ticket-3d-6 {
    top: 55%;
    right: 10%;
    animation-delay: 7.5s;
}

/* 3D Float Animation */
@keyframes float3D {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg) rotateZ(0deg);
    }
    25% {
        transform: translateY(-10px) rotateX(5deg) rotateZ(2deg);
    }
    50% {
        transform: translateY(-5px) rotateX(-3deg) rotateZ(-1deg);
    }
    75% {
        transform: translateY(-15px) rotateX(2deg) rotateZ(3deg);
    }
}

/* Category-based colors for 3D tickets */
.ticket-3d[data-category="musik"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.95) 0%,
        rgba(16, 185, 129, 0.85) 100%
    );
}

.ticket-3d[data-category="teknologi"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(59, 130, 246, 0.85) 100%
    );
}

.ticket-3d[data-category="seni"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.95) 0%,
        rgba(139, 92, 246, 0.85) 100%
    );
}

.ticket-3d[data-category="olahraga"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(245, 158, 11, 0.95) 0%,
        rgba(245, 158, 11, 0.85) 100%
    );
}

.ticket-3d[data-category="kuliner"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(236, 72, 153, 0.95) 0%,
        rgba(236, 72, 153, 0.85) 100%
    );
}

.ticket-3d[data-category="fashion"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(168, 85, 247, 0.95) 0%,
        rgba(168, 85, 247, 0.85) 100%
    );
}

/* Mobile Responsive 3D Tickets */
@media (max-width: 768px) {
    .ticket-3d {
        width: 120px;
        height: 80px;
    }

    .ticket-3d-content {
        padding: 8px;
    }

    .ticket-3d-header {
        font-size: 18px;
    }

    .ticket-3d-title {
        font-size: 10px;
    }

    .ticket-3d-price {
        font-size: 9px;
        padding: 1px 4px;
    }

    .ticket-3d-date {
        font-size: 8px;
    }

    .ticket-3d-qr {
        font-size: 20px;
    }

    .ticket-3d-code {
        font-size: 11px;
    }

    .ticket-3d-venue {
        font-size: 8px;
    }
}

/* Main TiXara Maskot Ticket */
.tixara-mascot-ticket {
    position: absolute;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
    width: 320px;
    height: 480px;
    cursor: pointer;
    transition: all 0.4s ease;
    transform-style: preserve-3d;
    animation: tixaraMascotFloat 6s ease-in-out infinite;
    pointer-events: auto;
    z-index: 10;
}

.tixara-ticket-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.tixara-mascot-ticket:hover .tixara-ticket-inner {
    transform: rotateY(180deg) scale(1.05);
}

.tixara-ticket-front,
.tixara-ticket-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px);
    overflow: hidden;
}

.tixara-ticket-front {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(240, 253, 244, 0.9) 50%,
        rgba(255, 255, 255, 0.85) 100%);
}

.tixara-ticket-back {
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.95) 0%,
        rgba(5, 150, 105, 0.9) 50%,
        rgba(6, 120, 85, 0.85) 100%);
    transform: rotateY(180deg);
    color: white;
}

.tixara-ticket-content {
    padding: 24px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
}

/* TiXara Mascot Character */
.tixara-mascot-logo {
    margin-bottom: 20px;
}

.mascot-character {
    position: relative;
    width: 120px;
    height: 140px;
    margin: 0 auto;
    animation: mascotBounce 3s ease-in-out infinite;
}

.mascot-head {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 50%;
    position: relative;
    margin: 0 auto 10px;
    box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
}

.mascot-eyes {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.eye {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    position: relative;
    animation: eyeBlink 4s infinite;
}

.eye::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 8px;
    height: 8px;
    background: #1f2937;
    border-radius: 50%;
    animation: eyeMove 6s infinite;
}

.mascot-smile {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 15px;
    border: 3px solid white;
    border-top: none;
    border-radius: 0 0 30px 30px;
}

.mascot-body {
    width: 60px;
    height: 50px;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 15px;
    margin: 0 auto;
    position: relative;
    box-shadow: 0 6px 15px rgba(59, 130, 246, 0.3);
}

.mascot-arms {
    position: absolute;
    top: 10px;
    width: 100%;
    display: flex;
    justify-content: space-between;
}

.arm {
    width: 20px;
    height: 30px;
    background: linear-gradient(135deg, #22c55e, #16a34a);
    border-radius: 10px;
    animation: armWave 2s ease-in-out infinite alternate;
}

.left-arm {
    transform: rotate(-20deg);
    animation-delay: 0s;
}

.right-arm {
    transform: rotate(20deg);
    animation-delay: 0.5s;
}

.mascot-ticket-hold {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    animation: ticketFloat 2s ease-in-out infinite;
}

/* TiXara Branding */
.tixara-branding {
    margin-bottom: 20px;
}

.tixara-title {
    font-size: 36px;
    font-weight: 900;
    background: linear-gradient(135deg, #22c55e, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.tixara-subtitle {
    font-size: 14px;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Features Icons */
.tixara-features {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.feature-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(34, 197, 94, 0.1));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border: 2px solid rgba(59, 130, 246, 0.2);
    animation: featureIconPulse 3s ease-in-out infinite;
}

.feature-icon:nth-child(1) { animation-delay: 0s; }
.feature-icon:nth-child(2) { animation-delay: 0.5s; }
.feature-icon:nth-child(3) { animation-delay: 1s; }
.feature-icon:nth-child(4) { animation-delay: 1.5s; }

/* Stats */
.tixara-stats {
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: 900;
    color: #22c55e;
    margin-bottom: 4px;
    animation: counterUp 2s ease-out;
}

.stat-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
}

/* Back Side Styling */
.tixara-qr-section {
    margin-bottom: 30px;
}

.qr-code-graphic {
    width: 120px;
    height: 120px;
    background: white;
    border-radius: 16px;
    margin: 0 auto 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.qr-pattern {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 20px;
}

.qr-dot {
    width: 16px;
    height: 16px;
    background: #1f2937;
    border-radius: 3px;
    animation: qrDotPulse 2s ease-in-out infinite;
}

.qr-dot:nth-child(odd) { animation-delay: 0s; }
.qr-dot:nth-child(even) { animation-delay: 0.5s; }

.qr-label {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 600;
}

.platform-info {
    margin-bottom: 30px;
}

.platform-title {
    font-size: 28px;
    font-weight: 900;
    margin-bottom: 8px;
}

.platform-desc {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 600;
}

.security-features {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30px;
}

.security-item {
    text-align: center;
}

.security-icon {
    font-size: 24px;
    display: block;
    margin-bottom: 8px;
}

.security-text {
    font-size: 12px;
    font-weight: 600;
    opacity: 0.9;
}

.ticket-footer {
    text-align: center;
}

.footer-text {
    font-size: 14px;
    font-weight: 700;
    opacity: 0.8;
}

/* Mini Floating Tickets */
.mini-ticket {
    position: absolute;
    width: 80px;
    height: 50px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    animation: miniTicketFloat 8s ease-in-out infinite;
    pointer-events: auto;
}

.mini-content {
    padding: 8px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.mini-icon {
    font-size: 20px;
    margin-bottom: 4px;
}

.mini-text {
    font-size: 10px;
    font-weight: 700;
    color: #1f2937;
}

/* Mini Ticket Positions */
.mini-ticket-1 {
    top: 20%;
    left: 8%;
    animation-delay: 0s;
}

.mini-ticket-2 {
    top: 35%;
    left: 15%;
    animation-delay: 1s;
}

.mini-ticket-3 {
    top: 60%;
    left: 5%;
    animation-delay: 2s;
}

.mini-ticket-4 {
    top: 75%;
    left: 12%;
    animation-delay: 3s;
}

.mini-ticket-5 {
    top: 85%;
    left: 20%;
    animation-delay: 4s;
}

/* TiXara Animations */
@keyframes tixaraMascotFloat {
    0%, 100% {
        transform: translateY(-50%) rotateY(0deg) rotateX(0deg);
    }
    25% {
        transform: translateY(-55%) rotateY(2deg) rotateX(1deg);
    }
    50% {
        transform: translateY(-45%) rotateY(-1deg) rotateX(-1deg);
    }
    75% {
        transform: translateY(-52%) rotateY(1deg) rotateX(2deg);
    }
}

@keyframes mascotBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes eyeMove {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(2px, -1px); }
    50% { transform: translate(-1px, 1px); }
    75% { transform: translate(1px, -2px); }
}

@keyframes armWave {
    0% { transform: rotate(-20deg); }
    100% { transform: rotate(-10deg); }
}

.right-arm {
    animation-name: armWaveRight;
}

@keyframes armWaveRight {
    0% { transform: rotate(20deg); }
    100% { transform: rotate(10deg); }
}

@keyframes ticketFloat {
    0%, 100% { transform: translateX(-50%) translateY(0px); }
    50% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes featureIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes counterUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes qrDotPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes miniTicketFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-15px) rotate(2deg);
    }
    50% {
        transform: translateY(-8px) rotate(-1deg);
    }
    75% {
        transform: translateY(-12px) rotate(1deg);
    }
}

/* Hover Effects */
.mini-ticket:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

/* Mobile Responsive TiXara Maskot */
@media (max-width: 1024px) {
    .tixara-mascot-ticket {
        width: 280px;
        height: 420px;
        right: 2%;
    }

    .tixara-title {
        font-size: 32px;
    }

    .mascot-character {
        width: 100px;
        height: 120px;
    }

    .mascot-head {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 768px) {
    .tixara-mascot-ticket {
        width: 240px;
        height: 360px;
        right: 50%;
        transform: translateX(50%) translateY(-50%);
        top: 60%;
    }

    .tixara-title {
        font-size: 28px;
    }

    .mascot-character {
        width: 80px;
        height: 100px;
    }

    .mascot-head {
        width: 60px;
        height: 60px;
    }

    .mini-ticket {
        width: 60px;
        height: 40px;
    }

    .mini-icon {
        font-size: 16px;
    }

    .mini-text {
        font-size: 8px;
    }
}

@media (max-width: 480px) {
    .tixara-mascot-ticket {
        width: 200px;
        height: 300px;
    }

    .tixara-title {
        font-size: 24px;
    }

    .mascot-character {
        width: 60px;
        height: 80px;
    }

    .mascot-head {
        width: 50px;
        height: 50px;
    }

    .mini-ticket {
        width: 50px;
        height: 35px;
    }
}

/* Mobile Responsive Tickets */
@media (max-width: 768px) {
    .floating-ticket {
        width: 80px;
        height: 60px;
    }

    .ticket-content {
        padding: 4px 6px;
    }

    .ticket-header {
        font-size: 14px;
    }

    .ticket-title {
        font-size: 8px;
    }

    .ticket-price {
        font-size: 7px;
        padding: 1px 4px;
    }
}

/* Enhanced Banner Slides with GoPay/OVO Style */
.banner-slide {
    position: absolute;
    inset: 0;
    transition: opacity 1s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
    contain: layout style paint;
    background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
}

.banner-slide::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(16, 185, 129, 0.1) 0%,
        rgba(59, 130, 246, 0.1) 50%,
        rgba(139, 92, 246, 0.1) 100%
    );
    opacity: 0.6;
    z-index: 1;
}

/* GoPay/OVO Style Cards for Content */
.gopay-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 24px;
    transition: all 0.3s ease;
}

.gopay-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Enhanced Terminal with GoPay/OVO Style */
.terminal-header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.terminal-header .text-green-400 {
    color: #059669 !important;
}

.terminal-header .text-green-300 {
    color: #10b981 !important;
}

/* GoPay/OVO Style Buttons */
.gopay-btn {
    background: linear-gradient(135deg, #00AA5B, #059669);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 170, 91, 0.3);
    transition: all 0.3s ease;
}

.gopay-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 170, 91, 0.4);
}

.ovo-btn {
    background: linear-gradient(135deg, #0066CC, #0056b3);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
    transition: all 0.3s ease;
}

.ovo-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 102, 204, 0.4);
}

/* PWA and Mobile Optimized Styles */
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* PWA Viewport Optimizations */
.pwa-safe-area {
    padding-top: var(--safe-area-inset-top);
    padding-right: var(--safe-area-inset-right);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
}

/* Hero Banner Responsive Container */
.hero-container {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    position: relative;
    overflow: hidden;
}

/* Banner Carousel Optimizations */
.banner-carousel {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    position: relative;
    contain: layout style paint;
}

.banner-slide {
    position: absolute;
    inset: 0;
    transition: opacity 1s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
    contain: layout style paint;
}

.banner-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    image-rendering: optimizeQuality;
}

/* Responsive Image Aspect Ratios */
@media (max-width: 768px) {
    .banner-slide img {
        object-position: center top;
        aspect-ratio: 9/16;
    }
}

@media (min-width: 769px) {
    .banner-slide img {
        aspect-ratio: 16/9;
    }
}

/* Unix-style animations with performance optimizations */
@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes codeRain {
    0% { transform: translateY(-100vh); opacity: 1; }
    100% { transform: translateY(100vh); opacity: 0; }
}

.typing-text {
    overflow: hidden;
    white-space: nowrap;
    animation: typing 3s steps(40, end);
    will-change: width;
}

.cursor-blink {
    animation: blink 1s infinite;
    will-change: opacity;
}

.code-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    contain: layout style paint;
}

.code-rain::before {
    content: '01001010 01100001 01110110 01100001 01010011 01100011 01110010 01101001 01110000 01110100';
    position: absolute;
    top: -100px;
    left: 10%;
    color: rgba(0, 255, 0, 0.3);
    font-family: 'Courier New', monospace;
    font-size: 12px;
    animation: codeRain 10s linear infinite;
    will-change: transform;
}

.code-rain::after {
    content: 'function() { return "TiXara"; } while(true) { events++; }';
    position: absolute;
    top: -100px;
    right: 20%;
    color: rgba(0, 255, 0, 0.2);
    font-family: 'Courier New', monospace;
    font-size: 10px;
    animation: codeRain 15s linear infinite 2s;
    will-change: transform;
}

/* Responsive Typography Scale */
.hero-title {
    font-size: clamp(1.5rem, 8vw, 4rem);
    line-height: 1.1;
}

.hero-subtitle {
    font-size: clamp(0.875rem, 3vw, 1.125rem);
}

.hero-description {
    font-size: clamp(0.875rem, 2.5vw, 1rem);
}

/* Mobile-First Responsive Breakpoints */
/* Extra Small Devices (320px - 480px) */
@media (max-width: 480px) {
    .hero-container {
        min-height: 100vh;
        min-height: 100dvh;
    }

    .banner-carousel {
        height: 100vh;
        height: 100dvh;
    }

    .typing-text {
        font-size: 1.25rem;
        line-height: 1.2;
    }

    .font-mono {
        font-size: 0.75rem;
    }

    .code-rain::before,
    .code-rain::after {
        font-size: 6px;
        display: none; /* Hide on very small screens for performance */
    }

    /* Terminal header adjustments */
    .terminal-header {
        padding: 0.5rem;
        margin: 0.5rem;
    }

    .terminal-dots {
        width: 2px;
        height: 2px;
    }

    /* Navigation buttons */
    .banner-nav-btn {
        width: 2.5rem;
        height: 2.5rem;
        left: 0.5rem;
        right: 0.5rem;
    }

    /* Dots indicator */
    .banner-dots {
        bottom: 1rem;
        padding: 0.5rem 1rem;
    }

    .banner-dot {
        width: 2px;
        height: 2px;
    }
}

/* Small Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .typing-text {
        font-size: 1.5rem;
    }

    .font-mono {
        font-size: 0.875rem;
    }

    .code-rain::before,
    .code-rain::after {
        font-size: 8px;
    }

    .banner-nav-btn {
        width: 3rem;
        height: 3rem;
    }
}

/* Medium Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .typing-text {
        font-size: 2rem;
    }

    .font-mono {
        font-size: 1rem;
    }
}

/* Large Devices (1025px+) */
@media (min-width: 1025px) {
    .typing-text {
        font-size: 2.5rem;
    }
}

/* PWA Specific Styles */
@media (display-mode: standalone) {
    .pwa-only {
        display: block !important;
    }

    .browser-only {
        display: none !important;
    }

    /* Adjust for PWA status bar */
    .hero-container {
        padding-top: var(--safe-area-inset-top);
    }

    /* PWA navigation adjustments */
    .banner-nav-btn {
        top: calc(50% + var(--safe-area-inset-top) / 2);
    }
}

/* Touch Optimizations */
.touch-target {
    min-width: 44px;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Prevent text selection on touch */
.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
}

/* Terminal glow effect with performance optimization */
.terminal-glow {
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
    will-change: box-shadow;
}

/* Smooth transitions optimized for mobile */
.transition-optimized {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
}

/* Custom scrollbar for terminal elements */
.terminal-scroll::-webkit-scrollbar {
    width: 8px;
}

.terminal-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.terminal-scroll::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 0, 0.5);
    border-radius: 4px;
}

.terminal-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 0, 0.7);
}

/* Carousel slide transitions */
.carousel-slide-enter {
    opacity: 0;
    transform: translateX(100%);
}

.carousel-slide-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 1s ease, transform 1s ease;
}

.carousel-slide-exit {
    opacity: 1;
    transform: translateX(0);
}

.carousel-slide-exit-active {
    opacity: 0;
    transform: translateX(-100%);
    transition: opacity 1s ease, transform 1s ease;
}

/* Performance optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .typing-text {
        animation: none;
    }

    .cursor-blink {
        animation: none;
        opacity: 1;
    }

    .code-rain::before,
    .code-rain::after {
        animation: none;
        display: none;
    }

    .transition-optimized {
        transition: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .terminal-glow {
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.8);
    }

    .bg-gray-900\/80 {
        background-color: rgba(0, 0, 0, 0.95) !important;
    }

    .border-green-500\/30 {
        border-color: rgba(34, 197, 94, 0.8) !important;
    }
}

/* Dark mode optimizations */
@media (prefers-color-scheme: dark) {
    .hero-container {
        background-color: #000;
    }
}

/* Landscape orientation adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-container {
        min-height: 100vh;
        min-height: 100dvh;
    }

    .hero-title {
        font-size: clamp(1rem, 6vw, 2rem);
    }

    .terminal-header {
        padding: 0.25rem 0.5rem;
        margin: 0.25rem 0.5rem;
    }

    .banner-dots {
        bottom: 0.5rem;
    }
}

/* Promotional Banner Carousel Styles */
.banner-slides-container {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.banner-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    transform: translateX(100%);
}

.banner-slide.active {
    opacity: 1;
    transform: translateX(0);
}

.banner-slide.prev {
    transform: translateX(-100%);
}

.banner-slide.next {
    transform: translateX(100%);
}

.promo-dot {
    cursor: pointer;
    transition: all 0.3s ease;
}

.promo-dot.active {
    background-color: white !important;
    transform: scale(1.2);
}

.promo-dot:hover {
    background-color: rgba(255, 255, 255, 0.8) !important;
    transform: scale(1.1);
}

/* Banner Animation Effects */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.banner-slide.slide-in-right {
    animation: slideInFromRight 0.8s ease-out;
}

.banner-slide.slide-in-left {
    animation: slideInFromLeft 0.8s ease-out;
}

.banner-slide .space-y-4 > * {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.banner-slide .space-y-4 > *:nth-child(1) { animation-delay: 0.1s; }
.banner-slide .space-y-4 > *:nth-child(2) { animation-delay: 0.2s; }
.banner-slide .space-y-4 > *:nth-child(3) { animation-delay: 0.3s; }
.banner-slide .space-y-4 > *:nth-child(4) { animation-delay: 0.4s; }

/* Mobile Responsive for Promotional Banner */
@media (max-width: 768px) {
    .banner-slide {
        height: 16rem; /* h-64 */
    }

    .banner-slide h3 {
        font-size: 1.5rem; /* text-2xl */
    }

    .banner-slide p {
        font-size: 0.875rem; /* text-sm */
    }

    .promo-banner-prev,
    .promo-banner-next {
        width: 2.5rem;
        height: 2.5rem;
    }

    .promo-banner-prev svg,
    .promo-banner-next svg {
        width: 1rem;
        height: 1rem;
    }
}

/* Print styles */
@media print {
    .code-rain,
    .banner-nav-btn,
    .banner-dots,
    .promo-banner-prev,
    .promo-banner-next {
        display: none !important;
    }

    .hero-container {
        min-height: auto;
        page-break-inside: avoid;
    }
}
</style>
@endpush

@push('scripts')
<script>
// PWA and Mobile Optimizations
let isStandalone = false;
let isMobile = false;
let touchStartX = 0;
let touchStartY = 0;
let touchEndX = 0;
let touchEndY = 0;

// Detect PWA mode and mobile device
function detectEnvironment() {
    isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                   window.navigator.standalone === true;
    isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;

    // Add classes for PWA and mobile detection
    if (isStandalone) {
        document.body.classList.add('pwa-mode');
    }
    if (isMobile) {
        document.body.classList.add('mobile-device');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Detect environment first
    detectEnvironment();

    // Initialize Unix-style effects
    initUnixEffects();

    // Initialize banner carousel if exists
    if (document.getElementById('bannerCarousel')) {
        initBannerCarousel();
    }

    // Initialize counter animations
    initCounterAnimations();

    // Initialize search functionality
    initSearchFunctionality();

    // Initialize PWA optimizations
    initPWAOptimizations();

    // Initialize mobile touch optimizations
    initMobileTouchOptimizations();

    // Initialize floating tickets animation
    initFloatingTickets();

    // Initialize TiXara Maskot 3D animation
    initTiXaraMascot();

    // Initialize promotional banner carousel
    initPromoBannerCarousel();
});

// Promotional Banner Carousel Functionality
function initPromoBannerCarousel() {
    const carousel = document.getElementById('promoBannerCarousel');
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.banner-slide');
    const dots = carousel.querySelectorAll('.promo-dot');
    const prevBtn = carousel.querySelector('.promo-banner-prev');
    const nextBtn = carousel.querySelector('.promo-banner-next');

    let currentSlide = 0;
    let isTransitioning = false;
    let autoSlideInterval;

    // Initialize first slide
    if (slides.length > 0) {
        slides[0].classList.add('active');
        if (dots.length > 0) {
            dots[0].classList.add('active');
        }
    }

    // Auto slide functionality
    function startAutoSlide() {
        autoSlideInterval = setInterval(() => {
            if (!isTransitioning) {
                nextSlide();
            }
        }, 5000); // Change slide every 5 seconds
    }

    function stopAutoSlide() {
        if (autoSlideInterval) {
            clearInterval(autoSlideInterval);
        }
    }

    // Show specific slide
    function showSlide(index, direction = 'next') {
        if (isTransitioning || index === currentSlide) return;

        isTransitioning = true;

        // Remove active class from current slide and dot
        slides[currentSlide].classList.remove('active');
        if (dots[currentSlide]) {
            dots[currentSlide].classList.remove('active');
        }

        // Add animation classes
        const currentSlideEl = slides[currentSlide];
        const nextSlideEl = slides[index];

        if (direction === 'next') {
            currentSlideEl.classList.add('slide-out-left');
            nextSlideEl.classList.add('slide-in-right');
        } else {
            currentSlideEl.classList.add('slide-out-right');
            nextSlideEl.classList.add('slide-in-left');
        }

        // Update current slide
        currentSlide = index;

        // Add active class to new slide and dot
        slides[currentSlide].classList.add('active');
        if (dots[currentSlide]) {
            dots[currentSlide].classList.add('active');
        }

        // Clean up animation classes after transition
        setTimeout(() => {
            slides.forEach(slide => {
                slide.classList.remove('slide-out-left', 'slide-out-right', 'slide-in-left', 'slide-in-right');
            });
            isTransitioning = false;
        }, 800);
    }

    // Next slide
    function nextSlide() {
        const nextIndex = (currentSlide + 1) % slides.length;
        showSlide(nextIndex, 'next');
    }

    // Previous slide
    function prevSlide() {
        const prevIndex = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prevIndex, 'prev');
    }

    // Event listeners
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });
    }

    // Dot navigation
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopAutoSlide();
            const direction = index > currentSlide ? 'next' : 'prev';
            showSlide(index, direction);
            startAutoSlide();
        });
    });

    // Pause on hover
    carousel.addEventListener('mouseenter', stopAutoSlide);
    carousel.addEventListener('mouseleave', startAutoSlide);

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    carousel.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        stopAutoSlide();
    });

    carousel.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
        startAutoSlide();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe left - next slide
                nextSlide();
            } else {
                // Swipe right - previous slide
                prevSlide();
            }
        }
    }

    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (carousel.matches(':hover')) {
            if (e.key === 'ArrowLeft') {
                stopAutoSlide();
                prevSlide();
                startAutoSlide();
            } else if (e.key === 'ArrowRight') {
                stopAutoSlide();
                nextSlide();
                startAutoSlide();
            }
        }
    });

    // Start auto slide
    startAutoSlide();

    // Pause when page is not visible
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopAutoSlide();
        } else {
            startAutoSlide();
        }
    });
}

// Unix-style effects
function initUnixEffects() {
    // Typing effect for terminal text
    const typingElements = document.querySelectorAll('.typing-text');
    typingElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        let i = 0;

        const typeWriter = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            }
        };

        // Start typing after a delay
        setTimeout(typeWriter, 1000);
    });

    // Terminal command simulation
    const terminalCommands = document.querySelectorAll('[data-terminal-command]');
    terminalCommands.forEach(cmd => {
        cmd.addEventListener('click', function() {
            const command = this.dataset.terminalCommand;
            simulateTerminalCommand(command);
        });
    });

    // Add matrix rain effect
    createMatrixRain();
}

// Matrix rain effect
function createMatrixRain() {
    const codeRainElements = document.querySelectorAll('.code-rain');
    codeRainElements.forEach(element => {
        // Create multiple falling code streams
        for (let i = 0; i < 5; i++) {
            const stream = document.createElement('div');
            stream.style.position = 'absolute';
            stream.style.left = Math.random() * 100 + '%';
            stream.style.color = 'rgba(0, 255, 0, 0.3)';
            stream.style.fontFamily = 'monospace';
            stream.style.fontSize = '12px';
            stream.style.animation = `codeRain ${10 + Math.random() * 10}s linear infinite ${Math.random() * 5}s`;
            stream.textContent = generateRandomCode();
            element.appendChild(stream);
        }
    });
}

// Generate random code for matrix effect
function generateRandomCode() {
    const codes = [
        'function() { return event; }',
        'while(true) { tickets++; }',
        'if (user.authenticated) { ... }',
        'const events = await fetch();',
        'console.log("TiXara");',
        '01001010 01100001',
        'SELECT * FROM events;',
        'npm install tixara',
        'git commit -m "feat"'
    ];
    return codes[Math.floor(Math.random() * codes.length)];
}

// Enhanced Floating Tickets Animation System
function initFloatingTickets() {
    const ticketsContainer = document.querySelector('.floating-tickets-container');
    if (!ticketsContainer) return;

    const tickets = ticketsContainer.querySelectorAll('.floating-ticket');

    // Comprehensive ticket data with categories
    const ticketTypes = [
        { emoji: '🎵', title: 'Konser', price: 'Rp 150K', category: 'Musik', color: 'rgba(16, 185, 129, 0.9)' },
        { emoji: '🎭', title: 'Theater', price: 'Rp 75K', category: 'Seni', color: 'rgba(59, 130, 246, 0.9)' },
        { emoji: '🏃', title: 'Marathon', price: 'Rp 200K', category: 'Olahraga', color: 'rgba(245, 158, 11, 0.9)' },
        { emoji: '🎨', title: 'Workshop', price: 'Rp 100K', category: 'Edukasi', color: 'rgba(139, 92, 246, 0.9)' },
        { emoji: '🍔', title: 'Food Fest', price: 'Rp 50K', category: 'Kuliner', color: 'rgba(236, 72, 153, 0.9)' },
        { emoji: '💼', title: 'Seminar', price: 'Rp 125K', category: 'Bisnis', color: 'rgba(6, 182, 212, 0.9)' },
        { emoji: '💻', title: 'Tech Talk', price: 'Rp 80K', category: 'Teknologi', color: 'rgba(168, 85, 247, 0.9)' },
        { emoji: '👗', title: 'Fashion Show', price: 'Rp 300K', category: 'Fashion', color: 'rgba(34, 197, 94, 0.9)' },
        { emoji: '🎪', title: 'Festival', price: 'Rp 180K', category: 'Hiburan', color: 'rgba(249, 115, 22, 0.9)' },
        { emoji: '🎬', title: 'Cinema', price: 'Rp 45K', category: 'Film', color: 'rgba(20, 184, 166, 0.9)' },
        { emoji: '🏀', title: 'Sports', price: 'Rp 90K', category: 'Olahraga', color: 'rgba(239, 68, 68, 0.9)' },
        { emoji: '📚', title: 'Book Fair', price: 'Rp 25K', category: 'Literasi', color: 'rgba(99, 102, 241, 0.9)' },
        { emoji: '🎤', title: 'Stand Up', price: 'Rp 120K', category: 'Komedi', color: 'rgba(217, 119, 6, 0.9)' },
        { emoji: '🧘', title: 'Yoga Class', price: 'Rp 60K', category: 'Kesehatan', color: 'rgba(34, 197, 94, 0.9)' },
        { emoji: '🎯', title: 'Gaming', price: 'Rp 85K', category: 'E-Sports', color: 'rgba(147, 51, 234, 0.9)' }
    ];

    // Enhanced ticket creation with better animations
    function createFloatingTicket() {
        const ticket = document.createElement('div');
        const ticketData = ticketTypes[Math.floor(Math.random() * ticketTypes.length)];

        ticket.className = 'floating-ticket';
        ticket.dataset.type = ticketData.category.toLowerCase();
        ticket.style.left = Math.random() * 85 + 5 + '%'; // Keep tickets within viewport
        ticket.style.background = `linear-gradient(135deg, ${ticketData.color}, ${ticketData.color.replace('0.9', '0.7')})`;
        ticket.style.animationDuration = (18 + Math.random() * 12) + 's';
        ticket.style.animationDelay = Math.random() * 3 + 's';

        // Add random rotation and scale variations
        const randomRotation = (Math.random() - 0.5) * 30; // -15 to 15 degrees
        const randomScale = 0.8 + Math.random() * 0.4; // 0.8 to 1.2
        ticket.style.transform = `rotate(${randomRotation}deg) scale(${randomScale})`;

        ticket.innerHTML = `
            <div class="ticket-content">
                <div class="ticket-header">${ticketData.emoji}</div>
                <div class="ticket-title">${ticketData.title}</div>
                <div class="ticket-price">${ticketData.price}</div>
                <div class="ticket-category">${ticketData.category}</div>
            </div>
        `;

        // Enhanced hover effects
        ticket.addEventListener('mouseenter', function() {
            this.style.animationPlayState = 'paused';
            this.style.transform += ' scale(1.15)';
            this.style.zIndex = '1000';
        });

        ticket.addEventListener('mouseleave', function() {
            this.style.animationPlayState = 'running';
            this.style.transform = this.style.transform.replace(' scale(1.15)', '');
            this.style.zIndex = '';
        });

        // Enhanced click effects with sound simulation
        ticket.addEventListener('click', function(e) {
            e.preventDefault();

            // Create multiple ripple effects
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = `rgba(255, 255, 255, ${0.8 - i * 0.2})`;
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = `ripple ${0.8 + i * 0.2}s ease-out`;
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = `${20 + i * 10}px`;
                    ripple.style.height = `${20 + i * 10}px`;
                    ripple.style.marginLeft = `-${10 + i * 5}px`;
                    ripple.style.marginTop = `-${10 + i * 5}px`;
                    ripple.style.pointerEvents = 'none';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        if (ripple.parentNode) {
                            ripple.remove();
                        }
                    }, 1000);
                }, i * 100);
            }

            // Bounce effect
            this.style.transform += ' scale(1.3)';
            setTimeout(() => {
                this.style.transform = this.style.transform.replace(' scale(1.3)', '');
            }, 200);
        });

        return ticket;
    }

    // Smart ticket generation based on time and user interaction
    let ticketGenerationInterval = 4000; // Start with 4 seconds
    let isUserActive = true;
    let lastInteraction = Date.now();

    function addNewTicket() {
        if (!isUserActive) return;

        const newTicket = createFloatingTicket();
        ticketsContainer.appendChild(newTicket);

        // Remove ticket after animation completes
        setTimeout(() => {
            if (newTicket.parentNode) {
                newTicket.remove();
            }
        }, 30000);
    }

    // Adaptive ticket generation based on user activity
    function startTicketGeneration() {
        const generateTickets = () => {
            addNewTicket();

            // Adjust generation speed based on user activity
            const timeSinceLastInteraction = Date.now() - lastInteraction;
            if (timeSinceLastInteraction > 30000) { // 30 seconds of inactivity
                ticketGenerationInterval = Math.min(ticketGenerationInterval * 1.2, 8000);
            } else {
                ticketGenerationInterval = Math.max(ticketGenerationInterval * 0.9, 2000);
            }

            setTimeout(generateTickets, ticketGenerationInterval);
        };

        generateTickets();
    }

    // Track user activity
    ['mouseenter', 'click', 'scroll', 'touchstart'].forEach(event => {
        document.addEventListener(event, () => {
            lastInteraction = Date.now();
            isUserActive = true;
        });
    });

    // Pause generation when tab is not visible
    document.addEventListener('visibilitychange', () => {
        isUserActive = !document.hidden;
    });

    // Enhanced interaction for existing tickets
    tickets.forEach((ticket, index) => {
        // Add staggered animation delays
        const baseDelay = index * 2;
        ticket.style.animationDelay = baseDelay + 's';

        // Add category-based styling
        const ticketType = ticket.dataset.type;
        if (ticketType) {
            ticket.classList.add(`ticket-${ticketType}`);
        }

        // Enhanced touch/click interactions
        ticket.addEventListener('click', function(e) {
            e.preventDefault();

            // Create enhanced ripple effect
            const ripple = document.createElement('div');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.8)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.8s ease-out';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.width = '30px';
            ripple.style.height = '30px';
            ripple.style.marginLeft = '-15px';
            ripple.style.marginTop = '-15px';
            ripple.style.pointerEvents = 'none';

            this.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 800);
        });

        // Add parallax effect for desktop
        if (!isMobile && window.innerWidth > 768) {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const speed = parseFloat(ticket.dataset.speed) || 1;
                const parallaxOffset = scrolled * speed * 0.05;
                ticket.style.transform += ` translateY(${parallaxOffset}px)`;
            });
        }
    });

    // Performance optimization with Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const tickets = entry.target.querySelectorAll('.floating-ticket');
            tickets.forEach(ticket => {
                if (entry.isIntersecting) {
                    ticket.style.animationPlayState = 'running';
                } else {
                    ticket.style.animationPlayState = 'paused';
                }
            });
        });
    }, {
        threshold: 0.1,
        rootMargin: '50px'
    });

    observer.observe(ticketsContainer);

    // Start the enhanced ticket generation system
    startTicketGeneration();

    // Add floating particles animation
    initFloatingParticles();
}

// Enhanced floating particles system
function initFloatingParticles() {
    const particlesContainer = document.querySelector('.particles-container');
    if (!particlesContainer) return;

    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (10 + Math.random() * 15) + 's';
        particle.style.animationDelay = Math.random() * 5 + 's';

        // Random particle colors
        const colors = [
            'rgba(255, 255, 255, 0.6)',
            'rgba(16, 185, 129, 0.4)',
            'rgba(59, 130, 246, 0.4)',
            'rgba(245, 158, 11, 0.4)',
            'rgba(139, 92, 246, 0.4)'
        ];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];

        particlesContainer.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.remove();
            }
        }, 25000);
    }

    // Generate particles periodically
    setInterval(createParticle, 3000);
}

// Add ripple animation CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// Initialize 3D Interactive Tickets System
function init3DTickets() {
    const tickets3DContainer = document.querySelector('.tickets-3d-container');
    if (!tickets3DContainer) return;

    const tickets3D = tickets3DContainer.querySelectorAll('.ticket-3d');
    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;
    let containerRect = tickets3DContainer.getBoundingClientRect();

    // Update container rect on resize
    window.addEventListener('resize', () => {
        containerRect = tickets3DContainer.getBoundingClientRect();
    });

    // Mouse movement handler for 3D effects
    function handleMouseMove(e) {
        const clientX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
        const clientY = e.clientY || (e.touches && e.touches[0] ? e.touches[0].clientY : 0);

        mouseX = clientX - containerRect.left;
        mouseY = clientY - containerRect.top;

        const centerX = containerRect.width / 2;
        const centerY = containerRect.height / 2;

        const rotateX = (mouseY - centerY) / centerY * 15; // Max 15 degrees
        const rotateY = (mouseX - centerX) / centerX * 15; // Max 15 degrees

        tickets3D.forEach((ticket, index) => {
            const ticketRect = ticket.getBoundingClientRect();
            const ticketCenterX = ticketRect.left + ticketRect.width / 2 - containerRect.left;
            const ticketCenterY = ticketRect.top + ticketRect.height / 2 - containerRect.top;

            // Calculate distance from mouse to ticket center
            const distanceX = mouseX - ticketCenterX;
            const distanceY = mouseY - ticketCenterY;
            const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

            // Apply stronger effect to closer tickets
            const maxDistance = 300;
            const influence = Math.max(0, 1 - distance / maxDistance);

            const ticketRotateX = -rotateX * influence * 0.5;
            const ticketRotateY = rotateY * influence * 0.5;
            const ticketTranslateZ = influence * 20;

            // Apply transform with smooth transition
            ticket.style.transform = `
                translateZ(${ticketTranslateZ}px)
                rotateX(${ticketRotateX}deg)
                rotateY(${ticketRotateY}deg)
                scale(${1 + influence * 0.1})
            `;

            // Add glow effect based on proximity
            const glowIntensity = influence * 0.3;
            ticket.style.filter = `drop-shadow(0 0 ${glowIntensity * 20}px rgba(16, 185, 129, ${glowIntensity}))`;
        });
    }

    // Reset tickets to original position
    function resetTickets() {
        tickets3D.forEach(ticket => {
            ticket.style.transform = '';
            ticket.style.filter = '';
        });
    }

    // Enhanced click/touch interactions for 3D tickets
    tickets3D.forEach((ticket, index) => {
        let isFlipped = false;
        let touchStartTime = 0;

        // Mouse events
        ticket.addEventListener('mouseenter', function() {
            this.style.zIndex = '1000';
            this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
        });

        ticket.addEventListener('mouseleave', function() {
            this.style.zIndex = '';
            this.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        });

        // Click/Touch to flip
        ticket.addEventListener('click', function(e) {
            e.preventDefault();
            flipTicket(this);
        });

        // Touch events for mobile
        ticket.addEventListener('touchstart', function(e) {
            e.preventDefault();
            touchStartTime = Date.now();
            this.style.zIndex = '1000';

            // Add touch feedback
            this.style.transform += ' scale(0.95)';
        });

        ticket.addEventListener('touchend', function(e) {
            e.preventDefault();
            const touchDuration = Date.now() - touchStartTime;

            // Reset scale
            this.style.transform = this.style.transform.replace(' scale(0.95)', '');

            // Flip if touch was quick (tap)
            if (touchDuration < 300) {
                flipTicket(this);
            }

            setTimeout(() => {
                this.style.zIndex = '';
            }, 300);
        });

        // Double-click for special effect
        ticket.addEventListener('dblclick', function(e) {
            e.preventDefault();
            createTicketExplosion(this);
        });

        function flipTicket(ticketElement) {
            const inner = ticketElement.querySelector('.ticket-3d-inner');
            isFlipped = !isFlipped;

            if (isFlipped) {
                inner.style.transform = 'rotateY(180deg)';
                ticketElement.style.transform += ' scale(1.1)';
            } else {
                inner.style.transform = 'rotateY(0deg)';
                ticketElement.style.transform = ticketElement.style.transform.replace(' scale(1.1)', '');
            }

            // Add sound effect simulation
            createSoundEffect();
        }

        function createTicketExplosion(ticketElement) {
            // Create multiple particles
            for (let i = 0; i < 12; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = '6px';
                particle.style.height = '6px';
                particle.style.background = `hsl(${Math.random() * 360}, 70%, 60%)`;
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '9999';

                const rect = ticketElement.getBoundingClientRect();
                particle.style.left = rect.left + rect.width / 2 + 'px';
                particle.style.top = rect.top + rect.height / 2 + 'px';

                document.body.appendChild(particle);

                // Animate particle
                const angle = (i / 12) * Math.PI * 2;
                const velocity = 100 + Math.random() * 50;
                const vx = Math.cos(angle) * velocity;
                const vy = Math.sin(angle) * velocity;

                particle.animate([
                    {
                        transform: 'translate(0, 0) scale(1)',
                        opacity: 1
                    },
                    {
                        transform: `translate(${vx}px, ${vy}px) scale(0)`,
                        opacity: 0
                    }
                ], {
                    duration: 800,
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
                }).onfinish = () => {
                    particle.remove();
                };
            }

            // Shake the ticket
            ticketElement.animate([
                { transform: 'translateX(0)' },
                { transform: 'translateX(-5px)' },
                { transform: 'translateX(5px)' },
                { transform: 'translateX(-3px)' },
                { transform: 'translateX(3px)' },
                { transform: 'translateX(0)' }
            ], {
                duration: 300,
                easing: 'ease-in-out'
            });
        }
    });

    // Mouse/Touch movement tracking
    tickets3DContainer.addEventListener('mousemove', handleMouseMove);
    tickets3DContainer.addEventListener('touchmove', function(e) {
        e.preventDefault();
        handleMouseMove(e);
    });

    // Reset on mouse leave
    tickets3DContainer.addEventListener('mouseleave', resetTickets);
    tickets3DContainer.addEventListener('touchend', function(e) {
        if (!e.touches.length) {
            resetTickets();
        }
    });

    // Gyroscope support for mobile devices
    if (window.DeviceOrientationEvent) {
        window.addEventListener('deviceorientation', function(e) {
            if (Math.abs(e.beta) < 90 && Math.abs(e.gamma) < 90) {
                const rotateX = e.beta * 0.3; // Tilt forward/backward
                const rotateY = e.gamma * 0.3; // Tilt left/right

                tickets3D.forEach(ticket => {
                    ticket.style.transform = `
                        rotateX(${rotateX}deg)
                        rotateY(${rotateY}deg)
                    `;
                });
            }
        });
    }

    // Auto-flip tickets periodically
    function autoFlipTickets() {
        tickets3D.forEach((ticket, index) => {
            setTimeout(() => {
                const inner = ticket.querySelector('.ticket-3d-inner');
                const isCurrentlyFlipped = inner.style.transform.includes('180deg');

                if (!isCurrentlyFlipped && Math.random() > 0.7) {
                    inner.style.transform = 'rotateY(180deg)';
                    setTimeout(() => {
                        inner.style.transform = 'rotateY(0deg)';
                    }, 2000);
                }
            }, index * 1000 + Math.random() * 3000);
        });
    }

    // Start auto-flip every 10 seconds
    setInterval(autoFlipTickets, 10000);

    // Performance optimization: pause 3D effects when not visible
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                tickets3DContainer.style.animationPlayState = 'running';
            } else {
                tickets3DContainer.style.animationPlayState = 'paused';
                resetTickets();
            }
        });
    }, {
        threshold: 0.1
    });

    observer.observe(tickets3DContainer);
}

// TiXara Maskot 3D Animation Functionality
function initTiXaraMascot() {
    const container = document.getElementById('tixaraMascotContainer');
    if (!container) return;

    const mascotTicket = container.querySelector('.tixara-mascot-ticket');
    const miniTickets = container.querySelectorAll('.mini-ticket');

    // Add click handler for main mascot ticket
    if (mascotTicket) {
        mascotTicket.addEventListener('click', function() {
            // Add special click animation
            this.style.transform = 'translateY(-50%) scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 200);

            // Optional: Navigate to events page or show modal
            console.log('TiXara Maskot clicked!');
        });
    }

    // Add click handlers for mini tickets
    miniTickets.forEach((ticket, index) => {
        ticket.addEventListener('click', function() {
            const categories = ['musik', 'seni', 'teknologi', 'olahraga', 'kuliner'];
            const category = categories[index];

            // Add click animation
            this.style.transform = 'scale(1.2) rotate(10deg)';
            setTimeout(() => {
                this.style.transform = '';
            }, 300);

            // Optional: Filter events by category
            console.log(`Mini ticket clicked: ${category}`);
        });

        // Add hover effects
        ticket.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });

        ticket.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });

    // Add mouse movement parallax effect for mascot
    if (mascotTicket) {
        container.addEventListener('mousemove', function(e) {
            const rect = container.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const mouseX = e.clientX - centerX;
            const mouseY = e.clientY - centerY;

            const intensity = 0.02;
            const rotateX = (mouseY * intensity);
            const rotateY = -(mouseX * intensity);

            mascotTicket.style.transform = `translateY(-50%) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;

            // Add parallax to mini tickets
            miniTickets.forEach((ticket, index) => {
                const miniIntensity = (index + 1) * 0.01;
                const miniRotateX = (mouseY * miniIntensity);
                const miniRotateY = -(mouseX * miniIntensity);

                ticket.style.transform = `rotateX(${miniRotateX}deg) rotateY(${miniRotateY}deg)`;
            });
        });

        // Reset on mouse leave
        container.addEventListener('mouseleave', function() {
            mascotTicket.style.transform = 'translateY(-50%)';
            miniTickets.forEach(ticket => {
                ticket.style.transform = '';
            });
        });
    }

    // Add touch support for mobile
    if ('ontouchstart' in window) {
        container.addEventListener('touchstart', function(e) {
            const touch = e.touches[0];
            const rect = container.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const touchX = touch.clientX - centerX;
            const touchY = touch.clientY - centerY;

            const intensity = 0.03;
            const rotateX = (touchY * intensity);
            const rotateY = -(touchX * intensity);

            if (mascotTicket) {
                mascotTicket.style.transform = `translateY(-50%) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            }
        });

        container.addEventListener('touchend', function() {
            if (mascotTicket) {
                mascotTicket.style.transform = 'translateY(-50%)';
            }
            miniTickets.forEach(ticket => {
                ticket.style.transform = '';
            });
        });
    }
}

// Sound effect simulation
function createSoundEffect() {
    // Create audio context for sound simulation
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        try {
            const audioContext = new (AudioContext || webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (e) {
            // Fallback: visual feedback only
            console.log('Audio not supported, using visual feedback only');
        }
    }
}

// Enhanced Banner carousel functionality with PWA and mobile optimizations
function initBannerCarousel() {
    const carousel = document.getElementById('bannerCarousel');
    if (!carousel) return;

    const slides = carousel.querySelectorAll('.banner-slide');
    const dots = carousel.querySelectorAll('.banner-dot');
    const prevBtn = carousel.querySelector('.banner-nav-prev');
    const nextBtn = carousel.querySelector('.banner-nav-next');

    let currentSlide = 0;
    let autoSlideInterval;
    let isTransitioning = false;
    let touchStartTime = 0;
    let isDragging = false;

    // Performance optimized slide transition
    function showSlide(index, direction = 'none') {
        if (isTransitioning) return;
        isTransitioning = true;

        const currentSlideElement = slides[currentSlide];
        const nextSlideElement = slides[index];

        // Update accessibility
        slides.forEach((slide, i) => {
            slide.setAttribute('tabindex', i === index ? '0' : '-1');
            slide.setAttribute('aria-hidden', i !== index ? 'true' : 'false');
        });

        // Optimized transition using requestAnimationFrame
        requestAnimationFrame(() => {
            // Hide current slide
            currentSlideElement.style.opacity = '0';
            currentSlideElement.style.zIndex = '0';

            // Show next slide
            nextSlideElement.style.opacity = '1';
            nextSlideElement.style.zIndex = '10';

            // Update dots
            dots.forEach((dot, i) => {
                dot.classList.toggle('bg-green-400', i === index);
                dot.classList.toggle('bg-gray-600', i !== index);
                dot.setAttribute('aria-pressed', i === index ? 'true' : 'false');
            });

            currentSlide = index;

            // Reset transition flag after animation
            setTimeout(() => {
                isTransitioning = false;
            }, 1000);
        });
    }

    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next, 'next');
    }

    function prevSlide() {
        const prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev, 'prev');
    }

    // Auto slide with PWA optimization
    function startAutoSlide() {
        // Reduce auto-slide frequency in PWA mode to save battery
        const interval = isStandalone ? 7000 : 5000;
        autoSlideInterval = setInterval(nextSlide, interval);
    }

    function stopAutoSlide() {
        clearInterval(autoSlideInterval);
    }

    // Enhanced touch/swipe support
    function handleTouchStart(e) {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
        touchStartTime = Date.now();
        isDragging = false;
        stopAutoSlide();
    }

    function handleTouchMove(e) {
        if (!touchStartX || !touchStartY) return;

        const touchCurrentX = e.touches[0].clientX;
        const touchCurrentY = e.touches[0].clientY;

        const diffX = touchStartX - touchCurrentX;
        const diffY = touchStartY - touchCurrentY;

        // Determine if this is a horizontal swipe
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 10) {
            isDragging = true;
            e.preventDefault(); // Prevent scrolling
        }
    }

    function handleTouchEnd(e) {
        if (!touchStartX || !isDragging) {
            startAutoSlide();
            return;
        }

        touchEndX = e.changedTouches[0].clientX;
        const touchDuration = Date.now() - touchStartTime;
        const swipeDistance = Math.abs(touchStartX - touchEndX);
        const swipeVelocity = swipeDistance / touchDuration;

        // Enhanced swipe detection
        const minSwipeDistance = isMobile ? 30 : 50;
        const minSwipeVelocity = 0.1;

        if (swipeDistance > minSwipeDistance || swipeVelocity > minSwipeVelocity) {
            if (touchStartX > touchEndX) {
                nextSlide();
            } else {
                prevSlide();
            }
        }

        // Reset touch variables
        touchStartX = 0;
        touchStartY = 0;
        touchEndX = 0;
        isDragging = false;

        startAutoSlide();
    }

    // Event listeners with passive option for better performance
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            stopAutoSlide();
            nextSlide();
            startAutoSlide();
        });

        // Keyboard support
        nextBtn.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                stopAutoSlide();
                nextSlide();
                startAutoSlide();
            }
        });
    }

    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            stopAutoSlide();
            prevSlide();
            startAutoSlide();
        });

        // Keyboard support
        prevBtn.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                stopAutoSlide();
                prevSlide();
                startAutoSlide();
            }
        });
    }

    // Dots navigation with enhanced accessibility
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            stopAutoSlide();
            showSlide(index);
            startAutoSlide();
        });

        // Keyboard support for dots
        dot.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                stopAutoSlide();
                showSlide(index);
                startAutoSlide();
            }
        });

        // Set initial ARIA attributes
        dot.setAttribute('role', 'button');
        dot.setAttribute('aria-pressed', index === 0 ? 'true' : 'false');
    });

    // Touch events with passive listeners for better performance
    carousel.addEventListener('touchstart', handleTouchStart, { passive: false });
    carousel.addEventListener('touchmove', handleTouchMove, { passive: false });
    carousel.addEventListener('touchend', handleTouchEnd, { passive: true });

    // Keyboard navigation for carousel
    carousel.addEventListener('keydown', (e) => {
        switch(e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                stopAutoSlide();
                prevSlide();
                startAutoSlide();
                break;
            case 'ArrowRight':
                e.preventDefault();
                stopAutoSlide();
                nextSlide();
                startAutoSlide();
                break;
        }
    });

    // Pause auto-slide when page is not visible (PWA optimization)
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            stopAutoSlide();
        } else {
            startAutoSlide();
        }
    });

    // Pause auto-slide on focus for accessibility
    carousel.addEventListener('focusin', stopAutoSlide);
    carousel.addEventListener('focusout', startAutoSlide);

    // Initialize carousel
    if (slides.length > 1) {
        startAutoSlide();

        // Set initial accessibility attributes
        slides.forEach((slide, i) => {
            slide.setAttribute('role', 'img');
            slide.setAttribute('tabindex', i === 0 ? '0' : '-1');
            slide.setAttribute('aria-hidden', i !== 0 ? 'true' : 'false');
        });
    }

    // Preload next images for better performance
    function preloadNextImages() {
        const nextIndex = (currentSlide + 1) % slides.length;
        const nextSlide = slides[nextIndex];
        const img = nextSlide.querySelector('img');
        if (img && img.loading === 'lazy') {
            img.loading = 'eager';
        }
    }

    // Preload images after initial load
    setTimeout(preloadNextImages, 1000);
}

// Counter animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('[data-counter]');

    const animateCounter = (counter) => {
        const target = parseFloat(counter.dataset.counter);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }

            if (target % 1 === 0) {
                counter.textContent = Math.floor(current);
            } else {
                counter.textContent = current.toFixed(1);
            }
        }, 16);
    };

    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });

    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// Search functionality
function initSearchFunctionality() {
    const searchInput = document.getElementById('heroSearch');
    const searchBtn = document.getElementById('heroSearchBtn');
    const suggestions = document.getElementById('searchSuggestions');

    if (!searchInput) return;

    // Terminal-style search
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        if (query.length > 2) {
            // Simulate terminal search
            showSearchSuggestions(query);
        } else {
            hideSuggestions();
        }
    });

    // Execute search
    const executeSearch = () => {
        const query = searchInput.value.trim();
        if (query) {
            // Simulate terminal command execution
            console.log(`$ grep -i "${query}" events.db`);
            window.location.href = `/events?search=${encodeURIComponent(query)}`;
        }
    };

    if (searchBtn) {
        searchBtn.addEventListener('click', executeSearch);
    }

    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            executeSearch();
        }
    });

    // Popular command buttons
    const commandButtons = document.querySelectorAll('[data-search-command]');
    commandButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const command = this.dataset.searchCommand;
            searchInput.value = command;
            executeSearch();
        });
    });
}

// Show search suggestions
function showSearchSuggestions(query) {
    const suggestions = document.getElementById('searchSuggestions');
    if (!suggestions) return;

    // Mock suggestions
    const mockSuggestions = [
        'konser musik jakarta',
        'seminar bisnis online',
        'workshop programming',
        'festival kuliner bandung',
        'stand up comedy'
    ].filter(item => item.toLowerCase().includes(query.toLowerCase()));

    if (mockSuggestions.length > 0) {
        suggestions.innerHTML = mockSuggestions.map(suggestion =>
            `<div class="bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 font-mono text-sm rounded p-2 cursor-pointer transition-colors border border-gray-600/50" onclick="selectSuggestion('${suggestion}')">
                $ grep "${suggestion}" events.db
            </div>`
        ).join('');
        suggestions.classList.remove('hidden');
    } else {
        hideSuggestions();
    }
}

// Hide suggestions
function hideSuggestions() {
    const suggestions = document.getElementById('searchSuggestions');
    if (suggestions) {
        suggestions.classList.add('hidden');
    }
}

// Select suggestion
function selectSuggestion(suggestion) {
    const searchInput = document.getElementById('heroSearch');
    if (searchInput) {
        searchInput.value = suggestion;
        hideSuggestions();
        // Execute search
        window.location.href = `/events?search=${encodeURIComponent(suggestion)}`;
    }
}

// Terminal command simulation
function simulateTerminalCommand(command) {
    console.log(`$ ${command}`);
    // Add visual feedback
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-gray-900 border border-green-500 text-green-400 font-mono text-sm p-3 rounded-lg z-50';
    notification.textContent = `$ ${command}`;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 2000);
}

// PWA Optimizations
function initPWAOptimizations() {
    // Adjust viewport height for PWA
    function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setViewportHeight();
    window.addEventListener('resize', setViewportHeight);
    window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 100);
    });

    // PWA status bar color
    if (isStandalone) {
        const metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (metaThemeColor) {
            metaThemeColor.content = '#000000';
        }
    }

    // Prevent zoom on double tap for PWA
    if (isStandalone) {
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }
}

// Mobile Touch Optimizations
function initMobileTouchOptimizations() {
    // Improve touch responsiveness
    if (isMobile) {
        // Add touch-action CSS for better touch handling
        const style = document.createElement('style');
        style.textContent = `
            .banner-carousel {
                touch-action: pan-y pinch-zoom;
            }
            .banner-nav-btn, .banner-dot {
                touch-action: manipulation;
            }
        `;
        document.head.appendChild(style);

        // Optimize scroll behavior for mobile
        document.body.style.overscrollBehavior = 'contain';

        // Prevent context menu on long press for better UX
        document.addEventListener('contextmenu', function(e) {
            if (e.target.closest('.banner-carousel')) {
                e.preventDefault();
            }
        });
    }

    // Enhanced focus management for mobile
    function handleFocusManagement() {
        const focusableElements = document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        focusableElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.classList.add('touch-active');
            });

            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            });
        });
    }

    handleFocusManagement();
}

// Performance monitoring for mobile
function initPerformanceMonitoring() {
    if (isMobile) {
        // Monitor frame rate
        let frameCount = 0;
        let lastTime = performance.now();

        function countFrames() {
            frameCount++;
            const currentTime = performance.now();

            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));

                // Reduce animations if FPS is too low
                if (fps < 30) {
                    document.body.classList.add('low-performance');
                }

                frameCount = 0;
                lastTime = currentTime;
            }

            requestAnimationFrame(countFrames);
        }

        requestAnimationFrame(countFrames);
    }
}

// Responsive image loading optimization
function optimizeImageLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.loading = 'eager';
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px'
        });

        images.forEach(img => imageObserver.observe(img));
    }
}

// Initialize performance monitoring
setTimeout(() => {
    initPerformanceMonitoring();
    optimizeImageLoading();
}, 1000);

// Add terminal glow effect on focus
document.addEventListener('focusin', function(e) {
    if (e.target.closest('.bg-gray-900')) {
        e.target.closest('.bg-gray-900').classList.add('terminal-glow');
    }
});

document.addEventListener('focusout', function(e) {
    if (e.target.closest('.bg-gray-900')) {
        e.target.closest('.bg-gray-900').classList.remove('terminal-glow');
    }
});

// Viewport height fix for mobile browsers
window.addEventListener('resize', () => {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
});

// Initial viewport height set
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);

// Simple Dark/Light Mode Toggle
function initSimpleThemeToggle() {
    // Check for saved theme preference or default to 'light'
    const currentTheme = localStorage.getItem('theme') || 'light';

    // Apply the theme
    document.documentElement.setAttribute('data-theme', currentTheme);

    // Create theme toggle button if it doesn't exist
    if (!document.getElementById('themeToggle')) {
        const themeToggle = document.createElement('button');
        themeToggle.id = 'themeToggle';
        themeToggle.className = 'fixed top-4 right-4 z-50 p-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-300';
        themeToggle.innerHTML = currentTheme === 'dark' ?
            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/></svg>' :
            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/></svg>';

        themeToggle.addEventListener('click', toggleTheme);
        document.body.appendChild(themeToggle);
    }
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

    // Apply new theme
    document.documentElement.setAttribute('data-theme', newTheme);

    // Save to localStorage
    localStorage.setItem('theme', newTheme);

    // Update toggle button icon
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.innerHTML = newTheme === 'dark' ?
            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/></svg>' :
            '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/></svg>';
    }

    // Show notification
    const message = newTheme === 'dark' ? 'Mode gelap diaktifkan' : 'Mode terang diaktifkan';
    showSimpleNotification(message);
}

function showSimpleNotification(message) {
    // Remove existing notification
    const existingNotification = document.getElementById('simpleNotification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Create notification
    const notification = document.createElement('div');
    notification.id = 'simpleNotification';
    notification.className = 'fixed top-20 right-4 z-50 px-4 py-2 bg-green-500 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
    notification.textContent = message;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Initialize simple theme toggle
initSimpleThemeToggle();
</script>

{{-- Simple Dark/Light Mode CSS --}}
<style>
/* Light Mode (Default) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Dark Mode */
[data-theme="dark"] {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Apply theme variables */
[data-theme="dark"] .gopay-card {
    background: rgba(31, 41, 55, 0.95) !important;
    color: #f9fafb !important;
    border: 1px solid rgba(75, 85, 99, 0.3) !important;
}

[data-theme="dark"] .gopay-card .text-gray-800 {
    color: #f9fafb !important;
}

[data-theme="dark"] .gopay-card .text-gray-700 {
    color: #d1d5db !important;
}

[data-theme="dark"] .gopay-card .text-gray-600 {
    color: #9ca3af !important;
}

[data-theme="dark"] .ticket-3d-front {
    background: linear-gradient(135deg,
        rgba(31, 41, 55, 0.95) 0%,
        rgba(31, 41, 55, 0.85) 100%
    ) !important;
    color: #f9fafb !important;
}

[data-theme="dark"] .ticket-3d-title {
    color: #f9fafb !important;
}

[data-theme="dark"] .floating-ticket {
    background: linear-gradient(135deg,
        rgba(31, 41, 55, 0.9),
        rgba(31, 41, 55, 0.7)
    ) !important;
}

[data-theme="dark"] .floating-ticket .ticket-title {
    color: #f9fafb !important;
}

[data-theme="dark"] .floating-ticket .ticket-category {
    color: #d1d5db !important;
    background: rgba(75, 85, 99, 0.8) !important;
}

/* Smooth transitions for theme changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}
</style>
@endpush

{{--
Catatan engineering:
- Kode sudah lengkap dan setiap pembuka div/blade statement sudah ada penutupnya.
- Kode placeholder diganti dengan isi real.
- Modularisasi disarankan kedepan untuk hero/banner jadi component.
--}}
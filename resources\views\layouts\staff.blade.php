<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="{ darkMode: localStorage.getItem('theme') === 'dark' || false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#A8D5BA">

    <title>@yield('title', 'Staff Dashboard - TiXara')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Vite Assets -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Additional Styles -->
    @stack('styles')

    <style>
        /* Staff Layout Styles */
        .staff-sidebar {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-right: 1px solid rgba(16, 185, 129, 0.1);
            backdrop-filter: blur(10px);
        }

        .staff-sidebar-nav a {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin-bottom: 4px;
        }

        .staff-sidebar-nav a:hover {
            background: rgba(16, 185, 129, 0.1);
            transform: translateX(4px);
        }

        .staff-sidebar-nav a.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .staff-main-content {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .staff-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .staff-sidebar.open {
                transform: translateX(0);
            }

            .staff-sidebar-overlay {
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
            }
        }

        /* Dark mode support */
        .dark .staff-sidebar {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            border-right-color: rgba(16, 185, 129, 0.2);
        }

        .dark .staff-main-content {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }

        .dark .staff-sidebar-nav a:hover {
            background: rgba(16, 185, 129, 0.2);
        }
    </style>
</head>
<body class="font-sans antialiased" x-data="staffLayout()">
    <div class="flex h-screen overflow-hidden">
        <!-- Mobile Sidebar Overlay -->
        <div x-show="sidebarOpen"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             @click="sidebarOpen = false"
             class="fixed inset-0 z-40 staff-sidebar-overlay md:hidden"></div>

        <!-- Sidebar -->
        <div class="fixed inset-y-0 left-0 z-50 w-64 staff-sidebar md:relative md:translate-x-0 transition-transform duration-300 ease-in-out"
             :class="{ 'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen }">

            <!-- Sidebar Header -->
            <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <i data-lucide="shield-check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900 dark:text-white">TiXara</h1>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Staff Panel</p>
                    </div>
                </div>
                <button @click="sidebarOpen = false" class="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i data-lucide="x" class="w-5 h-5 text-gray-500"></i>
                </button>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto staff-sidebar-nav">
                <!-- Dashboard -->
                <a href="{{ route('staff.dashboard') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 {{ request()->routeIs('staff.dashboard') ? 'active' : '' }}">
                    <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                    Dashboard
                </a>

                <!-- QR Scanner -->
                <a href="{{ route('validation.staff') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 {{ request()->routeIs('validation.*') ? 'active' : '' }}">
                    <i data-lucide="scan-line" class="w-5 h-5 mr-3"></i>
                    QR Scanner
                </a>

                <!-- Tickets -->
                <a href="{{ route('staff.tickets') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 {{ request()->routeIs('staff.tickets*') ? 'active' : '' }}">
                    <i data-lucide="ticket" class="w-5 h-5 mr-3"></i>
                    Tickets
                </a>

                <!-- Events -->
                <a href="{{ route('staff.events') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 {{ request()->routeIs('staff.events*') ? 'active' : '' }}">
                    <i data-lucide="calendar" class="w-5 h-5 mr-3"></i>
                    Events
                </a>

                <!-- Reports -->
                <a href="{{ route('staff.reports') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 {{ request()->routeIs('staff.reports*') ? 'active' : '' }}">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 mr-3"></i>
                    Reports
                </a>

                <!-- Divider -->
                <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>

                <!-- Profile -->
                <a href="{{ route('profile.edit') }}"
                   class="flex items-center px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300">
                    <i data-lucide="user" class="w-5 h-5 mr-3"></i>
                    Profile
                </a>

                <!-- Theme Toggle -->
                <button @click="toggleTheme()"
                        class="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                    <i data-lucide="sun" x-show="!darkMode" class="w-5 h-5 mr-3"></i>
                    <i data-lucide="moon" x-show="darkMode" class="w-5 h-5 mr-3"></i>
                    <span x-text="darkMode ? 'Light Mode' : 'Dark Mode'"></span>
                </button>

                <!-- Logout -->
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit"
                            class="flex items-center w-full px-4 py-3 text-sm font-medium text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg">
                        <i data-lucide="log-out" class="w-5 h-5 mr-3"></i>
                        Logout
                    </button>
                </form>
            </nav>

            <!-- User Info -->
            <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                        <span class="text-white font-semibold text-sm">{{ substr(auth()->user()->name, 0, 2) }}</span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ auth()->user()->name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ auth()->user()->email }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Mobile Header -->
            <div class="md:hidden flex items-center justify-between h-16 px-4 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <button @click="sidebarOpen = true" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i data-lucide="menu" class="w-6 h-6 text-gray-500"></i>
                </button>
                <h1 class="text-lg font-semibold text-gray-900 dark:text-white">@yield('page-title', 'Staff Dashboard')</h1>
                <div class="w-10"></div> <!-- Spacer -->
            </div>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto staff-main-content">
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    @livewireScripts

    <script>
        // Staff Layout Alpine.js Component
        function staffLayout() {
            return {
                sidebarOpen: false,
                darkMode: localStorage.getItem('theme') === 'dark' || false,

                init() {
                    this.applyTheme();

                    // Initialize Lucide icons
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }

                    // Initialize AOS
                    AOS.init({
                        duration: 800,
                        once: true
                    });
                },

                toggleTheme() {
                    this.darkMode = !this.darkMode;
                    this.applyTheme();
                    this.saveThemeSettings();
                    this.showNotification(
                        this.darkMode ? 'Mode gelap diaktifkan' : 'Mode terang diaktifkan'
                    );
                },

                applyTheme() {
                    const root = document.documentElement;

                    if (this.darkMode) {
                        root.classList.add('dark');
                        root.setAttribute('data-theme', 'dark');
                    } else {
                        root.classList.remove('dark');
                        root.setAttribute('data-theme', 'light');
                    }
                },

                saveThemeSettings() {
                    localStorage.setItem('theme', this.darkMode ? 'dark' : 'light');
                },

                showNotification(message) {
                    // Simple notification system
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 z-50 px-4 py-2 bg-green-500 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
                    notification.textContent = message;

                    document.body.appendChild(notification);

                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            notification.remove();
                        }, 300);
                    }, 3000);
                }
            }
        }
    </script>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>
</html>

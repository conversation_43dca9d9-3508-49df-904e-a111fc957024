<?php

echo "🔧 Fixing migration brace syntax errors (Final Fix)...\n";

$migrationPath = 'database/migrations/';
$migrationFiles = glob($migrationPath . '*.php');

$fixedCount = 0;
$errorFiles = [];

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Pattern to fix: Missing closing brace and parenthesis for Schema::create
    // Look for lines that end with table definitions but missing });
    $lines = explode("\n", $content);
    $newLines = [];
    $inSchemaCreate = false;
    $schemaCreateDepth = 0;
    
    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];
        $newLines[] = $line;
        
        // Detect start of Schema::create
        if (preg_match('/Schema::create\([^,]+,\s*function\s*\([^)]*\)\s*{/', $line)) {
            $inSchemaCreate = true;
            $schemaCreateDepth = 1;
            continue;
        }
        
        if ($inSchemaCreate) {
            // Count braces to track depth
            $openBraces = substr_count($line, '{');
            $closeBraces = substr_count($line, '}');
            $schemaCreateDepth += $openBraces - $closeBraces;
            
            // Check if this line should close the Schema::create but is missing });
            if ($schemaCreateDepth == 1 && 
                (preg_match('/\s*}\s*$/', $line) || 
                 preg_match('/\s*\$table->index\([^)]+\);\s*$/', $line) ||
                 preg_match('/\s*\$table->timestamps\(\);\s*$/', $line))) {
                
                // Look ahead to see if next line is just }
                if (isset($lines[$i + 1]) && preg_match('/^\s*}\s*$/', $lines[$i + 1])) {
                    // This is the pattern we need to fix
                    // Replace the } with });
                    $newLines[count($newLines) - 1] = preg_replace('/\s*}\s*$/', '            });', $line);
                    $inSchemaCreate = false;
                    echo "  🔧 Fixed Schema::create closing in {$filename} at line " . ($i + 1) . "\n";
                }
            }
            
            if ($schemaCreateDepth <= 0) {
                $inSchemaCreate = false;
            }
        }
    }
    
    $newContent = implode("\n", $newLines);
    
    // Additional fixes for common patterns
    $patterns = [
        // Fix missing closing brace for if statement
        '/(\s+}\);\s+}\s+\/\*\*)/s' => '$1' . "\n        }\n    }\n\n    /**",
        
        // Fix malformed comment blocks
        '/\/\*\*\s+}\s+\*\s+Reverse\s+the\s+migrations\.\s+\*\//' => "/**\n     * Reverse the migrations.\n     */",
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $newContent = preg_replace($pattern, $replacement, $newContent);
    }
    
    // Check if content changed
    if ($newContent !== $originalContent) {
        file_put_contents($file, $newContent);
        $fixedCount++;
        echo "  ✅ Fixed {$filename}\n";
    }
    
    // Validate syntax
    $tempFile = tempnam(sys_get_temp_dir(), 'migration_check');
    file_put_contents($tempFile, $newContent);
    
    $output = [];
    $returnCode = 0;
    exec("php -l \"{$tempFile}\" 2>&1", $output, $returnCode);
    unlink($tempFile);
    
    if ($returnCode !== 0) {
        $errorFiles[] = $filename;
        echo "  ❌ Syntax error in {$filename}: " . implode(' ', $output) . "\n";
    }
}

echo "\n📊 Summary:\n";
echo "  Fixed: {$fixedCount} files\n";
echo "  Errors: " . count($errorFiles) . " files\n";

if (!empty($errorFiles)) {
    echo "\n⚠️  Files with remaining syntax errors:\n";
    foreach ($errorFiles as $errorFile) {
        echo "  - {$errorFile}\n";
    }
} else {
    echo "\n🎉 All migration files are now syntax-error free!\n";
}

echo "\n✅ Migration syntax fix completed!\n";

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserBadgeLevel extends Model
{
    use HasFactory;

    protected $table = 'user_badge_level';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'icon',
        'badge_image',
        'min_spent_amount',
        'min_uangtix_balance',
        'min_transactions',
        'min_events_attended',
        'benefits',
        'requirements',
        'discount_percentage',
        'cashback_percentage',
        'is_active',
        'is_default',
        'sort_order',
    ];

    protected $casts = [
        'benefits' => 'array',
        'requirements' => 'array',
        'min_spent_amount' => 'decimal:2',
        'min_uangtix_balance' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'cashback_percentage' => 'decimal:2',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get users with this badge level
     */
    public function users()
    {
        return $this->hasMany(User::class, 'badge_level_id');
    }

    /**
     * Scope for active badge level
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered badge level
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('min_spent_amount');
    }

    /**
     * Check if user qualifies for this badge level
     */
    public function userQualifies(User $user)
    {
        // Check spent amount
        if ($user->total_spent_amount < $this->min_spent_amount) {
            return false;
        }

        // Check UangTix balance
        if ($user->uangtix_balance < $this->min_uangtix_balance) {
            return false;
        }

        // Check transaction count
        if ($user->total_transactions < $this->min_transactions) {
            return false;
        }

        // Check events attended
        if ($user->total_events_attended < $this->min_events_attended) {
            return false;
        }

        // Check additional requirements
        if ($this->requirements) {
            foreach ($this->requirements as $requirement => $value) {
                if (!$this->checkRequirement($user, $requirement, $value)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Check specific requirement
     */
    private function checkRequirement(User $user, $requirement, $value)
    {
        switch ($requirement) {
            case 'account_age_days':
                return $user->created_at->diffInDays(now()) >= $value;
            case 'verified_email':
                return $value ? $user->hasVerifiedEmail() : true;
            case 'verified_phone':
                return $value ? !empty($user->phone_verified_at) : true;
            case 'kyc_verified':
                return $value ? $user->kyc_verified : true;
            default:
                return true;
        }
    }

    /**
     * Get formatted benefits
     */
    public function getFormattedBenefitsAttribute()
    {
        if (!$this->benefits) {
            return [];
        }

        $formatted = [];
        foreach ($this->benefits as $benefit => $value) {
            $formatted[] = $this->formatBenefit($benefit, $value);
        }

        return $formatted;
    }

    /**
     * Format individual benefit
     */
    private function formatBenefit($benefit, $value)
    {
        switch ($benefit) {
            case 'discount_percentage':
                return "Diskon {$value}% untuk semua pembelian";
            case 'cashback_percentage':
                return "Cashback {$value}% dalam UangTix";
            case 'priority_support':
                return $value ? "Dukungan prioritas 24/7" : null;
            case 'early_access':
                return $value ? "Akses awal ke event eksklusif" : null;
            case 'free_shipping':
                return $value ? "Gratis ongkos kirim" : null;
            case 'exclusive_events':
                return $value ? "Undangan ke event eksklusif" : null;
            case 'personal_manager':
                return $value ? "Personal account manager" : null;
            default:
                return is_bool($value) ? ($value ? ucfirst(str_replace('_', ' ', $benefit)) : null) : "{$benefit}: {$value}";
        }
    }

    /**
     * Get next badge level
     */
    public function getNextLevel()
    {
        return static::active()
            ->where('sort_order', '>', $this->sort_order)
            ->orWhere(function($query) {
                $query->where('sort_order', $this->sort_order)
                      ->where('min_spent_amount', '>', $this->min_spent_amount);
            })
            ->ordered()
            ->first();
    }

    /**
     * Get previous badge level
     */
    public function getPreviousLevel()
    {
        return static::active()
            ->where('sort_order', '<', $this->sort_order)
            ->orWhere(function($query) {
                $query->where('sort_order', $this->sort_order)
                      ->where('min_spent_amount', '<', $this->min_spent_amount);
            })
            ->orderBy('sort_order', 'desc')
            ->orderBy('min_spent_amount', 'desc')
            ->first();
    }

    /**
     * Calculate progress to next level for user
     */
    public function calculateProgressToNext(User $user)
    {
        $nextLevel = $this->getNextLevel();
        if (!$nextLevel) {
            return 100; // Already at highest level
        }

        $currentSpent = $user->total_spent_amount;
        $currentRequired = $this->min_spent_amount;
        $nextRequired = $nextLevel->min_spent_amount;

        if ($nextRequired <= $currentRequired) {
            return 100;
        }

        $progress = (($currentSpent - $currentRequired) / ($nextRequired - $currentRequired)) * 100;
        return max(0, min(100, $progress));
    }

    /**
     * Get badge HTML
     */
    public function getBadgeHtml($size = 'md')
    {
        $sizes = [
            'sm' => 'width: 20px; height: 20px; font-size: 10px;',
            'md' => 'width: 32px; height: 32px; font-size: 14px;',
            'lg' => 'width: 48px; height: 48px; font-size: 18px;',
        ];

        $style = $sizes[$size] ?? $sizes['md'];

        if ($this->badge_image) {
            return '<img src="' . asset($this->badge_image) . '" alt="' . $this->name . '" style="' . $style . ' border-radius: 50%; object-fit: cover;">';
        }

        return '<div style="' . $style . ' background: ' . $this->color . '; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">' .
               '<i class="' . ($this->icon ?? 'fas fa-medal') . '"></i>' .
               '</div>';
    }

    /**
     * Auto-upgrade users who qualify
     */
    public static function autoUpgradeUsers()
    {
        $level = static::active()->ordered()->get();
        $users = User::with('badgeLevel')->get();

        $upgradedCount = 0;

        foreach ($users as $user) {
            $currentLevel = $user->badgeLevel;
            $qualifiedLevel = null;

            // Find highest level user qualifies for
            foreach ($level->reverse() as $level) {
                if ($level->userQualifies($user)) {
                    $qualifiedLevel = $level;
                    break;
                }
            }

            // Upgrade if qualified for higher level
            if ($qualifiedLevel && (!$currentLevel || $qualifiedLevel->sort_order > $currentLevel->sort_order)) {
                $user->update([
                    'badge_level_id' => $qualifiedLevel->id,
                    'badge_achieved_at' => now(),
                ]);
                $upgradedCount++;

                // Fire event for badge upgrade
                event(new \App\Events\UserBadgeUpgraded($user, $qualifiedLevel, $currentLevel));
            }
        }

        return $upgradedCount;
    }

    /**
     * Get statistics
     */
    public static function getStatistics()
    {
        $totalUsers = User::count();
        $usersWithBadges = User::whereNotNull('badge_level_id')->count();

        // Get level distribution
        $levelDistribution = static::withCount('users')
            ->get()
            ->pluck('users_count', 'name')
            ->toArray();

        // Calculate eligible for upgrade
        $eligibleForUpgrade = 0;
        $level = static::active()->ordered()->get();
        $users = User::with('badgeLevel')->get();

        foreach ($users as $user) {
            $currentLevel = $user->badgeLevel;
            foreach ($level->reverse() as $level) {
                if ($level->userQualifies($user)) {
                    if (!$currentLevel || $level->sort_order > $currentLevel->sort_order) {
                        $eligibleForUpgrade++;
                    }
                    break;
                }
            }
        }

        return [
            'total_level' => static::count(),
            'active_level' => static::active()->count(),
            'total_users' => $totalUsers,
            'users_with_badges' => $usersWithBadges,
            'users_without_badges' => $totalUsers - $usersWithBadges,
            'eligible_for_upgrade' => $eligibleForUpgrade,
            'level_distribution' => $levelDistribution,
        ];
    }
}

@extends('layouts.organizer')

@section('title', 'Ad Subscriptions')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-credit-card text-primary me-2"></i>Ad Subscriptions
            </h1>
            <p class="mb-0 text-muted">Manage your advertising subscription plans</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('organizer.ads.subscriptions.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Subscribe to Plan
            </a>
        </div>
    </div>

    <!-- Current Plan Status -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Current Plan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['current_plan'] ?? 'Free' }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-star fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Subscriptions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['active_subscriptions'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Ads Remaining
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['ads_remaining'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Spent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($stats['total_spent'] ?? 0, 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Plans -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Available Plans</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Free Plan -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 border-left-secondary">
                        <div class="card-header text-center bg-light">
                            <h5 class="card-title mb-0">Free Plan</h5>
                            <div class="h2 text-secondary">Rp 0</div>
                            <small class="text-muted">per month</small>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    5 ads per month
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Basic analytics
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    No priority support
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    Limited targeting
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer text-center">
                            <button class="btn btn-outline-secondary" disabled>Current Plan</button>
                        </div>
                    </div>
                </div>

                <!-- Basic Plan -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 border-left-primary">
                        <div class="card-header text-center bg-primary text-white">
                            <h5 class="card-title mb-0">Basic Plan</h5>
                            <div class="h2">Rp 99,000</div>
                            <small>per month</small>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    25 ads per month
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Advanced analytics
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Email support
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Basic targeting
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer text-center">
                            <a href="{{ route('organizer.ads.subscriptions.create') }}?plan=basic" class="btn btn-primary">
                                Subscribe Now
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Premium Plan -->
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 border-left-success">
                        <div class="card-header text-center bg-success text-white position-relative">
                            <span class="badge badge-warning position-absolute" style="top: -10px; right: 10px;">
                                Popular
                            </span>
                            <h5 class="card-title mb-0">Premium Plan</h5>
                            <div class="h2">Rp 299,000</div>
                            <small>per month</small>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Unlimited ads
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Premium analytics
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Priority support
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Advanced targeting
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    A/B testing
                                </li>
                            </ul>
                        </div>
                        <div class="card-footer text-center">
                            <a href="{{ route('organizer.ads.subscriptions.create') }}?plan=premium" class="btn btn-success">
                                Subscribe Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Subscription History</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-1"></i>Filter
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="?status=active">Active</a>
                    <a class="dropdown-item" href="?status=expired">Expired</a>
                    <a class="dropdown-item" href="?status=cancelled">Cancelled</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" href="?">All Subscriptions</a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Plan</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($subscriptions ?? [] as $subscription)
                            <tr>
                                <td>
                                    <div class="font-weight-bold">{{ $subscription['plan_name'] }}</div>
                                    <small class="text-muted">{{ $subscription['plan_description'] }}</small>
                                </td>
                                <td>{{ $subscription['start_date'] }}</td>
                                <td>{{ $subscription['end_date'] }}</td>
                                <td>
                                    <span class="font-weight-bold">Rp {{ number_format($subscription['amount'], 0, ',', '.') }}</span>
                                </td>
                                <td>
                                    @switch($subscription['status'])
                                        @case('active')
                                            <span class="badge badge-success">Active</span>
                                            @break
                                        @case('expired')
                                            <span class="badge badge-warning">Expired</span>
                                            @break
                                        @case('cancelled')
                                            <span class="badge badge-danger">Cancelled</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ ucfirst($subscription['status']) }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('organizer.ads.subscriptions.show', $subscription['id']) }}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($subscription['status'] === 'active')
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="cancelSubscription({{ $subscription['id'] }})" title="Cancel">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        @endif
                                        @if($subscription['status'] === 'expired')
                                            <a href="{{ route('organizer.ads.subscriptions.create') }}?renew={{ $subscription['id'] }}" 
                                               class="btn btn-sm btn-outline-success" title="Renew">
                                                <i class="fas fa-redo"></i>
                                            </a>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                                        <p>No subscription history found</p>
                                        <a href="{{ route('organizer.ads.subscriptions.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Subscribe to a Plan
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Usage Statistics -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Usage</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Plan Benefits</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-right">
                            <div class="h4 font-weight-bold text-primary">25</div>
                            <div class="text-muted">Ads This Month</div>
                        </div>
                        <div class="col-6">
                            <div class="h4 font-weight-bold text-success">15</div>
                            <div class="text-muted">Remaining</div>
                        </div>
                    </div>
                    <hr>
                    <div class="progress mb-3">
                        <div class="progress-bar bg-primary" role="progressbar" style="width: 40%" 
                             aria-valuenow="40" aria-valuemin="0" aria-valuemax="100">
                            40% Used
                        </div>
                    </div>
                    <div class="text-center">
                        <small class="text-muted">Resets on the 1st of each month</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Usage Chart
const ctx = document.getElementById('usageChart').getContext('2d');
const usageChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Ads Created',
            data: [12, 19, 15, 25, 22, 30],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function cancelSubscription(subscriptionId) {
    if (confirm('Are you sure you want to cancel this subscription? This action cannot be undone.')) {
        fetch(`/organizer/ads/subscriptions/${subscriptionId}/cancel`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the subscription.');
        });
    }
}
</script>
@endpush
@endsection

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class Banner extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'subtitle',
        'image_path',
        'mobile_image_path',
        'link_url',
        'button_text',
        'button_color',
        'is_active',
        'start_date',
        'end_date',
        'sort_order',
        'text_position',
        'text_color',
        'show_overlay',
        'overlay_color',
        'overlay_opacity',
        'animation_settings'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'show_overlay' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'overlay_opacity' => 'decimal:2',
        'animation_settings' => 'array'
    ];

    /**
     * Scope untuk banner yang aktif
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope untuk banner yang sedang dalam periode tampil
     */
    public function scopeInPeriod(Builder $query): Builder
    {
        $now = Carbon::now();
        
        return $query->where(function ($q) use ($now) {
            $q->where(function ($subQ) use ($now) {
                $subQ->whereNull('start_date')
                     ->orWhere('start_date', '<=', $now);
            })->where(function ($subQ) use ($now) {
                $subQ->whereNull('end_date')
                     ->orWhere('end_date', '>=', $now);
            });
        });
    }

    /**
     * Scope untuk banner yang dapat ditampilkan
     */
    public function scopeDisplayable(Builder $query): Builder
    {
        return $query->active()->inPeriod()->orderBy('sort_order');
    }

    /**
     * Get image URL
     */
    public function getImageUrlAttribute(): string
    {
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            return $this->image_path;
        }
        
        return Storage::url($this->image_path);
    }

    /**
     * Get mobile image URL
     */
    public function getMobileImageUrlAttribute(): ?string
    {
        if (!$this->mobile_image_path) {
            return $this->image_url;
        }

        if (filter_var($this->mobile_image_path, FILTER_VALIDATE_URL)) {
            return $this->mobile_image_path;
        }
        
        return Storage::url($this->mobile_image_path);
    }

    /**
     * Check if banner is currently active and in display period
     */
    public function isDisplayable(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();

        if ($this->start_date && $this->start_date->gt($now)) {
            return false;
        }

        if ($this->end_date && $this->end_date->lt($now)) {
            return false;
        }

        return true;
    }

    /**
     * Get overlay style
     */
    public function getOverlayStyleAttribute(): string
    {
        if (!$this->show_overlay) {
            return '';
        }

        $opacity = $this->overlay_opacity;
        
        switch ($this->overlay_color) {
            case 'light':
                return "background: rgba(255, 255, 255, {$opacity});";
            case 'gradient':
                return "background: linear-gradient(135deg, rgba(0, 0, 0, {$opacity}) 0%, rgba(0, 0, 0, " . ($opacity * 0.3) . ") 100%);";
            default: // dark
                return "background: rgba(0, 0, 0, {$opacity});";
        }
    }

    /**
     * Get text alignment class
     */
    public function getTextAlignmentClassAttribute(): string
    {
        switch ($this->text_position) {
            case 'center':
                return 'text-center items-center justify-center';
            case 'right':
                return 'text-right items-end justify-end';
            default: // left
                return 'text-left items-start justify-start';
        }
    }

    /**
     * Get text color class
     */
    public function getTextColorClassAttribute(): string
    {
        return $this->text_color === 'dark' ? 'text-gray-900' : 'text-white';
    }

    /**
     * Get button color class
     */
    public function getButtonColorClassAttribute(): string
    {
        switch ($this->button_color) {
            case 'secondary':
                return 'bg-gray-600 hover:bg-gray-700 text-white';
            case 'success':
                return 'bg-green-500 hover:bg-green-600 text-white';
            case 'warning':
                return 'bg-yellow-500 hover:bg-yellow-600 text-white';
            case 'danger':
                return 'bg-red-500 hover:bg-red-600 text-white';
            case 'pastel-green':
                return 'bg-gradient-to-r from-green-400 to-emerald-400 hover:from-green-500 hover:to-emerald-500 text-white';
            case 'pastel-pink':
                return 'bg-gradient-to-r from-pink-400 to-rose-400 hover:from-pink-500 hover:to-rose-500 text-white';
            case 'pastel-purple':
                return 'bg-gradient-to-r from-purple-400 to-violet-400 hover:from-purple-500 hover:to-violet-500 text-white';
            default: // primary
                return 'bg-primary hover:bg-primary/90 text-white';
        }
    }

    /**
     * Delete banner images when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($banner) {
            if ($banner->image_path && !filter_var($banner->image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($banner->image_path);
            }
            
            if ($banner->mobile_image_path && !filter_var($banner->mobile_image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($banner->mobile_image_path);
            }
        });
    }
}

@extends('layouts.admin')

@section('title', 'Edit Payment Method')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
/* Payment Method Edit Styles - Same as create */
.edit-container {
    background: #F8FAFC;
    min-height: 100vh;
    padding: 24px;
}

.form-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.form-header {
    background: #F8FAFC;
    padding: 24px;
    border-bottom: 1px solid #E5E7EB;
}

.form-title {
    font-size: 24px;
    font-weight: 700;
    color: #1A1A1A;
    margin: 0 0 8px 0;
}

.form-subtitle {
    color: #6B7280;
    margin: 0;
}

.form-body {
    padding: 32px;
}

.form-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1A1A1A;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #E5E7EB;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.form-label.required::after {
    content: ' *';
    color: #EF4444;
}

.form-input {
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus {
    border-color: #0066CC;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-input:disabled {
    background: #F9FAFB;
    color: #9CA3AF;
}

.form-textarea {
    min-height: 100px;
    resize: vertical;
}

.form-help {
    font-size: 12px;
    color: #6B7280;
    margin-top: 4px;
}

.form-error {
    font-size: 12px;
    color: #EF4444;
    margin-top: 4px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

.checkbox-input {
    width: 16px;
    height: 16px;
    accent-color: #0066CC;
}

.checkbox-label {
    font-size: 14px;
    color: #374151;
    margin: 0;
}

.config-section {
    background: #F8FAFC;
    border-radius: 8px;
    padding: 20px;
    margin-top: 16px;
    display: none;
}

.config-section.active {
    display: block;
}

.btn-primary {
    background: #0066CC;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: #0052A3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6B7280;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: #4B5563;
    color: white;
    text-decoration: none;
}

.btn-danger {
    background: #EF4444;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-danger:hover {
    background: #DC2626;
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    padding-top: 24px;
    border-top: 1px solid #E5E7EB;
}

.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border: 2px dashed #D1D5DB;
    border-radius: 8px;
    background: #F9FAFB;
    color: #6B7280;
    font-size: 14px;
    transition: all 0.3s ease;
}

.file-upload:hover .file-upload-label {
    border-color: #0066CC;
    background: #F0F9FF;
    color: #0066CC;
}

.preview-image {
    max-width: 100px;
    max-height: 60px;
    border-radius: 6px;
    margin-top: 8px;
    border: 1px solid #E5E7EB;
}

.current-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: #F8FAFC;
    border-radius: 8px;
}

.current-logo img {
    width: 60px;
    height: 40px;
    object-fit: contain;
    border-radius: 6px;
    border: 1px solid #E5E7EB;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.active {
    background: #D1FAE5;
    color: #065F46;
}

.status-badge.inactive {
    background: #FEE2E2;
    color: #991B1B;
}

/* Responsive Design */
@media (max-width: 768px) {
    .edit-container {
        padding: 16px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-actions {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="edit-container">
    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
        <div>
            <h1 style="font-size: 28px; font-weight: 700; color: #1A1A1A; margin: 0 0 8px 0;">Edit Payment Method</h1>
            <p style="color: #6B7280; margin: 0;">Update payment method settings and configuration</p>
        </div>
        <div style="display: flex; gap: 12px; align-items: center;">
            <div class="status-badge {{ $paymentMethod->is_active ? 'active' : 'inactive' }}">
                <i class="fas fa-{{ $paymentMethod->is_active ? 'check-circle' : 'times-circle' }}"></i>
                {{ $paymentMethod->is_active ? 'Active' : 'Inactive' }}
            </div>
            <a href="{{ route('admin.payment-methods.index') }}" class="btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Back to List
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="form-card">
        <div class="form-header">
            <h2 class="form-title">{{ $paymentMethod->name }}</h2>
            <p class="form-subtitle">{{ $paymentMethod->description }}</p>
        </div>

        <form action="{{ route('admin.payment-methods.update', $paymentMethod) }}" method="POST" enctype="multipart/form-data" class="form-body">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="form-section">
                <h3 class="section-title">Basic Information</h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label required">Name</label>
                        <input type="text" name="name" class="form-input" value="{{ old('name', $paymentMethod->name) }}"
                               placeholder="e.g., Transfer Bank BCA" required>
                        @error('name')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">Display name for the payment method</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Code</label>
                        <input type="text" name="code" class="form-input" value="{{ old('code', $paymentMethod->code) }}"
                               placeholder="e.g., bank_bca" required>
                        @error('code')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">Unique identifier (lowercase, underscore only)</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Type</label>
                        <select name="type" class="form-input" id="paymentType" required>
                            <option value="">Select Type</option>
                            <option value="manual" {{ old('type', $paymentMethod->type) === 'manual' ? 'selected' : '' }}>Manual</option>
                            <option value="tripay" {{ old('type', $paymentMethod->type) === 'tripay' ? 'selected' : '' }}>TriPay</option>
                            <option value="midtrans" {{ old('type', $paymentMethod->type) === 'midtrans' ? 'selected' : '' }}>Midtrans</option>
                            <option value="xendit" {{ old('type', $paymentMethod->type) === 'xendit' ? 'selected' : '' }}>Xendit</option>
                        </select>
                        @error('type')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Category</label>
                        <select name="category" class="form-input" required>
                            <option value="">Select Category</option>
                            <option value="bank_transfer" {{ old('category', $paymentMethod->category) === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                            <option value="e_wallet" {{ old('category', $paymentMethod->category) === 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                            <option value="qris" {{ old('category', $paymentMethod->category) === 'qris' ? 'selected' : '' }}>QRIS</option>
                            <option value="virtual_account" {{ old('category', $paymentMethod->category) === 'virtual_account' ? 'selected' : '' }}>Virtual Account</option>
                            <option value="credit_card" {{ old('category', $paymentMethod->category) === 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                        </select>
                        @error('category')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label class="form-label">Description</label>
                        <textarea name="description" class="form-input form-textarea"
                                  placeholder="Brief description of the payment method">{{ old('description', $paymentMethod->description) }}</textarea>
                        @error('description')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Display Settings -->
            <div class="form-section">
                <h3 class="section-title">Display Settings</h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Icon Class</label>
                        <input type="text" name="icon" class="form-input" value="{{ old('icon', $paymentMethod->icon) }}"
                               placeholder="e.g., fas fa-university">
                        @error('icon')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">FontAwesome icon class</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Logo Image</label>

                        @if($paymentMethod->logo)
                            <div class="current-logo">
                                <img src="{{ asset($paymentMethod->logo) }}" alt="{{ $paymentMethod->name }}">
                                <div>
                                    <div style="font-weight: 600; font-size: 14px;">Current Logo</div>
                                    <div style="font-size: 12px; color: #6B7280;">{{ basename($paymentMethod->logo) }}</div>
                                </div>
                            </div>
                        @endif

                        <div class="file-upload">
                            <input type="file" name="logo" accept="image/*" id="logoUpload">
                            <div class="file-upload-label">
                                <i class="fas fa-upload"></i>
                                {{ $paymentMethod->logo ? 'Change logo image' : 'Choose logo image' }}
                            </div>
                        </div>
                        @error('logo')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">PNG, JPG, GIF up to 2MB</div>
                        <img id="logoPreview" class="preview-image" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Sort Order</label>
                        <input type="number" name="sort_order" class="form-input" value="{{ old('sort_order', $paymentMethod->sort_order) }}"
                               min="0" required>
                        @error('sort_order')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">Lower numbers appear first</div>
                    </div>
                </div>
            </div>

            <!-- Fee Settings -->
            <div class="form-section">
                <h3 class="section-title">Fee & Limits</h3>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label required">Fee Percentage (%)</label>
                        <input type="number" name="fee_percentage" class="form-input" value="{{ old('fee_percentage', $paymentMethod->fee_percentage) }}"
                               min="0" max="100" step="0.01" required>
                        @error('fee_percentage')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Fixed Fee (IDR)</label>
                        <input type="number" name="fee_fixed" class="form-input" value="{{ old('fee_fixed', $paymentMethod->fee_fixed) }}"
                               min="0" step="0.01" required>
                        @error('fee_fixed')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label required">Minimum Amount (IDR)</label>
                        <input type="number" name="min_amount" class="form-input" value="{{ old('min_amount', $paymentMethod->min_amount) }}"
                               min="0" step="0.01" required>
                        @error('min_amount')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Maximum Amount (IDR)</label>
                        <input type="number" name="max_amount" class="form-input" value="{{ old('max_amount', $paymentMethod->max_amount) }}"
                               min="0" step="0.01">
                        @error('max_amount')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                        <div class="form-help">Leave empty for no limit</div>
                    </div>
                </div>
            </div>

            <!-- Status Settings -->
            <div class="form-section">
                <h3 class="section-title">Status Settings</h3>

                <div class="checkbox-group">
                    <input type="checkbox" name="is_active" class="checkbox-input" id="isActive"
                           value="1" {{ old('is_active', $paymentMethod->is_active) ? 'checked' : '' }}>
                    <label for="isActive" class="checkbox-label">Active</label>
                </div>

                <div class="checkbox-group">
                    <input type="checkbox" name="is_manual" class="checkbox-input" id="isManual"
                           value="1" {{ old('is_manual', $paymentMethod->is_manual) ? 'checked' : '' }}>
                    <label for="isManual" class="checkbox-label">Manual Payment</label>
                </div>
            </div>

            <!-- Manual Payment Configuration -->
            <div class="config-section" id="manualConfig">
                <h4 style="font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">Manual Payment Configuration</h4>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Bank Name</label>
                        <input type="text" name="bank_name" class="form-input"
                               value="{{ old('bank_name', $paymentMethod->manual_config['bank_name'] ?? '') }}"
                               placeholder="e.g., Bank Central Asia">
                        @error('bank_name')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Account Number</label>
                        <input type="text" name="account_number" class="form-input"
                               value="{{ old('account_number', $paymentMethod->manual_config['account_number'] ?? '') }}"
                               placeholder="e.g., **********">
                        @error('account_number')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Account Name</label>
                        <input type="text" name="account_name" class="form-input"
                               value="{{ old('account_name', $paymentMethod->manual_config['account_name'] ?? '') }}"
                               placeholder="e.g., PT TiXara Indonesia">
                        @error('account_name')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label class="form-label">Bank Code</label>
                        <input type="text" name="bank_code" class="form-input"
                               value="{{ old('bank_code', $paymentMethod->manual_config['bank_code'] ?? '') }}"
                               placeholder="e.g., BCA">
                        @error('bank_code')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label class="form-label">Payment Instructions</label>
                        <textarea name="instructions" class="form-input form-textarea"
                                  placeholder="Instructions for customers on how to make payment">{{ old('instructions', $paymentMethod->instructions) }}</textarea>
                        @error('instructions')
                            <div class="form-error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Gateway Configuration -->
            <div class="config-section" id="tripayConfig">
                <h4 style="font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">TriPay Configuration</h4>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">API Key</label>
                        <input type="text" name="tripay_api_key" class="form-input"
                               value="{{ old('tripay_api_key', $paymentMethod->config['api_key'] ?? '') }}"
                               placeholder="Your TriPay API Key">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Private Key</label>
                        <input type="text" name="tripay_private_key" class="form-input"
                               value="{{ old('tripay_private_key', $paymentMethod->config['private_key'] ?? '') }}"
                               placeholder="Your TriPay Private Key">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Merchant Code</label>
                        <input type="text" name="tripay_merchant_code" class="form-input"
                               value="{{ old('tripay_merchant_code', $paymentMethod->config['merchant_code'] ?? '') }}"
                               placeholder="Your TriPay Merchant Code">
                    </div>
                </div>
            </div>

            <div class="config-section" id="midtransConfig">
                <h4 style="font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">Midtrans Configuration</h4>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Server Key</label>
                        <input type="text" name="midtrans_server_key" class="form-input"
                               value="{{ old('midtrans_server_key', $paymentMethod->config['server_key'] ?? '') }}"
                               placeholder="Your Midtrans Server Key">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Client Key</label>
                        <input type="text" name="midtrans_client_key" class="form-input"
                               value="{{ old('midtrans_client_key', $paymentMethod->config['client_key'] ?? '') }}"
                               placeholder="Your Midtrans Client Key">
                    </div>
                </div>
            </div>

            <div class="config-section" id="xenditConfig">
                <h4 style="font-size: 16px; font-weight: 600; margin: 0 0 16px 0;">Xendit Configuration</h4>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Secret Key</label>
                        <input type="text" name="xendit_secret_key" class="form-input"
                               value="{{ old('xendit_secret_key', $paymentMethod->config['secret_key'] ?? '') }}"
                               placeholder="Your Xendit Secret Key">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <div>
                    <form action="{{ route('admin.payment-methods.destroy', $paymentMethod) }}" method="POST"
                          style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this payment method?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn-danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </form>
                </div>

                <div style="display: flex; gap: 12px;">
                    <a href="{{ route('admin.payment-methods.index') }}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i>
                        Update Payment Method
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentType = document.getElementById('paymentType');
    const isManual = document.getElementById('isManual');
    const logoUpload = document.getElementById('logoUpload');
    const logoPreview = document.getElementById('logoPreview');

    // Handle payment type change
    function updateConfigSections() {
        const type = paymentType.value;
        const manual = isManual.checked;

        // Hide all config sections
        document.querySelectorAll('.config-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show relevant config section
        if (manual || type === 'manual') {
            document.getElementById('manualConfig').classList.add('active');
            if (type === 'manual') {
                isManual.checked = true;
            }
        } else if (type === 'tripay') {
            document.getElementById('tripayConfig').classList.add('active');
        } else if (type === 'midtrans') {
            document.getElementById('midtransConfig').classList.add('active');
        } else if (type === 'xendit') {
            document.getElementById('xenditConfig').classList.add('active');
        }
    }

    paymentType.addEventListener('change', updateConfigSections);
    isManual.addEventListener('change', updateConfigSections);

    // Handle logo preview
    logoUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                logoPreview.src = e.target.result;
                logoPreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            logoPreview.style.display = 'none';
        }
    });

    // Initialize on page load
    updateConfigSections();
});
</script>
@endpush

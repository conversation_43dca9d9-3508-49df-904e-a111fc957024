@php
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#F59E0B';
    $secondaryColor = $config['secondary_color'] ?? '#EC4899';
    $backgroundColor = $config['background_color'] ?? '#7C3AED';
    $textColor = $config['text_color'] ?? '#ffffff';
@endphp

<div class="ticket-container festival-theme" style="
    background: linear-gradient(135deg, {{ $backgroundColor }} 0%, {{ $secondaryColor }} 50%, {{ $primaryColor }} 100%);
    color: {{ $textColor }};
    font-family: 'Poppins', sans-serif;
    width: 850px;
    height: 350px;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.25);
">
    <!-- Animated Background Elements -->
    <div style="
        position: absolute;
        top: -50px;
        left: -50px;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, {{ $primaryColor }}40 0%, transparent 70%);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    "></div>
    
    <div style="
        position: absolute;
        bottom: -30px;
        right: -30px;
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, {{ $secondaryColor }}40 0%, transparent 70%);
        border-radius: 50%;
        animation: float 4s ease-in-out infinite reverse;
    "></div>
    
    <!-- Music Wave Pattern -->
    <div style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: repeating-linear-gradient(
            90deg,
            transparent 0px,
            {{ $primaryColor }}20 10px,
            transparent 20px,
            {{ $secondaryColor }}20 30px,
            transparent 40px
        );
        opacity: 0.3;
    "></div>
    
    <!-- Header -->
    <div style="
        padding: 32px 32px 24px 32px;
        text-align: center;
        position: relative;
        z-index: 2;
    ">
        <div style="
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 16px;
        ">
            <div style="
                width: 40px;
                height: 40px;
                background: {{ $primaryColor }};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
                box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            ">
                <i class="fas fa-music" style="color: white; font-size: 18px;"></i>
            </div>
            
            <h1 style="
                font-size: 28px;
                font-weight: 800;
                margin: 0;
                color: {{ $textColor }};
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                letter-spacing: 1px;
            ">{{ $ticket['event']['title'] ?? 'Festival Event' }}</h1>
            
            <div style="
                width: 40px;
                height: 40px;
                background: {{ $secondaryColor }};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 16px;
                box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            ">
                <i class="fas fa-star" style="color: white; font-size: 18px;"></i>
            </div>
        </div>
        
        <p style="
            font-size: 16px;
            margin: 0;
            color: {{ $textColor }};
            opacity: 0.9;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 3px;
        ">🎵 Festival Ticket 🎵</p>
    </div>
    
    <!-- Content -->
    <div style="
        padding: 0 32px 24px 32px;
        display: flex;
        gap: 32px;
        position: relative;
        z-index: 2;
    ">
        <!-- Left Section -->
        <div style="flex: 2;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 20px;
            ">
                <div style="
                    background: rgba(255,255,255,0.1);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                ">
                    <h3 style="
                        font-size: 12px;
                        font-weight: 700;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">🗓️ Date & Time</h3>
                    <p style="
                        font-size: 18px;
                        font-weight: 700;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('M j, Y') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: rgba(255,255,255,0.8);
                        font-weight: 500;
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('l, g:i A') }}</p>
                </div>
                
                <div style="
                    background: rgba(255,255,255,0.1);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                ">
                    <h3 style="
                        font-size: 12px;
                        font-weight: 700;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">🎪 Venue</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['event']['venue_name'] ?? 'Festival Grounds' }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: rgba(255,255,255,0.8);
                        font-weight: 500;
                    ">{{ $ticket['event']['city'] ?? 'Location TBA' }}</p>
                </div>
            </div>
            
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
            ">
                <div style="
                    background: rgba(255,255,255,0.1);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                ">
                    <h3 style="
                        font-size: 12px;
                        font-weight: 700;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">🎤 Attendee</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['attendee_name'] ?? $ticket['buyer']['name'] ?? 'Festival Goer' }}</p>
                    <p style="
                        font-size: 12px;
                        margin: 0;
                        color: rgba(255,255,255,0.7);
                        font-weight: 500;
                    ">{{ $ticket['attendee_email'] ?? $ticket['buyer']['email'] ?? 'N/A' }}</p>
                </div>
                
                <div style="
                    background: rgba(255,255,255,0.1);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                ">
                    <h3 style="
                        font-size: 12px;
                        font-weight: 700;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">💰 Price</h3>
                    <p style="
                        font-size: 20px;
                        font-weight: 800;
                        margin: 0;
                        color: {{ $primaryColor }};
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                    ">Rp {{ number_format($ticket['price'] ?? 0, 0, ',', '.') }}</p>
                </div>
            </div>
            
            <!-- Ticket Number -->
            <div style="
                margin-top: 20px;
                padding: 16px;
                background: linear-gradient(90deg, {{ $primaryColor }}30 0%, {{ $secondaryColor }}30 100%);
                border-radius: 12px;
                border: 2px solid rgba(255,255,255,0.3);
                backdrop-filter: blur(10px);
            ">
                <h3 style="
                    font-size: 12px;
                    font-weight: 700;
                    color: {{ $textColor }};
                    margin: 0 0 8px 0;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                ">🎫 Festival Pass ID</h3>
                <p style="
                    font-size: 18px;
                    font-weight: 800;
                    font-family: 'Courier New', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                    letter-spacing: 2px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                ">{{ $ticket['ticket_number'] ?? 'FEST-000000' }}</p>
            </div>
        </div>
        
        <!-- Right Section - QR Code -->
        <div style="
            width: 220px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            padding: 24px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.2);
        ">
            <div style="
                background: white;
                padding: 16px;
                border-radius: 16px;
                margin-bottom: 16px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
                border: 4px solid {{ $primaryColor }};
            ">
                {!! QrCode::size(120)->generate($ticket['qr_code'] ?? $ticket['ticket_number'] ?? 'INVALID') !!}
            </div>
            
            <div style="text-align: center;">
                <p style="
                    font-size: 14px;
                    font-weight: 700;
                    color: {{ $primaryColor }};
                    margin: 0 0 8px 0;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                ">🎵 Scan to Rock! 🎵</p>
                <p style="
                    font-size: 12px;
                    color: rgba(255,255,255,0.8);
                    margin: 0;
                    line-height: 1.4;
                    font-weight: 500;
                ">Show this QR code<br>at the festival entrance</p>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 16px 32px;
        background: linear-gradient(90deg, {{ $primaryColor }}20 0%, {{ $secondaryColor }}20 100%);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255,255,255,0.2);
        text-align: center;
    ">
        <p style="
            font-size: 12px;
            color: rgba(255,255,255,0.8);
            margin: 0;
            font-weight: 600;
            letter-spacing: 0.5px;
        ">🎪 One-time festival entry • Keep the music alive • Powered by Tixara 🎪</p>
    </div>
    
    <!-- Decorative Music Notes -->
    <div style="
        position: absolute;
        top: 20%;
        left: 10%;
        font-size: 24px;
        color: {{ $primaryColor }};
        opacity: 0.3;
        animation: bounce 2s infinite;
    ">♪</div>
    
    <div style="
        position: absolute;
        top: 60%;
        right: 15%;
        font-size: 20px;
        color: {{ $secondaryColor }};
        opacity: 0.3;
        animation: bounce 2s infinite 0.5s;
    ">♫</div>
    
    <div style="
        position: absolute;
        top: 40%;
        left: 5%;
        font-size: 18px;
        color: {{ $textColor }};
        opacity: 0.2;
        animation: bounce 2s infinite 1s;
    ">♬</div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');

.ticket-container {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@media print {
    .ticket-container {
        width: 850px !important;
        height: 350px !important;
        margin: 0 !important;
        box-shadow: none !important;
    }
}
</style>

@php
    // Configuration with fallbacks
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#059669';
    $secondaryColor = $config['secondary_color'] ?? '#D1D5DB';
    $backgroundColor = $config['background_color'] ?? '#F9FAFB';
    $textColor = $config['text_color'] ?? '#1F2937';

    // Helper function to safely get nested values
    function getTicketValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket[$key] ?? $default;
        } elseif (is_object($ticket)) {
            return $ticket->{$key} ?? $default;
        }
        return $default;
    }

    function getEventValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['event'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->event)) {
            return $ticket->event->{$key} ?? $default;
        }
        return $default;
    }

    function getBuyerValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['buyer'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->buyer)) {
            return $ticket->buyer->{$key} ?? $default;
        }
        return $default;
    }
@endphp

<div class="ticket-container classic-ticket" style="
    background: {{ $backgroundColor }};
    color: {{ $textColor }};
    font-family: 'Georgia', serif;
    width: 700px;
    height: 350px;
    position: relative;
    border: 3px dashed {{ $primaryColor }};
    border-radius: 8px;
    overflow: hidden;
">
    <!-- Corner Decorations -->
    <div style="
        position: absolute;
        top: -15px;
        left: -15px;
        width: 30px;
        height: 30px;
        background: {{ $primaryColor }};
        transform: rotate(45deg);
    "></div>

    <div style="
        position: absolute;
        top: -15px;
        right: -15px;
        width: 30px;
        height: 30px;
        background: {{ $primaryColor }};
        transform: rotate(45deg);
    "></div>

    <div style="
        position: absolute;
        bottom: -15px;
        left: -15px;
        width: 30px;
        height: 30px;
        background: {{ $primaryColor }};
        transform: rotate(45deg);
    "></div>

    <div style="
        position: absolute;
        bottom: -15px;
        right: -15px;
        width: 30px;
        height: 30px;
        background: {{ $primaryColor }};
        transform: rotate(45deg);
    "></div>

    <!-- Header -->
    <div style="
        background: {{ $primaryColor }};
        color: white;
        padding: 20px;
        text-align: center;
        border-bottom: 3px solid {{ $secondaryColor }};
    ">
        <h1 style="
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 8px 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        ">{{ getEventValue($ticket, 'title', 'Event Title') }}</h1>
        <p style="
            font-size: 16px;
            margin: 0;
            opacity: 0.9;
            font-style: italic;
        ">ADMIT ONE • KEEP THIS TICKET</p>
    </div>

    <!-- Main Content -->
    <div class="main-content" style="
        padding: 24px;
        display: flex;
        gap: 24px;
        height: calc(100% - 120px);
    ">
        <!-- Left Section -->
        <div style="flex: 2;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            ">
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: bold;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">Event Date</h3>
                    @php
                        $startDate = getEventValue($ticket, 'start_date', now());
                        $parsedDate = \Carbon\Carbon::parse($startDate);
                    @endphp
                    <p style="
                        font-size: 18px;
                        font-weight: bold;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $parsedDate->format('M j, Y') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ $parsedDate->format('l, g:i A') }}</p>
                </div>

                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: bold;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">Venue</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: bold;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ getEventValue($ticket, 'venue_name', 'TBA') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ getEventValue($ticket, 'city', 'Location TBA') }}</p>
                </div>
            </div>

            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
            ">
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: bold;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">Attendee</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: bold;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ getTicketValue($ticket, 'attendee_name', getBuyerValue($ticket, 'name', 'Guest')) }}</p>
                    <p style="
                        font-size: 12px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ getTicketValue($ticket, 'attendee_email', getBuyerValue($ticket, 'email', 'N/A')) }}</p>
                </div>

                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: bold;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    ">Ticket Price</h3>
                    @php
                        $price = getTicketValue($ticket, 'price', 0);
                    @endphp
                    <p style="
                        font-size: 20px;
                        font-weight: bold;
                        margin: 0;
                        color: {{ $primaryColor }};
                    ">Rp {{ number_format($price, 0, ',', '.') }}</p>
                </div>
            </div>

            <!-- Ticket Number -->
            <div style="
                background: {{ $secondaryColor }};
                padding: 12px;
                border-radius: 6px;
                border: 2px solid {{ $primaryColor }};
            ">
                <h3 style="
                    font-size: 12px;
                    font-weight: bold;
                    color: {{ $primaryColor }};
                    margin: 0 0 4px 0;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                ">Ticket Number</h3>
                <p style="
                    font-size: 18px;
                    font-weight: bold;
                    font-family: 'Courier New', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                    letter-spacing: 2px;
                ">{{ getTicketValue($ticket, 'ticket_number', getTicketValue($ticket, 'ticket_code', 'TIX-000000')) }}</p>
            </div>
        </div>

        <!-- Right Section - QR Code -->
        <div style="
            width: 180px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 3px dashed {{ $primaryColor }};
            padding-left: 20px;
        ">
            <div style="
                background: white;
                padding: 16px;
                border-radius: 8px;
                border: 3px solid {{ $primaryColor }};
                margin-bottom: 16px;
            ">
                @php
                    $qrData = getTicketValue($ticket, 'qr_code',
                        getTicketValue($ticket, 'ticket_number',
                            getTicketValue($ticket, 'ticket_code', 'INVALID')
                        )
                    );
                @endphp
                @if(class_exists('SimpleSoftwareIO\QrCode\Facades\QrCode'))
                    {!! QrCode::size(120)->generate($qrData) !!}
                @else
                    <div style="
                        width: 120px;
                        height: 120px;
                        background: #f3f4f6;
                        border: 2px dashed #d1d5db;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        color: #6b7280;
                        text-align: center;
                    ">
                        QR Code<br>{{ $qrData }}
                    </div>
                @endif
            </div>

            <div style="text-align: center;">
                <p style="
                    font-size: 12px;
                    font-weight: bold;
                    color: {{ $primaryColor }};
                    margin: 0 0 4px 0;
                    text-transform: uppercase;
                ">Scan to Verify</p>
                <p style="
                    font-size: 10px;
                    color: #6B7280;
                    margin: 0;
                    line-height: 1.4;
                ">Present this QR code<br>at the venue entrance</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: {{ $secondaryColor }};
        padding: 8px 20px;
        border-top: 2px solid {{ $primaryColor }};
        text-align: center;
    ">
        <p style="
            font-size: 10px;
            color: #6B7280;
            margin: 0;
            font-style: italic;
        ">This ticket is valid for one-time entry only • Powered by Tixara • {{ now()->format('Y') }}</p>
    </div>

    <!-- Decorative Elements -->
    <div style="
        position: absolute;
        top: 50%;
        right: 200px;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        background: {{ $backgroundColor }};
        border-radius: 50%;
        border: 3px solid {{ $primaryColor }};
    "></div>

    <div style="
        position: absolute;
        top: 30%;
        right: 200px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background: {{ $primaryColor }};
        border-radius: 50%;
    "></div>

    <div style="
        position: absolute;
        top: 70%;
        right: 200px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background: {{ $primaryColor }};
        border-radius: 50%;
    "></div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Georgia:wght@400;700&display=swap');

.ticket-container {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    box-sizing: border-box;
}

.classic-ticket * {
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .ticket-container {
        width: 100% !important;
        max-width: 700px !important;
        height: auto !important;
        min-height: 350px !important;
    }

    .ticket-container .main-content {
        flex-direction: column !important;
        gap: 16px !important;
    }

    .ticket-container .qr-section {
        width: 100% !important;
        border-left: none !important;
        border-top: 3px dashed {{ $primaryColor }} !important;
        padding-left: 0 !important;
        padding-top: 20px !important;
        align-items: center !important;
    }
}

@media print {
    .ticket-container {
        width: 700px !important;
        height: 350px !important;
        margin: 0 !important;
        border: 3px dashed {{ $primaryColor }} !important;
        page-break-inside: avoid;
    }

    .ticket-container .main-content {
        flex-direction: row !important;
    }

    .ticket-container .qr-section {
        width: 180px !important;
        border-left: 3px dashed {{ $primaryColor }} !important;
        border-top: none !important;
        padding-left: 20px !important;
        padding-top: 0 !important;
    }
}

/* Error handling styles */
.qr-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-family: 'Courier New', monospace;
}
</style>

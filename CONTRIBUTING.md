# Contributing to TiXara

Thank you for your interest in contributing to TiXara! This document provides guidelines and information for contributors.

## 🤝 **How to Contribute**

### **Types of Contributions**
- 🐛 **Bug Reports** - Report issues you encounter
- 💡 **Feature Requests** - Suggest new features
- 📝 **Documentation** - Improve docs and guides
- 🔧 **Code Contributions** - Submit bug fixes and features
- 🎨 **Design** - UI/UX improvements
- 🧪 **Testing** - Add or improve tests

## 🚀 **Getting Started**

### **Development Setup**

1. **Fork the Repository**
```bash
git clone https://github.com/your-username/tixara.git
cd tixara
```

2. **Install Dependencies**
```bash
composer install
npm install
```

3. **Environment Setup**
```bash
cp .env.example .env.local
php artisan key:generate
```

4. **Database Setup**
```bash
php artisan migrate
php artisan db:seed
```

5. **Run Development Server**
```bash
php artisan serve
npm run dev
```

### **Development Workflow**

1. **Create Feature Branch**
```bash
git checkout -b feature/your-feature-name
```

2. **Make Changes**
   - Follow coding standards
   - Add tests for new features
   - Update documentation

3. **Test Your Changes**
```bash
php artisan test
npm run test
```

4. **Commit Changes**
```bash
git add .
git commit -m "feat(scope): description of changes"
```

5. **Push and Create PR**
```bash
git push origin feature/your-feature-name
```

## 📋 **Coding Standards**

### **PHP/Laravel Standards**
- Follow [PSR-12](https://www.php-fig.org/psr/psr-12/) coding standard
- Use Laravel best practices
- Write meaningful variable and method names
- Add PHPDoc comments for public methods

```php
/**
 * Generate unique boarding pass ID
 *
 * @param string $template Template name
 * @return string Generated boarding pass ID
 */
public function generateBoardingPassId(string $template = 'unix'): string
{
    // Implementation
}
```

### **JavaScript Standards**
- Use ES6+ features
- Follow consistent naming conventions
- Add JSDoc comments for functions
- Use async/await for promises

```javascript
/**
 * Validate ticket via API
 * @param {string} qrCode - QR code to validate
 * @returns {Promise<Object>} Validation result
 */
async function validateTicket(qrCode) {
    // Implementation
}
```

### **CSS/Styling**
- Use Tailwind CSS classes
- Follow mobile-first approach
- Maintain consistent spacing
- Use semantic class names

## 🧪 **Testing Guidelines**

### **Writing Tests**
- Write tests for all new features
- Include both positive and negative test cases
- Use descriptive test names
- Mock external dependencies

```php
/** @test */
public function staff_can_scan_assigned_organizer_tickets()
{
    // Arrange
    $ticket = Ticket::factory()->create([...]);
    
    // Act
    $result = $validationService->staffScanTicket($ticket->qr_code, $this->staff);
    
    // Assert
    $this->assertTrue($result['success']);
}
```

### **Running Tests**
```bash
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/TicketingSystemTest.php

# Run with coverage
php artisan test --coverage
```

## 📝 **Documentation**

### **Code Documentation**
- Add PHPDoc comments for all public methods
- Include parameter types and return types
- Explain complex logic with inline comments
- Update README.md for new features

### **API Documentation**
- Document all API endpoints
- Include request/response examples
- Specify authentication requirements
- Add error response codes

## 🎨 **UI/UX Guidelines**

### **Design Principles**
- **Mobile-first**: Design for mobile, enhance for desktop
- **Accessibility**: Follow WCAG guidelines
- **Consistency**: Use design system components
- **Performance**: Optimize for fast loading

### **Template Development**
When adding new boarding pass templates:

1. **Create Template File**
```bash
resources/views/templates/tickets/new-template.blade.php
```

2. **Add Template Prefix**
```php
// In BoardingPassService.php
private const TEMPLATE_PREFIXES = [
    'new' => 'NEW',
    // ... existing templates
];
```

3. **Update Validation**
```php
// In EventController.php
'boarding_pass_template' => 'required|in:unix,minimalist,...,new'
```

4. **Add Preview Route**
```php
Route::get('/templates/new-preview', function() {
    return view('templates.tickets.new-template', $sampleData);
});
```

## 🐛 **Bug Reports**

### **Before Reporting**
- Search existing issues
- Check if it's already fixed in latest version
- Reproduce the bug consistently

### **Bug Report Template**
```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. See error

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- PHP Version: [e.g. 8.1]
- Laravel Version: [e.g. 10.x]

**Screenshots**
If applicable, add screenshots
```

## 💡 **Feature Requests**

### **Feature Request Template**
```markdown
**Feature Description**
Clear description of the feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other solutions you've considered

**Additional Context**
Any other context or screenshots
```

## 🔄 **Pull Request Process**

### **PR Checklist**
- [ ] Code follows project standards
- [ ] Tests added for new features
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
- [ ] All tests pass
- [ ] No merge conflicts

### **PR Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests added/updated
- [ ] All tests pass
- [ ] Manual testing completed

## Screenshots
If applicable, add screenshots

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
```

## 🏷️ **Commit Message Guidelines**

### **Format**
```
type(scope): description

[optional body]

[optional footer]
```

### **Types**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

### **Examples**
```bash
feat(templates): add neon boarding pass template
fix(validation): resolve staff authorization issue
docs(readme): update installation instructions
test(tickets): add boarding pass generation tests
```

## 🎯 **Priority Areas**

We especially welcome contributions in these areas:

1. **Template System** - New boarding pass designs
2. **Mobile Experience** - PWA improvements
3. **Performance** - Optimization and caching
4. **Testing** - Increase test coverage
5. **Documentation** - User guides and API docs
6. **Accessibility** - WCAG compliance
7. **Internationalization** - Multi-language support

## 📞 **Getting Help**

- **Discord**: [TiXara Community](https://discord.gg/tixara)
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/your-username/tixara/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/tixara/discussions)

## 📄 **License**

By contributing to TiXara, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for contributing to TiXara! 🎫✨**

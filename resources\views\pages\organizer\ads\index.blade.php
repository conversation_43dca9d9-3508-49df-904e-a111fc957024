@extends('layouts.organizer')

@section('title', 'Advertisement Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Advertisement Management</h1>
            <p class="mb-0 text-muted">Manage your event advertisements and promotions</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('organizer.ads.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Create Ad
            </a>
            <a href="{{ route('organizer.ads.subscriptions.index') }}" class="btn btn-outline-success">
                <i class="fas fa-crown me-2"></i>Subscriptions
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Ads
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_ads']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Ads
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['active_ads']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Impressions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_impressions']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-eye fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Total Clicks
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_clicks']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Advertisements</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('organizer.ads.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="Search ads...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="paused" {{ request('status') == 'paused' ? 'selected' : '' }}>Paused</option>
                                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                <option value="expired" {{ request('status') == 'expired' ? 'selected' : '' }}>Expired</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="banner" {{ request('type') == 'banner' ? 'selected' : '' }}>Banner</option>
                                <option value="sidebar" {{ request('type') == 'sidebar' ? 'selected' : '' }}>Sidebar</option>
                                <option value="popup" {{ request('type') == 'popup' ? 'selected' : '' }}>Popup</option>
                                <option value="sponsored" {{ request('type') == 'sponsored' ? 'selected' : '' }}>Sponsored</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="event_id">Event</label>
                            <select class="form-control" id="event_id" name="event_id">
                                <option value="">All Events</option>
                                @foreach($events as $event)
                                    <option value="{{ $event->id }}" {{ request('event_id') == $event->id ? 'selected' : '' }}>
                                        {{ $event->title }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="sort">Sort By</label>
                            <select class="form-control" id="sort" name="sort">
                                <option value="created_at" {{ request('sort') == 'created_at' ? 'selected' : '' }}>Date Created</option>
                                <option value="impressions" {{ request('sort') == 'impressions' ? 'selected' : '' }}>Impressions</option>
                                <option value="clicks" {{ request('sort') == 'clicks' ? 'selected' : '' }}>Clicks</option>
                                <option value="ctr" {{ request('sort') == 'ctr' ? 'selected' : '' }}>CTR</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Ads Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Advertisement List</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-1"></i>Actions
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                        <i class="fas fa-play me-2"></i>Activate Selected
                    </a>
                    <a class="dropdown-item" href="#" onclick="bulkAction('pause')">
                        <i class="fas fa-pause me-2"></i>Pause Selected
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-2"></i>Delete Selected
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>Ad</th>
                            <th>Event</th>
                            <th>Type</th>
                            <th>Performance</th>
                            <th>Budget</th>
                            <th>Status</th>
                            <th>Period</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($ads as $ad)
                            <tr>
                                <td>
                                    <input type="checkbox" class="ad-checkbox" value="{{ $ad->id }}">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if($ad->image)
                                            <img src="{{ $ad->image }}" alt="{{ $ad->title }}" 
                                                 class="rounded me-3" width="60" height="40" style="object-fit: cover;">
                                        @else
                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                 style="width: 60px; height: 40px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        @endif
                                        <div>
                                            <div class="font-weight-bold">{{ $ad->title }}</div>
                                            <small class="text-muted">{{ Str::limit($ad->description, 50) }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if($ad->event)
                                        <a href="{{ route('organizer.events.show', $ad->event) }}" class="text-decoration-none">
                                            {{ Str::limit($ad->event->title, 30) }}
                                        </a>
                                    @else
                                        <span class="text-muted">All Events</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ ucfirst($ad->type) }}</span>
                                </td>
                                <td>
                                    <div class="small">
                                        <div><strong>{{ number_format($ad->impressions) }}</strong> impressions</div>
                                        <div><strong>{{ number_format($ad->clicks) }}</strong> clicks</div>
                                        <div class="text-muted">CTR: {{ $ad->impressions > 0 ? number_format(($ad->clicks / $ad->impressions) * 100, 2) : 0 }}%</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="small">
                                        @if($ad->daily_budget)
                                            <div>Daily: Rp {{ number_format($ad->daily_budget, 0, ',', '.') }}</div>
                                        @endif
                                        @if($ad->total_budget)
                                            <div>Total: Rp {{ number_format($ad->total_budget, 0, ',', '.') }}</div>
                                        @endif
                                        <div class="text-muted">Spent: Rp {{ number_format($ad->spent_amount, 0, ',', '.') }}</div>
                                    </div>
                                </td>
                                <td>
                                    @switch($ad->status)
                                        @case('active')
                                            <span class="badge badge-success">Active</span>
                                            @break
                                        @case('paused')
                                            <span class="badge badge-warning">Paused</span>
                                            @break
                                        @case('pending')
                                            <span class="badge badge-info">Pending</span>
                                            @break
                                        @case('rejected')
                                            <span class="badge badge-danger">Rejected</span>
                                            @break
                                        @case('expired')
                                            <span class="badge badge-secondary">Expired</span>
                                            @break
                                        @default
                                            <span class="badge badge-light">{{ ucfirst($ad->status) }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <div class="small">
                                        <div>{{ $ad->start_date->format('d M Y') }}</div>
                                        <div class="text-muted">{{ $ad->end_date->format('d M Y') }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('organizer.ads.show', $ad) }}" 
                                           class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('organizer.ads.edit', $ad) }}" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($ad->status === 'active')
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="pauseAd({{ $ad->id }})" title="Pause">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        @elseif($ad->status === 'paused')
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="resumeAd({{ $ad->id }})" title="Resume">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteAd({{ $ad->id }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-bullhorn fa-3x mb-3"></i>
                                        <p>No advertisements found</p>
                                        <a href="{{ route('organizer.ads.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Create Your First Ad
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($ads->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $ads->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.ad-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Bulk actions
function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.ad-checkbox:checked')).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Please select at least one advertisement.');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action} ${selectedIds.length} advertisement(s)?`)) {
        fetch(`/organizer/ads/bulk-${action}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ ad_ids: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while performing the action.');
        });
    }
}

// Individual actions
function pauseAd(adId) {
    if (confirm('Are you sure you want to pause this advertisement?')) {
        fetch(`/organizer/ads/${adId}/pause`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function resumeAd(adId) {
    if (confirm('Are you sure you want to resume this advertisement?')) {
        fetch(`/organizer/ads/${adId}/resume`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function deleteAd(adId) {
    if (confirm('Are you sure you want to delete this advertisement? This action cannot be undone.')) {
        fetch(`/organizer/ads/${adId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>
@endpush
@endsection

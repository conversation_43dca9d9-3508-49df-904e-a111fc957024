<!-- Header Component with Alpine.js Data -->
<header class="fixed top-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-pasta-cream/50 dark:border-gray-700 shadow-sm"
        x-data="{
            // Mobile menu state
            mobileMenuOpen: false,
            searchOpen: false,

            // Search functionality
            searchQuery: '',
            searchResults: [],
            searchLoading: false,

            // Simple theme management (dark/light only)
            darkMode: localStorage.getItem('theme') === 'dark' || false,

            // Time display
            currentTime: '',

            // Notification count
            notificationCount: 0,

            // Initialize component
            init() {
                this.updateTime();
                this.applyTheme();
                this.initializeNotifications();

                // Update time every second
                setInterval(() => {
                    this.updateTime();
                }, 1000);
            },

            // Update current time
            updateTime() {
                const now = new Date();
                this.currentTime = now.toLocaleTimeString('id-ID', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            },

            // Simple theme toggle function
            toggleDarkMode() {
                this.darkMode = !this.darkMode;
                this.applyTheme();
                this.saveThemeSettings();

                // Show simple notification
                this.showSimpleNotification(
                    this.darkMode ? 'Mode gelap diaktifkan' : 'Mode terang diaktifkan'
                );
            },

            applyTheme() {
                const root = document.documentElement;

                // Apply dark mode class
                if (this.darkMode) {
                    root.classList.add('dark');
                    root.setAttribute('data-theme', 'dark');
                } else {
                    root.classList.remove('dark');
                    root.setAttribute('data-theme', 'light');
                }
            },

            saveThemeSettings() {
                localStorage.setItem('theme', this.darkMode ? 'dark' : 'light');
            },

            showSimpleNotification(message) {
                // Remove existing notification
                const existingNotification = document.getElementById('headerNotification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                // Create notification
                const notification = document.createElement('div');
                notification.id = 'headerNotification';
                notification.className = 'fixed top-20 right-4 z-50 px-4 py-2 bg-green-500 text-white rounded-lg shadow-lg transform translate-x-full transition-transform duration-300';
                notification.textContent = message;

                document.body.appendChild(notification);

                // Show notification
                setTimeout(() => {
                    notification.classList.remove('translate-x-full');
                }, 100);

                // Hide notification after 3 seconds
                setTimeout(() => {
                    notification.classList.add('translate-x-full');
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }, 3000);
            },

            // Search functionality
            async searchEvents() {
                if (this.searchQuery.length < 2) {
                    this.searchResults = [];
                    return;
                }

                this.searchLoading = true;

                try {
                    const response = await fetch(`/api/search?q=${encodeURIComponent(this.searchQuery)}`, {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.searchResults = data.results || [];
                    } else {
                        this.searchResults = [];
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    this.searchResults = [];
                } finally {
                    this.searchLoading = false;
                }
            },

            // Notification management
            initializeNotifications() {
                // Initialize notification count from server or localStorage
                this.updateNotificationCount();

                // Listen for new notifications
                window.addEventListener('newNotification', (event) => {
                    this.notificationCount = event.detail.count || 0;
                    this.updateNotificationBadge();
                });
            },

            updateNotificationCount() {
                // This would typically fetch from server
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    const count = parseInt(badge.textContent) || 0;
                    this.notificationCount = count;
                    this.updateNotificationBadge();
                }
            },

            updateNotificationBadge() {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    if (this.notificationCount > 0) {
                        badge.style.display = 'flex';
                        badge.textContent = this.notificationCount > 99 ? '99+' : this.notificationCount;
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }
        }"
        x-init="init()">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex items-center space-x-3">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 group">
                    <div class="relative w-12 h-12 bg-gradient-to-br from-primary via-pasta-sage to-accent rounded-2xl flex items-center justify-center group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                        <div class="absolute inset-0 bg-gradient-to-br from-pasta-cream/20 to-pasta-mint/20 rounded-2xl"></div>
                        <svg class="w-7 h-7 text-white relative z-10 drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-br from-pasta-peach to-pasta-salmon rounded-full animate-pulse"></div>
                    </div>
                    <div class="flex flex-col">
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-primary via-pasta-sage to-accent bg-clip-text text-transparent group-hover:from-accent group-hover:to-primary transition-all duration-300">
                            TiXara
                        </h1>
                        <span class="text-xs text-gray-500 dark:text-gray-400 font-medium tracking-wide"></span>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-6">
 <div></div>
                <!-- Panduan Dropdown -->
                <div class="relative" x-data="{ showGuideMenu: false }">
                    <button @click="showGuideMenu = !showGuideMenu"
                            class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group flex items-center space-x-1 {{ request()->routeIs('guide.*') ? 'text-primary dark:text-primary' : '' }}">
                        <span class="relative z-10">Menu</span>
                        <svg class="w-4 h-4 transition-transform duration-300" :class="{ 'rotate-180': showGuideMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                        @if(request()->routeIs('guide.*'))
                            <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                        @endif
                    </button>
                    <!--Beranda Dropdown -->
                        <div x-show="showGuideMenu"
                         @click.away="showGuideMenu = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm">
                        <div class="px-4 py-2 border-b border-pasta-cream/50 dark:border-gray-600">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                <i data-lucide="book-open" class="w-4 h-4 text-primary"></i>
                                <span>Menu Utama</span>
                            </h3>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('home') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="user-plus" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Home</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Home</p>
                                </div>
                            </a>
                            <a href="{{ route('tickets.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="user-plus" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Beli Tiket</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Beli Tiket</p>
                                </div>
                            </a>
                            <a href="{{ route('tickets.authenticity-check') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="shield-check" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Cek Keaslian Tiket</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Verifikasi Authenticity</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Panduan Dropdown -->
                <div class="relative" x-data="{ showGuideMenu: false }">
                    <button @click="showGuideMenu = !showGuideMenu"
                            class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group flex items-center space-x-1 {{ request()->routeIs('guide.*') ? 'text-primary dark:text-primary' : '' }}">
                        <span class="relative z-10">Panduan</span>
                        <svg class="w-4 h-4 transition-transform duration-300" :class="{ 'rotate-180': showGuideMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                        @if(request()->routeIs('guide.*'))
                            <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                        @endif
                    </button>

                    <!-- Guide Dropdown -->
                    <div x-show="showGuideMenu"
                         @click.away="showGuideMenu = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm">
                        <div class="px-4 py-2 border-b border-pasta-cream/50 dark:border-gray-600">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                <i data-lucide="book-open" class="w-4 h-4 text-primary"></i>
                                <span>Panduan & Bantuan</span>
                            </h3>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('guide.event-creator') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="user-plus" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Event Creator</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Panduan lengkap membuat event</p>
                                </div>
                            </a>
                            <a href="{{ route('guide.organizer') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="users" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Organizer</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Tips sukses organizer</p>
                                </div>
                            </a>
                            <a href="{{ route('guide.buyer') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="shopping-cart" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Pembeli</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Cara membeli tiket</p>
                                </div>
                            </a>
                            <div class="border-t border-pasta-cream/50 dark:border-gray-600 mt-1 pt-1">
                                <a href="{{ route('guide.faq') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <i data-lucide="help-circle" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                    <span>FAQ</span>
                                </a>
                                <a href="{{ route('guide.help-center') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <i data-lucide="headphones" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                    <span>Help Center</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ArtPosure Dropdown -->
                <div class="relative" x-data="{ showArtPosureMenu: false }">
                    <button @click="showArtPosureMenu = !showArtPosureMenu"
                            class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group flex items-center space-x-1 {{ request()->routeIs('artposure.*') ? 'text-primary dark:text-primary' : '' }}">
                        <span class="relative z-10">ArtPosure</span>
                        <svg class="w-4 h-4 transition-transform duration-300" :class="{ 'rotate-180': showArtPosureMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                        @if(request()->routeIs('artposure.*'))
                            <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                        @endif
                    </button>

                    <!-- ArtPosure Dropdown -->
                    <div x-show="showArtPosureMenu"
                         @click.away="showArtPosureMenu = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm">
                        <div class="px-4 py-2 border-b border-pasta-cream/50 dark:border-gray-600">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                <i data-lucide="megaphone" class="w-4 h-4 text-purple-600"></i>
                                <span>ArtPosure Marketing</span>
                            </h3>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Layanan marketing premium untuk event</p>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('artposure.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="home" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Overview</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Semua layanan ArtPosure</p>
                                </div>
                            </a>
                            <a href="{{ route('artposure.socmed') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-all duration-300 group">
                                <i data-lucide="share-2" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Social Media</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Marketing di sosial media</p>
                                </div>
                            </a>
                            <a href="{{ route('artposure.website') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 group">
                                <i data-lucide="globe" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Website Placement</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Iklan di website TiXara</p>
                                </div>
                            </a>
                            <a href="{{ route('artposure.package') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-cyan-50 dark:hover:bg-cyan-900/20 hover:text-cyan-600 dark:hover:text-cyan-400 transition-all duration-300 group">
                                <i data-lucide="package" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Packages</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Paket marketing lengkap</p>
                                </div>
                            </a>
                            <a href="{{ route('artposure.addon') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-purple-50 dark:hover:bg-purple-900/20 hover:text-purple-600 dark:hover:text-purple-400 transition-all duration-300 group">
                                <i data-lucide="plus-circle" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Add-ons</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Layanan tambahan</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Support Dropdown -->
                <div class="relative" x-data="{ showSupportMenu: false }">
                    <button @click="showSupportMenu = !showSupportMenu"
                            class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group flex items-center space-x-1 {{ request()->routeIs(['contact.*', 'reports.*', 'feedback.*']) ? 'text-primary dark:text-primary' : '' }}">
                        <span class="relative z-10">Support</span>
                        <svg class="w-4 h-4 transition-transform duration-300" :class="{ 'rotate-180': showSupportMenu }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                        <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                        @if(request()->routeIs(['contact.*', 'reports.*', 'feedback.*']))
                            <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                        @endif
                    </button>

                    <!-- Support Dropdown -->
                    <div x-show="showSupportMenu"
                         @click.away="showSupportMenu = false"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm">
                        <div class="px-4 py-2 border-b border-pasta-cream/50 dark:border-gray-600">
                            <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                <i data-lucide="headphones" class="w-4 h-4 text-blue-600"></i>
                                <span>Bantuan & Support</span>
                            </h3>
                        </div>
                        <div class="py-1">
                            <a href="{{ route('contact.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                <i data-lucide="mail" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Hubungi Kami</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Kontak dan informasi</p>
                                </div>
                            </a>
                            <a href="{{ route('reports.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 transition-all duration-300 group">
                                <i data-lucide="flag" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Laporan Pengaduan</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Laporkan masalah atau pelanggaran</p>
                                </div>
                            </a>
                            <a href="{{ route('feedback.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 group">
                                <i data-lucide="message-square" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Kritik & Saran</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Berikan feedback untuk kami</p>
                                </div>
                            </a>
                            <a href="{{ route('developer.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 group">                            <!-- Developer -->
                            <i data-lucide="message-square" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                                <div>
                                    <span class="font-medium">Developer</span>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">Halaman Pengembang</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="relative" x-data="{ showSearchResults: false }">
                    <div class="relative">
                        <input type="text"
                               x-model="searchQuery"
                               @input.debounce.300ms="searchEvents()"
                               @focus="showSearchResults = true"
                               @click.away="showSearchResults = false"
                               placeholder="Cari event..."
                               class="w-64 px-4 py-2 pl-10 pr-4 rounded-xl border-2 border-pasta-cream/50 dark:border-gray-600 bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-300 backdrop-blur-sm">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        <div x-show="searchLoading" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        </div>
                    </div>

                    <!-- Search Results Dropdown -->
                    <div x-show="showSearchResults && (searchResults.length > 0 || searchQuery.length > 0)"
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 transform scale-95"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-150"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-95"
                         class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm max-h-64 overflow-y-auto">

                        <template x-if="searchResults.length === 0 && searchQuery.length > 0 && !searchLoading">
                            <div class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                                <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                <p class="text-sm">Tidak ada hasil ditemukan</p>
                            </div>
                        </template>

                        <template x-for="result in searchResults" :key="result.id">
                            <a :href="result.url" class="block px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 transition-colors duration-200">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="result.title"></p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400" x-text="result.description"></p>
                                    </div>
                                </div>
                            </a>
                        </template>
                    </div>
                </div>

                @auth
                    @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                        <a href="{{ route('organizer.dashboard') }}"
                           class="relative text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-all duration-300 font-medium group {{ request()->routeIs('organizer.*') ? 'text-primary dark:text-primary' : '' }}">
                            <span class="relative z-10">Dashboard</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream to-pasta-mint opacity-0 group-hover:opacity-20 rounded-lg transition-opacity duration-300"></div>
                            @if(request()->routeIs('organizer.*'))
                                <div class="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                            @endif
                        </a>
                    @endif
                @endauth
            </div>

            <!-- Right Side Actions -->
            <div class="flex items-center space-x-3">
                <!-- Clock -->
                <div class="hidden lg:flex items-center space-x-2 px-3 py-2 rounded-xl bg-pasta-cream/50 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-sm font-medium" x-text="currentTime"></span>
                </div>

                @auth
                    <!-- Notifications -->
                    <div class="relative" x-data="{ showNotifications: false }">
                        <button @click="showNotifications = !showNotifications"
                                class="relative p-2.5 rounded-xl bg-pasta-cream/50 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-mint/50 dark:hover:bg-gray-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                            <svg class="w-5 h-5 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            <!-- Notification Badge -->
                            <span class="notification-badge absolute -top-1 -right-1 bg-gradient-to-r from-pasta-salmon to-pasta-peach text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse shadow-lg"
                                  style="display: none;">0</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-peach/20 to-pasta-salmon/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </button>

                        <!-- Notifications Dropdown -->
                        <div x-show="showNotifications"
                             @click.away="showNotifications = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-3 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm"
                             :class="{ 'w-80': window.innerWidth >= 768, 'w-screen max-w-sm -right-4': window.innerWidth < 768 }">
                            <div class="px-4 py-3 border-b border-pasta-cream/50 dark:border-gray-600 flex items-center justify-between bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 dark:from-gray-700/50 dark:to-gray-600/50">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center space-x-2">
                                    <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                    </svg>
                                    <span>Notifikasi</span>
                                </h3>
                                <button onclick="markAllNotificationsAsRead()"
                                        class="text-xs text-primary hover:text-accent font-medium px-3 py-1 rounded-lg bg-primary/10 hover:bg-primary/20 transition-all duration-300">
                                    Tandai Semua Dibaca
                                </button>
                            </div>
                            <div class="notifications-dropdown-content max-h-64 overflow-y-auto">
                                <!-- Dynamic Notifications will be loaded here -->
                                <div id="notifications-list">
                                    <!-- Sample Notification -->
                                    <div class="px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 cursor-pointer border-b border-pasta-cream/30 dark:border-dark-600 transition-all duration-300 group">
                                        <div class="flex items-start space-x-3">
                                            <div class="w-3 h-3 bg-gradient-to-r from-pasta-salmon to-pasta-peach rounded-full mt-2 shadow-sm group-hover:scale-110 transition-transform duration-300"></div>
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between">
                                                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Selamat datang!</h4>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">Baru saja</span>
                                                </div>
                                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Terima kasih telah bergabung dengan TiXara. Jelajahi event menarik di sekitar Anda!</p>
                                                <div class="flex items-center space-x-2 mt-2">
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                        </svg>
                                                        Info
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Empty State -->
                                    <div id="notifications-empty" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400" style="display: none;">
                                        <svg class="w-12 h-12 mx-auto mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                        </svg>
                                        <p class="text-sm font-medium">Tidak ada notifikasi</p>
                                        <p class="text-xs mt-1">Notifikasi baru akan muncul di sini</p>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-3 border-t border-pasta-cream/50 dark:border-dark-600 bg-gradient-to-r from-pasta-cream/10 to-pasta-mint/10 dark:from-dark-700/30 dark:to-dark-600/30">
                                <a href="{{ route('notifications.index') }}" class="text-sm text-primary hover:text-accent font-medium flex items-center space-x-2 group">
                                    <span>Lihat semua notifikasi</span>
                                    <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative" x-data="{ showUserMenu: false }">
                        <button @click="showUserMenu = !showUserMenu"
                                class="flex items-center space-x-3 p-2 rounded-xl bg-pasta-cream/50 dark:bg-dark-700 hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                            <div class="relative">
                                <img src="{{ auth()->user()->avatar_url }}"
                                     alt="{{ auth()->user()->name }}"
                                     class="w-9 h-9 rounded-xl object-cover ring-2 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                                <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-accent rounded-full border-2 border-white dark:border-dark-700"></div>
                            </div>
                            <div class="hidden md:block">
                                <span class="text-sm font-semibold text-gray-700 dark:text-gray-200 block">{{ auth()->user()->name }}</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">{{ ucfirst(auth()->user()->role) }}</span>
                            </div>
                            <svg class="w-4 h-4 text-gray-400 dark:text-gray-500 group-hover:text-primary transition-all duration-300 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>

                        <!-- User Dropdown -->
                        <div x-show="showUserMenu"
                             @click.away="showUserMenu = false"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 transform scale-95"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-95"
                             class="absolute right-0 mt-3 bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm"
                             :class="{ 'w-56': window.innerWidth >= 768, 'w-screen max-w-sm -right-4': window.innerWidth < 768 }">
                            <div class="px-4 py-3 border-b border-pasta-cream/50 dark:border-dark-600 bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 dark:from-dark-700/50 dark:to-dark-600/50">
                                <div class="flex items-center space-x-3">
                                    <img src="{{ auth()->user()->avatar_url }}"
                                         alt="{{ auth()->user()->name }}"
                                         class="w-10 h-10 rounded-xl object-cover ring-2 ring-primary/30">
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900 dark:text-gray-100">{{ auth()->user()->name }}</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ auth()->user()->email }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 mt-2">
                                    <span class="inline-block px-3 py-1 text-xs bg-gradient-to-r from-primary/20 to-accent/20 text-primary dark:text-primary-300 rounded-full font-medium">
                                        {{ ucfirst(auth()->user()->role) }}
                                    </span>
                                    @if(auth()->user()->badgeLevels)
                                        <x-badge-level :user="auth()->user()" size="xs" />
                                    @endif
                                </div>
                            </div>
                            <div class="py-1">
                                <a href="{{ route('profile') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <span>Profil Saya</span>
                                </a>
                                <a href="{{ route('uangtix.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 hover:text-yellow-700 dark:hover:text-yellow-300 transition-all duration-300 group">
                                    <div class="w-4 h-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-coins text-white text-xs"></i>
                                    </div>
                                    <span>UangTix</span>
                                    <span class="ml-auto px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-xs font-medium">
                                        UTX {{ number_format(auth()->user()->getUangTixBalanceAmount(), 0) }}
                                    </span>
                                </a>
                                <a href="{{ route('wishlist.index') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                    <span>Wishlist Saya</span>
                                </a>
                                <a href="{{ route('tiket-saya') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                    </svg>
                                    <span>Tiket Saya</span>
                                </a>
                                <a href="{{ route('history-pembelian') }}" class="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                    </svg>
                                    <span>Riwayat Pembelian</span>
                                </a>
                            </div>

                            <!-- Theme Toggle Section -->
                            <div class="border-t border-pasta-cream/50 dark:border-dark-600 mt-1 pt-1">
                                <button @click="toggleDarkMode()" class="flex items-center space-x-3 w-full px-4 py-2.5 text-sm text-gray-700 dark:text-gray-300 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 hover:text-primary dark:hover:text-primary transition-all duration-300 group">
                                    <svg x-show="!darkMode" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                                    </svg>
                                    <svg x-show="darkMode" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                                    </svg>
                                    <span x-text="darkMode ? 'Mode Terang' : 'Mode Gelap'"></span>
                                </button>
                            </div>

                            <div class="border-t border-pasta-cream/50 dark:border-dark-600 mt-1 pt-1">
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="flex items-center space-x-3 w-full px-4 py-2.5 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 group">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                        </svg>
                                        <span>Logout</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Guest Actions -->
                    <div class="hidden md:flex items-center space-x-3">
                        <a href="{{ route('login') }}"
                           class="relative px-5 py-2.5 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary rounded-xl bg-pasta-cream/50 dark:bg-dark-700 hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 font-medium group shadow-sm hover:shadow-md">
                            <span class="relative z-10">Masuk</span>
                            <div class="absolute inset-0 bg-gradient-to-r from-pasta-cream/20 to-pasta-mint/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </a>
                        <a href="{{ route('register') }}"
                           class="relative bg-gradient-to-r from-primary via-pasta-sage to-accent text-white hover:shadow-lg px-6 py-2.5 rounded-xl transition-all duration-300 font-medium transform hover:scale-105 hover:-translate-y-0.5 shadow-md group overflow-hidden">
                            <span class="relative z-10 flex items-center space-x-2">
                                <span>Daftar</span>
                                <svg class="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                </svg>
                            </span>
                            <div class="absolute inset-0 bg-gradient-to-r from-accent to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </a>
                    </div>
                @endauth

                <!-- Mobile menu button -->
                <button @click="mobileMenuOpen = !mobileMenuOpen"
                        class="md:hidden relative p-2.5 rounded-xl bg-pasta-cream/50 dark:bg-dark-700 text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-mint/50 dark:hover:bg-dark-600 transition-all duration-300 group shadow-sm hover:shadow-md">
                    <svg class="w-5 h-5 transition-transform duration-300" :class="{ 'rotate-90': mobileMenuOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    <div class="absolute inset-0 bg-gradient-to-r from-pasta-peach/20 to-pasta-salmon/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div x-show="searchOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="lg:hidden border-t border-pasta-cream/50 dark:border-gray-600 bg-gradient-to-r from-pasta-cream/10 to-pasta-mint/10 dark:from-gray-800/50 dark:to-gray-700/50 px-4 py-4">
        <div class="relative group" x-data="{ showMobileSearchResults: false }">
            <input type="text"
                   x-model="searchQuery"
                   @input.debounce.300ms="searchEvents()"
                   @focus="showMobileSearchResults = true"
                   @click.away="showMobileSearchResults = false"
                   placeholder="Cari Tiket Event Favorit Anda..."
                   class="w-full px-4 py-3 pl-12 pr-10 rounded-2xl border-2 border-pasta-cream dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:border-primary focus:outline-none focus:ring-4 focus:ring-primary/20 transition-all duration-300 shadow-sm">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400 dark:text-gray-500 group-focus-within:text-primary transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
            <div x-show="searchLoading" class="absolute inset-y-0 right-0 pr-4 flex items-center">
                <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
            </div>

            <!-- Mobile Search Results -->
            <div x-show="showMobileSearchResults && (searchResults.length > 0 || searchQuery.length > 0)"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform scale-95"
                 x-transition:enter-end="opacity-100 transform scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 transform scale-100"
                 x-transition:leave-end="opacity-0 transform scale-95"
                 class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-pasta-cream/50 dark:border-gray-600 py-2 z-50 backdrop-blur-sm max-h-64 overflow-y-auto">

                <template x-if="searchResults.length === 0 && searchQuery.length > 0 && !searchLoading">
                    <div class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                        <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <p class="text-sm">Tidak ada hasil ditemukan</p>
                    </div>
                </template>

                <template x-for="result in searchResults" :key="result.id">
                    <a :href="result.url" class="block px-4 py-3 hover:bg-pasta-cream/30 dark:hover:bg-gray-700/50 transition-colors duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100" x-text="result.title"></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400" x-text="result.description"></p>
                            </div>
                        </div>
                    </a>
                </template>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-2"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-2"
         class="md:hidden bg-gradient-to-b from-white to-pasta-cream/30 dark:from-gray-800 dark:to-gray-900 border-t border-pasta-cream/50 dark:border-gray-600 backdrop-blur-sm">
        <div class="px-4 py-6 space-y-3">
            <!-- Mobile Theme Toggle -->
            <div class="mb-4 p-3 bg-pasta-cream/30 dark:bg-gray-700/30 rounded-xl">
                <button @click="toggleDarkMode()"
                        class="w-full flex items-center justify-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors duration-200">
                    <svg x-show="!darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                    <svg x-show="darkMode" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                    </svg>
                    <span x-text="darkMode ? 'Mode Terang' : 'Mode Gelap'"></span>
                </button>
            </div>

            <!-- Mobile Clock -->
            <div class="mb-4 p-3 bg-pasta-cream/30 dark:bg-gray-700/30 rounded-xl flex items-center justify-center space-x-2">
                <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span class="text-sm font-medium text-gray-600 dark:text-gray-300" x-text="currentTime"></span>
            </div>
            <a href="{{ route('home') }}" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                </svg>
                <span>Beranda</span>
            </a>
            <a href="{{ route('tickets.index') }}" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                </svg>
                <span>Tickets</span>
            </a>
            <a href="{{ route('tickets.authenticity-check') }}" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                </svg>
                <span>Cek Keaslian Tiket</span>
            </a>
            <!-- Panduan Section -->
            <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center space-x-2">
                    <i data-lucide="book-open" class="w-4 h-4 text-blue-600"></i>
                    <span>Panduan & Bantuan</span>
                </h4>
                <div class="space-y-2">
                    <a href="{{ route('guide.event-creator') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="user-plus" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Event Creator</span>
                    </a>
                    <a href="{{ route('guide.organizer') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="users" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Organizer</span>
                    </a>
                    <a href="{{ route('guide.buyer') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="shopping-cart" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Pembeli</span>
                    </a>
                    <a href="{{ route('guide.faq') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="help-circle" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">FAQ</span>
                    </a>
                </div>
            </div>

            <!-- ArtPosure Section -->
            <div class="mb-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center space-x-2">
                    <i data-lucide="megaphone" class="w-4 h-4 text-purple-600"></i>
                    <span>ArtPosure Marketing</span>
                </h4>
                <div class="space-y-2">
                    <a href="{{ route('artposure.index') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-100 dark:hover:bg-purple-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="home" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Overview</span>
                    </a>
                    <a href="{{ route('artposure.socmed') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="share-2" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Social Media</span>
                    </a>
                    <a href="{{ route('artposure.website') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="globe" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Website</span>
                    </a>
                    <a href="{{ route('artposure.package') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-cyan-600 dark:hover:text-cyan-400 hover:bg-cyan-100 dark:hover:bg-cyan-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="package" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Packages</span>
                    </a>
                </div>
            </div>

            <!-- Contact & Developer Section -->
            <div class="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-xl">
                <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center space-x-2">
                    <i data-lucide="phone" class="w-4 h-4 text-green-600"></i>
                    <span>Kontak & Info</span>
                </h4>
                <div class="space-y-2">
                    <a href="{{ route('contact.index') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="mail" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Hubungi Kami</span>
                    </a>
                    <a href="{{ route('developer.index') }}" class="flex items-center space-x-3 p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-300 font-medium group">
                        <i data-lucide="code" class="w-4 h-4 group-hover:scale-110 transition-transform duration-300"></i>
                        <span class="text-sm">Developer</span>
                    </a>
                </div>
            </div>

            @auth
                @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                    <a href="{{ route('organizer.dashboard') }}" class="flex items-center space-x-3 p-3 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary hover:bg-pasta-cream/30 dark:hover:bg-dark-700/50 rounded-xl transition-all duration-300 font-medium group">
                        <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        <span>Dashboard</span>
                    </a>
                @endif
            @else
                <div class="pt-4 border-t border-pasta-cream/50 dark:border-dark-600 space-y-3">
                    <a href="{{ route('login') }}"
                       class="block w-full text-center bg-pasta-cream/50 dark:bg-dark-700 text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary px-4 py-3 rounded-xl transition-all duration-300 font-medium hover:bg-pasta-mint/50 dark:hover:bg-dark-600">
                        Masuk
                    </a>
                    <a href="{{ route('register') }}"
                       class="block w-full text-center bg-gradient-to-r from-primary via-pasta-sage to-accent text-white px-4 py-3 rounded-xl transition-all duration-300 font-medium hover:shadow-lg transform hover:scale-105">
                        Daftar
                    </a>
                </div>
            @endauth
        </div>
    </div>
</header>

<!-- Header Spacer -->
<div class="h-16"></div>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #1f2937;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            position: relative;
        }

        .boarding-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #7c3aed, #a855f7, #c084fc, #e879f9);
        }

        .boarding-header {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
            color: white;
            padding: 32px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .boarding-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
            50% { transform: translateX(0%) translateY(0%) rotate(180deg); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
            letter-spacing: 3px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            font-size: 16px;
            font-weight: 300;
            opacity: 0.95;
            letter-spacing: 1px;
        }

        .boarding-body {
            padding: 40px;
            background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 40px;
            padding: 24px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .event-title {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 12px;
            line-height: 1.2;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .boarding-main {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .details-panel {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .details-grid {
            display: grid;
            gap: 24px;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .detail-item {
            position: relative;
            padding: 16px 0;
        }

        .detail-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, #e5e7eb, transparent);
        }

        .detail-label {
            font-size: 11px;
            font-weight: 600;
            color: #7c3aed;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
        }

        .detail-value.highlight {
            font-size: 18px;
            color: #7c3aed;
        }

        .qr-panel {
            background: white;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            position: relative;
            overflow: hidden;
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #7c3aed, #a855f7, #c084fc);
        }

        .qr-title {
            font-size: 14px;
            font-weight: 600;
            color: #7c3aed;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .qr-code {
            width: 160px;
            height: 160px;
            background: linear-gradient(135deg, #1f2937, #374151);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .qr-code::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #7c3aed, #a855f7, #c084fc, #e879f9);
            border-radius: 12px;
            z-index: -1;
        }

        .boarding-id {
            font-size: 16px;
            font-weight: 700;
            color: #7c3aed;
            font-family: 'JetBrains Mono', monospace;
            background: #f3f4f6;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 16px;
            border: 2px solid #e5e7eb;
        }

        .security-strip {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white;
            padding: 16px 40px;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .security-strip::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: security-scan 2s linear infinite;
        }

        @keyframes security-scan {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .footer-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 40px;
            background: #f9fafb;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }

        .tixara-brand {
            font-size: 18px;
            font-weight: 800;
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .boarding-main {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .footer-info {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 2px solid #000;
            }
            
            .boarding-header::before,
            .qr-panel::before,
            .security-strip::before {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="boarding-header">
            <div class="header-content">
                <div class="header-title">PROFESSIONAL</div>
                <div class="header-subtitle">Premium Boarding Pass</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Premium Event' }}</div>
            </div>

            <div class="boarding-main">
                <div class="details-panel">
                    <div class="details-grid">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Event Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('l, d F Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Event Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Venue Location</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Attendee Name</div>
                            <div class="detail-value highlight">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Ticket Number</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Seat Assignment</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Order Reference</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Ticket Price</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-title">Validation Code</div>
                    <div class="qr-code">
                        QR CODE<br>
                        PROFESSIONAL<br>
                        VALIDATION
                    </div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-PRO-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="security-strip">
            SECURE PROFESSIONAL BOARDING PASS • TIXARA PREMIUM SYSTEM • ANTI-COUNTERFEIT PROTECTION
        </div>

        <div class="footer-info">
            <div>Issued: {{ $ticket->order->created_at->format('d M Y H:i') ?? now()->format('d M Y H:i') }} | Status: {{ ucfirst($ticket->status ?? 'Active') }}</div>
            <div class="tixara-brand">TiXara Pro</div>
        </div>
    </div>
</body>
</html>

@php
    // Configuration with fallbacks
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#2563EB';
    $secondaryColor = $config['secondary_color'] ?? '#F8FAFC';
    $backgroundColor = $config['background_color'] ?? '#FFFFFF';
    $textColor = $config['text_color'] ?? '#1E293B';
    
    // Helper functions
    function getTicketValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket[$key] ?? $default;
        } elseif (is_object($ticket)) {
            return $ticket->{$key} ?? $default;
        }
        return $default;
    }
    
    function getEventValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['event'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->event)) {
            return $ticket->event->{$key} ?? $default;
        }
        return $default;
    }
    
    function getBuyerValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['buyer'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->buyer)) {
            return $ticket->buyer->{$key} ?? $default;
        }
        return $default;
    }
@endphp

<div class="ticket-container minimal-ticket" style="
    background: {{ $backgroundColor }};
    color: {{ $textColor }};
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    width: 700px;
    height: 350px;
    position: relative;
    border: 1px solid #E2E8F0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
">
    <!-- Header -->
    <div style="
        background: {{ $secondaryColor }};
        padding: 24px;
        border-bottom: 1px solid #E2E8F0;
    ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="
                    font-size: 24px;
                    font-weight: 600;
                    margin: 0 0 4px 0;
                    color: {{ $textColor }};
                    letter-spacing: -0.025em;
                ">{{ getEventValue($ticket, 'title', 'Event Title') }}</h1>
                <p style="
                    font-size: 14px;
                    margin: 0;
                    color: #64748B;
                    font-weight: 400;
                ">Electronic Ticket</p>
            </div>
            <div style="
                width: 48px;
                height: 48px;
                background: {{ $primaryColor }};
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div style="
        padding: 24px;
        display: flex;
        gap: 32px;
        height: calc(100% - 120px);
    ">
        <!-- Left Section -->
        <div style="flex: 1;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 24px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 500;
                        color: #64748B;
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    ">Date & Time</h3>
                    @php
                        $startDate = getEventValue($ticket, 'start_date', now());
                        $parsedDate = \Carbon\Carbon::parse($startDate);
                    @endphp
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 2px 0;
                        color: {{ $textColor }};
                    ">{{ $parsedDate->format('M j, Y') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #64748B;
                    ">{{ $parsedDate->format('g:i A') }}</p>
                </div>

                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 500;
                        color: #64748B;
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    ">Venue</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 2px 0;
                        color: {{ $textColor }};
                    ">{{ getEventValue($ticket, 'venue_name', 'TBA') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #64748B;
                    ">{{ getEventValue($ticket, 'city', 'Location TBA') }}</p>
                </div>
            </div>

            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 24px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 500;
                        color: #64748B;
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    ">Attendee</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 2px 0;
                        color: {{ $textColor }};
                    ">{{ getTicketValue($ticket, 'attendee_name', getBuyerValue($ticket, 'name', 'Guest')) }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #64748B;
                    ">{{ getTicketValue($ticket, 'attendee_email', getBuyerValue($ticket, 'email', 'N/A')) }}</p>
                </div>

                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 500;
                        color: #64748B;
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.05em;
                    ">Price</h3>
                    @php
                        $price = getTicketValue($ticket, 'price', 0);
                    @endphp
                    <p style="
                        font-size: 20px;
                        font-weight: 700;
                        margin: 0;
                        color: {{ $primaryColor }};
                    ">Rp {{ number_format($price, 0, ',', '.') }}</p>
                </div>
            </div>

            <!-- Ticket Number -->
            <div style="
                background: {{ $secondaryColor }};
                padding: 16px;
                border-radius: 8px;
                border: 1px solid #E2E8F0;
            ">
                <h3 style="
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748B;
                    margin: 0 0 8px 0;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                ">Ticket Number</h3>
                <p style="
                    font-size: 16px;
                    font-weight: 600;
                    font-family: 'JetBrains Mono', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                    letter-spacing: 1px;
                ">{{ getTicketValue($ticket, 'ticket_number', getTicketValue($ticket, 'ticket_code', 'TIX-000000')) }}</p>
            </div>
        </div>

        <!-- Right Section - QR Code -->
        <div style="
            width: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #E2E8F0;
            padding-left: 32px;
        ">
            <div style="
                background: white;
                padding: 16px;
                border-radius: 12px;
                border: 1px solid #E2E8F0;
                margin-bottom: 16px;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            ">
                @php
                    $qrData = getTicketValue($ticket, 'qr_code', 
                        getTicketValue($ticket, 'ticket_number', 
                            getTicketValue($ticket, 'ticket_code', 'INVALID')
                        )
                    );
                @endphp
                @if(class_exists('SimpleSoftwareIO\QrCode\Facades\QrCode'))
                    {!! QrCode::size(120)->generate($qrData) !!}
                @else
                    <div style="
                        width: 120px;
                        height: 120px;
                        background: #f8fafc;
                        border: 1px dashed #cbd5e1;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 12px;
                        color: #64748b;
                        text-align: center;
                        border-radius: 8px;
                    ">
                        QR Code<br>{{ substr($qrData, 0, 8) }}
                    </div>
                @endif
            </div>

            <div style="text-align: center;">
                <p style="
                    font-size: 12px;
                    font-weight: 500;
                    color: #64748B;
                    margin: 0 0 4px 0;
                ">Scan to verify</p>
                <p style="
                    font-size: 10px;
                    color: #94A3B8;
                    margin: 0;
                    line-height: 1.4;
                ">Present at entrance</p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: {{ $secondaryColor }};
        padding: 12px 24px;
        border-top: 1px solid #E2E8F0;
        text-align: center;
    ">
        <p style="
            font-size: 10px;
            color: #94A3B8;
            margin: 0;
        ">Valid for single entry • Powered by Tixara • {{ now()->format('Y') }}</p>
    </div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

.minimal-ticket {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    box-sizing: border-box;
}

.minimal-ticket * {
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .minimal-ticket {
        width: 100% !important;
        max-width: 700px !important;
        height: auto !important;
        min-height: 350px !important;
    }
}

@media print {
    .minimal-ticket {
        width: 700px !important;
        height: 350px !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }
}
</style>

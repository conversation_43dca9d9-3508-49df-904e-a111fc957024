@extends('layouts.staff')

@section('title', 'Staff Dashboard - TiXara')
@section('page-title', 'Dashboard')

@push('styles')
<style>
/* Enhanced Staff Dashboard Styles */

.scanner-card {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.scanner-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.scanner-card:hover::before {
    transform: translateX(100%);
}

.scanner-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 25px 50px rgba(16, 185, 129, 0.4);
}

.stat-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.pulse-dot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

.validation-status {
    position: relative;
    padding-left: 16px;
}

.validation-status::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transform: translateY(-50%);
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.validation-status.valid::before {
    background: #10b981;
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
}

.validation-status.invalid::before {
    background: #ef4444;
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.6);
}

.event-card {
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.event-card:hover {
    transform: translateX(4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.event-card.active {
    border-left-color: #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.08) 0%, white 100%);
}

.event-card.upcoming {
    border-left-color: #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.08) 0%, white 100%);
}

.quick-action-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.quick-action-btn:hover::before {
    width: 100%;
    height: 100%;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.performance-ring {
    position: relative;
    width: 80px;
    height: 80px;
}

.performance-ring svg {
    transform: rotate(-90deg);
}

.performance-ring .ring-bg {
    fill: none;
    stroke: #e5e7eb;
    stroke-width: 8;
}

.performance-ring .ring-progress {
    fill: none;
    stroke: #8b5cf6;
    stroke-width: 8;
    stroke-linecap: round;
    transition: stroke-dasharray 1s ease;
}

.glass-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.scan-animation {
    position: relative;
    overflow: hidden;
}

.scan-animation::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: scan 2s infinite;
}

@keyframes scan {
    0% { left: -100%; }
    100% { left: 100%; }
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.progress-bar {
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Dashboard Header -->
        <div class="mb-8" data-aos="fade-up">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="mb-6 lg:mb-0">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center floating-element">
                            <i data-lucide="shield-check" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-4xl font-bold gradient-text">Staff Dashboard</h1>
                            <p class="text-gray-600 text-lg">Sistem Validasi E-Tiket Terpadu</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <div class="flex items-center">
                            <i data-lucide="user" class="w-4 h-4 mr-1"></i>
                            <span>{{ auth()->user()->name }}</span>
                        </div>
                        <div class="flex items-center">
                            <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                            <span>{{ now()->format('d M Y') }}</span>
                        </div>
                    </div>

                    <!-- Organizer Assignment Info -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-semibold text-blue-900 mb-1">Assigned Organizers</h3>
                                @if(auth()->user()->hasOrganizerAssignments())
                                    <div class="flex flex-wrap gap-1 mb-2">
                                        @foreach(auth()->user()->assignedOrganizerUsers() as $organizer)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $organizer->name }}
                                        </span>
                                        @endforeach
                                    </div>
                                    <p class="text-xs text-blue-700">
                                        You can validate tickets from {{ auth()->user()->getAssignedOrganizersCount() }} organizer(s)
                                    </p>
                                @else
                                    <p class="text-sm text-orange-700 font-medium">⚠️ No organizers assigned</p>
                                    <p class="text-xs text-orange-600">Contact admin to assign organizers for ticket validation</p>
                                @endif
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold text-blue-600">{{ auth()->user()->getAssignedOrganizersCount() }}</div>
                                <div class="text-xs text-blue-500">Organizers</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <!-- Enhanced Live Status -->
                    <div class="glass-card px-4 py-3 rounded-xl">
                        <div class="flex items-center space-x-2">
                            <div class="relative">
                                <div class="w-3 h-3 bg-green-500 rounded-full pulse-dot"></div>
                                <div class="absolute inset-0 w-3 h-3 bg-green-500 rounded-full animate-ping opacity-30"></div>
                            </div>
                            <span class="text-green-700 font-semibold text-sm">System Online</span>
                        </div>
                    </div>

                    <!-- Enhanced Current Time -->
                    <div class="glass-card px-4 py-3 rounded-xl">
                        <div class="flex items-center space-x-2">
                            <i data-lucide="clock" class="w-4 h-4 text-blue-600"></i>
                            <span class="text-gray-700 font-semibold text-sm" id="currentTime">{{ now()->format('H:i:s') }}</span>
                        </div>
                    </div>

                    <!-- Quick Scanner Access -->                </div>
            </div>
        </div>

        <!-- Enhanced Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- E-Tiket QR Scanner -->
            <div class="scanner-card rounded-xl shadow-lg p-6 text-white scan-animation" data-aos="fade-up" data-aos-delay="100">
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                        <i data-lucide="scan" class="w-7 h-7"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-white/90 text-sm font-medium">Staff E-Ticket Scanner</p>
                        <p class="text-2xl font-bold">Mobile QR</p>
                    </div>
                </div>
                <div class="space-y-3">
                    <a href="{{ route('validation.staff') }}"
                       class="block w-full bg-white/20 backdrop-blur-sm text-white text-center py-3 rounded-xl hover:bg-white/30 transition-all duration-300 font-semibold group">
                        <i data-lucide="smartphone" class="w-4 h-4 inline mr-2 group-hover:scale-110 transition-transform"></i>
                        Jalankan Scanner
                    </a>
                </div>
            </div>

            <!-- Enhanced Today's Validations -->
            <div class="stat-card glass-card rounded-xl shadow-lg p-6 relative overflow-hidden" data-aos="fade-up" data-aos-delay="200">
                <div class="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl shadow-lg">
                        <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">Validasi Hari Ini</p>
                        <p class="text-3xl font-bold text-gray-900" id="todayValidations">{{ $stats['today_validations'] ?? 0 }}</p>
                        <div class="flex items-center justify-end mt-1">
                            <i data-lucide="trending-up" class="w-4 h-4 text-green-500 mr-1"></i>
                            <span class="text-xs text-green-600 font-medium">+12% dari kemarin</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600" id="validTickets">{{ $stats['valid_tickets'] ?? 0 }}</div>
                        <div class="text-xs text-green-700 font-medium">Valid</div>
                    </div>
                    <div class="text-center p-3 bg-red-50 rounded-lg">
                        <div class="text-2xl font-bold text-red-600" id="invalidTickets">{{ $stats['invalid_tickets'] ?? 0 }}</div>
                        <div class="text-xs text-red-700 font-medium">Invalid</div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-xs font-medium text-gray-600">Success Rate</span>
                        <span class="text-xs font-bold text-green-600">{{ $stats['today_validations'] > 0 ? round(($stats['valid_tickets'] / $stats['today_validations']) * 100, 1) : 0 }}%</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-3 overflow-hidden">
                        <div class="progress-bar h-3 transition-all duration-1000 ease-out" style="width: {{ $stats['today_validations'] > 0 ? ($stats['valid_tickets'] / $stats['today_validations']) * 100 : 0 }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Active Events -->
            <div class="stat-card glass-card rounded-xl shadow-lg p-6 relative overflow-hidden" data-aos="fade-up" data-aos-delay="300">
                <div class="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full -mr-8 -mt-8"></div>
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                        <i data-lucide="calendar-check" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-600 font-medium">Event Aktif</p>
                        <p class="text-3xl font-bold text-gray-900">{{ $stats['active_events'] ?? 0 }}</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2 pulse-dot"></div>
                            <span class="text-blue-700 font-medium text-sm">Berlangsung</span>
                        </div>
                        <span class="text-blue-600 font-bold">{{ $stats['active_events'] ?? 0 }}</span>
                    </div>

                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                            <span class="text-orange-700 font-medium text-sm">Upcoming</span>
                        </div>
                        <span class="text-orange-600 font-bold">{{ $stats['upcoming_events'] ?? 0 }}</span>
                    </div>
                </div>

                <button onclick="showTodayEvents()" class="w-full mt-4 bg-blue-50 hover:bg-blue-100 text-blue-700 py-2 rounded-lg transition-colors text-sm font-medium">
                    <i data-lucide="eye" class="w-4 h-4 inline mr-1"></i>
                    Lihat Detail
                </button>
            </div>

            <!-- Enhanced Performance Score -->
            <div class="stat-card glass-card rounded-xl shadow-lg p-6 relative overflow-hidden" data-aos="fade-up" data-aos-delay="400">
                <div class="absolute top-0 right-0 w-24 h-24 bg-purple-500/10 rounded-full -mr-12 -mt-12"></div>
                <div class="flex items-center justify-between mb-6">
                    <div class="p-3 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl shadow-lg">
                        <i data-lucide="award" class="w-6 h-6 text-white"></i>
                    </div>
                    <div class="performance-ring">
                        <svg class="w-20 h-20">
                            <circle class="ring-bg" cx="40" cy="40" r="32"></circle>
                            <circle class="ring-progress" cx="40" cy="40" r="32"
                                    stroke-dasharray="{{ 2 * pi() * 32 }}"
                                    stroke-dashoffset="{{ 2 * pi() * 32 * (1 - ($stats['performance_score'] ?? 95) / 100) }}"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center">
                                <div class="text-lg font-bold text-gray-900">{{ $stats['performance_score'] ?? 95 }}%</div>
                                <div class="text-xs text-gray-600">Score</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Accuracy</span>
                        <span class="text-green-600 font-medium">{{ $stats['success_rate'] ?? 100 }}%</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Speed</span>
                        <span class="text-blue-600 font-medium">Excellent</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Efficiency</span>
                        <span class="text-purple-600 font-medium">High</span>
                    </div>
                </div>

                <div class="mt-4 p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                    <div class="flex items-center">
                        <i data-lucide="trending-up" class="w-4 h-4 text-purple-600 mr-2"></i>
                        <span class="text-purple-700 font-medium text-sm">Performance Excellent!</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Quick Actions Bar -->
        <div class="mb-8" data-aos="fade-up" data-aos-delay="500">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-900">Quick Actions</h2>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-sm text-gray-600">All systems operational</span>
                </div>
            </div>


                <!-- Today's Events -->
                <button onclick="showTodayEvents()" class="quick-action-btn glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300 group">
                    <div class="text-center">
                        <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                            <i data-lucide="calendar-days" class="w-7 h-7 text-white"></i>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 mb-1">Today's Events</p>
                        <p class="text-xs text-gray-600">Active Events</p>
                    </div>
                </button>

                <!-- Validation History -->
                <button onclick="showValidationHistory()" class="quick-action-btn glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300 group relative">
                    <div class="notification-badge">3</div>
                    <div class="text-center">
                        <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                            <i data-lucide="history" class="w-7 h-7 text-white"></i>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 mb-1">History</p>
                        <p class="text-xs text-gray-600">Recent Validations</p>
                    </div>
                </button>

                <!-- Help & Support -->
                <button onclick="showHelp()" class="quick-action-btn glass-card p-6 rounded-xl hover:shadow-lg transition-all duration-300 group">
                    <div class="text-center">
                        <div class="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform shadow-lg">
                            <i data-lucide="help-circle" class="w-7 h-7 text-white"></i>
                        </div>
                        <p class="text-sm font-semibold text-gray-900 mb-1">Help</p>
                        <p class="text-xs text-gray-600">Support & Guide</p>
                    </div>
                </button>
            </div>
        </div>

    <!-- Recent Validations -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-8" data-aos="fade-up" data-aos-delay="400">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Validasi Terbaru</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-primary text-white rounded-lg">Hari Ini</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">Minggu Ini</button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tiket
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Event
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pembeli
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Waktu
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                <p>Belum ada validasi tiket hari ini</p>
                                <p class="text-sm mt-1">Gunakan scanner untuk memvalidasi tiket</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Today's Tickets -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="500">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Event Hari Ini</h2>
                <span class="text-sm text-gray-600">{{ now()->format('d M Y') }}</span>
            </div>

            <div class="space-y-4">
                <div class="text-center py-12">
                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <p class="text-gray-600">Tidak ada event yang berlangsung hari ini</p>
                    <p class="text-sm text-gray-500 mt-1">Event akan muncul di sini saat jadwal dimulai</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Help -->
    <div class="mt-8 bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6" data-aos="fade-up" data-aos-delay="600">
        <div class="flex items-start space-x-4">
            <div class="p-2 bg-primary/20 rounded-lg">
                <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
            </div>
            <div class="flex-1">
                <h3 class="font-semibold text-gray-900 mb-2">Panduan Cepat</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Validasi Tiket:</h4>
                        <ul class="space-y-1">
                            <li>• Buka scanner QR code</li>
                            <li>• Arahkan kamera ke QR tiket</li>
                            <li>• Sistem akan otomatis memvalidasi</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-900 mb-1">Tips Penting:</h4>
                        <ul class="space-y-1">
                            <li>• Pastikan pencahayaan cukup</li>
                            <li>• QR code dalam kondisi baik</li>
                            <li>• Periksa status tiket sebelum validasi</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scanner Modal (if needed) -->
<div id="scanner-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">E-Tiket QR Scanner</h3>
                <button onclick="closeScanner()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div id="scanner-container" class="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Scanner akan dimuat di sini</p>
            </div>
            <div class="mt-4 text-center">
                <p class="text-sm text-gray-600">Arahkan kamera ke QR code tiket</p>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update time every second
    updateTime();
    setInterval(updateTime, 1000);

    // Auto-refresh stats every 30 seconds
    setInterval(refreshStats, 30000);

    // Initialize Lucide icons
    if (window.lucide) {
        window.lucide.createIcons();
    }
});

// Update current time
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// Refresh dashboard stats
async function refreshStats() {
    try {
        const response = await fetch('/staff/dashboard/stats');
        const data = await response.json();

        // Update validation count
        document.getElementById('todayValidations').textContent = data.today_validations;

        // Update other stats if needed
        console.log('Stats refreshed:', data);
    } catch (error) {
        console.error('Error refreshing stats:', error);
    }
}

// Quick scanner function
function openQuickScanner() {
    // Open scanner modal or redirect to scanner page
    window.location.href = '{{ route("staff.scanner") }}';
}

// Show today's events
function showTodayEvents() {
    const modal = document.getElementById('eventsModal');
    if (modal) {
        modal.classList.remove('hidden');
        loadTodayEvents();
    }
}

// Show validation history
function showValidationHistory() {
    const modal = document.getElementById('historyModal');
    if (modal) {
        modal.classList.remove('hidden');
        loadValidationHistory();
    }
}

// Show help
function showHelp() {
    const modal = document.getElementById('helpModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

// Load today's events
async function loadTodayEvents() {
    try {
        const response = await fetch('/staff/dashboard/today-events');
        const events = await response.json();

        const container = document.getElementById('todayEventsContainer');
        if (container) {
            container.innerHTML = events.map(event => `
                <div class="event-card p-4 rounded-lg border ${(event.status || 'upcoming') === 'active' ? 'active' : 'upcoming'}">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-gray-900">${event.title || 'Unknown Event'}</h4>
                            <p class="text-sm text-gray-600">${event.venue_name || 'Unknown Venue'}</p>
                            <p class="text-xs text-gray-500">${event.start_time || '00:00'} - ${event.end_time || '00:00'}</p>
                        </div>
                        <div class="text-right">
                            <span class="px-2 py-1 text-xs rounded-full ${(event.status || 'upcoming') === 'active' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}">
                                ${(event.status || 'upcoming') === 'active' ? 'Active' : 'Upcoming'}
                            </span>
                            <p class="text-sm text-gray-600 mt-1">${event.attendees || 0}/${event.capacity || 0}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Error loading events:', error);
    }
}

// Load validation history
async function loadValidationHistory() {
    try {
        const response = await fetch('/staff/dashboard/validation-history');
        const validations = await response.json();

        const container = document.getElementById('validationHistoryContainer');
        if (container) {
            container.innerHTML = validations.map(validation => `
                <div class="flex items-center justify-between p-3 border-b border-gray-100 last:border-b-0">
                    <div class="flex items-center">
                        <div class="validation-status ${validation.status || 'unknown'} mr-3">
                            <span class="text-sm font-medium">${validation.ticket_code || 'Unknown'}</span>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">${validation.event_title || 'Unknown Event'}</p>
                            <p class="text-sm text-gray-600">${validation.customer_name || 'Unknown Customer'}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="px-2 py-1 text-xs rounded-full ${(validation.status || 'unknown') === 'valid' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}">
                            ${(validation.status || 'unknown') === 'valid' ? 'Valid' : 'Invalid'}
                        </span>
                        <p class="text-xs text-gray-500 mt-1">${validation.validated_at || 'Unknown time'}</p>
                    </div>
                </div>
            `).join('');
        }
    } catch (error) {
        console.error('Error loading validation history:', error);
    }
}

// Close modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

// Scanner functions
function openScanner() {
    document.getElementById('scanner-modal').classList.remove('hidden');
    // Initialize scanner here
}

function closeScanner() {
    document.getElementById('scanner-modal').classList.add('hidden');
    // Stop scanner here
}

// Real-time notifications (if WebSocket is available)
if (window.Echo) {
    window.Echo.channel('staff-dashboard')
        .listen('TicketValidated', (e) => {
            // Update stats in real-time
            refreshStats();

            // Show notification
            showNotification(`Tiket ${e.ticket_code} berhasil divalidasi`, 'success');
        });
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endpush

</div>
@endsection
@props(['user', 'size' => 'md'])

@php
    // Get badge info with fallbacks
    $badgeName = 'Bronze';
    $badgeColor = '#CD7F32';
    $badgeIcon = 'fas fa-medal';
    
    if ($user) {
        if ($user->badgelevel) {
            $badgeName = $user->badgeLevel->name ?? 'Bronze';
            $badgeColor = $user->badgeLevel->color ?? '#CD7F32';
            $badgeIcon = $user->badgeLevel->icon ?? 'fas fa-medal';
        } elseif ($user->badge_level_id) {
            $badgeName = 'Badge #' . $user->badge_level_id;
            $badgeColor = '#F59E0B';
            $badgeIcon = 'fas fa-exclamation-triangle';
        }
    }
    
    // Size mapping
    $sizes = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-2 py-1 text-sm',
        'md' => 'px-3 py-1 text-sm',
        'lg' => 'px-4 py-2 text-base'
    ];
    
    $iconSizes = [
        'xs' => 'text-xs',
        'sm' => 'text-sm', 
        'md' => 'text-sm',
        'lg' => 'text-base'
    ];
    
    $sizeClass = $sizes[$size] ?? $sizes['md'];
    $iconClass = $iconSizes[$size] ?? $iconSizes['md'];
@endphp

<span class="inline-flex items-center {{ $sizeClass }} rounded-full font-semibold text-white shadow-sm"
      style="background-color: {{ $badgeColor }};">
    <i class="{{ $badgeIcon }} {{ $iconClass }} mr-1"></i>
    {{ $badgeName }}
</span>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('notifications')) {
            Schema::create('notifications', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('title');
                $table->text('message');
                $table->enum('type', ['system', 'user', 'event', 'payment', 'order', 'ticket'])->default('system');
                $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
                $table->json('data')->nullable();
                $table->string('action_url')->nullable();
                $table->timestamp('read_at')->nullable();
                $table->timestamps();

            // Indexes for better performance
                $table->index(['user_id', 'read_at']);
                $table->index(['type', 'created_at']);
                $table->index(['priority', 'created_at']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};

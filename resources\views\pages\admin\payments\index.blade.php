@extends('layouts.admin')

@section('title', 'Payment Management')

@push('styles')
<style>
.payment-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.payment-status-pending {
    background: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.payment-status-paid {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.payment-status-failed {
    background: rgba(239, 68, 68, 0.1);
    color: #EF4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.payment-method-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: rgba(99, 102, 241, 0.1);
    color: #6366F1;
    border: 1px solid rgba(99, 102, 241, 0.2);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.filter-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #E5E7EB;
    margin-bottom: 24px;
}

.table-container {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    overflow: hidden;
}

.table-header {
    background: #F9FAFB;
    padding: 16px 20px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: between;
    align-items: center;
}

.btn-action {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.btn-approve {
    background: #10B981;
    color: white;
    border: 1px solid #10B981;
}

.btn-approve:hover {
    background: #059669;
    color: white;
}

.btn-reject {
    background: #EF4444;
    color: white;
    border: 1px solid #EF4444;
}

.btn-reject:hover {
    background: #DC2626;
    color: white;
}

.btn-view {
    background: #6366F1;
    color: white;
    border: 1px solid #6366F1;
}

.btn-view:hover {
    background: #4F46E5;
    color: white;
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Payment Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Monitor and manage all payment transactions</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('admin.payments.export') }}" class="btn btn-secondary">
                <i data-lucide="download" class="w-4 h-4"></i>
                Export CSV
            </a>
            <button onclick="refreshPayments()" class="btn btn-primary">
                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ 'Rp ' . number_format($stats['total_revenue'], 0, ',', '.') }}</div>
            <div class="stat-label">Total Revenue</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ number_format($stats['pending_payments']) }}</div>
            <div class="stat-label">Pending Payments</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ number_format($stats['completed_payments']) }}</div>
            <div class="stat-label">Completed Payments</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ number_format($stats['failed_payments']) }}</div>
            <div class="stat-label">Failed Payments</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ 'Rp ' . number_format($stats['today_revenue'], 0, ',', '.') }}</div>
            <div class="stat-label">Today's Revenue</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{{ number_format($stats['today_payments']) }}</div>
            <div class="stat-label">Today's Payments</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="Order number, customer, event..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                <select name="payment_status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Status</option>
                    <option value="pending" {{ request('payment_status') === 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="paid" {{ request('payment_status') === 'paid' ? 'selected' : '' }}>Paid</option>
                    <option value="failed" {{ request('payment_status') === 'failed' ? 'selected' : '' }}>Failed</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input type="date" name="date_from" value="{{ request('date_from') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input type="date" name="date_to" value="{{ request('date_to') }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div class="md:col-span-2 lg:col-span-4 flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i data-lucide="search" class="w-4 h-4"></i>
                    Filter
                </button>
                <a href="{{ route('admin.payments.index') }}" class="btn btn-secondary">
                    <i data-lucide="x" class="w-4 h-4"></i>
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Payments Table -->
    <div class="table-container">
        <div class="table-header">
            <h3 class="text-lg font-semibold text-gray-900">Payment Transactions</h3>
            <div class="flex gap-2">
                <button onclick="bulkApprove()" class="btn btn-sm btn-success" id="bulkApproveBtn" style="display: none;">
                    <i data-lucide="check" class="w-4 h-4"></i>
                    Approve Selected
                </button>
            </div>
        </div>
        
        @if($payments->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300">
                            </th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Method</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($payments as $payment)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-4">
                                    <input type="checkbox" name="selected_payments[]" value="{{ $payment->id }}" 
                                           class="payment-checkbox rounded border-gray-300">
                                </td>
                                <td class="px-4 py-4">
                                    <div class="font-medium text-gray-900">#{{ $payment->order_number }}</div>
                                    <div class="text-sm text-gray-500">{{ $payment->status }}</div>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="font-medium text-gray-900">{{ $payment->user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $payment->user->email }}</div>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="font-medium text-gray-900">{{ Str::limit($payment->event->title, 30) }}</div>
                                    <div class="text-sm text-gray-500">{{ $payment->event->category->name ?? 'No Category' }}</div>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="font-medium text-gray-900">Rp {{ number_format($payment->total_amount, 0, ',', '.') }}</div>
                                </td>
                                <td class="px-4 py-4">
                                    <span class="payment-method-badge">
                                        {{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}
                                    </span>
                                </td>
                                <td class="px-4 py-4">
                                    <span class="payment-status-badge payment-status-{{ $payment->payment_status }}">
                                        <i data-lucide="{{ $payment->payment_status === 'paid' ? 'check-circle' : ($payment->payment_status === 'failed' ? 'x-circle' : 'clock') }}" class="w-3 h-3"></i>
                                        {{ ucfirst($payment->payment_status) }}
                                    </span>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="text-sm text-gray-900">{{ $payment->created_at->format('M d, Y') }}</div>
                                    <div class="text-sm text-gray-500">{{ $payment->created_at->format('H:i') }}</div>
                                </td>
                                <td class="px-4 py-4">
                                    <div class="flex gap-1">
                                        <a href="{{ route('admin.payments.show', $payment) }}" class="btn-action btn-view">
                                            <i data-lucide="eye" class="w-3 h-3"></i>
                                            View
                                        </a>
                                        
                                        @if($payment->payment_status === 'pending')
                                            <form method="POST" action="{{ route('admin.payments.approve', $payment) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="btn-action btn-approve" 
                                                        onclick="return confirm('Are you sure you want to approve this payment?')">
                                                    <i data-lucide="check" class="w-3 h-3"></i>
                                                    Approve
                                                </button>
                                            </form>
                                            
                                            <form method="POST" action="{{ route('admin.payments.reject', $payment) }}" class="inline">
                                                @csrf
                                                <button type="submit" class="btn-action btn-reject"
                                                        onclick="return confirm('Are you sure you want to reject this payment?')">
                                                    <i data-lucide="x" class="w-3 h-3"></i>
                                                    Reject
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            @if($payments->hasPages())
                <div class="px-4 py-3 border-t border-gray-200">
                    {{ $payments->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <i data-lucide="credit-card" class="w-12 h-12 mx-auto text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
                <p class="text-gray-500">No payment transactions match your current filters.</p>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.payment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    toggleBulkActions();
});

// Individual checkbox change
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('payment-checkbox')) {
        toggleBulkActions();
    }
});

function toggleBulkActions() {
    const checkedBoxes = document.querySelectorAll('.payment-checkbox:checked');
    const bulkBtn = document.getElementById('bulkApproveBtn');
    
    if (checkedBoxes.length > 0) {
        bulkBtn.style.display = 'inline-flex';
    } else {
        bulkBtn.style.display = 'none';
    }
}

function bulkApprove() {
    const checkedBoxes = document.querySelectorAll('.payment-checkbox:checked');
    const orderIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (orderIds.length === 0) {
        alert('Please select payments to approve');
        return;
    }
    
    if (!confirm(`Are you sure you want to approve ${orderIds.length} payments?`)) {
        return;
    }
    
    fetch('{{ route("admin.payments.bulk-approve") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            order_ids: orderIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing the request');
    });
}

function refreshPayments() {
    location.reload();
}

// Auto-refresh every 30 seconds
setInterval(() => {
    fetch('{{ route("admin.payments.check-updates") }}?last_check=' + encodeURIComponent(new Date().toISOString()))
        .then(response => response.json())
        .then(data => {
            if (data.hasUpdates) {
                // Show notification that updates are available
                const notification = document.createElement('div');
                notification.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                notification.innerHTML = `
                    <div class="flex items-center gap-2">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        <span>New payment updates available</span>
                        <button onclick="location.reload()" class="ml-2 text-blue-200 hover:text-white">
                            Refresh
                        </button>
                    </div>
                `;
                document.body.appendChild(notification);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 5000);
            }
        })
        .catch(error => console.error('Error checking for updates:', error));
}, 30000);
</script>
@endpush

@extends('layouts.app')

@section('title', 'Ticket Validation Scanner')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <div class="text-center mb-12">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <i data-lucide="scan-line" class="w-10 h-10 text-white"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Ticket Validation System</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Choose your preferred scanning interface for validating event tickets with advanced camera functionality
            </p>
        </div>

        <!-- Scanner Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Enhanced Desktop Scanner -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-6">
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="monitor" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2">Enhanced Desktop Scanner</h3>
                    <p class="text-blue-100">Full-featured scanner with advanced controls</p>
                </div>
                <div class="p-6">
                    <ul class="space-y-3 mb-6">
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Camera switching (front/back)
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Torch/flashlight control
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Detailed validation history
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Session statistics
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Manual input fallback
                        </li>
                    </ul>
                    <a href="{{ route('validation.enhanced') }}" 
                       class="w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center">
                        <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                        Launch Enhanced Scanner
                    </a>
                </div>
            </div>

            <!-- Mobile PWA Scanner -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-green-500 to-emerald-600 p-6">
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="smartphone" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2">Mobile PWA Scanner</h3>
                    <p class="text-green-100">Optimized for mobile devices and PWA</p>
                </div>
                <div class="p-6">
                    <ul class="space-y-3 mb-6">
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Mobile-optimized interface
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Touch-friendly controls
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Vibration feedback
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Offline capability
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Quick scan history
                        </li>
                    </ul>
                    <a href="{{ route('validation.staff') }}" 
                       class="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center">
                        <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                        Jalankan E-Ticket Scanner
                    </a>
                </div>
            </div>

            <!-- Classic Scanner -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="bg-gradient-to-r from-purple-500 to-pink-600 p-6">
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                        <i data-lucide="qr-code" class="w-6 h-6 text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2">Classic Scanner</h3>
                    <p class="text-purple-100">Simple and reliable scanning interface</p>
                </div>
                <div class="p-6">
                    <ul class="space-y-3 mb-6">
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Basic QR code scanning
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Manual ticket validation
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Validation statistics
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Bulk validation support
                        </li>
                        <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i data-lucide="check" class="w-4 h-4 text-green-500 mr-2"></i>
                            Recent validations
                        </li>
                    </ul>
                    <a href="{{ route('pages.validation.index') }}" 
                       class="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center">
                        <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                        Launch Classic Scanner
                    </a>
                </div>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">Advanced Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="camera" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Camera Switching</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Switch between front and back cameras for optimal scanning</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="flashlight" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Torch Control</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Toggle flashlight for scanning in low-light conditions</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="vibrate" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Haptic Feedback</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Vibration and sound feedback for validation results</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i data-lucide="bar-chart" class="w-6 h-6 text-orange-600 dark:text-orange-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Real-time Stats</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Track validation statistics and success rates</p>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ route('tickets.validation.stats') }}" 
                   class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3"></i>
                    <span class="font-medium text-gray-900 dark:text-white">View Statistics</span>
                </a>
                <a href="{{ route('tickets.validation.recent') }}" 
                   class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <i data-lucide="clock" class="w-5 h-5 text-green-600 dark:text-green-400 mr-3"></i>
                    <span class="font-medium text-gray-900 dark:text-white">Recent Validations</span>
                </a>
                <a href="{{ route('tickets.authenticity-check') }}" 
                   class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <i data-lucide="shield-check" class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-3"></i>
                    <span class="font-medium text-gray-900 dark:text-white">Authenticity Check</span>
                </a>
                <a href="{{ route('admin.dashboard') }}" 
                   class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                    <i data-lucide="settings" class="w-5 h-5 text-orange-600 dark:text-orange-400 mr-3"></i>
                    <span class="font-medium text-gray-900 dark:text-white">Admin Dashboard</span>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.hover\:scale-105:hover {
    transform: scale(1.05);
}
</style>
@endpush

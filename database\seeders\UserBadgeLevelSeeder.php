<?php

namespace Database\Seeders;

use App\Models\UserBadgeLevel;
use Illuminate\Database\Seeder;

class UserBadgeLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $badgelevel = [
            [
                'name' => 'Bronze',
                'slug' => 'bronze',
                'description' => 'Level pemula untuk pengguna baru TiXara',
                'color' => '#CD7F32',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'benefits' => [
                    'discount_percentage' => 0,
                    'cashback_percentage' => 1,
                    'priority_support' => false,
                    'early_access' => false,
                ],
                'requirements' => [
                    'account_age_days' => 0,
                    'verified_email' => true,
                ],
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Silver',
                'slug' => 'silver',
                'description' => 'Level menengah untuk pengguna aktif',
                'color' => '#C0C0C0',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 500000,
                'min_uangtix_balance' => 50000,
                'min_transactions' => 3,
                'min_events_attended' => 2,
                'benefits' => [
                    'discount_percentage' => 2,
                    'cashback_percentage' => 2,
                    'priority_support' => false,
                    'early_access' => false,
                    'free_shipping' => false,
                ],
                'requirements' => [
                    'account_age_days' => 30,
                    'verified_email' => true,
                    'verified_phone' => false,
                ],
                'discount_percentage' => 2,
                'cashback_percentage' => 2,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Gold',
                'slug' => 'gold',
                'description' => 'Level tinggi untuk pengguna setia',
                'color' => '#FFD700',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 2000000,
                'min_uangtix_balance' => 200000,
                'min_transactions' => 10,
                'min_events_attended' => 5,
                'benefits' => [
                    'discount_percentage' => 5,
                    'cashback_percentage' => 3,
                    'priority_support' => true,
                    'early_access' => true,
                    'free_shipping' => true,
                    'exclusive_events' => false,
                ],
                'requirements' => [
                    'account_age_days' => 90,
                    'verified_email' => true,
                    'verified_phone' => true,
                ],
                'discount_percentage' => 5,
                'cashback_percentage' => 3,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Platinum',
                'slug' => 'platinum',
                'description' => 'Level premium untuk VIP member',
                'color' => '#E5E4E2',
                'icon' => 'fas fa-crown',
                'min_spent_amount' => 5000000,
                'min_uangtix_balance' => 500000,
                'min_transactions' => 25,
                'min_events_attended' => 15,
                'benefits' => [
                    'discount_percentage' => 8,
                    'cashback_percentage' => 5,
                    'priority_support' => true,
                    'early_access' => true,
                    'free_shipping' => true,
                    'exclusive_events' => true,
                    'personal_manager' => false,
                ],
                'requirements' => [
                    'account_age_days' => 180,
                    'verified_email' => true,
                    'verified_phone' => true,
                    'kyc_verified' => true,
                ],
                'discount_percentage' => 8,
                'cashback_percentage' => 5,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Diamond',
                'slug' => 'diamond',
                'description' => 'Level tertinggi untuk member eksklusif',
                'color' => '#B9F2FF',
                'icon' => 'fas fa-gem',
                'min_spent_amount' => ********,
                'min_uangtix_balance' => 1000000,
                'min_transactions' => 50,
                'min_events_attended' => 30,
                'benefits' => [
                    'discount_percentage' => 12,
                    'cashback_percentage' => 8,
                    'priority_support' => true,
                    'early_access' => true,
                    'free_shipping' => true,
                    'exclusive_events' => true,
                    'personal_manager' => true,
                ],
                'requirements' => [
                    'account_age_days' => 365,
                    'verified_email' => true,
                    'verified_phone' => true,
                    'kyc_verified' => true,
                ],
                'discount_percentage' => 12,
                'cashback_percentage' => 8,
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($badgelevel as $level) {
            UserBadgeLevel::updateOrCreate(
                ['slug' => $level['slug']],
                $level
            );
        }

        $this->command->info('User badge level seeded successfully!');
    }
}

<!-- Floating Footer Navigation -->
<nav class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-200 z-50 md:hidden"
     x-data="{
        activeTab: '{{ request()->routeIs('home') ? 'home' : (request()->routeIs('validation.*') ? 'scan' : (request()->routeIs('tickets.*') ? 'tickets' : 'home')) }}',
        userRole: '{{ auth()->user()->role ?? 'guest' }}'
     }">

    @auth
        @if(auth()->user()->isStaff())
            <!-- Staff Navigation - Enhanced with Contact (Home, E-Ticket QR Scanner, Tickets, Contact) -->
            <div class="flex items-center justify-around py-3 px-4 max-w-lg mx-auto">

                <!-- Home -->
                <a href="{{ route('home') }}"
                   @click="activeTab = 'home'"
                   class="flex flex-col items-center justify-center p-2.5 rounded-xl transition-all duration-300 min-w-0 flex-1 touch-target"
                   :class="activeTab === 'home' ? 'bg-blue-50 text-blue-600 scale-105' : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50/50'">
                    <div class="relative">
                        <svg class="w-6 h-6 transition-transform duration-300"
                             :class="activeTab === 'home' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        <!-- Active indicator -->
                        <div x-show="activeTab === 'home'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-0"
                             x-transition:enter-end="opacity-100 scale-100"
                             class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-blue-600 rounded-full shadow-sm"></div>
                    </div>
                    <span class="text-xs font-semibold mt-1.5 transition-all duration-300"
                          :class="activeTab === 'home' ? 'text-blue-600' : ''">
                        Home
                    </span>
                </a>

                <!-- E-Ticket QR Scanner (Center Button) -->
                <a href="{{ route('validation.staff') }}"
                   @click="activeTab = 'scan'"
                   class="flex flex-col items-center justify-center p-3.5 rounded-2xl transition-all duration-300 transform hover:scale-105 relative mx-2 touch-target staff-scanner-button"
                   :class="activeTab === 'scan' ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-xl scale-110' : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-lg'">
                    <div class="relative">
                        <svg class="w-8 h-8 transition-transform duration-300"
                             :class="activeTab === 'scan' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4M4 4h4m0 0V4m0 0h4m0 0v4M4 16h4m0 0v4m0 0h4m0 0v-4"/>
                        </svg>
                        <!-- Pulse animation for center button -->
                        <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-400 to-emerald-500 animate-ping opacity-40"></div>
                        <!-- Scanning effect -->
                        <div class="absolute inset-0 rounded-2xl bg-white/20 animate-pulse"></div>
                    </div>
                    <span class="text-xs font-bold mt-1.5">Scanner</span>
                </a>

                <!-- Tickets -->
                <a href="{{ route('tickets.index') }}"
                   @click="activeTab = 'tickets'"
                   class="flex flex-col items-center justify-center p-2.5 rounded-xl transition-all duration-300 min-w-0 flex-1 touch-target"
                   :class="activeTab === 'tickets' ? 'bg-purple-50 text-purple-600 scale-105' : 'text-gray-500 hover:text-purple-600 hover:bg-purple-50/50'">
                    <div class="relative">
                        <svg class="w-6 h-6 transition-transform duration-300"
                             :class="activeTab === 'tickets' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                        </svg>
                        <!-- Active indicator -->
                        <div x-show="activeTab === 'tickets'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-0"
                             x-transition:enter-end="opacity-100 scale-100"
                             class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-purple-600 rounded-full shadow-sm"></div>
                    </div>
                    <span class="text-xs font-semibold mt-1.5 transition-all duration-300"
                          :class="activeTab === 'tickets' ? 'text-purple-600' : ''">
                        Tickets
                    </span>
                </a>

                <!-- Contact (Staff Only) -->
                <a href="{{ route('contact.index') }}"
                   @click="activeTab = 'contact'"
                   class="flex flex-col items-center justify-center p-2.5 rounded-xl transition-all duration-300 min-w-0 flex-1 touch-target"
                   :class="activeTab === 'contact' ? 'bg-orange-50 text-orange-600 scale-105' : 'text-gray-500 hover:text-orange-600 hover:bg-orange-50/50'">
                    <div class="relative">
                        <svg class="w-6 h-6 transition-transform duration-300"
                             :class="activeTab === 'contact' ? 'scale-110' : ''"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <!-- Active indicator -->
                        <div x-show="activeTab === 'contact'"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-0"
                             x-transition:enter-end="opacity-100 scale-100"
                             class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-orange-600 rounded-full shadow-sm"></div>
                    </div>
                    <span class="text-xs font-semibold mt-1.5 transition-all duration-300"
                          :class="activeTab === 'contact' ? 'text-orange-600' : ''">
                        Contact
                    </span>
                </a>
            </div>
        @else
            <!-- Regular User Navigation -->
            <div class="flex items-center justify-around py-2 px-4 max-w-md mx-auto">
                <!-- Home -->
                <a href="{{ route('home') }}" class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    <span class="text-xs font-medium mt-1">Home</span>
                </a>
                <!-- My Tickets -->
                <a href="{{ route('tiket-saya') }}" class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z"/>
                    </svg>
                    <span class="text-xs font-medium mt-1">Tiket</span>
                </a>
                <!-- Search -->
                <button class="flex flex-col items-center justify-center p-3 rounded-2xl bg-primary text-white shadow-md">
                    <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                    <span class="text-xs font-bold mt-1">Cari</span>
                </button>
                <!-- Contact -->
                <a href="{{ route('contact.index') }}" class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    <span class="text-xs font-medium mt-1">Kontak</span>
                </a>
                <!-- Profile -->
                <a href="#" class="flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span class="text-xs font-medium mt-1">Akun</span>
                </a>
            </div>
        @endif
    @else
        <!-- Guest Navigation -->
        <div class="flex items-center justify-around py-2 px-4 max-w-md mx-auto">
            <a href="{{ route('login') }}" class="flex flex-col items-center justify-center p-3 rounded-2xl bg-primary text-white shadow-md">
                <svg class="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                </svg>
                <span class="text-xs font-bold mt-1">Login</span>
            </a>
        </div>
    @endauth

    <!-- Bottom safe area for devices with home indicator -->
    <div class="h-safe-bottom bg-white/95"></div>
</nav>

<!-- Floating Footer Styles -->
<style>
    /* Safe area for devices with home indicator */
    .h-safe-bottom {
        height: env(safe-area-inset-bottom, 0px);
    }

    /* Smooth transitions for all navigation elements */
    nav a, nav button {
        -webkit-tap-highlight-color: transparent;
    }

    /* Haptic feedback simulation */
    nav a:active, nav button:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }

    /* Enhanced animations for staff center button */
    .staff-scanner-button {
        position: relative;
        overflow: hidden;
    }

    .staff-scanner-button::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .staff-scanner-button:active::before {
        width: 300px;
        height: 300px;
    }
</style>

<!-- JavaScript for enhanced interactions -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add haptic feedback for supported devices
    if ('vibrate' in navigator) {
        document.querySelectorAll('nav a, nav button').forEach(element => {
            element.addEventListener('touchstart', () => {
                navigator.vibrate(10); // Short vibration
            });
        });
    }

    // Enhanced feedback for staff scanner button
    const scannerButton = document.querySelector('a[href*="validation.staff"]');
    if (scannerButton) {
        scannerButton.addEventListener('touchstart', () => {
            if ('vibrate' in navigator) {
                navigator.vibrate([50, 30, 50]); // Double vibration pattern
            }
        });
    }
});
</script>

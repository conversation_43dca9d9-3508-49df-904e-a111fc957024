@props(['user', 'size' => 'md', 'showTooltip' => true])

@php
    // Badge level configuration
    $badgeLevels = [
        1 => [
            'name' => 'Bronze',
            'color' => '#CD7F32',
            'icon' => 'fas fa-medal',
            'description' => 'Bronze Badge - Basic access with 3 months duration'
        ],
        2 => [
            'name' => 'Silver',
            'color' => '#C0C0C0',
            'icon' => 'fas fa-award',
            'description' => 'Silver Badge - Enhanced features with 6 months duration'
        ],
        3 => [
            'name' => 'Gold',
            'color' => '#FFD700',
            'icon' => 'fas fa-crown',
            'description' => 'Gold Badge - Premium features with 1 year duration'
        ],
        4 => [
            'name' => 'Platinum',
            'color' => '#E5E4E2',
            'icon' => 'fas fa-gem',
            'description' => 'Platinum Badge - Full access + Custom Templates with 2 years duration'
        ]
    ];

    // Initialize default values
    $displayName = 'Bronze';
    $color = '#CD7F32';
    $icon = 'fas fa-medal';
    $description = 'Default badge level';

    // Get user badge level
    if ($user && $user->badge_level_id) {
        $badgeId = $user->badge_level_id;
        if (isset($badgeLevels[$badgeId])) {
            $badge = $badgeLevels[$badgeId];
            $displayName = $badge['name'];
            $color = $badge['color'];
            $icon = $badge['icon'];
            $description = $badge['description'];
        } else {
            // Unknown badge level
            $displayName = 'Badge #' . $badgeId;
            $color = '#6B7280';
            $icon = 'fas fa-question-circle';
            $description = 'Unknown badge level';
        }
    }

    // Size classes
    $sizeClasses = [
        'xs' => 'px-2 py-1 text-xs',
        'sm' => 'px-2 py-1 text-sm',
        'md' => 'px-3 py-1 text-sm',
        'lg' => 'px-4 py-2 text-base',
        'xl' => 'px-6 py-3 text-lg'
    ];

    $iconSizes = [
        'xs' => 'w-3 h-3',
        'sm' => 'w-3 h-3',
        'md' => 'w-4 h-4',
        'lg' => 'w-5 h-5',
        'xl' => 'w-6 h-6'
    ];

    $sizeClass = $sizeClasses[$size] ?? $sizeClasses['md'];
    $iconSize = $iconSizes[$size] ?? $iconSizes['md'];
@endphp

<div class="inline-flex items-center {{ $sizeClass }} rounded-full font-semibold text-white shadow-sm relative group"
     style="background: {{ $color }}; color: white;"
     @if($showTooltip)
         title="{{ $description }}"
         data-tooltip="{{ $description }}"
     @endif>

    <!-- Badge Icon -->
    <i class="{{ $icon }} {{ $iconSize }} mr-1"></i>

    <!-- Badge Name -->
    <span>{{ $displayName }}</span>

    <!-- Tooltip (if enabled) -->
    @if($showTooltip && $description)
        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {{ $description }}
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
    @endif
</div>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pro E-Ticket Boarding Pass</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 20px;
            min-height: 100vh;
        }

        .boarding-pass {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            position: relative;
        }

        .boarding-pass::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
        }

        .pass-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 40px;
            position: relative;
            overflow: hidden;
        }

        .pass-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .airline-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -1px;
        }

        .boarding-pass-label {
            font-size: 14px;
            font-weight: 500;
            opacity: 0.9;
            letter-spacing: 2px;
        }

        .event-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-subtitle {
            font-size: 16px;
            opacity: 0.9;
            font-weight: 400;
        }

        .pass-body {
            padding: 40px;
            background: white;
        }

        .main-info {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .left-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .info-group {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #8b5cf6;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 6px;
        }

        .info-value {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
        }

        .info-value.large {
            font-size: 24px;
        }

        .right-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .qr-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 16px;
            border: 2px solid #e5e7eb;
        }

        .qr-code {
            display: block;
        }

        .qr-instruction {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .ticket-details {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 11px;
            font-weight: 600;
            color: #8b5cf6;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .perforated-line {
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                #d1d5db 0px,
                #d1d5db 10px,
                transparent 10px,
                transparent 20px
            );
            margin: 30px 0;
        }

        .footer-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-icon {
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
        }

        .support-info {
            text-align: right;
            font-size: 12px;
            color: #6b7280;
        }

        .support-info strong {
            color: #374151;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px 16px;
            }

            .pass-header {
                padding: 24px;
            }

            .event-title {
                font-size: 24px;
            }

            .pass-body {
                padding: 24px;
            }

            .main-info {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .left-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .details-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .footer-section {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .boarding-pass {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-pass">
        <!-- Header -->
        <div class="pass-header">
            <div class="header-content">
                <div class="airline-info">
                    <div class="logo">TiXara</div>
                    <div class="boarding-pass-label">E-TICKET BOARDING PASS</div>
                </div>
                <h1 class="event-title">{{ $ticket['event_title'] ?? 'Premium Event Experience' }}</h1>
                <p class="event-subtitle">{{ $ticket['venue_name'] ?? 'Premium Venue Location' }}</p>
            </div>
        </div>

        <!-- Body -->
        <div class="pass-body">
            <!-- Main Information -->
            <div class="main-info">
                <div class="left-section">
                    <div class="info-group">
                        <div class="info-label">Passenger Name</div>
                        <div class="info-value">{{ $ticket['buyer_name'] ?? 'John Doe' }}</div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Ticket Number</div>
                        <div class="info-value">{{ $ticket['ticket_number'] ?? 'TIX-PRO-001' }}</div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Boarding Pass</div>
                        <div class="info-value">{{ $ticket['boarding_pass_id'] ?? 'BP250127ABC123' }}</div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Date</div>
                        <div class="info-value large">{{ $ticket['event_date'] ?? 'Dec 31' }}</div>
                    </div>
                    <div class="info-group">
                        <div class="info-label">Time</div>
                        <div class="info-value large">{{ $ticket['event_time'] ?? '19:00' }}</div>
                    </div>
                </div>

                <div class="right-section">
                    <div class="qr-container">
                        <div class="qr-code">
                            {!! QrCode::size(140)->generate($ticket['qr_code'] ?? 'SAMPLE-QR-CODE') !!}
                        </div>
                    </div>
                    <div class="qr-instruction">Scan at venue entrance</div>
                </div>
            </div>

            <!-- Perforated Line -->
            <div class="perforated-line"></div>

            <!-- Ticket Details -->
            <div class="ticket-details">
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-label">Event Category</div>
                        <div class="detail-value">{{ $ticket['category'] ?? 'Premium Event' }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Ticket Price</div>
                        <div class="detail-value">{{ $ticket['formatted_price'] ?? 'Rp 250.000' }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Email</div>
                        <div class="detail-value">{{ $ticket['buyer_email'] ?? '<EMAIL>' }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Generated</div>
                        <div class="detail-value">{{ now()->format('M d, Y H:i') }}</div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="footer-section">
                <div class="status-badge">
                    <div class="status-icon"></div>
                    Valid Ticket
                </div>
                <div class="support-info">
                    <div><strong>Support:</strong> <EMAIL></div>
                    <div><strong>Website:</strong> tixara.my.id</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

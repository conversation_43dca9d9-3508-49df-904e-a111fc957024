<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Card Ticket - {{ $ticket->event->title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .card-container {
            background: #fff;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            position: relative;
            transform: perspective(1000px) rotateX(5deg);
            transition: transform 0.3s ease;
        }
        
        .card-container:hover {
            transform: perspective(1000px) rotateX(0deg);
        }
        
        .card-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px;
            position: relative;
            overflow: hidden;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%); }
            50% { transform: translateX(-50%) translateY(-50%); }
        }
        
        .brand-logo {
            font-size: 24px;
            font-weight: 800;
            letter-spacing: 2px;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
        }
        
        .event-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .event-category {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            z-index: 2;
        }
        
        .card-body {
            padding: 40px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
        
        .ticket-grid {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 40px;
            align-items: start;
        }
        
        .ticket-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
        }
        
        .detail-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .detail-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .detail-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 18px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
        }
        
        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #f8f9fa;
        }
        
        .qr-code {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            position: relative;
        }
        
        .qr-code::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            border-radius: 17px;
            z-index: -1;
            animation: rotate 3s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .qr-code img {
            width: 140px;
            height: 140px;
            display: block;
            border-radius: 10px;
        }
        
        .ticket-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 1px;
            margin-bottom: 15px;
        }
        
        .qr-instructions {
            font-size: 12px;
            color: #6c757d;
            line-height: 1.5;
            max-width: 160px;
        }
        
        .attendee-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .attendee-info::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }
        
        .attendee-label {
            font-size: 12px;
            opacity: 0.8;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .attendee-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .attendee-email {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .card-footer {
            background: #2c3e50;
            color: white;
            padding: 25px 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .footer-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        .status-indicator.used {
            background: #dc3545;
        }
        
        .status-indicator.cancelled {
            background: #6c757d;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .footer-info {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .footer-right {
            text-align: right;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .security-features {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 10px;
            font-size: 10px;
            color: #6c757d;
            backdrop-filter: blur(10px);
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .card-container {
                box-shadow: none;
                max-width: none;
                transform: none;
                border: 1px solid #dee2e6;
            }
        }
        
        @media (max-width: 768px) {
            .ticket-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .ticket-details {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .card-footer {
                flex-direction: column;
                text-align: center;
            }
            
            .footer-right {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="security-features">
            SECURE<br>VERIFIED
        </div>
        
        <!-- Header -->
        <div class="card-header">
            <div class="brand-logo">TiXara</div>
            <div class="event-title">{{ $ticket->event->title }}</div>
            <div class="event-category">{{ $ticket->event->category->name ?? 'Event Ticket' }}</div>
        </div>
        
        <!-- Body -->
        <div class="card-body">
            <!-- Attendee Info -->
            <div class="attendee-info">
                <div class="attendee-label">Ticket Holder</div>
                <div class="attendee-name">{{ $ticket->attendee_name }}</div>
                <div class="attendee-email">{{ $ticket->order->customer_email }}</div>
            </div>
            
            <!-- Ticket Grid -->
            <div class="ticket-grid">
                <!-- Details -->
                <div class="ticket-details">
                    <div class="detail-card">
                        <div class="detail-icon">📅</div>
                        <div class="detail-label">Date</div>
                        <div class="detail-value">{{ $ticket->event->start_date->format('M j, Y') }}</div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="detail-icon">🕐</div>
                        <div class="detail-label">Time</div>
                        <div class="detail-value">{{ $ticket->event->start_date->format('g:i A') }}</div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="detail-icon">📍</div>
                        <div class="detail-label">Location</div>
                        <div class="detail-value">{{ $ticket->event->location }}</div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="detail-icon">🎫</div>
                        <div class="detail-label">Seat</div>
                        <div class="detail-value">{{ $ticket->seat_number ?? 'GA' }}</div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="detail-icon">💰</div>
                        <div class="detail-label">Price</div>
                        <div class="detail-value">Rp {{ number_format($ticket->order->unit_price, 0, ',', '.') }}</div>
                    </div>
                    
                    <div class="detail-card">
                        <div class="detail-icon">🎟️</div>
                        <div class="detail-label">Order</div>
                        <div class="detail-value">{{ $ticket->order->order_number }}</div>
                    </div>
                </div>
                
                <!-- QR Section -->
                <div class="qr-section">
                    <div class="ticket-number">{{ $ticket->ticket_number }}</div>
                    <div class="qr-code">
                        {!! QrCode::size(140)->generate($ticket->qr_code) !!}
                    </div>
                    <div class="qr-instructions">
                        Scan at venue entrance for quick access
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="card-footer">
            <div class="footer-left">
                <div class="status-indicator {{ strtolower($ticket->status) }}"></div>
                <div class="status-text">{{ ucfirst($ticket->status) }}</div>
                <div class="footer-info">
                    Issued: {{ $ticket->order->created_at->format('M j, Y') }}
                </div>
            </div>
            <div class="footer-right">
                <div><strong>TiXara Event Platform</strong></div>
                <div>Secure Digital Ticketing</div>
                <div>Generated: {{ now()->format('M j, Y g:i A') }}</div>
            </div>
        </div>
    </div>
</body>
</html>

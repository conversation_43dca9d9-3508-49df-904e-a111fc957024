@php
    // Configuration with extensive customization options
    $config = $config ?? [];
    $customConfig = $config['custom_config'] ?? [];
    
    // Colors
    $primaryColor = $config['primary_color'] ?? '#6366F1';
    $secondaryColor = $config['secondary_color'] ?? '#F1F5F9';
    $backgroundColor = $config['background_color'] ?? '#FFFFFF';
    $textColor = $config['text_color'] ?? '#0F172A';
    $accentColor = $config['accent_color'] ?? '#F59E0B';
    
    // Layout options
    $layout = $customConfig['layout'] ?? 'horizontal'; // horizontal, vertical, split
    $headerStyle = $customConfig['header_style'] ?? 'gradient'; // gradient, solid, image
    $borderStyle = $customConfig['border_style'] ?? 'rounded'; // rounded, sharp, dashed
    $fontFamily = $customConfig['font_family'] ?? 'Inter'; // Inter, Poppins, Roboto, Custom
    
    // Background options
    $backgroundPattern = $customConfig['background_pattern'] ?? 'none'; // none, dots, lines, waves
    $backgroundImage = $customConfig['background_image'] ?? null;
    
    // Logo and branding
    $organizerLogo = $customConfig['organizer_logo'] ?? null;
    $showWatermark = $customConfig['show_watermark'] ?? true;
    $customWatermark = $customConfig['custom_watermark'] ?? null;
    
    // Content customization
    $showPrice = $customConfig['show_price'] ?? true;
    $showQrCode = $customConfig['show_qr_code'] ?? true;
    $customFields = $customConfig['custom_fields'] ?? [];
    
    // Helper functions
    function getTicketValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket[$key] ?? $default;
        } elseif (is_object($ticket)) {
            return $ticket->{$key} ?? $default;
        }
        return $default;
    }
    
    function getEventValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['event'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->event)) {
            return $ticket->event->{$key} ?? $default;
        }
        return $default;
    }
    
    function getBuyerValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['buyer'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->buyer)) {
            return $ticket->buyer->{$key} ?? $default;
        }
        return $default;
    }
    
    // Dynamic styles based on configuration
    $containerStyle = "
        background: " . ($backgroundImage ? "url('{$backgroundImage}') center/cover, " : "") . 
        ($backgroundPattern === 'dots' ? "radial-gradient(circle, {$primaryColor}20 1px, transparent 1px), " : "") .
        ($backgroundPattern === 'lines' ? "linear-gradient(45deg, {$primaryColor}10 25%, transparent 25%), " : "") .
        ($backgroundPattern === 'waves' ? "url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 20\"><path d=\"M0,10 Q25,0 50,10 T100,10 V20 H0 Z\" fill=\"{$primaryColor}10\"/></svg>'), " : "") .
        "{$backgroundColor};
        color: {$textColor};
        font-family: '{$fontFamily}', sans-serif;
        width: 700px;
        height: 350px;
        position: relative;
        border-radius: " . ($borderStyle === 'rounded' ? '16px' : ($borderStyle === 'sharp' ? '0' : '8px')) . ";
        border: " . ($borderStyle === 'dashed' ? "2px dashed {$primaryColor}" : "1px solid #E2E8F0") . ";
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    ";
    
    $headerBg = $headerStyle === 'gradient' ? 
        "linear-gradient(135deg, {$primaryColor} 0%, {$accentColor} 100%)" : 
        ($headerStyle === 'solid' ? $primaryColor : 'transparent');
@endphp

<div class="ticket-container custom-ticket" style="{{ $containerStyle }}">
    @if($backgroundPattern !== 'none')
        <!-- Background Pattern Overlay -->
        <div style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            pointer-events: none;
            @if($backgroundPattern === 'dots')
                background-image: radial-gradient(circle, {{ $primaryColor }} 2px, transparent 2px);
                background-size: 20px 20px;
            @elseif($backgroundPattern === 'lines')
                background-image: 
                    linear-gradient(45deg, {{ $primaryColor }} 25%, transparent 25%),
                    linear-gradient(-45deg, {{ $primaryColor }} 25%, transparent 25%);
                background-size: 30px 30px;
            @elseif($backgroundPattern === 'waves')
                background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 20\"><path d=\"M0,10 Q25,0 50,10 T100,10 V20 H0 Z\" fill=\"{{ $primaryColor }}\"/></svg>');
                background-size: 100px 20px;
            @endif
        "></div>
    @endif

    <!-- Header -->
    <div style="
        background: {{ $headerBg }};
        padding: 24px;
        position: relative;
        @if($headerStyle === 'image' && $backgroundImage)
            background-image: url('{{ $backgroundImage }}');
            background-size: cover;
            background-position: center;
        @endif
    ">
        @if($headerStyle === 'image')
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
            "></div>
        @endif
        
        <div style="position: relative; z-index: 2;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div style="flex: 1;">
                    @if($organizerLogo)
                        <img src="{{ $organizerLogo }}" alt="Organizer Logo" style="
                            height: 40px;
                            margin-bottom: 12px;
                            filter: brightness(0) invert(1);
                        ">
                    @endif
                    
                    <h1 style="
                        font-size: 28px;
                        font-weight: 700;
                        margin: 0 0 8px 0;
                        color: white;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                        letter-spacing: -0.025em;
                    ">{{ getEventValue($ticket, 'title', 'Event Title') }}</h1>
                    
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 6px 12px;
                        border-radius: 20px;
                        backdrop-filter: blur(10px);
                    ">
                        <span style="
                            color: white;
                            font-size: 12px;
                            font-weight: 600;
                            text-transform: uppercase;
                            letter-spacing: 0.05em;
                        ">{{ $customConfig['ticket_type'] ?? 'Custom Ticket' }}</span>
                    </div>
                </div>
                
                @if($customConfig['header_icon'])
                    <div style="
                        width: 64px;
                        height: 64px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    ">
                        {!! $customConfig['header_icon'] !!}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div style="
        padding: 24px;
        display: {{ $layout === 'vertical' ? 'block' : 'flex' }};
        gap: 24px;
        height: calc(100% - {{ $showWatermark ? '140px' : '120px' }});
    ">
        <!-- Left Section / Main Content -->
        <div style="{{ $layout === 'vertical' ? 'margin-bottom: 20px;' : 'flex: 1;' }}">
            <div style="
                display: grid;
                grid-template-columns: {{ $layout === 'vertical' ? '1fr 1fr 1fr' : '1fr 1fr' }};
                gap: 20px;
                margin-bottom: 20px;
            ">
                <!-- Event Date -->
                <div style="
                    background: rgba(255, 255, 255, 0.8);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                ">
                    <h3 style="
                        font-size: 11px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 6px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.1em;
                    ">{{ $customConfig['date_label'] ?? 'Event Date' }}</h3>
                    @php
                        $startDate = getEventValue($ticket, 'start_date', now());
                        $parsedDate = \Carbon\Carbon::parse($startDate);
                    @endphp
                    <p style="
                        font-size: 16px;
                        font-weight: 700;
                        margin: 0 0 2px 0;
                        color: {{ $textColor }};
                    ">{{ $parsedDate->format($customConfig['date_format'] ?? 'M j, Y') }}</p>
                    <p style="
                        font-size: 12px;
                        margin: 0;
                        color: #6B7280;
                        font-weight: 500;
                    ">{{ $parsedDate->format('g:i A') }}</p>
                </div>

                <!-- Venue -->
                <div style="
                    background: rgba(255, 255, 255, 0.8);
                    padding: 16px;
                    border-radius: 12px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                ">
                    <h3 style="
                        font-size: 11px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 6px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.1em;
                    ">{{ $customConfig['venue_label'] ?? 'Venue' }}</h3>
                    <p style="
                        font-size: 14px;
                        font-weight: 700;
                        margin: 0 0 2px 0;
                        color: {{ $textColor }};
                    ">{{ getEventValue($ticket, 'venue_name', 'TBA') }}</p>
                    <p style="
                        font-size: 12px;
                        margin: 0;
                        color: #6B7280;
                        font-weight: 500;
                    ">{{ getEventValue($ticket, 'city', 'Location TBA') }}</p>
                </div>

                @if($showPrice)
                    <!-- Price -->
                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 16px;
                        border-radius: 12px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    ">
                        <h3 style="
                            font-size: 11px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 6px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">{{ $customConfig['price_label'] ?? 'Price' }}</h3>
                        @php
                            $price = getTicketValue($ticket, 'price', 0);
                        @endphp
                        <p style="
                            font-size: 18px;
                            font-weight: 800;
                            margin: 0;
                            color: {{ $accentColor }};
                        ">Rp {{ number_format($price, 0, ',', '.') }}</p>
                    </div>
                @endif
            </div>

            <!-- Attendee Info -->
            <div style="
                background: rgba(255, 255, 255, 0.9);
                padding: 20px;
                border-radius: 12px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                margin-bottom: 16px;
            ">
                <h3 style="
                    font-size: 11px;
                    font-weight: 600;
                    color: {{ $primaryColor }};
                    margin: 0 0 8px 0;
                    text-transform: uppercase;
                    letter-spacing: 0.1em;
                ">{{ $customConfig['attendee_label'] ?? 'Attendee' }}</h3>
                <p style="
                    font-size: 16px;
                    font-weight: 700;
                    margin: 0 0 4px 0;
                    color: {{ $textColor }};
                ">{{ getTicketValue($ticket, 'attendee_name', getBuyerValue($ticket, 'name', 'Guest')) }}</p>
                <p style="
                    font-size: 12px;
                    margin: 0;
                    color: #6B7280;
                    font-weight: 500;
                ">{{ getTicketValue($ticket, 'attendee_email', getBuyerValue($ticket, 'email', 'N/A')) }}</p>
            </div>

            <!-- Custom Fields -->
            @if(!empty($customFields))
                @foreach($customFields as $field)
                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 16px;
                        border-radius: 12px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        margin-bottom: 12px;
                    ">
                        <h3 style="
                            font-size: 11px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 6px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">{{ $field['label'] }}</h3>
                        <p style="
                            font-size: 14px;
                            font-weight: 600;
                            margin: 0;
                            color: {{ $textColor }};
                        ">{{ $field['value'] }}</p>
                    </div>
                @endforeach
            @endif
        </div>

        @if($showQrCode)
            <!-- Right Section - QR Code -->
            <div style="
                width: {{ $layout === 'vertical' ? '100%' : '180px' }};
                display: flex;
                flex-direction: {{ $layout === 'vertical' ? 'row' : 'column' }};
                align-items: center;
                justify-content: center;
                {{ $layout !== 'vertical' ? 'border-left: 1px solid #E2E8F0; padding-left: 24px;' : '' }}
            ">
                <div style="
                    background: rgba(255, 255, 255, 0.95);
                    padding: 20px;
                    border-radius: 16px;
                    backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                    margin-bottom: 16px;
                ">
                    @php
                        $qrData = getTicketValue($ticket, 'qr_code', 
                            getTicketValue($ticket, 'ticket_number', 
                                getTicketValue($ticket, 'ticket_code', 'INVALID')
                            )
                        );
                    @endphp
                    @if(class_exists('SimpleSoftwareIO\QrCode\Facades\QrCode'))
                        {!! QrCode::size(120)->generate($qrData) !!}
                    @else
                        <div style="
                            width: 120px;
                            height: 120px;
                            background: #f8fafc;
                            border: 2px dashed #cbd5e1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 12px;
                            color: #64748b;
                            text-align: center;
                            border-radius: 8px;
                        ">
                            QR Code<br>{{ substr($qrData, 0, 8) }}
                        </div>
                    @endif
                </div>

                <div style="text-align: center;">
                    <p style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 4px 0;
                    ">{{ $customConfig['qr_label'] ?? 'Scan to Verify' }}</p>
                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 6px 10px;
                        border-radius: 6px;
                        backdrop-filter: blur(10px);
                    ">
                        <p style="
                            font-size: 9px;
                            font-weight: 600;
                            font-family: 'JetBrains Mono', monospace;
                            margin: 0;
                            color: {{ $textColor }};
                            letter-spacing: 1px;
                        ">{{ getTicketValue($ticket, 'ticket_number', getTicketValue($ticket, 'ticket_code', 'TIX-000000')) }}</p>
                    </div>
                </div>
            </div>
        @endif
    </div>

    @if($showWatermark)
        <!-- Footer / Watermark -->
        <div style="
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 24px;
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.3);
            text-align: center;
        ">
            <p style="
                font-size: 9px;
                color: #94A3B8;
                margin: 0;
            ">{{ $customWatermark ?? 'Custom Design • Powered by Tixara • ' . now()->format('Y') }}</p>
        </div>
    @endif
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

.custom-ticket {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    box-sizing: border-box;
}

.custom-ticket * {
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .custom-ticket {
        width: 100% !important;
        max-width: 700px !important;
        height: auto !important;
        min-height: 350px !important;
    }
}

@media print {
    .custom-ticket {
        width: 700px !important;
        height: 350px !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }
}
</style>

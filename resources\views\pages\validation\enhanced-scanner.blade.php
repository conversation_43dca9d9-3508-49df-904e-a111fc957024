@extends('layouts.app')

@section('title', 'Enhanced Ticket Validation Scanner')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                            <i data-lucide="scan-line" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900 dark:text-white">Enhanced Scanner</h1>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Advanced ticket validation system</p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Scanner Status -->
                    <div class="flex items-center space-x-2">
                        <div id="statusIndicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                        <span id="statusText" class="text-sm text-gray-600 dark:text-gray-400">Ready</span>
                    </div>

                    <!-- Stats -->
                    <div class="hidden sm:flex items-center space-x-4 text-sm">
                        <div class="text-center">
                            <div class="font-bold text-green-600" id="validCount">0</div>
                            <div class="text-gray-500">Valid</div>
                        </div>
                        <div class="text-center">
                            <div class="font-bold text-red-600" id="invalidCount">0</div>
                            <div class="text-gray-500">Invalid</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Scanner Section -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <!-- Scanner Header -->
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-white">E-Tiket Scanner</h2>
                            <div class="flex items-center space-x-2">
                                <!-- Camera Toggle -->
                                <button id="toggleCamera" class="hidden bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-lg text-sm transition-colors">
                                    <i data-lucide="camera" class="w-4 h-4 mr-1"></i>
                                    <span id="cameraLabel">Back</span>
                                </button>

                                <!-- Torch Toggle -->
                                <button id="toggleTorch" class="hidden bg-white/20 hover:bg-white/30 text-white px-3 py-1 rounded-lg text-sm transition-colors">
                                    <i data-lucide="flashlight" class="w-4 h-4 mr-1"></i>
                                    Torch
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Scanner Container -->
                    <div class="p-6">
                        <div class="relative">
                            <!-- Video Container -->
                            <div id="scannerContainer" class="relative bg-black rounded-xl overflow-hidden" style="height: 400px;">
                                <video id="scannerVideo" class="w-full h-full object-cover" playsinline></video>

                                <!-- Scanning Overlay -->
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="w-64 h-64 border-2 border-white rounded-lg relative">
                                        <div class="absolute top-0 left-0 w-8 h-8 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                                        <div class="absolute top-0 right-0 w-8 h-8 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                                        <div class="absolute bottom-0 left-0 w-8 h-8 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                                        <div class="absolute bottom-0 right-0 w-8 h-8 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>

                                        <!-- Scanning Line Animation -->
                                        <div id="scanLine" class="absolute left-0 right-0 h-0.5 bg-blue-500 opacity-75 animate-pulse"></div>
                                    </div>
                                </div>

                                <!-- Status Overlay -->
                                <div id="scannerStatus" class="absolute top-4 left-4 right-4 text-center">
                                    <div class="bg-black/50 text-white px-4 py-2 rounded-lg text-sm">
                                        <span id="scannerStatusText">Initializing camera...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Controls -->
                            <div class="mt-6 flex flex-col sm:flex-row gap-4">
                                <div class="flex-1">
                                    <button id="startScanner" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105">
                                        <i data-lucide="play" class="w-5 h-5 mr-2"></i>
                                        Start Scanner
                                    </button>
                                </div>
                                <div class="flex-1">
                                    <button id="stopScanner" class="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105" disabled>
                                        <i data-lucide="square" class="w-5 h-5 mr-2"></i>
                                        Stop Scanner
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Manual Input Section -->
                <div class="mt-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Manual Input</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Enter ticket number or QR code manually</p>
                    </div>
                    <div class="p-6">
                        <form id="manualValidationForm" class="space-y-4">
                            <div>
                                <label for="manualInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Ticket Number or QR Code
                                </label>
                                <div class="flex space-x-2">
                                    <input type="text" id="manualInput"
                                           class="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                                           placeholder="Enter ticket number or scan QR code...">
                                    <button type="submit"
                                            class="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-semibold px-6 py-3 rounded-lg transition-all duration-200">
                                        <i data-lucide="check" class="w-5 h-5"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Validation Result -->
                <div id="validationResult" class="hidden bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div id="resultContent">
                            <!-- Result will be populated here -->
                        </div>
                    </div>
                </div>

                <!-- Recent Validations -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Validations</h3>
                    </div>
                    <div class="p-6">
                        <div id="validationHistory" class="space-y-3">
                            <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                                <i data-lucide="clock" class="w-8 h-8 mx-auto mb-2"></i>
                                <p>No validations yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Session Stats</h3>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600" id="sessionValid">0</div>
                                <div class="text-sm text-gray-500">Valid Tickets</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-red-600" id="sessionInvalid">0</div>
                                <div class="text-sm text-gray-500">Invalid Tickets</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600" id="sessionTotal">0</div>
                                <div class="text-sm text-gray-500">Total Scanned</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600" id="sessionRate">0%</div>
                                <div class="text-sm text-gray-500">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio for feedback -->
<audio id="successSound" preload="auto">
    <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
</audio>
<audio id="errorSound" preload="auto">
    <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
</audio>

@endsection

@push('styles')
<style>
#scanLine {
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% { top: 0; }
    50% { top: 50%; }
    100% { top: 100%; }
}

.scanner-active #scanLine {
    animation-play-state: running;
}

.scanner-inactive #scanLine {
    animation-play-state: paused;
}

.validation-success {
    animation: successPulse 0.6s ease-in-out;
}

.validation-error {
    animation: errorShake 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>
@endpush

@push('scripts')
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
class EnhancedTicketScanner {
    constructor() {
        this.html5QrCode = null;
        this.isScanning = false;
        this.currentCameraIndex = 0;
        this.availableCameras = [];
        this.currentFacingMode = 'environment'; // Start with back camera
        this.validCount = 0;
        this.invalidCount = 0;
        this.sessionStats = {
            valid: 0,
            invalid: 0,
            total: 0
        };

        this.initializeElements();
        this.bindEvents();
        this.initializeCamera();
    }

    initializeElements() {
        // Scanner elements
        this.startButton = document.getElementById('startScanner');
        this.stopButton = document.getElementById('stopScanner');
        this.toggleCameraButton = document.getElementById('toggleCamera');
        this.toggleTorchButton = document.getElementById('toggleTorch');
        this.cameraLabel = document.getElementById('cameraLabel');

        // Status elements
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusText = document.getElementById('statusText');
        this.scannerStatusText = document.getElementById('scannerStatusText');

        // Form elements
        this.manualForm = document.getElementById('manualValidationForm');
        this.manualInput = document.getElementById('manualInput');

        // Result elements
        this.validationResult = document.getElementById('validationResult');
        this.resultContent = document.getElementById('resultContent');
        this.validationHistory = document.getElementById('validationHistory');

        // Stats elements
        this.validCountEl = document.getElementById('validCount');
        this.invalidCountEl = document.getElementById('invalidCount');
        this.sessionValidEl = document.getElementById('sessionValid');
        this.sessionInvalidEl = document.getElementById('sessionInvalid');
        this.sessionTotalEl = document.getElementById('sessionTotal');
        this.sessionRateEl = document.getElementById('sessionRate');

        // Audio elements
        this.successSound = document.getElementById('successSound');
        this.errorSound = document.getElementById('errorSound');
    }

    bindEvents() {
        this.startButton.addEventListener('click', () => this.startScanning());
        this.stopButton.addEventListener('click', () => this.stopScanning());
        this.toggleCameraButton.addEventListener('click', () => this.toggleCamera());
        this.toggleTorchButton.addEventListener('click', () => this.toggleTorch());
        this.manualForm.addEventListener('submit', (e) => this.handleManualInput(e));

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                if (this.isScanning) {
                    this.stopScanning();
                } else {
                    this.startScanning();
                }
            }
        });
    }

    async initializeCamera() {
        try {
            this.updateStatus('Initializing camera...', 'loading');

            // Get available cameras
            const cameras = await Html5Qrcode.getCameras();
            this.availableCameras = cameras;

            if (cameras.length === 0) {
                throw new Error('No cameras found');
            }

            // Show camera toggle if multiple cameras available
            if (cameras.length > 1) {
                this.toggleCameraButton.classList.remove('hidden');
            }

            this.updateStatus('Camera ready', 'ready');
            this.updateCameraLabel();

        } catch (error) {
            console.error('Failed to initialize camera:', error);
            this.updateStatus('Camera initialization failed', 'error');
            this.showError('Failed to initialize camera: ' + error.message);
        }
    }

    async startScanning() {
        if (this.isScanning) return;

        try {
            this.updateStatus('Starting scanner...', 'loading');

            this.html5QrCode = new Html5Qrcode('scannerVideo');

            const cameraId = this.availableCameras.length > 0 ?
                this.availableCameras[this.currentCameraIndex].id :
                { facingMode: this.currentFacingMode };

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0,
                disableFlip: false,
            };

            await this.html5QrCode.start(
                cameraId,
                config,
                (decodedText, decodedResult) => this.onScanSuccess(decodedText, decodedResult),
                (errorMessage) => this.onScanError(errorMessage)
            );

            this.isScanning = true;
            this.updateStatus('Scanner active', 'active');
            this.updateButtons();
            this.updateCameraLabel();

            // Show torch button if supported
            this.checkTorchSupport();

            // Add scanning animation
            document.getElementById('scannerContainer').classList.add('scanner-active');

        } catch (error) {
            console.error('Failed to start scanner:', error);
            this.updateStatus('Failed to start scanner', 'error');
            this.showError('Failed to start scanner: ' + error.message);
        }
    }

    async stopScanning() {
        if (!this.isScanning || !this.html5QrCode) return;

        try {
            await this.html5QrCode.stop();
            this.html5QrCode.clear();
            this.html5QrCode = null;

            this.isScanning = false;
            this.updateStatus('Scanner stopped', 'ready');
            this.updateButtons();

            // Remove scanning animation
            document.getElementById('scannerContainer').classList.remove('scanner-active');

        } catch (error) {
            console.error('Failed to stop scanner:', error);
        }
    }

    async toggleCamera() {
        if (this.availableCameras.length <= 1) return;

        const wasScanning = this.isScanning;

        if (wasScanning) {
            await this.stopScanning();
        }

        // Switch to next camera
        this.currentCameraIndex = (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.currentFacingMode = this.currentCameraIndex === 0 ? 'environment' : 'user';

        this.updateCameraLabel();

        if (wasScanning) {
            setTimeout(() => this.startScanning(), 500);
        }
    }

    async toggleTorch() {
        if (!this.html5QrCode || !this.isScanning) return;

        try {
            const capabilities = await this.html5QrCode.getRunningTrackCapabilities();
            if (capabilities.torch) {
                const settings = await this.html5QrCode.getRunningTrackSettings();
                await this.html5QrCode.applyVideoConstraints({
                    advanced: [{ torch: !settings.torch }]
                });

                this.toggleTorchButton.classList.toggle('bg-yellow-500');
            }
        } catch (error) {
            console.error('Failed to toggle torch:', error);
        }
    }

    async checkTorchSupport() {
        if (!this.html5QrCode || !this.isScanning) return;

        try {
            const capabilities = await this.html5QrCode.getRunningTrackCapabilities();
            if (capabilities.torch) {
                this.toggleTorchButton.classList.remove('hidden');
            }
        } catch (error) {
            // Torch not supported
        }
    }

    onScanSuccess(decodedText, decodedResult) {
        console.log('QR Code detected:', decodedText);
        this.validateTicket(decodedText);
    }

    onScanError(errorMessage) {
        // Ignore frequent scan errors
    }

    async handleManualInput(event) {
        event.preventDefault();
        const input = this.manualInput.value.trim();

        if (!input) {
            this.showError('Please enter a ticket number or QR code');
            return;
        }

        await this.validateTicket(input);
        this.manualInput.value = '';
    }

    async validateTicket(ticketCode) {
        try {
            this.updateStatus('Validating ticket...', 'loading');

            const response = await fetch('{{ route("tickets.validate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    identifier: ticketCode,
                    type: 'qr_code'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showValidationSuccess(result);
                this.addToHistory(result.ticket, true);
                this.updateSessionStats(true);
                this.playSound('success');
                this.vibrate([100, 50, 100]);
                this.updateStatus('Ticket validated successfully', 'success');
            } else {
                this.showValidationError(result.message || 'Invalid ticket');
                this.addToHistory({ code: ticketCode, error: result.message }, false);
                this.updateSessionStats(false);
                this.playSound('error');
                this.vibrate([200]);
                this.updateStatus('Ticket validation failed', 'error');
            }

        } catch (error) {
            console.error('Validation error:', error);
            this.showValidationError('Network error occurred');
            this.updateSessionStats(false);
            this.playSound('error');
            this.updateStatus('Network error', 'error');
        }
    }

    showValidationSuccess(result) {
        const ticket = result.ticket;

        this.resultContent.innerHTML = `
            <div class="validation-success">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                        <i data-lucide="check" class="w-6 h-6 text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-green-800">Valid Ticket</h3>
                        <p class="text-sm text-green-600">Ticket validated successfully</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div>
                        <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Ticket Number</label>
                        <p class="text-sm font-mono">${ticket.ticket_number}</p>
                    </div>
                    <div>
                        <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Event</label>
                        <p class="text-sm font-semibold">${ticket.event?.title || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Attendee</label>
                        <p class="text-sm">${ticket.attendee_name || ticket.buyer?.name || 'N/A'}</p>
                    </div>
                    <div>
                        <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ${ticket.status}
                        </span>
                    </div>
                </div>
            </div>
        `;

        this.validationResult.classList.remove('hidden');
        this.validationResult.classList.add('validation-success');

        // Re-initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }

        setTimeout(() => {
            this.validationResult.classList.remove('validation-success');
        }, 600);
    }

    showValidationError(message) {
        this.resultContent.innerHTML = `
            <div class="validation-error">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                        <i data-lucide="x" class="w-6 h-6 text-red-600"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-red-800">Invalid Ticket</h3>
                        <p class="text-sm text-red-600">${message}</p>
                    </div>
                </div>
            </div>
        `;

        this.validationResult.classList.remove('hidden');
        this.validationResult.classList.add('validation-error');

        // Re-initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }

        setTimeout(() => {
            this.validationResult.classList.remove('validation-error');
        }, 600);
    }

    addToHistory(ticket, isValid) {
        const historyItem = document.createElement('div');
        historyItem.className = `flex items-center space-x-3 p-3 rounded-lg ${isValid ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`;

        const time = new Date().toLocaleTimeString();

        historyItem.innerHTML = `
            <div class="flex-shrink-0">
                <div class="w-8 h-8 ${isValid ? 'bg-green-100' : 'bg-red-100'} rounded-full flex items-center justify-center">
                    <i data-lucide="${isValid ? 'check' : 'x'}" class="w-4 h-4 ${isValid ? 'text-green-600' : 'text-red-600'}"></i>
                </div>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium ${isValid ? 'text-green-900' : 'text-red-900'} truncate">
                    ${isValid ? (ticket.ticket_number || 'Valid Ticket') : (ticket.code || 'Invalid Code')}
                </p>
                <p class="text-xs ${isValid ? 'text-green-600' : 'text-red-600'}">
                    ${time} • ${isValid ? (ticket.event?.title || 'Event') : (ticket.error || 'Invalid')}
                </p>
            </div>
        `;

        // Remove "no validations" message if it exists
        const noValidationsMsg = this.validationHistory.querySelector('.text-center');
        if (noValidationsMsg) {
            noValidationsMsg.remove();
        }

        // Add to top of history
        this.validationHistory.insertBefore(historyItem, this.validationHistory.firstChild);

        // Keep only last 10 items
        const items = this.validationHistory.children;
        if (items.length > 10) {
            this.validationHistory.removeChild(items[items.length - 1]);
        }

        // Re-initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    updateSessionStats(isValid) {
        if (isValid) {
            this.sessionStats.valid++;
            this.validCount++;
        } else {
            this.sessionStats.invalid++;
            this.invalidCount++;
        }
        this.sessionStats.total++;

        // Update display
        this.validCountEl.textContent = this.validCount;
        this.invalidCountEl.textContent = this.invalidCount;
        this.sessionValidEl.textContent = this.sessionStats.valid;
        this.sessionInvalidEl.textContent = this.sessionStats.invalid;
        this.sessionTotalEl.textContent = this.sessionStats.total;

        const rate = this.sessionStats.total > 0 ?
            Math.round((this.sessionStats.valid / this.sessionStats.total) * 100) : 0;
        this.sessionRateEl.textContent = rate + '%';
    }

    updateStatus(message, type = 'ready') {
        this.statusText.textContent = message;
        this.scannerStatusText.textContent = message;

        // Update status indicator
        this.statusIndicator.className = 'w-3 h-3 rounded-full ';
        switch (type) {
            case 'loading':
                this.statusIndicator.className += 'bg-yellow-400 animate-pulse';
                break;
            case 'active':
                this.statusIndicator.className += 'bg-green-400 animate-pulse';
                break;
            case 'success':
                this.statusIndicator.className += 'bg-green-500';
                break;
            case 'error':
                this.statusIndicator.className += 'bg-red-500';
                break;
            default:
                this.statusIndicator.className += 'bg-gray-400';
        }
    }

    updateButtons() {
        this.startButton.disabled = this.isScanning;
        this.stopButton.disabled = !this.isScanning;

        if (this.isScanning) {
            this.startButton.classList.add('opacity-50', 'cursor-not-allowed');
            this.stopButton.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            this.startButton.classList.remove('opacity-50', 'cursor-not-allowed');
            this.stopButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }

    updateCameraLabel() {
        if (this.availableCameras.length > 1) {
            this.cameraLabel.textContent = this.currentFacingMode === 'environment' ? 'Back' : 'Front';
        }
    }

    playSound(type) {
        try {
            const audio = type === 'success' ? this.successSound : this.errorSound;
            audio.currentTime = 0;
            audio.play().catch(() => {
                // Audio play failed, ignore
            });
        } catch (error) {
            // Audio not supported
        }
    }

    vibrate(pattern) {
        if (navigator.vibrate) {
            navigator.vibrate(pattern);
        }
    }

    showError(message) {
        // You can implement a toast notification here
        console.error(message);
    }
}

// Initialize scanner when page loads
document.addEventListener('DOMContentLoaded', function() {
    new EnhancedTicketScanner();
});
</script>

@extends('layouts.organizer')

@section('title', 'Dashboard Organizer')

@push('styles')
<style>
/* Custom styles for Organizer Dashboard */
.dashboard-card {
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.metric-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .chart-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Loading animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Modern Header -->
        <div class="mb-8">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i data-lucide="layout-dashboard" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Dashboard Organizer</h1>
                            <p class="text-gray-600 dark:text-gray-400">Kelola event dan pantau performa penjualan Anda</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        Selamat datang kembali, <span class="font-medium text-blue-600">{{ auth()->user()->name }}</span> • {{ now()->format('l, d F Y') }}
                    </p>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-wrap items-center gap-3">
                    <a href="{{ route('organizer.events.create') }}"
                       class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Buat Event Baru
                    </a>

                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                                class="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 shadow-sm">
                            <i data-lucide="download" class="w-4 h-4 mr-2"></i>
                            Export
                            <i data-lucide="chevron-down" class="w-4 h-4 ml-2"></i>
                        </button>

                        <div x-show="open"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             @click.away="open = false"
                             class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                            <div class="py-1">
                                <a href="{{ route('organizer.dashboard.export', ['format' => 'csv']) }}"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export CSV
                                </a>
                                <a href="{{ route('organizer.dashboard.export', ['format' => 'pdf']) }}"
                                   class="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Export PDF
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 stats-grid">
            <!-- Total Events -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 dashboard-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Event</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_tickets'] ?? $stats['total'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            <i data-lucide="calendar" class="w-3 h-3 inline mr-1"></i>
                            {{ $stats['published_tickets'] ?? 0 }} Published • {{ $stats['draft_tickets'] ?? 0 }} Draft
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="calendar" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 dashboard-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            Rp {{ number_format($stats['total_revenue'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                            <i data-lucide="trending-up" class="w-3 h-3 inline mr-1"></i>
                            +Rp {{ number_format($stats['period_revenue'] ?? 0, 0, ',', '.') }} ({{ $dateRange ?? 30 }} hari)
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="dollar-sign" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                    </div>
                </div>
            </div>

            <!-- Tickets Sold -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 dashboard-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Tiket Terjual</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_tickets_sold'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-purple-600 dark:text-purple-400 mt-1">
                            <i data-lucide="ticket" class="w-3 h-3 inline mr-1"></i>
                            +{{ number_format($stats['period_tickets_sold'] ?? 0, 0, ',', '.') }} ({{ $dateRange ?? 30 }} hari)
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="ticket" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                    </div>
                </div>
            </div>

            <!-- Total Customers -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 dashboard-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Total Pelanggan</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                            {{ number_format($stats['total_customers'] ?? 0, 0, ',', '.') }}
                        </p>
                        <p class="text-xs text-orange-600 dark:text-orange-400 mt-1">
                            <i data-lucide="users" class="w-3 h-3 inline mr-1"></i>
                            +{{ number_format($stats['period_customers'] ?? 0, 0, ',', '.') }} ({{ $dateRange ?? 30 }} hari)
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-xl flex items-center justify-center">
                        <i data-lucide="users" class="w-6 h-6 text-orange-600 dark:text-orange-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 chart-grid">
            <!-- Revenue Chart -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden dashboard-card">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="trending-up" class="w-5 h-5 text-green-600 dark:text-green-400"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Revenue Harian</h3>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">7 Hari</button>
                            <button class="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">30 Hari</button>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <div class="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="bar-chart-3" class="w-8 h-8 text-gray-400"></i>
                                </div>
                                <p class="text-lg font-medium text-gray-900 dark:text-white mb-2">Chart Revenue</p>
                                <p class="text-gray-600 dark:text-gray-400">Chart akan ditampilkan di sini</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Performance -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden dashboard-card">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="calendar" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performa Event</h3>
                        </div>
                        <a href="{{ route('organizer.events.index') }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors text-sm font-medium">
                            Lihat Semua
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @forelse(collect($eventPerformance ?? [])->take(5) as $event)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 dark:text-white">{{ $event['title'] ?? 'Event Title' }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ $event['sold_tickets'] ?? 0 }}/{{ $event['total_capacity'] ?? 0 }} tiket terjual
                                </p>
                                <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $event['conversion_rate'] ?? 0 }}%"></div>
                                </div>
                            </div>
                            <div class="text-right ml-4">
                                <p class="font-semibold text-gray-900 dark:text-white">
                                    Rp {{ number_format($event['revenue'] ?? 0, 0, ',', '.') }}
                                </p>
                                <p class="text-sm text-blue-600 dark:text-blue-400">{{ $event['conversion_rate'] ?? 0 }}%</p>
                            </div>
                        </div>
                        @empty
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="calendar" class="w-8 h-8 text-gray-400"></i>
                            </div>
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Belum ada event</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">Buat event pertama Anda untuk melihat performa</p>
                            <a href="{{ route('organizer.events.create') }}"
                               class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200">
                                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                                Buat Event
                            </a>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

    <!-- Recent Activities & Upcoming Tickets -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Activities -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="700">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Aktivitas Terbaru</h2>
                <div class="space-y-4">
                    @foreach($recentActivities->take(5) as $activity)
                    <div class="flex items-start space-x-4">
                        <div class="p-2 bg-primary/10 rounded-lg">
                            @if($activity['type'] === 'order')
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                </svg>
                            @else
                                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium text-gray-900">{{ $activity['title'] }}</h3>
                            <p class="text-sm text-gray-600">{{ $activity['description'] }}</p>
                            <p class="text-xs text-gray-500 mt-1">
                                {{ $activity['created_at']->diffForHumans() }}
                            </p>
                        </div>
                        @if(isset($activity['amount']))
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">
                                +Rp {{ number_format($activity['amount'], 0, ',', '.') }}
                            </p>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Upcoming Tickets -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="800">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-lg font-semibold">Event Mendatang</h2>
                    <a href="{{ route('organizer.events.create') }}" class="text-primary hover:text-primary/80 transition-colors">
                        Buat Event
                    </a>
                </div>
                <div class="space-y-4">
                    @forelse($upcomingTickets->take(5) as $event)
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                            <span class="text-primary font-semibold text-sm">
                                {{ \Carbon\Carbon::parse($event['start_date'])->format('d') }}
                            </span>
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium truncate">{{ $event['title'] }}</h3>
                            <p class="text-sm text-gray-600">
                                {{ \Carbon\Carbon::parse($event['start_date'])->format('d M Y, H:i') }}
                            </p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ ($event['status'] ?? 'draft') === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($event['status'] ?? 'draft') }}
                            </span>
                        </div>
                    </div>
                    @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">Belum ada event mendatang</p>
                        <a href="{{ route('organizer.events.create') }}" class="text-primary hover:text-primary/80 transition-colors">
                            Buat event pertama Anda
                        </a>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Add loading states to cards
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Chart period buttons
    const chartButtons = document.querySelectorAll('.chart-container button');
    chartButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            chartButtons.forEach(btn => {
                btn.classList.remove('bg-green-500', 'text-white');
                btn.classList.add('text-gray-600', 'dark:text-gray-400');
            });

            // Add active class to clicked button
            this.classList.add('bg-green-500', 'text-white');
            this.classList.remove('text-gray-600', 'dark:text-gray-400');

            // Here you would typically load new chart data
            console.log('Loading chart data for period:', this.textContent);
        });
    });
});

// Notification system
function showNotification(title, message, type = 'info') {
    // Use the global notification system from organizer layout
    if (typeof showToast !== 'undefined') {
        showToast(type, title, message);
    } else {
        alert(`${title}: ${message}`);
    }
}
</script>
@endpush

/**
 * Process purchase and create order
 */
public function process(Request $request, Event $event)
{
    // Validate request
    $request->validate([
        'customer_name' => 'required|string|max:255',
        'customer_email' => 'required|email|max:255',
        'customer_phone' => 'required|string|max:20',
        'identity_type' => 'required|string|in:ktp,sim,passport,student_card',
        'identity_number' => 'required|string|max:50',
        'quantity' => 'required|integer|min:1|max:' . $event->max_tickets_per_purchase,
        'payment_method' => 'required|string|in:bank_transfer,credit_card,e_wallet,qris,virtual_account,cash,uangtix',
        'voucher_code' => 'nullable|string',
    ]);

    // Check if UangTix is selected but user is not logged in
    if ($request->payment_method === 'uangtix' && !auth()->check()) {
        return back()->with('error', 'Anda harus login untuk menggunakan metode pembayaran UangTix.');
    }

    // Check UangTix balance if that payment method is selected
    if ($request->payment_method === 'uangtix' && auth()->check()) {
        $userBalance = auth()->user()->getUangTixBalance();
        $totalAmount = $event->price * $request->quantity;

        // Apply voucher discount if any
        if ($request->voucher_code) {
            // Voucher processing logic here
            // $totalAmount = $totalAmount - $voucherDiscount;
        }

        if ($userBalance->balance < $totalAmount) {
            return back()->with('error', 'Saldo UangTix Anda tidak mencukupi. Silakan top up terlebih dahulu.');
        }
    }

    // Calculate total amount
    $totalQuantity = $request->quantity;
    $totalAmount = $event->price * $totalQuantity;

    // Apply payment method fee
    switch ($request->payment_method) {
        case 'qris':
            $fee = round($totalAmount * 0.007); // 0.7%
            $totalAmount += $fee;
            break;
        case 'e_wallet':
            $fee = round($totalAmount * 0.015); // 1.5%
            $totalAmount += $fee;
            break;
        case 'credit_card':
            $fee = round($totalAmount * 0.029); // 2.9%
            $totalAmount += $fee;
            break;
        case 'virtual_account':
            $totalAmount += 4000; // Flat fee
            break;
    }

    try {
        DB::beginTransaction();

        // Create order
        $order = Order::create([
            'user_id' => auth()->id(),
            'event_id' => $event->id,
            'order_number' => $this->generateOrderNumber(),
            'customer_name' => $request->customer_name,
            'customer_email' => $request->customer_email,
            'customer_phone' => $request->customer_phone,
            'quantity' => $totalQuantity,
            'unit_price' => $event->price,
            'total_amount' => $totalAmount,
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => $request->payment_method,
            'expires_at' => now()->addHours(2), // Order expires in 2 hours
        ]);

        // Create tickets
        for ($i = 0; $i < $totalQuantity; $i++) {
            Ticket::create([
                'user_id' => auth()->id(),
                'event_id' => $event->id,
                'order_id' => $order->id,
                'ticket_number' => $this->generateTicketNumber(),
                'status' => 'pending',
                'identity_type' => $request->identity_type,
                'identity_number' => $request->identity_number,
                'attendee_name' => $request->customer_name,
                'price' => $event->price,
            ]);
        }

        // Process UangTix payment immediately if selected
        if ($request->payment_method === 'uangtix') {
            $userBalance = auth()->user()->getUangTixBalance();
            $userBalance->deductBalance($totalAmount, 'purchase', [
                'order_id' => $order->id,
                'event_id' => $event->id,
                'event_title' => $event->title,
                'tickets_count' => $totalQuantity
            ]);

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'paid_at' => now(),
            ]);

            // Update ticket status
            Ticket::where('order_id', $order->id)->update([
                'status' => 'active'
            ]);

            // Generate E-Tickets automatically
            $ticketGenerator = new \App\Services\TicketGeneratorService();
            $generatedTickets = $ticketGenerator->generateOrderTickets($order);

            // Create notification
            \App\Models\Notification::create([
                'user_id' => auth()->id(),
                'title' => 'E-Tiket Siap Diunduh!',
                'message' => "Pembayaran untuk pesanan #{$order->order_number} berhasil menggunakan UangTix. E-Tiket Anda sudah siap diunduh.",
                'type' => 'ticket',
                'data' => [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'event_title' => $event->title,
                    'tickets_count' => count($generatedTickets),
                    'generated_tickets' => $generatedTickets
                ]
            ]);

            DB::commit();

            // Redirect to success page
            return redirect()->route('orders.success', $order)
                ->with('success', 'Pembayaran berhasil! Tiket Anda telah dikonfirmasi.');
        }

        // Store order ID in session for guest users
        if (!auth()->check()) {
            $guestOrderIds = session('guest_order_ids', []);
            $guestOrderIds[] = $order->id;
            session(['guest_order_ids' => $guestOrderIds]);
        }

        DB::commit();

        // Redirect to payment page for other payment methods
        return redirect()->route('orders.payment', $order);

    } catch (\Exception $e) {
        DB::rollBack();
        \Log::error('Purchase processing error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return back()->with('error', 'Terjadi kesalahan saat memproses pembelian. Silakan coba lagi.');
    }
}

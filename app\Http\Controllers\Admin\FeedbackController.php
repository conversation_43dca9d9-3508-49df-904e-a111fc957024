<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Feedback;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FeedbackController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display feedback management page
     */
    public function index(Request $request)
    {
        $query = Feedback::with(['user', 'event', 'responder']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('rating')) {
            $query->where('rating', $request->rating);
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('comment', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('event', function($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $feedback = $query->latest()->paginate(20);

        // Statistics
        $stats = [
            'total_feedback' => Feedback::count(),
            'pending_feedback' => Feedback::where('status', 'pending')->count(),
            'approved_feedback' => Feedback::where('status', 'approved')->count(),
            'rejected_feedback' => Feedback::where('status', 'rejected')->count(),
            'average_rating' => Feedback::where('status', 'approved')->avg('rating') ?? 0,
            'needs_response' => Feedback::where('status', 'approved')
                ->whereNull('admin_response')
                ->count(),
        ];

        // Events for filter
        $events = \App\Models\Event::select('id', 'title')
            ->whereHas('feedback')
            ->orderBy('title')
            ->get();

        return view('pages.admin.feedback.index', compact('feedback', 'stats', 'events'));
    }

    /**
     * Get recent feedback for dashboard
     */
    public function recent()
    {
        $feedback = Feedback::with(['user', 'event'])
            ->latest()
            ->limit(10)
            ->get()
            ->map(function($item) {
                return [
                    'id' => $item->id,
                    'event_title' => $item->event->title ?? 'Unknown Event',
                    'display_name' => $item->display_name,
                    'rating' => $item->rating,
                    'comment' => $item->comment,
                    'status' => $item->status,
                    'created_at' => $item->created_at->diffForHumans(),
                ];
            });

        return response()->json([
            'success' => true,
            'feedback' => $feedback
        ]);
    }

    /**
     * Get pending feedback count
     */
    public function pendingCount()
    {
        $count = Feedback::where('status', 'pending')->count();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Update feedback status
     */
    public function updateStatus(Request $request, Feedback $feedback)
    {
        $request->validate([
            'status' => 'required|in:pending,approved,rejected'
        ]);

        $feedback->update([
            'status' => $request->status,
            'responded_by' => auth()->id(),
            'responded_at' => now()
        ]);

        // Send notification to user
        if ($request->status === 'approved') {
            \App\Models\Notification::create([
                'user_id' => $feedback->user_id,
                'title' => 'Feedback Approved',
                'message' => "Your feedback for '{$feedback->event->title}' has been approved and is now visible to other users.",
                'type' => 'feedback',
                'data' => json_encode([
                    'feedback_id' => $feedback->id,
                    'event_id' => $feedback->event_id
                ])
            ]);
        } elseif ($request->status === 'rejected') {
            \App\Models\Notification::create([
                'user_id' => $feedback->user_id,
                'title' => 'Feedback Rejected',
                'message' => "Your feedback for '{$feedback->event->title}' has been rejected and will not be displayed.",
                'type' => 'feedback',
                'data' => json_encode([
                    'feedback_id' => $feedback->id,
                    'event_id' => $feedback->event_id
                ])
            ]);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Feedback {$request->status} successfully!"
            ]);
        }

        return back()->with('success', "Feedback {$request->status} successfully!");
    }

    /**
     * Respond to feedback
     */
    public function respond(Request $request, Feedback $feedback)
    {
        $request->validate([
            'admin_response' => 'required|string|max:1000'
        ]);

        $feedback->update([
            'admin_response' => $request->admin_response,
            'responded_by' => auth()->id(),
            'responded_at' => now(),
            'status' => 'responded'
        ]);

        // Send notification to user
        \App\Models\Notification::create([
            'user_id' => $feedback->user_id,
            'title' => 'Admin Response to Your Feedback',
            'message' => "An admin has responded to your feedback for '{$feedback->event->title}'.",
            'type' => 'feedback',
            'data' => json_encode([
                'feedback_id' => $feedback->id,
                'event_id' => $feedback->event_id,
                'response' => $request->admin_response
            ])
        ]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Response sent successfully!'
            ]);
        }

        return back()->with('success', 'Response sent successfully!');
    }

    /**
     * Delete feedback
     */
    public function destroy(Feedback $feedback)
    {
        $feedback->delete();

        return response()->json([
            'success' => true,
            'message' => 'Feedback deleted successfully!'
        ]);
    }

    /**
     * Bulk actions on feedback
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,delete,respond',
            'feedback_ids' => 'required|array',
            'feedback_ids.*' => 'exists:feedback,id',
            'admin_response' => 'required_if:action,respond|string|max:1000'
        ]);

        $feedbackItems = Feedback::whereIn('id', $request->feedback_ids)->get();
        $count = $feedbackItems->count();

        DB::transaction(function() use ($request, $feedbackItems) {
            foreach ($feedbackItems as $feedback) {
                switch ($request->action) {
                    case 'approve':
                        $feedback->update([
                            'status' => 'approved',
                            'responded_by' => auth()->id(),
                            'responded_at' => now()
                        ]);
                        break;

                    case 'reject':
                        $feedback->update([
                            'status' => 'rejected',
                            'responded_by' => auth()->id(),
                            'responded_at' => now()
                        ]);
                        break;

                    case 'respond':
                        $feedback->update([
                            'admin_response' => $request->admin_response,
                            'responded_by' => auth()->id(),
                            'responded_at' => now(),
                            'status' => 'responded'
                        ]);
                        break;

                    case 'delete':
                        $feedback->delete();
                        break;
                }

                // Send notifications for bulk actions
                if (in_array($request->action, ['approve', 'reject', 'respond']) && !$feedback->trashed()) {
                    $this->sendBulkActionNotification($feedback, $request->action, $request->admin_response ?? null);
                }
            }
        });

        $actionText = [
            'approve' => 'approved',
            'reject' => 'rejected',
            'respond' => 'responded to',
            'delete' => 'deleted'
        ];

        return response()->json([
            'success' => true,
            'message' => "{$count} feedback items {$actionText[$request->action]} successfully!"
        ]);
    }

    /**
     * Send notification for bulk actions
     */
    private function sendBulkActionNotification($feedback, $action, $response = null)
    {
        $titles = [
            'approve' => 'Feedback Approved',
            'reject' => 'Feedback Rejected',
            'respond' => 'Admin Response to Your Feedback'
        ];

        $messages = [
            'approve' => "Your feedback for '{$feedback->event->title}' has been approved.",
            'reject' => "Your feedback for '{$feedback->event->title}' has been rejected.",
            'respond' => "An admin has responded to your feedback for '{$feedback->event->title}'."
        ];

        $data = [
            'feedback_id' => $feedback->id,
            'event_id' => $feedback->event_id
        ];

        if ($action === 'respond' && $response) {
            $data['response'] = $response;
        }

        \App\Models\Notification::create([
            'user_id' => $feedback->user_id,
            'title' => $titles[$action],
            'message' => $messages[$action],
            'type' => 'feedback',
            'data' => json_encode($data)
        ]);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $columns = Schema::getColumnListing('users');

            // Add badge_level_id first if it doesn't exist
            if (!in_array('badge_level_id', $columns)) {
                $table->unsignedBigInteger('badge_level_id')->nullable()->after('role');
            }

            // Add badge duration fields only if they don't exist
            if (!in_array('badge_assigned_at', $columns)) {
                $table->timestamp('badge_assigned_at')->nullable()->after('badge_level_id');
            }
            if (!in_array('badge_expires_at', $columns)) {
                $table->timestamp('badge_expires_at')->nullable()->after('badge_assigned_at');
            }
            if (!in_array('badge_duration_days', $columns)) {
                $table->integer('badge_duration_days')->nullable()->after('badge_expires_at')->comment('Duration in days, null for permanent');
            }
            if (!in_array('badge_auto_renew', $columns)) {
                $table->boolean('badge_auto_renew')->default(false)->after('badge_duration_days');
            }

            // Remove badge_level column if exists (but keep it for now as it's used in the system)
            // if (Schema::hasColumn('users', 'badge_level')) {
            //     $table->dropColumn('badge_level');
            // }
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'badge_assigned_at',
                'badge_expires_at',
                'badge_duration_days',
                'badge_auto_renew'
            ]);

            // Re-add badge_level if needed
            $table->string('badge_level')->default('star')->after('role');
        });
    }
};

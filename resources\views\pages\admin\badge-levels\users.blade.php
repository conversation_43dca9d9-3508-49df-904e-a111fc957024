@extends('layouts.admin')

@section('title', 'Badge User Management')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users-cog text-primary me-2"></i>Badge User Management
            </h1>
            <p class="mb-0 text-muted">Manage user badges, durations, and expiry</p>
        </div>
        <div class="d-flex gap-2">
            <button onclick="showExpiredBadges()" class="btn btn-warning">
                <i class="fas fa-clock me-2"></i>Expired Badges
            </button>
            <button onclick="autoRenewBadges()" class="btn btn-success">
                <i class="fas fa-sync me-2"></i>Auto Renew
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">Badge Level</label>
                    <select id="filterBadge" class="form-select">
                        <option value="">All Badges</option>
                        @foreach($badgelevel as $level)
                            <option value="{{ $level->id }}">{{ $level->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select id="filterStatus" class="form-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="expiring_soon">Expiring Soon</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Role</label>
                    <select id="filterRole" class="form-select">
                        <option value="">All Roles</option>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                        <option value="penjual">Organizer</option>
                        <option value="pembeli">Customer</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button onclick="applyFilters()" class="btn btn-primary">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Users with Badges</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="usersTable">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Badge</th>
                            <th>Assigned</th>
                            <th>Expires</th>
                            <th>Status</th>
                            <th>Auto Renew</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Extend Badge Modal -->
<div class="modal fade" id="extendBadgeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Extend Badge Duration</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="extendBadgeForm">
                    <input type="hidden" id="extendUserId" name="user_id">
                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <div id="extendUserInfo" class="form-control-plaintext"></div>
                    </div>
                    <div class="mb-3">
                        <label for="additionalDays" class="form-label">Additional Days</label>
                        <input type="number" class="form-control" id="additionalDays" name="additional_days" min="1" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" onclick="submitExtendBadge()" class="btn btn-primary">Extend Badge</button>
            </div>
        </div>
    </div>
</div>

<!-- Downgrade Badge Modal -->
<div class="modal fade" id="downgradeBadgeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Downgrade Badge</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="downgradeBadgeForm">
                    <input type="hidden" id="downgradeUserId" name="user_id">
                    <div class="mb-3">
                        <label class="form-label">User</label>
                        <div id="downgradeUserInfo" class="form-control-plaintext"></div>
                    </div>
                    <div class="mb-3">
                        <label for="targetBadgeId" class="form-label">Target Badge Level</label>
                        <select class="form-select" id="targetBadgeId" name="target_badge_id">
                            <option value="">Bronze (Default)</option>
                            @foreach($badgelevel as $level)
                                <option value="{{ $level->id }}">{{ $level->name }}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" onclick="submitDowngradeBadge()" class="btn btn-warning">Downgrade</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Load users data
function loadUsers() {
    const badgeFilter = document.getElementById('filterBadge').value;
    const statusFilter = document.getElementById('filterStatus').value;
    const roleFilter = document.getElementById('filterRole').value;
    
    const params = new URLSearchParams();
    if (badgeFilter) params.append('badge', badgeFilter);
    if (statusFilter) params.append('status', statusFilter);
    if (roleFilter) params.append('role', roleFilter);
    
    fetch(`{{ route('admin.badge-level.users') }}?${params.toString()}`)
        .then(response => response.json())
        .then(users => {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';
            
            users.forEach(user => {
                const row = createUserRow(user);
                tbody.appendChild(row);
            });
        })
        .catch(error => {
            console.error('Error loading users:', error);
        });
}

// Create user table row
function createUserRow(user) {
    const tr = document.createElement('tr');
    
    const badgeInfo = user.badge_level ? 
        `<span class="badge" style="background-color: ${user.badge_level.color}">${user.badge_level.name}</span>` :
        '<span class="badge badge-secondary">No Badge</span>';
    
    const assignedDate = user.badge_assigned_at ? 
        new Date(user.badge_assigned_at).toLocaleDateString() : '-';
    
    const expiryDate = user.badge_expires_at ? 
        new Date(user.badge_expires_at).toLocaleDateString() : 'Permanent';
    
    const status = getBadgeStatus(user);
    const autoRenew = user.badge_auto_renew ? 
        '<i class="fas fa-check text-success"></i>' : 
        '<i class="fas fa-times text-danger"></i>';
    
    tr.innerHTML = `
        <td>
            <div class="d-flex align-items-center">
                <img src="${user.avatar || '/assets/img/default-avatar.png'}" 
                     alt="Avatar" class="rounded-circle me-2" width="32" height="32">
                <div>
                    <div class="fw-bold">${user.name}</div>
                    <div class="text-muted small">${user.email}</div>
                    <span class="badge badge-outline-primary">${user.role}</span>
                </div>
            </div>
        </td>
        <td>${badgeInfo}</td>
        <td>${assignedDate}</td>
        <td>${expiryDate}</td>
        <td>${status}</td>
        <td class="text-center">${autoRenew}</td>
        <td>
            <div class="btn-group btn-group-sm">
                <button onclick="extendBadge(${user.id}, '${user.name}')" class="btn btn-outline-primary btn-sm" title="Extend">
                    <i class="fas fa-clock"></i>
                </button>
                <button onclick="downgradeBadge(${user.id}, '${user.name}')" class="btn btn-outline-warning btn-sm" title="Downgrade">
                    <i class="fas fa-arrow-down"></i>
                </button>
                <button onclick="removeBadge(${user.id})" class="btn btn-outline-danger btn-sm" title="Remove">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    return tr;
}

// Get badge status
function getBadgeStatus(user) {
    if (!user.badge_expires_at) {
        return '<span class="badge badge-success">Permanent</span>';
    }
    
    const expiryDate = new Date(user.badge_expires_at);
    const now = new Date();
    const daysUntilExpiry = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
    
    if (daysUntilExpiry < 0) {
        return '<span class="badge badge-danger">Expired</span>';
    } else if (daysUntilExpiry <= 7) {
        return `<span class="badge badge-warning">Expires in ${daysUntilExpiry} days</span>`;
    } else {
        return `<span class="badge badge-success">Active (${daysUntilExpiry} days)</span>`;
    }
}

// Apply filters
function applyFilters() {
    loadUsers();
}

// Extend badge
function extendBadge(userId, userName) {
    document.getElementById('extendUserId').value = userId;
    document.getElementById('extendUserInfo').textContent = userName;
    new bootstrap.Modal(document.getElementById('extendBadgeModal')).show();
}

// Submit extend badge
function submitExtendBadge() {
    const userId = document.getElementById('extendUserId').value;
    const additionalDays = document.getElementById('additionalDays').value;
    
    fetch('{{ route("admin.badge-level.extend-badge") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId,
            additional_days: parseInt(additionalDays)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('extendBadgeModal')).hide();
            loadUsers();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

// Downgrade badge
function downgradeBadge(userId, userName) {
    document.getElementById('downgradeUserId').value = userId;
    document.getElementById('downgradeUserInfo').textContent = userName;
    new bootstrap.Modal(document.getElementById('downgradeBadgeModal')).show();
}

// Submit downgrade badge
function submitDowngradeBadge() {
    const userId = document.getElementById('downgradeUserId').value;
    const targetBadgeId = document.getElementById('targetBadgeId').value;
    
    fetch('{{ route("admin.badge-level.downgrade-badge") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId,
            target_badge_id: targetBadgeId || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('downgradeBadgeModal')).hide();
            loadUsers();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

// Remove badge
function removeBadge(userId) {
    if (!confirm('Are you sure you want to remove this user\'s badge?')) {
        return;
    }
    
    fetch('{{ route("admin.badge-level.remove-badge") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadUsers();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

// Show expired badges
function showExpiredBadges() {
    fetch('{{ route("admin.badge-level.expired-badges") }}')
        .then(response => response.json())
        .then(expiredUsers => {
            if (expiredUsers.length === 0) {
                alert('No expired badges found!');
                return;
            }
            
            const message = `Found ${expiredUsers.length} expired badges:\n\n` +
                expiredUsers.map(user => `- ${user.name} (${user.badge_level.name})`).join('\n');
            
            alert(message);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
}

// Auto renew badges
function autoRenewBadges() {
    if (!confirm('Auto-renew all eligible expired badges?')) {
        return;
    }
    
    fetch('{{ route("admin.badge-level.auto-renew-badges") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        alert(data.message);
        loadUsers();
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

// Load users on page load
document.addEventListener('DOMContentLoaded', function() {
    loadUsers();
});
</script>
@endpush
@endsection

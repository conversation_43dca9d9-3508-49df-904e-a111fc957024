@extends('layouts.app')

@section('title', 'QR Code UangTix')

@push('styles')
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- QR Code JS -->
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<!-- UangTix QR CSS -->
<link rel="stylesheet" href="{{ asset('css/uangtix.css') }}?v={{ time() }}">
<style>
/* UangTix QR Code Page Styles */
.qr-page-container {
    --uangtix-primary-blue: #0066CC;
    --uangtix-primary-green: #00AA5B;
    --uangtix-secondary-blue: #E6F3FF;
    --uangtix-text-dark: #1A1A1A;
    --uangtix-text-gray: #666666;
    --uangtix-bg-light: #F8FAFC;
    --uangtix-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --uangtix-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --uangtix-border-radius: 16px;
    
    background: var(--uangtix-bg-light);
    min-height: 100vh;
    padding-bottom: 100px;
    font-family: 'Poppins', 'DM Sans', sans-serif;
}

/* QR Header */
.qr-header {
    background: linear-gradient(135deg, var(--uangtix-primary-blue) 0%, var(--uangtix-primary-green) 100%);
    padding: 24px 16px;
    border-radius: var(--uangtix-border-radius);
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.qr-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(60px);
}

.qr-header-content {
    position: relative;
    z-index: 2;
}

.qr-header-title {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.qr-header-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 0;
}

/* QR Code Display */
.qr-display-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 32px;
    box-shadow: var(--uangtix-shadow-light);
    text-align: center;
    margin-bottom: 24px;
}

.qr-code-container {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid #E5E7EB;
    display: inline-block;
    margin-bottom: 20px;
}

.qr-code-info {
    margin-bottom: 24px;
}

.qr-user-name {
    font-size: 20px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin: 0 0 4px 0;
}

.qr-user-id {
    font-size: 14px;
    color: var(--uangtix-text-gray);
    margin: 0 0 8px 0;
}

.qr-balance {
    font-size: 16px;
    font-weight: 600;
    color: var(--uangtix-primary-blue);
    margin: 0;
}

.qr-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

.qr-action-btn {
    background: var(--uangtix-primary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.qr-action-btn:hover {
    background: #0052A3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.qr-action-btn.secondary {
    background: var(--uangtix-primary-green);
}

.qr-action-btn.secondary:hover {
    background: #059669;
}

/* QR UangTiX Scanner */
.qr-scanner-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
    margin-bottom: 24px;
}

.scanner-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.scanner-icon {
    width: 40px;
    height: 40px;
    background: var(--uangtix-secondary-blue);
    color: var(--uangtix-primary-blue);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.scanner-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin: 0;
}

.scanner-area {
    border: 2px dashed #E5E7EB;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.scanner-area:hover {
    border-color: var(--uangtix-primary-blue);
    background: var(--uangtix-secondary-blue);
}

.scanner-area.active {
    border-color: var(--uangtix-primary-green);
    background: rgba(0, 170, 91, 0.1);
}

.scanner-icon-large {
    width: 80px;
    height: 80px;
    background: var(--uangtix-secondary-blue);
    color: var(--uangtix-primary-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    margin: 0 auto 16px;
}

.scanner-text {
    font-size: 16px;
    font-weight: 600;
    color: var(--uangtix-text-dark);
    margin: 0 0 8px 0;
}

.scanner-subtext {
    font-size: 14px;
    color: var(--uangtix-text-gray);
    margin: 0;
}

/* Recent Scans */
.recent-scans-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
}

.recent-scans-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.recent-scan-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.recent-scan-item:hover {
    background: var(--uangtix-bg-light);
}

.recent-scan-avatar {
    width: 40px;
    height: 40px;
    background: var(--uangtix-primary-blue);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
}

.recent-scan-info {
    flex: 1;
}

.recent-scan-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--uangtix-text-dark);
    margin: 0 0 2px 0;
}

.recent-scan-time {
    font-size: 12px;
    color: var(--uangtix-text-gray);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .qr-page-container {
        padding: 16px;
    }
    
    .qr-header {
        margin: 0 -16px 24px;
        border-radius: 0;
    }
    
    .qr-display-card {
        padding: 24px 16px;
    }
    
    .qr-actions {
        flex-direction: column;
    }
    
    .qr-action-btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
@endpush

@section('content')
<div class="qr-page-container">
    <!-- Header -->
    <div class="qr-header">
        <div class="qr-header-content">
            <h1 class="qr-header-title">QR Code UangTix</h1>
            <p class="qr-header-subtitle">Scan atau bagikan QR Code untuk transfer cepat</p>
        </div>
    </div>

    <!-- QR Code Display -->
    <div class="qr-display-card">
        <div class="qr-code-container">
            <canvas id="qrcode" width="200" height="200"></canvas>
        </div>
        
        <div class="qr-code-info">
            <h3 class="qr-user-name">{{ auth()->user()->name ?? 'User' }}</h3>
            <p class="qr-user-id">ID: {{ auth()->user()->id ?? '12345' }}</p>
            <p class="qr-balance">Saldo: {{ number_format($balance->balance ?? 100000, 0, ',', '.') }} UTX</p>
        </div>
        
        <div class="qr-actions">
            <button class="qr-action-btn" onclick="shareQRCode()">
                <i class="fas fa-share"></i>
                Bagikan QR
            </button>
            <button class="qr-action-btn secondary" onclick="downloadQRCode()">
                <i class="fas fa-download"></i>
                Unduh QR
            </button>
            <a href="{{ route('uangtix.index') }}" class="qr-action-btn">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
        </div>
    </div>

    <!-- E-Tiket QR Scanner -->
    <div class="qr-scanner-card">
        <div class="scanner-header">
            <div class="scanner-icon">
                <i class="fas fa-qrcode"></i>
            </div>
            <h3 class="scanner-title">Scan E-Tiket QR Code</h3>
        </div>
        
        <div class="scanner-area" onclick="startScanning()">
            <div class="scanner-icon-large">
                <i class="fas fa-camera"></i>
            </div>
            <p class="scanner-text">Tap untuk Scan QR Code</p>
            <p class="scanner-subtext">Scan QR Code teman untuk transfer langsung</p>
        </div>
    </div>

    <!-- Recent Scans -->
    <div class="recent-scans-card">
        <div class="recent-scans-header">
            <div class="scanner-icon">
                <i class="fas fa-history"></i>
            </div>
            <h3 class="scanner-title">Scan Terbaru</h3>
        </div>
        
        <div class="recent-scan-item" onclick="transferTo('user1')">
            <div class="recent-scan-avatar">JD</div>
            <div class="recent-scan-info">
                <p class="recent-scan-name">John Doe</p>
                <p class="recent-scan-time">2 menit yang lalu</p>
            </div>
        </div>
        
        <div class="recent-scan-item" onclick="transferTo('user2')">
            <div class="recent-scan-avatar">AS</div>
            <div class="recent-scan-info">
                <p class="recent-scan-name">Alice Smith</p>
                <p class="recent-scan-time">1 jam yang lalu</p>
            </div>
        </div>
        
        <div class="recent-scan-item" onclick="transferTo('user3')">
            <div class="recent-scan-avatar">BJ</div>
            <div class="recent-scan-info">
                <p class="recent-scan-name">Bob Johnson</p>
                <p class="recent-scan-time">Kemarin</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// QR Code Generation and Management
document.addEventListener('DOMContentLoaded', function() {
    generateQRCode();
});

function generateQRCode() {
    const canvas = document.getElementById('qrcode');
    const userId = '{{ auth()->user()->id ?? "12345" }}';
    const userName = '{{ auth()->user()->name ?? "User" }}';
    
    // QR Code data - in real app, this would be encrypted
    const qrData = {
        type: 'uangtix_transfer',
        user_id: userId,
        user_name: userName,
        timestamp: Date.now()
    };
    
    QRCode.toCanvas(canvas, JSON.stringify(qrData), {
        width: 200,
        height: 200,
        color: {
            dark: '#0066CC',
            light: '#FFFFFF'
        },
        margin: 2
    }, function (error) {
        if (error) {
            console.error('QR Code generation failed:', error);
            showToast('Gagal membuat QR Code', 'error');
        } else {
            console.log('QR Code generated successfully');
        }
    });
}

function shareQRCode() {
    if (navigator.share) {
        const canvas = document.getElementById('qrcode');
        canvas.toBlob(function(blob) {
            const file = new File([blob], 'uangtix-qr.png', { type: 'image/png' });
            
            navigator.share({
                title: 'QR Code UangTix',
                text: 'Scan QR Code ini untuk transfer UangTix ke saya',
                files: [file]
            }).then(() => {
                showToast('QR Code berhasil dibagikan', 'success');
            }).catch((error) => {
                console.error('Share failed:', error);
                fallbackShare();
            });
        });
    } else {
        fallbackShare();
    }
}

function fallbackShare() {
    // Fallback for browsers that don't support Web Share API
    const canvas = document.getElementById('qrcode');
    const dataURL = canvas.toDataURL();
    
    // Copy to clipboard or show modal with share options
    showToast('QR Code siap dibagikan. Klik kanan untuk menyimpan.', 'info');
}

function downloadQRCode() {
    const canvas = document.getElementById('qrcode');
    const link = document.createElement('a');
    link.download = 'uangtix-qr-code.png';
    link.href = canvas.toDataURL();
    link.click();
    
    showToast('QR Code berhasil diunduh', 'success');
}

function startScanning() {
    const scannerArea = document.querySelector('.scanner-area');
    scannerArea.classList.add('active');
    
    // Simulate scanning process
    showToast('Memulai scan QR Code...', 'info');
    
    setTimeout(() => {
        scannerArea.classList.remove('active');
        // In real app, this would open camera and scan QR code
        showToast('Scan selesai! Mengarahkan ke transfer...', 'success');
        
        // Simulate successful scan
        setTimeout(() => {
            window.location.href = '{{ route("uangtix.index") }}#transfer';
        }, 1500);
    }, 3000);
}

function transferTo(userId) {
    showToast('Mengarahkan ke halaman transfer...', 'info');
    
    // In real app, this would navigate to transfer page with pre-filled recipient
    setTimeout(() => {
        window.location.href = '{{ route("uangtix.index") }}#transfer';
    }, 1000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
@endpush

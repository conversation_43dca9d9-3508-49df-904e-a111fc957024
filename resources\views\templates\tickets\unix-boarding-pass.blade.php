<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unix Terminal Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'JetBrains Mono', monospace;
            background: #0d1117;
            color: #c9d1d9;
            padding: 20px;
            line-height: 1.4;
        }

        .terminal-container {
            max-width: 800px;
            margin: 0 auto;
            background: #161b22;
            border: 2px solid #30363d;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .terminal-header {
            background: #21262d;
            padding: 12px 16px;
            border-bottom: 1px solid #30363d;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-dots {
            display: flex;
            gap: 6px;
        }

        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot.red { background: #ff5f56; }
        .dot.yellow { background: #ffbd2e; }
        .dot.green { background: #27ca3f; }

        .terminal-title {
            color: #8b949e;
            font-size: 12px;
            margin-left: 12px;
        }

        .terminal-body {
            padding: 20px;
            background: #0d1117;
        }

        .command-line {
            color: #58a6ff;
            margin-bottom: 8px;
        }

        .prompt {
            color: #7c3aed;
        }

        .output {
            color: #c9d1d9;
            margin-bottom: 16px;
            padding-left: 20px;
        }

        .boarding-pass {
            border: 2px dashed #30363d;
            padding: 24px;
            margin: 20px 0;
            background: #161b22;
            position: relative;
        }

        .boarding-pass::before,
        .boarding-pass::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: #0d1117;
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
        }

        .boarding-pass::before {
            left: -10px;
        }

        .boarding-pass::after {
            right: -10px;
        }

        .pass-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #30363d;
            padding-bottom: 16px;
        }

        .pass-title {
            color: #58a6ff;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .pass-subtitle {
            color: #8b949e;
            font-size: 12px;
        }

        .pass-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            align-items: center;
        }

        .pass-info {
            display: grid;
            gap: 12px;
        }

        .info-row {
            display: grid;
            grid-template-columns: 120px 1fr;
            gap: 12px;
        }

        .info-label {
            color: #7c3aed;
            font-size: 11px;
            text-transform: uppercase;
        }

        .info-value {
            color: #c9d1d9;
            font-size: 13px;
            font-weight: 600;
        }

        .qr-section {
            text-align: center;
            padding: 16px;
            border: 1px solid #30363d;
            border-radius: 4px;
            background: #21262d;
        }

        .qr-code {
            width: 120px;
            height: 120px;
            background: #c9d1d9;
            margin: 0 auto 12px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0d1117;
            font-size: 10px;
            text-align: center;
            line-height: 1.2;
        }

        .qr-label {
            color: #8b949e;
            font-size: 10px;
            text-transform: uppercase;
        }

        .boarding-id {
            color: #58a6ff;
            font-size: 14px;
            font-weight: 700;
            margin-top: 8px;
        }

        .footer-commands {
            margin-top: 20px;
            padding-top: 16px;
            border-top: 1px solid #30363d;
        }

        .success-msg {
            color: #3fb950;
        }

        .warning-msg {
            color: #f85149;
        }

        .highlight {
            color: #58a6ff;
            font-weight: 700;
        }

        @media print {
            body {
                background: white;
                color: black;
            }
            
            .terminal-container {
                border: 2px solid #000;
                box-shadow: none;
            }
            
            .terminal-header {
                background: #f0f0f0;
                border-bottom: 1px solid #000;
            }
            
            .terminal-body {
                background: white;
            }
            
            .boarding-pass {
                background: white;
                border: 2px dashed #000;
            }
            
            .qr-section {
                background: #f0f0f0;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="terminal-container">
        <div class="terminal-header">
            <div class="terminal-dots">
                <div class="dot red"></div>
                <div class="dot yellow"></div>
                <div class="dot green"></div>
            </div>
            <div class="terminal-title">tixara-terminal — boarding-pass-generator</div>
        </div>
        
        <div class="terminal-body">
            <div class="command-line">
                <span class="prompt">user@tixara:~$</span> ./generate_boarding_pass.sh --event="{{ $ticket->event->title }}"
            </div>
            <div class="output success-msg">✓ Boarding pass generation initiated...</div>
            <div class="output">📋 Loading event data...</div>
            <div class="output">🎫 Processing ticket: <span class="highlight">{{ $ticket->ticket_number }}</span></div>
            <div class="output success-msg">✓ Boarding pass ready for display</div>

            <div class="boarding-pass">
                <div class="pass-header">
                    <div class="pass-title">UNIX BOARDING PASS</div>
                    <div class="pass-subtitle">{{ strtoupper($ticket->event->category->name ?? 'EVENT') }} TERMINAL</div>
                </div>

                <div class="pass-content">
                    <div class="pass-info">
                        <div class="info-row">
                            <div class="info-label">EVENT:</div>
                            <div class="info-value">{{ $ticket->event->title }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">LOCATION:</div>
                            <div class="info-value">{{ $ticket->event->location }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">DATE/TIME:</div>
                            <div class="info-value">{{ $ticket->event->start_date->format('d M Y H:i') }} WIB</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">ATTENDEE:</div>
                            <div class="info-value">{{ strtoupper($ticket->attendee_name) }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">TICKET:</div>
                            <div class="info-value">{{ $ticket->ticket_number }}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">SEAT:</div>
                            <div class="info-value">{{ $ticket->seat_number ?? 'GENERAL' }}</div>
                        </div>
                    </div>

                    <div class="qr-section">
                        <div class="qr-code">
                            QR CODE<br>
                            SCAN TO<br>
                            VALIDATE
                        </div>
                        <div class="qr-label">SCAN FOR ENTRY</div>
                        <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-UNX-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                    </div>
                </div>
            </div>

            <div class="footer-commands">
                <div class="command-line">
                    <span class="prompt">user@tixara:~$</span> validate_ticket --id={{ $ticket->boarding_pass_id ?? 'BP-UNX-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}
                </div>
                <div class="output success-msg">✓ Ticket validation ready</div>
                <div class="output">🔒 Secure boarding pass generated by TiXara Terminal v2.0</div>
                <div class="output">📧 Email: {{ $ticket->order->customer_email ?? 'N/A' }}</div>
                <div class="output warning-msg">⚠ Keep this boarding pass secure until event day</div>
            </div>
        </div>
    </div>
</body>
</html>

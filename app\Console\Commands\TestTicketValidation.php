<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Event;
use App\Services\TicketValidationService;

class TestTicketValidation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tickets:test-validation 
                            {--ticket= : Specific ticket number to test}
                            {--staff= : Staff user ID to test with}
                            {--organizer= : Test with specific organizer events only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test ticket validation system with staff authorization';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎫 Testing Ticket Validation System...');
        
        $validationService = app(TicketValidationService::class);
        
        // Get test data
        $ticket = $this->getTestTicket();
        $staff = $this->getTestStaff();
        
        if (!$ticket || !$staff) {
            return 1;
        }
        
        $this->info("\n📋 Test Setup:");
        $this->info("  Ticket: #{$ticket->ticket_number}");
        $this->info("  Event: {$ticket->event->title}");
        $this->info("  Organizer: {$ticket->event->organizer->name}");
        $this->info("  Staff: {$staff->name} ({$staff->role})");
        
        // Test 1: Check staff assignment
        $this->info("\n🔍 Test 1: Staff Assignment Check");
        $assignedOrganizers = $validationService->getStaffAssignedOrganizers($staff);
        
        if (empty($assignedOrganizers)) {
            $this->warn("  ⚠️  Staff is not assigned to any organizers");
        } else {
            $this->info("  ✅ Staff is assigned to " . count($assignedOrganizers) . " organizer(s):");
            foreach ($assignedOrganizers as $organizer) {
                $this->line("    - {$organizer['name']} (ID: {$organizer['id']})");
            }
        }
        
        // Test 2: Staff scan ticket
        $this->info("\n🔍 Test 2: Staff Scan Ticket");
        $scanResult = $validationService->staffScanTicket($ticket->qr_code, $staff);
        
        $this->displayValidationResult($scanResult);
        
        // Test 3: Authenticity check
        $this->info("\n🔍 Test 3: Authenticity Check");
        $authenticityResult = $validationService->checkAuthenticity($ticket->boarding_pass_id ?? $ticket->ticket_number);
        
        $this->displayAuthenticityResult($authenticityResult);
        
        // Test 4: Staff validatable events
        $this->info("\n🔍 Test 4: Staff Validatable Events");
        $validatableEvents = $validationService->getStaffValidatableEvents($staff);
        
        if (empty($validatableEvents)) {
            $this->warn("  ⚠️  Staff cannot validate any events");
        } else {
            $this->info("  ✅ Staff can validate " . count($validatableEvents) . " event(s):");
            foreach (array_slice($validatableEvents, 0, 5) as $event) {
                $this->line("    - {$event['title']} (Organizer: {$event['organizer']['name']})");
            }
            if (count($validatableEvents) > 5) {
                $this->line("    ... and " . (count($validatableEvents) - 5) . " more events");
            }
        }
        
        $this->info("\n🎉 Ticket validation testing completed!");
        
        return 0;
    }
    
    private function getTestTicket(): ?Ticket
    {
        if ($ticketNumber = $this->option('ticket')) {
            $ticket = Ticket::where('ticket_number', $ticketNumber)
                ->with(['event', 'event.organizer'])
                ->first();
            
            if (!$ticket) {
                $this->error("Ticket #{$ticketNumber} not found");
                return null;
            }
            
            return $ticket;
        }
        
        // Get a random active ticket
        $ticket = Ticket::where('status', 'active')
            ->with(['event', 'event.organizer'])
            ->inRandomOrder()
            ->first();
        
        if (!$ticket) {
            $this->error("No active tickets found for testing");
            return null;
        }
        
        return $ticket;
    }
    
    private function getTestStaff(): ?User
    {
        if ($staffId = $this->option('staff')) {
            $staff = User::find($staffId);
            
            if (!$staff) {
                $this->error("Staff user #{$staffId} not found");
                return null;
            }
            
            if ($staff->role !== 'staff' && $staff->role !== 'admin') {
                $this->error("User #{$staffId} is not staff or admin");
                return null;
            }
            
            return $staff;
        }
        
        // Get a random staff user
        $staff = User::where('role', 'staff')->inRandomOrder()->first();
        
        if (!$staff) {
            $this->error("No staff users found for testing");
            return null;
        }
        
        return $staff;
    }
    
    private function displayValidationResult(array $result): void
    {
        if ($result['success']) {
            $this->info("  ✅ {$result['message']}");
            if (isset($result['data'])) {
                $this->line("    Status: {$result['status']}");
                $this->line("    Icon: {$result['icon']}");
                $this->line("    Color: {$result['color']}");
            }
        } else {
            $this->error("  ❌ {$result['message']}");
            $this->line("    Status: {$result['status']}");
            $this->line("    Icon: {$result['icon']}");
            $this->line("    Color: {$result['color']}");
        }
    }
    
    private function displayAuthenticityResult(array $result): void
    {
        if ($result['success']) {
            $this->info("  ✅ {$result['message']}");
            if (isset($result['data'])) {
                $this->line("    Boarding Pass ID: {$result['data']['boarding_pass_id']}");
                $this->line("    Template: {$result['data']['template_used']}");
                $this->line("    Status: " . ($result['data']['is_used'] ? 'Used' : 'Available'));
            }
        } else {
            $this->error("  ❌ {$result['message']}");
            $this->line("    Status: {$result['status']}");
        }
    }
}

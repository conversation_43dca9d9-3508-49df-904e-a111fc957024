<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classic Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Times+New+Roman:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: #fffbeb;
            border: 4px double #d97706;
            position: relative;
            box-shadow: 0 20px 25px -5px rgba(217, 119, 6, 0.1);
        }

        .classic-border {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 2px solid #f59e0b;
        }

        .inner-border {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 1px solid #fbbf24;
        }

        .boarding-header {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            color: #fffbeb;
            padding: 40px;
            text-align: center;
            position: relative;
            border-bottom: 4px double #f59e0b;
        }

        .header-ornament {
            position: absolute;
            top: 16px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: #fbbf24;
        }

        .header-ornament::before,
        .header-ornament::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            background: #fbbf24;
            border-radius: 50%;
            top: -4px;
        }

        .header-ornament::before { left: -16px; }
        .header-ornament::after { right: -16px; }

        .header-title {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: 3px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            font-size: 16px;
            font-weight: 400;
            font-style: italic;
            letter-spacing: 2px;
            opacity: 0.95;
        }

        .boarding-body {
            padding: 48px;
            background: #fffbeb;
            position: relative;
            z-index: 2;
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 48px;
            padding: 32px;
            background: #fef3c7;
            border: 3px double #d97706;
            position: relative;
        }

        .event-showcase::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #f59e0b;
        }

        .event-title {
            font-size: 32px;
            font-weight: 700;
            color: #92400e;
            margin-bottom: 16px;
            line-height: 1.2;
            position: relative;
            z-index: 2;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(135deg, #d97706, #b45309);
            color: #fffbeb;
            padding: 10px 24px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            border: 2px solid #f59e0b;
            position: relative;
            z-index: 2;
        }

        .boarding-main {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 48px;
            align-items: start;
        }

        .details-panel {
            background: #fef3c7;
            padding: 40px;
            border: 3px double #d97706;
            position: relative;
        }

        .details-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #f59e0b;
        }

        .panel-title {
            font-size: 24px;
            font-weight: 700;
            color: #d97706;
            margin-bottom: 32px;
            text-align: center;
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .details-grid {
            display: grid;
            gap: 24px;
            position: relative;
            z-index: 2;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .detail-item {
            text-align: center;
            padding: 20px;
            background: #fffbeb;
            border: 2px solid #f59e0b;
            position: relative;
        }

        .detail-item::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px solid #fbbf24;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 700;
            color: #d97706;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 700;
            color: #92400e;
            line-height: 1.3;
            position: relative;
            z-index: 2;
        }

        .detail-value.highlight {
            font-size: 20px;
            color: #d97706;
        }

        .qr-panel {
            background: #fef3c7;
            padding: 40px;
            text-align: center;
            border: 3px double #d97706;
            position: relative;
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #f59e0b;
        }

        .qr-title {
            font-size: 20px;
            font-weight: 700;
            color: #d97706;
            margin-bottom: 24px;
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .qr-code {
            width: 160px;
            height: 160px;
            background: linear-gradient(135deg, #92400e, #d97706);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fffbeb;
            font-size: 14px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 24px;
            position: relative;
            z-index: 2;
            border: 4px double #f59e0b;
        }

        .qr-code::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 1px solid #fbbf24;
        }

        .boarding-id {
            font-size: 16px;
            font-weight: 700;
            color: #d97706;
            background: #fffbeb;
            padding: 16px 20px;
            border: 2px solid #f59e0b;
            margin-top: 20px;
            position: relative;
            z-index: 2;
            letter-spacing: 1px;
        }

        .boarding-id::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px solid #fbbf24;
        }

        .classic-footer {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            padding: 24px 48px;
            border-top: 4px double #d97706;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            position: relative;
            z-index: 2;
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-label {
            font-size: 11px;
            color: #d97706;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
        }

        .footer-value {
            font-size: 14px;
            font-weight: 700;
            color: #92400e;
        }

        .tixara-classic {
            font-size: 22px;
            font-weight: 700;
            color: #d97706;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .corner-ornament {
            position: absolute;
            width: 24px;
            height: 24px;
            border: 2px solid #f59e0b;
            z-index: 3;
        }

        .corner-ornament.top-left {
            top: 32px;
            left: 32px;
            border-right: none;
            border-bottom: none;
        }

        .corner-ornament.top-right {
            top: 32px;
            right: 32px;
            border-left: none;
            border-bottom: none;
        }

        .corner-ornament.bottom-left {
            bottom: 32px;
            left: 32px;
            border-right: none;
            border-top: none;
        }

        .corner-ornament.bottom-right {
            bottom: 32px;
            right: 32px;
            border-left: none;
            border-top: none;
        }

        @media (max-width: 768px) {
            .boarding-main {
                grid-template-columns: 1fr;
                gap: 32px;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 32px 24px;
            }
            
            .classic-footer {
                flex-direction: column;
                text-align: center;
                padding: 24px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 4px double #000;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="classic-border"></div>
        <div class="inner-border"></div>
        <div class="corner-ornament top-left"></div>
        <div class="corner-ornament top-right"></div>
        <div class="corner-ornament bottom-left"></div>
        <div class="corner-ornament bottom-right"></div>

        <div class="boarding-header">
            <div class="header-ornament"></div>
            <div class="header-title">CLASSIC</div>
            <div class="header-subtitle">Timeless Elegance Boarding Pass</div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Classic Event' }}</div>
            </div>

            <div class="boarding-main">
                <div class="details-panel">
                    <div class="panel-title">Event Information</div>
                    <div class="details-grid">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Event Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('l') }}<br>{{ $ticket->event->start_date->format('d F Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Event Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }}<br>WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Venue Location</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Honored Guest</div>
                            <div class="detail-value highlight">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Ticket Number</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Seating</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'Reserved' }}</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Order Reference</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Price</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-title">Validation</div>
                    <div class="qr-code">
                        CLASSIC<br>
                        QR CODE<br>
                        VALIDATION
                    </div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-CLS-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="classic-footer">
            <div class="footer-info">
                <div class="footer-label">Issued</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y H:i') ?? now()->format('d M Y H:i') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Category</div>
                <div class="footer-value">{{ $ticket->event->category->name ?? 'Classic' }}</div>
            </div>
            <div class="tixara-classic">TiXara Classic</div>
        </div>
    </div>
</body>
</html>

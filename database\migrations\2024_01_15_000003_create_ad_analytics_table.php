<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('ad_analytics')) {
            Schema::create('ad_analytics', function (Blueprint $table) {
                $table->id();
                $table->foreignId('ad_id')->constrained('ads')->onDelete('cascade');
                $table->date('date');
                $table->integer('impressions')->default(0);
                $table->integer('clicks')->default(0);
                $table->decimal('cost', 10, 2)->default(0);
                $table->decimal('ctr', 5, 2)->default(0); // Click-through rate
                $table->decimal('cpc', 10, 2)->default(0); // Cost per click
                $table->decimal('cpm', 10, 2)->default(0); // Cost per mille (1000 impressions)
                $table->string('device_type')->nullable(); // mobile, desktop, tablet
                $table->string('browser')->nullable();
                $table->string('location')->nullable(); // city or region
                $table->string('referrer')->nullable();
                $table->timestamps();

                $table->unique(['ad_id', 'date']);
                $table->index(['ad_id', 'date']);
                $table->index(['date']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('ad_analytics');
    }
};

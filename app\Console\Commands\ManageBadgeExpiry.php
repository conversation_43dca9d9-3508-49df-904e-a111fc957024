<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserBadgeLevel;

class ManageBadgeExpiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'badges:manage-expiry {--auto-renew : Auto-renew expired badges} {--downgrade : Downgrade expired badges to Bronze}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage badge expiry - auto-renew or downgrade expired badges';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Managing badge expiry...');

        // Get expired badges
        $expiredUsers = User::whereNotNull('badge_expires_at')
                           ->where('badge_expires_at', '<', now())
                           ->with('badgeLevel')
                           ->get();

        if ($expiredUsers->isEmpty()) {
            $this->info('No expired badges found.');
            return Command::SUCCESS;
        }

        $this->info("Found {$expiredUsers->count()} expired badges.");

        $renewedCount = 0;
        $downgradedCount = 0;

        foreach ($expiredUsers as $user) {
            $this->line("Processing: {$user->name} ({$user->email}) - Badge: {$user->badgeLevel->name}");

            // Auto-renew if enabled and requested
            if ($this->option('auto-renew') && $user->badge_auto_renew && $user->badge_duration_days) {
                $user->renewBadge();
                $renewedCount++;
                $this->line("  ✓ Auto-renewed badge for {$user->badge_duration_days} days");
                continue;
            }

            // Downgrade if requested
            if ($this->option('downgrade')) {
                $user->downgradeToBronze();
                $downgradedCount++;
                $this->line("  ✓ Downgraded to Bronze badge");
                continue;
            }

            // Just mark as expired (no action)
            $this->line("  ! Badge expired (no action taken)");
        }

        // Summary
        $this->info("\nSummary:");
        $this->info("- Renewed: {$renewedCount} badges");
        $this->info("- Downgraded: {$downgradedCount} badges");
        $this->info("- Total processed: " . ($renewedCount + $downgradedCount));

        return Command::SUCCESS;
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Festival Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Fredoka:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Fredoka', cursive;
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #064e3b;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 
                0 20px 25px -5px rgba(5, 150, 105, 0.1),
                0 10px 10px -5px rgba(5, 150, 105, 0.04);
            border: 3px solid #059669;
            position: relative;
        }

        .festival-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 32px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .festival-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="30" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="60" cy="70" r="4" fill="rgba(255,255,255,0.1)"/><circle cx="30" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="10" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="80" r="3" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float-confetti 6s ease-in-out infinite;
        }

        @keyframes float-confetti {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-family: 'Fredoka One', cursive;
            font-size: 36px;
            font-weight: 400;
            margin-bottom: 8px;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .header-subtitle {
            font-size: 16px;
            font-weight: 300;
            opacity: 0.95;
            letter-spacing: 1px;
        }

        .rainbow-strip {
            height: 8px;
            background: linear-gradient(90deg, 
                #ef4444, #f97316, #eab308, #22c55e, 
                #3b82f6, #8b5cf6, #ec4899, #ef4444);
            animation: rainbow-slide 3s linear infinite;
        }

        @keyframes rainbow-slide {
            0% { background-position: 0% 50%; }
            100% { background-position: 100% 50%; }
        }

        .boarding-body {
            padding: 40px;
            background: linear-gradient(135deg, #fefefe 0%, #f0fdf4 100%);
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 40px;
            padding: 32px;
            background: white;
            border-radius: 20px;
            border: 3px solid #34d399;
            box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.1);
            position: relative;
            overflow: hidden;
        }

        .event-showcase::before {
            content: '🎉🎊🎈';
            position: absolute;
            top: 16px;
            right: 16px;
            font-size: 20px;
            animation: party-icons 2s ease-in-out infinite;
        }

        @keyframes party-icons {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(10deg) scale(1.1); }
        }

        .event-title {
            font-family: 'Fredoka One', cursive;
            font-size: 32px;
            font-weight: 400;
            color: #064e3b;
            margin-bottom: 16px;
            line-height: 1.2;
            background: linear-gradient(135deg, #059669, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .event-category {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #059669, #34d399);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.3);
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .details-panel {
            background: white;
            border-radius: 20px;
            padding: 32px;
            border: 3px solid #34d399;
            box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.1);
            position: relative;
        }

        .panel-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #d1fae5;
        }

        .panel-icon {
            font-size: 24px;
            animation: wiggle 2s ease-in-out infinite;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }

        .panel-title {
            font-family: 'Fredoka One', cursive;
            font-size: 20px;
            font-weight: 400;
            color: #059669;
        }

        .details-grid {
            display: grid;
            gap: 20px;
        }

        .detail-pair {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            background: #f0fdf4;
            border-radius: 16px;
            padding: 16px;
            border: 2px solid #bbf7d0;
            transition: all 0.3s ease;
            text-align: center;
        }

        .detail-item:hover {
            background: #dcfce7;
            border-color: #34d399;
            transform: translateY(-2px) rotate(1deg);
            box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.2);
        }

        .detail-label {
            font-size: 11px;
            font-weight: 700;
            color: #059669;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .detail-value {
            font-size: 15px;
            font-weight: 600;
            color: #064e3b;
            line-height: 1.3;
        }

        .detail-value.primary {
            font-family: 'Fredoka One', cursive;
            font-size: 18px;
            font-weight: 400;
            color: #059669;
        }

        .qr-panel {
            background: white;
            border-radius: 20px;
            padding: 32px;
            text-align: center;
            border: 3px solid #34d399;
            box-shadow: 0 4px 6px -1px rgba(52, 211, 153, 0.1);
            position: relative;
        }

        .qr-header {
            margin-bottom: 24px;
        }

        .qr-title {
            font-family: 'Fredoka One', cursive;
            font-size: 18px;
            font-weight: 400;
            color: #059669;
            margin-bottom: 4px;
        }

        .qr-subtitle {
            font-size: 12px;
            color: #34d399;
            font-weight: 500;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #064e3b, #059669);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.3);
            border: 3px solid #34d399;
            position: relative;
            overflow: hidden;
        }

        .qr-code::before {
            content: '✨';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 16px;
            animation: sparkle 1.5s ease-in-out infinite;
        }

        @keyframes sparkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        .boarding-id {
            background: #f0fdf4;
            border: 2px solid #34d399;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 700;
            color: #059669;
            font-family: 'Fredoka', cursive;
            margin-top: 16px;
        }

        .festival-footer {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            padding: 24px 40px;
            border-top: 3px solid #34d399;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            align-items: center;
        }

        .footer-item {
            text-align: center;
            background: white;
            padding: 12px;
            border-radius: 12px;
            border: 2px solid #bbf7d0;
        }

        .footer-label {
            font-size: 10px;
            color: #059669;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .footer-value {
            font-size: 12px;
            font-weight: 600;
            color: #064e3b;
        }

        .tixara-festival {
            font-family: 'Fredoka One', cursive;
            font-size: 20px;
            font-weight: 400;
            color: #059669;
            letter-spacing: 1px;
            text-align: center;
            grid-column: 1 / -1;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 2px solid #bbf7d0;
            animation: festival-glow 2s ease-in-out infinite alternate;
        }

        @keyframes festival-glow {
            0% { text-shadow: 0 0 5px rgba(5, 150, 105, 0.3); }
            100% { text-shadow: 0 0 15px rgba(5, 150, 105, 0.6); }
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-pair {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .festival-footer {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 3px solid #000;
            }
            
            * {
                animation: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="rainbow-strip"></div>
        
        <div class="festival-header">
            <div class="header-content">
                <div class="header-title">FESTIVAL</div>
                <div class="header-subtitle">Fun & Colorful Boarding Pass</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">
                    🎪 {{ $ticket->event->category->name ?? 'Festival Event' }}
                </div>
            </div>

            <div class="boarding-content">
                <div class="details-panel">
                    <div class="panel-header">
                        <div class="panel-icon">🎵</div>
                        <div class="panel-title">Event Info</div>
                    </div>
                    
                    <div class="details-grid">
                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Festival Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('d M Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Start Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Festival Grounds</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Festival Goer</div>
                            <div class="detail-value primary">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Ticket ID</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Zone</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'General' }}</div>
                            </div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Order</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Price</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-header">
                        <div class="qr-title">Entry Pass</div>
                        <div class="qr-subtitle">Scan for Fun!</div>
                    </div>
                    
                    <div class="qr-code">
                        FESTIVAL<br>
                        QR CODE<br>
                        PARTY TIME!
                    </div>
                    
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-FES-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="festival-footer">
            <div class="footer-item">
                <div class="footer-label">Issued</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y') ?? now()->format('d M Y') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Type</div>
                <div class="footer-value">{{ $ticket->event->category->name ?? 'Festival' }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Fun Level</div>
                <div class="footer-value">Maximum! 🎉</div>
            </div>
            <div class="tixara-festival">TiXara Festival</div>
        </div>
    </div>
</body>
</html>

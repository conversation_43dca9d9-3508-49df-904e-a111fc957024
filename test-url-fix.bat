@echo off
echo Testing URL Fix for TiXara...

echo.
echo [1/5] Starting Laravel development server...
start /B php artisan serve --host=127.0.0.1 --port=8000
timeout /t 3 /nobreak >nul

echo.
echo [2/5] Testing correct URLs...
echo.
echo Testing: http://127.0.0.1:8000/tickets/my-tickets
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/tickets/my-tickets
echo.
echo Testing: http://127.0.0.1:8000/my-tickets  
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/my-tickets
echo.
echo Testing: http://127.0.0.1:8000/tickets
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/tickets
echo.
echo Testing: http://127.0.0.1:8000/
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/

echo.
echo [3/5] Testing wrong URLs (should redirect)...
echo.
echo Testing: http://127.0.0.1:8000/public/tickets/my-tickets
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/public/tickets/my-tickets
echo.
echo Testing: http://127.0.0.1:8000/public/my-tickets
curl -s -o nul -w "Status: %%{http_code}\n" http://127.0.0.1:8000/public/my-tickets

echo.
echo [4/5] Testing route generation...
php artisan tinker --execute="
try {
    echo 'Route URLs:' . PHP_EOL;
    echo '- Home: ' . route('home') . PHP_EOL;
    echo '- Tickets: ' . route('tickets.index') . PHP_EOL;
    echo '- My Tickets: ' . route('tiket-saya') . PHP_EOL;
    echo '- My Tickets (alias): ' . route('my-tickets') . PHP_EOL;
    echo '- Debug Routes: ' . route('debug.routes') . PHP_EOL;
    echo PHP_EOL . 'All routes generated successfully!' . PHP_EOL;
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"

echo.
echo [5/5] Opening test URLs in browser...
echo.
echo Opening correct URLs...
start http://127.0.0.1:8000/
timeout /t 2 /nobreak >nul
start http://127.0.0.1:8000/tickets
timeout /t 2 /nobreak >nul

echo.
echo ========================================
echo URL Fix Test Results
echo ========================================
echo.
echo ✓ CORRECT URLs (should work):
echo   - http://127.0.0.1:8000/tickets/my-tickets
echo   - http://127.0.0.1:8000/my-tickets
echo   - http://127.0.0.1:8000/tickets
echo   - http://127.0.0.1:8000/
echo.
echo ✗ WRONG URLs (should redirect or show 404):
echo   - http://127.0.0.1:8000/public/tickets/my-tickets
echo   - http://127.0.0.1:8000/public/my-tickets
echo.
echo Status Codes:
echo - 200: Success
echo - 302: Redirect (good for wrong URLs)
echo - 401: Unauthorized (need login for my-tickets)
echo - 404: Not Found
echo.
echo If you see 401 for my-tickets routes, that's normal!
echo You need to login first to access those pages.
echo.
echo To stop the test server, press Ctrl+C in the command window
echo or close this window.
echo.
pause

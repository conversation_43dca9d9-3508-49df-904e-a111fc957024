<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, round existing data to remove decimal places
        DB::statement('UPDATE uangtix_balances SET
            balance = ROUND(balance, 0),
            total_earned = ROUND(total_earned, 0),
            total_spent = ROUND(total_spent, 0),
            total_deposited = ROUND(total_deposited, 0),
            total_withdrawn = ROUND(total_withdrawn, 0)
        ');

        DB::statement('UPDATE uangtix_transactions SET
            amount = ROUND(amount, 0),
            balance_before = ROUND(balance_before, 0),
            balance_after = ROUND(balance_after, 0)
        ');

        DB::statement('UPDATE uangtix_requests SET
            amount_idr = ROUND(amount_idr, 0),
            amount_uangtix = ROUND(amount_uangtix, 0),
            fee_amount = ROUND(fee_amount, 0)
        ');

        // Then update schema to remove decimal places
        Schema::table('uangtix_balances', function (Blueprint $table) {
            $table->decimal('balance', 15, 0)->change();
            $table->decimal('total_earned', 15, 0)->change();
            $table->decimal('total_spent', 15, 0)->change();
            $table->decimal('total_deposited', 15, 0)->change();
            $table->decimal('total_withdrawn', 15, 0)->change();
        });

        Schema::table('uangtix_transactions', function (Blueprint $table) {
            $table->decimal('amount', 15, 0)->change();
            $table->decimal('balance_before', 15, 0)->change();
            $table->decimal('balance_after', 15, 0)->change();
        });

        Schema::table('uangtix_requests', function (Blueprint $table) {
            $table->decimal('amount_idr', 15, 0)->change();
            $table->decimal('amount_uangtix', 15, 0)->change();
            $table->decimal('fee_amount', 15, 0)->change();
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore decimal places
        Schema::table('uangtix_balances', function (Blueprint $table) {
            $table->decimal('balance', 15, 2)->change();
            $table->decimal('total_earned', 15, 2)->change();
            $table->decimal('total_spent', 15, 2)->change();
            $table->decimal('total_deposited', 15, 2)->change();
            $table->decimal('total_withdrawn', 15, 2)->change();
        });

        Schema::table('uangtix_transactions', function (Blueprint $table) {
            $table->decimal('amount', 15, 2)->change();
            $table->decimal('balance_before', 15, 2)->change();
            $table->decimal('balance_after', 15, 2)->change();
        });

        Schema::table('uangtix_requests', function (Blueprint $table) {
            $table->decimal('amount_idr', 15, 2)->change();
            $table->decimal('amount_uangtix', 15, 2)->change();
            $table->decimal('fee_amount', 15, 2)->change();
        });
    }
};

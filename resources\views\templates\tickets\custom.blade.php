<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom E-Ticket</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family={{ $config['font_family'] ?? 'Inter' }}:wght@300;400;500;600;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '{{ $config['font_family'] ?? 'Inter' }}', sans-serif;
            background: {{ $config['background_color'] ?? '#f8fafc' }};
            padding: 40px 20px;
            min-height: 100vh;
        }

        .custom-ticket {
            max-width: {{ $config['ticket_width'] ?? '800px' }};
            margin: 0 auto;
            background: {{ $config['ticket_background'] ?? 'white' }};
            border-radius: {{ $config['border_radius'] ?? '16px' }};
            overflow: hidden;
            box-shadow: {{ $config['shadow'] ?? '0 20px 40px rgba(0, 0, 0, 0.1)' }};
            position: relative;
            @if($config['layout'] ?? 'vertical' == 'horizontal')
            display: grid;
            grid-template-columns: 2fr 1fr;
            @endif
        }

        @if(($config['background_pattern'] ?? 'none') != 'none')
        .custom-ticket::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="{{ $config['primary_color'] ?? '#3b82f6' }}"/></svg>');
            background-size: 20px 20px;
            pointer-events: none;
        }
        @endif

        .ticket-header {
            background: {{ $config['header_background'] ?? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)' }};
            color: {{ $config['header_text_color'] ?? 'white' }};
            padding: {{ $config['header_padding'] ?? '32px' }};
            position: relative;
            @if(($config['header_style'] ?? 'solid') == 'gradient')
            background: linear-gradient(135deg, {{ $config['primary_color'] ?? '#3b82f6' }} 0%, {{ $config['secondary_color'] ?? '#1d4ed8' }} 100%);
            @endif
        }

        .brand-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .brand-logo {
            font-size: {{ $config['logo_size'] ?? '24px' }};
            font-weight: 700;
            letter-spacing: -0.5px;
        }

        .ticket-type {
            font-size: 12px;
            font-weight: 500;
            opacity: 0.9;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .event-title {
            font-size: {{ $config['title_size'] ?? '28px' }};
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-venue {
            font-size: {{ $config['subtitle_size'] ?? '16px' }};
            opacity: 0.9;
            font-weight: 400;
        }

        .ticket-content {
            padding: {{ $config['content_padding'] ?? '32px' }};
            background: {{ $config['content_background'] ?? 'white' }};
            position: relative;
            z-index: 2;
        }

        .info-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 11px;
            font-weight: 600;
            color: {{ $config['label_color'] ?? '#6b7280' }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .info-value {
            font-size: {{ $config['value_size'] ?? '16px' }};
            font-weight: 500;
            color: {{ $config['text_color'] ?? '#1f2937' }};
            line-height: 1.3;
        }

        .ticket-number-section {
            text-align: center;
            padding: 24px;
            background: {{ $config['accent_background'] ?? '#f8fafc' }};
            border-radius: {{ $config['inner_radius'] ?? '12px' }};
            margin-bottom: 32px;
            @if(($config['border_style'] ?? 'solid') == 'dashed')
            border: 2px dashed {{ $config['border_color'] ?? '#d1d5db' }};
            @else
            border: 1px solid {{ $config['border_color'] ?? '#e5e7eb' }};
            @endif
        }

        .ticket-number-label {
            font-size: 11px;
            font-weight: 600;
            color: {{ $config['label_color'] ?? '#6b7280' }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .ticket-number {
            font-size: {{ $config['ticket_number_size'] ?? '20px' }};
            font-weight: 700;
            color: {{ $config['primary_color'] ?? '#3b82f6' }};
            letter-spacing: 1px;
        }

        @if($config['show_qr_code'] ?? true)
        .qr-section {
            text-align: center;
            margin: 32px 0;
        }

        .qr-container {
            display: inline-block;
            padding: {{ $config['qr_padding'] ?? '16px' }};
            background: {{ $config['qr_background'] ?? '#f8fafc' }};
            border-radius: {{ $config['qr_radius'] ?? '12px' }};
            border: 1px solid {{ $config['border_color'] ?? '#e5e7eb' }};
            margin-bottom: 16px;
        }

        .qr-instruction {
            font-size: 12px;
            color: {{ $config['label_color'] ?? '#6b7280' }};
            font-weight: 500;
        }
        @endif

        @if($config['show_price'] ?? true)
        .price-section {
            background: {{ $config['price_background'] ?? 'linear-gradient(135deg, #10b981 0%, #059669 100%)' }};
            color: white;
            padding: 16px 24px;
            border-radius: {{ $config['inner_radius'] ?? '12px' }};
            text-align: center;
            margin-bottom: 24px;
        }

        .price-label {
            font-size: 11px;
            font-weight: 500;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .price-value {
            font-size: 24px;
            font-weight: 700;
        }
        @endif

        .footer-section {
            padding-top: 24px;
            border-top: 1px solid {{ $config['border_color'] ?? '#e5e7eb' }};
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: {{ $config['status_background'] ?? '#dcfce7' }};
            color: {{ $config['status_color'] ?? '#166534' }};
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            background: currentColor;
            border-radius: 50%;
        }

        @if($config['show_watermark'] ?? true)
        .watermark {
            position: absolute;
            bottom: 16px;
            right: 16px;
            font-size: 10px;
            color: {{ $config['watermark_color'] ?? '#d1d5db' }};
            font-weight: 500;
            opacity: 0.7;
        }
        @endif

        .support-info {
            font-size: 11px;
            color: {{ $config['label_color'] ?? '#6b7280' }};
            text-align: right;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px 16px;
            }

            .custom-ticket {
                grid-template-columns: 1fr;
            }

            .ticket-header,
            .ticket-content {
                padding: 24px;
            }

            .info-section {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .footer-section {
                flex-direction: column;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .custom-ticket {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="custom-ticket">
        <!-- Header -->
        <div class="ticket-header">
            <div class="brand-section">
                <div class="brand-logo">{{ $config['brand_name'] ?? 'TiXara' }}</div>
                <div class="ticket-type">{{ $config['ticket_type'] ?? 'E-Ticket' }}</div>
            </div>
            <h1 class="event-title">{{ $ticket['event_title'] ?? 'Custom Event Title' }}</h1>
            <p class="event-venue">{{ $ticket['venue_name'] ?? 'Custom Venue Location' }}</p>
        </div>

        <!-- Content -->
        <div class="ticket-content">
            <!-- Event Information -->
            <div class="info-section">
                <div class="info-item">
                    <div class="info-label">{{ $config['date_label'] ?? 'Date' }}</div>
                    <div class="info-value">{{ $ticket['event_date'] ?? 'December 31, 2024' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">{{ $config['time_label'] ?? 'Time' }}</div>
                    <div class="info-value">{{ $ticket['event_time'] ?? '7:00 PM' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">{{ $config['holder_label'] ?? 'Ticket Holder' }}</div>
                    <div class="info-value">{{ $ticket['buyer_name'] ?? 'John Doe' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">{{ $config['email_label'] ?? 'Email' }}</div>
                    <div class="info-value">{{ $ticket['buyer_email'] ?? '<EMAIL>' }}</div>
                </div>
            </div>

            <!-- Ticket Number -->
            <div class="ticket-number-section">
                <div class="ticket-number-label">{{ $config['ticket_number_label'] ?? 'Ticket Number' }}</div>
                <div class="ticket-number">{{ $ticket['ticket_number'] ?? 'TIX-CUSTOM-001' }}</div>
                <div class="ticket-number-label" style="margin-top: 8px; font-size: 10px;">{{ $config['boarding_pass_label'] ?? 'Boarding Pass ID' }}</div>
                <div class="ticket-number" style="font-size: 14px; opacity: 0.8;">{{ $ticket['boarding_pass_id'] ?? 'BP250127ABC123' }}</div>
            </div>

            @if($config['show_price'] ?? true)
            <!-- Price Section -->
            <div class="price-section">
                <div class="price-label">{{ $config['price_label'] ?? 'Ticket Price' }}</div>
                <div class="price-value">{{ $ticket['formatted_price'] ?? 'Rp 200.000' }}</div>
            </div>
            @endif

            @if($config['show_qr_code'] ?? true)
            <!-- QR Code -->
            <div class="qr-section">
                <div class="qr-container">
                    {!! QrCode::size($config['qr_size'] ?? 120)->generate($ticket['qr_code'] ?? 'SAMPLE-QR-CODE') !!}
                </div>
                <div class="qr-instruction">
                    {{ $config['qr_instruction'] ?? 'Present this QR code at the venue entrance' }}
                </div>
            </div>
            @endif

            <!-- Footer -->
            <div class="footer-section">
                <div class="status-badge">
                    <div class="status-dot"></div>
                    {{ $config['status_text'] ?? 'Valid Ticket' }}
                </div>
                <div class="support-info">
                    <div>{{ $config['support_text'] ?? 'Support: <EMAIL>' }}</div>
                    <div>{{ $config['website_text'] ?? 'Website: tixara.my.id' }}</div>
                </div>
            </div>

            @if($config['show_watermark'] ?? true)
            <div class="watermark">
                {{ $config['watermark_text'] ?? 'Powered by TiXara' }}
            </div>
            @endif
        </div>
    </div>
</body>
</html>

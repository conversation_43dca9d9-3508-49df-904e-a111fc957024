@echo off
echo ========================================
echo    UangTix - Digital Wallet Setup
echo    GoPay/OVO Style Implementation
echo ========================================
echo.

echo [1/5] Checking Laravel installation...
if not exist "artisan" (
    echo ERROR: Laravel not found! Please run this script from Laravel root directory.
    pause
    exit /b 1
)

echo [2/5] Installing/Updating dependencies...
call composer install --no-dev --optimize-autoloader
if errorlevel 1 (
    echo ERROR: Composer install failed!
    pause
    exit /b 1
)

echo [3/5] Building frontend assets...
call npm install
call npm run build
if errorlevel 1 (
    echo ERROR: NPM build failed!
    pause
    exit /b 1
)

echo [4/5] Running database migrations...
php artisan migrate --force
if errorlevel 1 (
    echo ERROR: Migration failed!
    pause
    exit /b 1
)

echo [5/5] Seeding UangTix data...
php artisan db:seed --class=UangTixSeeder --force
if errorlevel 1 (
    echo WARNING: Seeder failed, but continuing...
)

echo.
echo ========================================
echo    Setup Complete! Starting Server...
echo ========================================
echo.
echo UangTix Features:
echo - GoPay/OVO Style Interface
echo - Responsive Design (Desktop/Mobile)
echo - PWA Support
echo - Floating Footer Navigation
echo - Real-time Balance Updates
echo - Top Up, Withdrawal, Transfer
echo - Transaction History
echo.
echo Access UangTix at: http://localhost:8000/uangtix
echo.
echo Press Ctrl+C to stop the server
echo ========================================

php artisan serve --host=0.0.0.0 --port=8000

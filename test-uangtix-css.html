<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test UangTix CSS</title>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Bootstrap -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- UangTix CSS -->
    <link rel="stylesheet" href="css/uangtix.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Poppins', sans-serif;
        }
    </style>
</head>
<body>
    <div class="uangtix-container">
        <!-- Wallet Header -->
        <div class="wallet-header">
            <div class="header-content">
                <!-- Header Top -->
                <div class="header-top">
                    <div>
                        <div class="greeting">Selamat datang,</div>
                        <div class="user-name">Test User</div>
                    </div>
                    <div class="header-actions">
                        <button class="header-btn">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="header-btn">
                            <i class="fas fa-qrcode"></i>
                        </button>
                    </div>
                </div>

                <!-- Balance Card -->
                <div class="balance-card">
                    <div class="balance-label">Saldo UangTix</div>
                    <div class="balance-amount">
                        100,000 
                        <span class="balance-currency">UTX</span>
                    </div>
                    <div class="balance-idr">
                        ≈ Rp 100,000
                    </div>
                    
                    <div class="balance-actions">
                        <button class="balance-btn">
                            <i class="fas fa-plus"></i>
                            Top Up
                        </button>
                        <button class="balance-btn secondary">
                            <i class="fas fa-minus"></i>
                            Tarik
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="quick-actions-title">Layanan Utama</div>
            <div class="actions-grid">
                <a href="#" class="action-item">
                    <div class="action-icon deposit">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="action-label">Top Up</div>
                </a>
                
                <a href="#" class="action-item">
                    <div class="action-icon withdraw">
                        <i class="fas fa-minus"></i>
                    </div>
                    <div class="action-label">Tarik Dana</div>
                </a>
                
                <a href="#" class="action-item">
                    <div class="action-icon transfer">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="action-label">Transfer</div>
                </a>
                
                <a href="#" class="action-item">
                    <div class="action-icon history">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="action-label">Riwayat</div>
                </a>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <div class="stats-title">Statistik</div>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">500,000</div>
                    <div class="stat-label">Total Earned</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">300,000</div>
                    <div class="stat-label">Total Deposit</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">200,000</div>
                    <div class="stat-label">Total Tarik</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">15</div>
                    <div class="stat-label">Transaksi Bulan Ini</div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="transactions-section">
            <div class="section-header">
                <div class="section-title">Transaksi Terbaru</div>
                <a href="#" class="view-all-btn">Lihat Semua</a>
            </div>

            <div class="transaction-item">
                <div class="transaction-icon deposit">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">Deposit</div>
                    <div class="transaction-subtitle">15 Nov 2024, 14:30</div>
                </div>
                <div class="transaction-amount positive">
                    +50,000 UTX
                    <div class="transaction-date">success</div>
                </div>
            </div>

            <div class="transaction-item">
                <div class="transaction-icon withdrawal">
                    <i class="fas fa-arrow-up"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">Penarikan</div>
                    <div class="transaction-subtitle">14 Nov 2024, 10:15</div>
                </div>
                <div class="transaction-amount negative">
                    -25,000 UTX
                    <div class="transaction-date">pending</div>
                </div>
            </div>

            <div class="transaction-item">
                <div class="transaction-icon transfer_in">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">Transfer Masuk</div>
                    <div class="transaction-subtitle">13 Nov 2024, 16:45</div>
                </div>
                <div class="transaction-amount positive">
                    +10,000 UTX
                    <div class="transaction-date">success</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Buttons -->
    <div style="padding: 20px; background: white; margin: 20px;">
        <h3>Test Buttons</h3>
        <button class="btn btn-uangtix" style="margin: 10px;">
            <i class="fas fa-plus"></i> Primary Button
        </button>
        <button class="btn btn-uangtix secondary" style="margin: 10px;">
            <i class="fas fa-minus"></i> Secondary Button
        </button>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        console.log('UangTix CSS Test Page Loaded');
        
        // Test hover effects
        document.querySelectorAll('.action-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('Action clicked:', item.querySelector('.action-label').textContent);
            });
        });
        
        // Test button clicks
        document.querySelectorAll('.balance-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                console.log('Balance button clicked:', btn.textContent.trim());
            });
        });
    </script>
</body>
</html>

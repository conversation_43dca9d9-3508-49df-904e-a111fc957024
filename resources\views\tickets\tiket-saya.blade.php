@extends('layouts.app')

@section('title', 'Tiket Saya')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-ticket-alt mr-3 text-blue-600"></i>
                Tiket <PERSON>a
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Kelola dan lihat semua tiket yang Anda miliki</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $stats['total'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Total Tiket</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $stats['active'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Aktif</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $stats['used'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Digunakan</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $stats['upcoming'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Mendatang</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{{ $stats['downloaded'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Downloaded</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-2xl font-bold text-pink-600 dark:text-pink-400">{{ $stats['printed'] }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Printed</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
            <form method="GET" class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-64">
                    <label for="event" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Cari Event
                    </label>
                    <input type="text" id="event" name="event" value="{{ request('event') }}"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                           placeholder="Nama event...">
                </div>
                
                <div class="min-w-48">
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Status
                    </label>
                    <select id="status" name="status"
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">Semua Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Aktif</option>
                        <option value="used" {{ request('status') === 'used' ? 'selected' : '' }}>Digunakan</option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                    </select>
                </div>
                
                <div class="min-w-48">
                    <label for="sort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Urutkan
                    </label>
                    <select id="sort" name="sort"
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>Tanggal Beli</option>
                        <option value="event_date" {{ request('sort') === 'event_date' ? 'selected' : '' }}>Tanggal Event</option>
                        <option value="price" {{ request('sort') === 'price' ? 'selected' : '' }}>Harga</option>
                    </select>
                </div>
                
                <div class="flex gap-2">
                    <button type="submit" 
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    <a href="{{ route('tiket-saya') }}" 
                       class="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>Reset
                    </a>
                </div>
            </form>
        </div>

        <!-- Tickets Grid -->
        @if($tickets->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                @foreach($tickets as $ticket)
                    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                        <!-- Event Image -->
                        <div class="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600">
                            @if($ticket->event->poster)
                                <img src="{{ Storage::url($ticket->event->poster) }}" 
                                     alt="{{ $ticket->event->title }}"
                                     class="w-full h-full object-cover">
                            @endif
                            
                            <!-- Status Badge -->
                            <div class="absolute top-4 right-4">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    @if($ticket->status === 'active') bg-green-100 text-green-800
                                    @elseif($ticket->status === 'used') bg-orange-100 text-orange-800
                                    @elseif($ticket->status === 'cancelled') bg-red-100 text-red-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    @if($ticket->status === 'active')
                                        <i class="fas fa-check-circle mr-1"></i>Aktif
                                    @elseif($ticket->status === 'used')
                                        <i class="fas fa-clock mr-1"></i>Digunakan
                                    @elseif($ticket->status === 'cancelled')
                                        <i class="fas fa-times-circle mr-1"></i>Dibatalkan
                                    @endif
                                </span>
                            </div>
                            
                            <!-- Ticket Number -->
                            <div class="absolute bottom-4 left-4">
                                <span class="bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg text-sm font-mono">
                                    {{ $ticket->ticket_number }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Content -->
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                {{ $ticket->event->title }}
                            </h3>
                            
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-calendar mr-2"></i>
                                    {{ \Carbon\Carbon::parse($ticket->event->start_date)->format('d M Y, H:i') }}
                                </div>
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    {{ $ticket->event->venue_name }}
                                </div>
                                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                    <i class="fas fa-user mr-2"></i>
                                    {{ $ticket->attendee_name }}
                                </div>
                                <div class="flex items-center text-sm font-semibold text-green-600">
                                    <i class="fas fa-money-bill mr-2"></i>
                                    Rp {{ number_format($ticket->price, 0, ',', '.') }}
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex gap-2">
                                <a href="{{ route('tickets.ticket.show', $ticket) }}" 
                                   class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg transition-colors duration-200 text-sm">
                                    <i class="fas fa-eye mr-1"></i>Detail
                                </a>
                                
                                @if($ticket->status === 'active')
                                    <a href="{{ route('tickets.download', $ticket) }}" 
                                       class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
                                       title="Download PDF">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    
                                    <a href="{{ route('tickets.print', $ticket) }}" 
                                       target="_blank"
                                       class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
                                       title="Print Tiket">
                                        <i class="fas fa-print"></i>
                                    </a>
                                @endif
                            </div>
                            
                            <!-- Usage Stats -->
                            @if($ticket->download_count > 0 || $ticket->print_count > 0)
                                <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                                        <span><i class="fas fa-download mr-1"></i>{{ $ticket->download_count ?? 0 }}x</span>
                                        <span><i class="fas fa-print mr-1"></i>{{ $ticket->print_count ?? 0 }}x</span>
                                        @if($ticket->used_at)
                                            <span><i class="fas fa-check mr-1"></i>{{ $ticket->used_at->format('d/m/y') }}</span>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $tickets->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <i class="fas fa-ticket-alt text-6xl text-gray-400 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Belum Ada Tiket</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Anda belum memiliki tiket apapun. Mulai jelajahi event menarik dan beli tiket pertama Anda!
                    </p>
                    <a href="{{ route('tickets.index') }}" 
                       class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-search mr-2"></i>
                        Jelajahi Event
                    </a>
                </div>
            </div>
        @endif

        <!-- Recent Orders -->
        @if($recentOrders->count() > 0)
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mt-8">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-history mr-2"></i>Pesanan Terbaru
                </h2>
                
                <div class="space-y-4">
                    @foreach($recentOrders as $order)
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $order->event->title }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ $order->order_number }} • {{ $order->tickets->count() }} tiket • 
                                    Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-500">
                                    {{ $order->created_at->format('d M Y H:i') }}
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                    Lunas
                                </span>
                                <a href="{{ route('orders.show', $order) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Template Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .ticket {
            width: 400px;
            height: 240px;
            background: {{ request('background_color', '#ffffff') }};
            border: 1px solid {{ request('secondary_color', '#e2e8f0') }};
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .ticket-header {
            padding: 24px 24px 0;
            flex: 1;
        }

        .event-title {
            font-size: 20px;
            font-weight: 600;
            color: {{ request('primary_color', '#1f2937') }};
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-subtitle {
            font-size: 14px;
            color: {{ request('secondary_color', '#6b7280') }};
            margin-bottom: 24px;
        }

        .ticket-details {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 24px;
            align-items: end;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-label {
            font-size: 11px;
            font-weight: 500;
            color: {{ request('secondary_color', '#6b7280') }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 500;
            color: {{ request('text_color', '#1f2937') }};
        }

        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .qr-code {
            width: 64px;
            height: 64px;
            background: {{ request('secondary_color', '#f3f4f6') }};
            border: 1px solid {{ request('secondary_color', '#e5e7eb') }};
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: {{ request('secondary_color', '#6b7280') }};
            text-align: center;
        }

        .qr-label {
            font-size: 10px;
            color: {{ request('secondary_color', '#6b7280') }};
            text-align: center;
        }

        .ticket-footer {
            border-top: 1px solid {{ request('secondary_color', '#f3f4f6') }};
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: {{ request('secondary_color', '#f9fafb') }};
        }

        .ticket-number {
            font-size: 12px;
            font-weight: 500;
            color: {{ request('secondary_color', '#6b7280') }};
            font-family: 'Courier New', monospace;
        }

        .price {
            font-size: 16px;
            font-weight: 700;
            color: {{ request('primary_color', '#1f2937') }};
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 32px;
            font-weight: 100;
            color: rgba(0, 0, 0, 0.03);
            pointer-events: none;
            z-index: 1;
            letter-spacing: 4px;
        }

        /* Subtle animations */
        .ticket {
            transition: all 0.3s ease;
        }

        .ticket:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="watermark">TIXARA</div>
        
        <div class="ticket-header">
            <div class="event-title">{{ request('event_title', 'Sample Event') }}</div>
            <div class="event-subtitle">{{ request('venue_name', 'Sample Venue') }}</div>
            
            <div class="ticket-details">
                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-label">Date</div>
                        <div class="detail-value">{{ request('start_date', now()->addDays(7)->format('M d, Y')) }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Time</div>
                        <div class="detail-value">{{ request('start_date', now()->addDays(7)->format('H:i')) }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Attendee</div>
                        <div class="detail-value">{{ request('attendee_name', 'John Doe') }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Seat</div>
                        <div class="detail-value">{{ request('seat_number', 'A001') }}</div>
                    </div>
                </div>
                
                <div class="qr-section">
                    <div class="qr-code">
                        QR<br>CODE
                    </div>
                    <div class="qr-label">Scan to verify</div>
                </div>
            </div>
        </div>
        
        <div class="ticket-footer">
            <div class="ticket-number">{{ request('ticket_number', 'TIK-PREVIEW-001') }}</div>
            <div class="price">{{ request('price', 'Rp 150.000') }}</div>
        </div>
    </div>
</body>
</html>

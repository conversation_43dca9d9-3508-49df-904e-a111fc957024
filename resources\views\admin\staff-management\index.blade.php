@extends('layouts.admin')

@section('title', 'Staff Management - Admin Dashboard')

@push('styles')
<style>
.staff-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.staff-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.staff-card.assigned {
    border-left-color: #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, transparent 100%);
}

.staff-card.unassigned {
    border-left-color: #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.05) 0%, transparent 100%);
}

.organizer-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    margin: 0.125rem;
    transition: all 0.2s ease;
}

.organizer-badge:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.assignment-modal {
    backdrop-filter: blur(10px);
}

.organizer-checkbox {
    transform: scale(1.2);
    margin-right: 0.75rem;
}

.bulk-action-bar {
    background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #cbd5e1;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: none;
}

.bulk-action-bar.active {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.search-box {
    position: relative;
}

.search-box input {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
}

.filter-dropdown {
    min-width: 200px;
}

.assignment-progress {
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
}

.assignment-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    transition: width 0.5s ease;
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-6 lg:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">👥 Staff Management</h1>
                <p class="text-gray-600">Kelola assignment staff ke organizer untuk validasi tiket</p>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
                <button onclick="showBulkAssignmentModal()"
                        class="bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i data-lucide="users" class="w-5 h-5 inline mr-2"></i>
                    Bulk Assignment
                </button>
                <button onclick="refreshData()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                    <i data-lucide="refresh-cw" class="w-5 h-5 inline mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" id="statsContainer">
        <!-- Stats will be loaded here -->
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div class="search-box flex-1 max-w-md">
                <div class="relative">
                    <i data-lucide="search" class="search-icon w-5 h-5"></i>
                    <input type="text" id="searchInput"
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="Cari staff atau organizer...">
                </div>
            </div>

            <div class="flex gap-3">
                <select id="filterStatus" class="filter-dropdown px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="">Semua Status</option>
                    <option value="assigned">Sudah Ditugaskan</option>
                    <option value="unassigned">Belum Ditugaskan</option>
                </select>

                <select id="sortBy" class="filter-dropdown px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                    <option value="name">Urutkan: Nama</option>
                    <option value="assignments">Urutkan: Jumlah Assignment</option>
                    <option value="created_at">Urutkan: Tanggal Dibuat</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Bulk Action Bar -->
    <div id="bulkActionBar" class="bulk-action-bar">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <span class="text-gray-700 font-medium mr-4">
                    <span id="selectedCount">0</span> staff dipilih
                </span>
                <button onclick="selectAllStaff()" class="text-blue-600 hover:text-blue-800 text-sm font-medium mr-4">
                    Pilih Semua
                </button>
                <button onclick="clearSelection()" class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                    Batal Pilih
                </button>
            </div>

            <div class="flex gap-2">
                <button onclick="bulkAssignOrganizers()"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="user-plus" class="w-4 h-4 inline mr-1"></i>
                    Assign Organizers
                </button>
                <button onclick="bulkClearAssignments()"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="user-minus" class="w-4 h-4 inline mr-1"></i>
                    Clear Assignments
                </button>
            </div>
        </div>
    </div>

    <!-- Staff List -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" id="staffContainer">
        @foreach($staffMembers as $staff)
        <div class="staff-card {{ $staff['assigned_organizers_count'] > 0 ? 'assigned' : 'unassigned' }} bg-white rounded-xl shadow-sm p-6"
             data-staff-id="{{ $staff['id'] }}"
             data-staff-name="{{ strtolower($staff['name']) }}"
             data-assignment-count="{{ $staff['assigned_organizers_count'] }}">

            <!-- Staff Header -->
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <input type="checkbox" class="staff-checkbox organizer-checkbox" value="{{ $staff['id'] }}" onchange="updateBulkActions()">
                    <div class="ml-3">
                        <h3 class="text-lg font-semibold text-gray-900">{{ $staff['name'] }}</h3>
                        <p class="text-sm text-gray-600">{{ $staff['email'] }}</p>
                        @if($staff['phone'])
                        <p class="text-sm text-gray-500">{{ $staff['phone'] }}</p>
                        @endif
                    </div>
                </div>

                <div class="flex items-center space-x-2">
                    @if($staff['is_active'])
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                        Active
                    </span>
                    @else
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <div class="w-1.5 h-1.5 bg-red-400 rounded-full mr-1"></div>
                        Inactive
                    </span>
                    @endif
                </div>
            </div>

            <!-- Assignment Progress -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Organizer Assignments</span>
                    <span class="text-sm text-gray-600">{{ $staff['assigned_organizers_count'] }} assigned</span>
                </div>
                <div class="assignment-progress">
                    <div class="assignment-progress-bar" style="width: {{ min(100, ($staff['assigned_organizers_count'] / max(1, count($organizers))) * 100) }}%"></div>
                </div>
            </div>

            <!-- Assigned Organizers -->
            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Assigned Organizers:</h4>
                @if($staff['assigned_organizers_count'] > 0)
                <div class="flex flex-wrap gap-1">
                    @foreach($staff['assigned_organizers'] as $organizer)
                    <span class="organizer-badge" title="{{ $organizer['email'] }} - {{ $organizer['events_count'] }} events">
                        {{ $organizer['name'] }}
                        <button onclick="removeOrganizerAssignment({{ $staff['id'] }}, {{ $organizer['id'] }})"
                                class="ml-2 text-white hover:text-red-200 transition-colors">
                            <i data-lucide="x" class="w-3 h-3"></i>
                        </button>
                    </span>
                    @endforeach
                </div>
                @else
                <p class="text-sm text-gray-500 italic">Belum ada organizer yang ditugaskan</p>
                @endif
            </div>

            <!-- Action Buttons -->
            <div class="flex gap-2">
                <button onclick="showAssignmentModal({{ $staff['id'] }}, '{{ $staff['name'] }}')"
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="user-plus" class="w-4 h-4 inline mr-1"></i>
                    Manage Assignments
                </button>

                @if($staff['assigned_organizers_count'] > 0)
                <button onclick="clearAllAssignments({{ $staff['id'] }}, '{{ $staff['name'] }}')"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                </button>
                @endif
            </div>

            <!-- Last Login Info -->
            @if($staff['last_login_at'])
            <div class="mt-3 pt-3 border-t border-gray-200">
                <p class="text-xs text-gray-500">
                    Last login: {{ \Carbon\Carbon::parse($staff['last_login_at'])->diffForHumans() }}
                </p>
            </div>
            @endif
        </div>
        @endforeach
    </div>

    <!-- Empty State -->
    <div id="emptyState" class="hidden text-center py-12">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i data-lucide="users" class="w-12 h-12 text-gray-400"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No staff found</h3>
        <p class="text-gray-600">Try adjusting your search or filter criteria.</p>
    </div>
</div>

<!-- Assignment Modal -->
<div id="assignmentModal" class="fixed inset-0 bg-black/50 assignment-modal z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Manage Organizer Assignments</h2>
                    <p class="text-gray-600" id="modalStaffName">Staff: -</p>
                </div>
                <button onclick="closeAssignmentModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div class="mb-6">
                <div class="search-box">
                    <div class="relative">
                        <i data-lucide="search" class="search-icon w-5 h-5"></i>
                        <input type="text" id="organizerSearch"
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               placeholder="Cari organizer...">
                    </div>
                </div>
            </div>

            <form id="assignmentForm">
                <input type="hidden" id="modalStaffId" value="">

                <div class="space-y-3 mb-6" id="organizersList">
                    @foreach($organizers as $organizer)
                    <div class="organizer-item flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                         data-organizer-name="{{ strtolower($organizer['name']) }}"
                         data-organizer-email="{{ strtolower($organizer['email']) }}">
                        <input type="checkbox" name="organizer_ids[]" value="{{ $organizer['id'] }}"
                               class="organizer-checkbox" id="org_{{ $organizer['id'] }}">
                        <label for="org_{{ $organizer['id'] }}" class="flex-1 ml-3 cursor-pointer">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ $organizer['name'] }}</h4>
                                    <p class="text-sm text-gray-600">{{ $organizer['email'] }}</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900">{{ $organizer['events_count'] }} Events</div>
                                    <div class="text-sm text-green-600">{{ $organizer['active_events_count'] }} Active</div>
                                    <div class="text-xs text-gray-500">{{ $organizer['assigned_staff_count'] }} Staff</div>
                                </div>
                            </div>
                        </label>
                    </div>
                    @endforeach
                </div>

                <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                    <div class="flex items-center space-x-4">
                        <button type="button" onclick="selectAllOrganizers()"
                                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Select All
                        </button>
                        <button type="button" onclick="clearOrganizerSelection()"
                                class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                            Clear All
                        </button>
                        <span class="text-sm text-gray-600">
                            <span id="selectedOrganizerCount">0</span> organizers selected
                        </span>
                    </div>

                    <div class="flex space-x-3">
                        <button type="button" onclick="closeAssignmentModal()"
                                class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                            <i data-lucide="save" class="w-4 h-4 inline mr-2"></i>
                            Save Assignments
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Assignment Modal -->
<div id="bulkAssignmentModal" class="fixed inset-0 bg-black/50 assignment-modal z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">Bulk Assignment</h2>
                    <p class="text-gray-600">Assign organizers to multiple staff members</p>
                </div>
                <button onclick="closeBulkAssignmentModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="bulkAssignmentForm">
                <!-- Assignment Mode -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Assignment Mode</label>
                    <div class="grid grid-cols-2 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="assignment_mode" value="add" checked class="mr-3">
                            <div>
                                <div class="font-medium text-gray-900">Add to Existing</div>
                                <div class="text-sm text-gray-600">Add organizers to current assignments</div>
                            </div>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="assignment_mode" value="replace" class="mr-3">
                            <div>
                                <div class="font-medium text-gray-900">Replace All</div>
                                <div class="text-sm text-gray-600">Replace all current assignments</div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Staff Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Staff Members</label>
                    <div class="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                        @foreach($staffMembers as $staff)
                        <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <input type="checkbox" name="staff_ids[]" value="{{ $staff['id'] }}" class="mr-3">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ $staff['name'] }}</div>
                                <div class="text-sm text-gray-600">{{ $staff['assigned_organizers_count'] }} current assignments</div>
                            </div>
                        </label>
                        @endforeach
                    </div>
                </div>

                <!-- Organizer Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">Select Organizers</label>
                    <div class="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3">
                        @foreach($organizers as $organizer)
                        <label class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <input type="checkbox" name="organizer_ids[]" value="{{ $organizer['id'] }}" class="mr-3">
                            <div class="flex-1">
                                <div class="font-medium text-gray-900">{{ $organizer['name'] }}</div>
                                <div class="text-sm text-gray-600">{{ $organizer['events_count'] }} events, {{ $organizer['assigned_staff_count'] }} staff</div>
                            </div>
                        </label>
                        @endforeach
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeBulkAssignmentModal()"
                            class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 font-medium transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors">
                        <i data-lucide="users" class="w-4 h-4 inline mr-2"></i>
                        Apply Bulk Assignment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Global variables
let selectedStaff = [];
let currentStaffId = null;
let staffData = @json($staffMembers);
let organizerData = @json($organizers);

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadStats();
    initializeEventListeners();
    updateBulkActions();
});

// Load statistics
function loadStats() {
    fetch('/admin/staff-management/stats')
        .then(response => response.json())
        .then(data => {
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <div class="stats-card">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-white/20 rounded-lg">
                            <i data-lucide="users" class="w-6 h-6"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-white/80 text-sm">Total Staff</p>
                            <p class="text-2xl font-bold">${data.total_staff}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i data-lucide="user-check" class="w-6 h-6 text-green-600"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Assigned Staff</p>
                            <p class="text-2xl font-bold text-gray-900">${data.assigned_staff}</p>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: ${(data.assigned_staff / data.total_staff) * 100}%"></div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-orange-100 rounded-lg">
                            <i data-lucide="user-x" class="w-6 h-6 text-orange-600"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Unassigned Staff</p>
                            <p class="text-2xl font-bold text-gray-900">${data.unassigned_staff}</p>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-orange-500 h-2 rounded-full" style="width: ${(data.unassigned_staff / data.total_staff) * 100}%"></div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Total Organizers</p>
                            <p class="text-2xl font-bold text-gray-900">${data.total_organizers}</p>
                        </div>
                    </div>
                    <div class="text-xs text-gray-600">
                        ${data.organizers_with_staff} with staff assigned
                    </div>
                </div>
            `;

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        })
        .catch(error => {
            console.error('Error loading stats:', error);
        });
}

// Initialize event listeners
function initializeEventListeners() {
    // Search functionality
    document.getElementById('searchInput').addEventListener('input', filterStaff);
    document.getElementById('filterStatus').addEventListener('change', filterStaff);
    document.getElementById('sortBy').addEventListener('change', sortStaff);

    // Organizer search in modal
    document.getElementById('organizerSearch').addEventListener('input', filterOrganizers);

    // Assignment form submission
    document.getElementById('assignmentForm').addEventListener('submit', handleAssignmentSubmit);
    document.getElementById('bulkAssignmentForm').addEventListener('submit', handleBulkAssignmentSubmit);

    // Organizer checkbox change
    document.querySelectorAll('#organizersList input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedOrganizerCount);
    });
}

// Filter staff based on search and filters
function filterStaff() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('filterStatus').value;

    const staffCards = document.querySelectorAll('.staff-card');
    let visibleCount = 0;

    staffCards.forEach(card => {
        const staffName = card.dataset.staffName;
        const assignmentCount = parseInt(card.dataset.assignmentCount);

        let matchesSearch = staffName.includes(searchTerm);
        let matchesStatus = true;

        if (statusFilter === 'assigned') {
            matchesStatus = assignmentCount > 0;
        } else if (statusFilter === 'unassigned') {
            matchesStatus = assignmentCount === 0;
        }

        if (matchesSearch && matchesStatus) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // Show/hide empty state
    const emptyState = document.getElementById('emptyState');
    if (visibleCount === 0) {
        emptyState.classList.remove('hidden');
    } else {
        emptyState.classList.add('hidden');
    }
}

// Sort staff
function sortStaff() {
    const sortBy = document.getElementById('sortBy').value;
    const container = document.getElementById('staffContainer');
    const cards = Array.from(container.children);

    cards.sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.dataset.staffName.localeCompare(b.dataset.staffName);
            case 'assignments':
                return parseInt(b.dataset.assignmentCount) - parseInt(a.dataset.assignmentCount);
            case 'created_at':
                // This would need additional data attributes
                return 0;
            default:
                return 0;
        }
    });

    cards.forEach(card => container.appendChild(card));
}

// Filter organizers in modal
function filterOrganizers() {
    const searchTerm = document.getElementById('organizerSearch').value.toLowerCase();
    const organizerItems = document.querySelectorAll('.organizer-item');

    organizerItems.forEach(item => {
        const name = item.dataset.organizerName;
        const email = item.dataset.organizerEmail;

        if (name.includes(searchTerm) || email.includes(searchTerm)) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

// Show assignment modal
function showAssignmentModal(staffId, staffName) {
    currentStaffId = staffId;
    document.getElementById('modalStaffId').value = staffId;
    document.getElementById('modalStaffName').textContent = `Staff: ${staffName}`;

    // Load current assignments
    loadCurrentAssignments(staffId);

    document.getElementById('assignmentModal').classList.remove('hidden');
}

// Close assignment modal
function closeAssignmentModal() {
    document.getElementById('assignmentModal').classList.add('hidden');
    currentStaffId = null;

    // Clear form
    document.getElementById('assignmentForm').reset();
    updateSelectedOrganizerCount();
}

// Load current assignments for staff
function loadCurrentAssignments(staffId) {
    const staff = staffData.find(s => s.id === staffId);
    if (!staff) return;

    // Clear all checkboxes first
    document.querySelectorAll('#organizersList input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Check assigned organizers
    staff.assigned_organizers.forEach(organizer => {
        const checkbox = document.querySelector(`#org_${organizer.id}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    updateSelectedOrganizerCount();
}

// Handle assignment form submission
function handleAssignmentSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const organizerIds = formData.getAll('organizer_ids[]');

    if (organizerIds.length === 0) {
        showNotification('Please select at least one organizer', 'warning');
        return;
    }

    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i data-lucide="loader" class="w-4 h-4 inline mr-2 animate-spin"></i>Saving...';
    submitButton.disabled = true;

    fetch(`/admin/staff-management/${currentStaffId}/assign-organizers`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            organizer_ids: organizerIds.map(id => parseInt(id))
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeAssignmentModal();
            refreshData();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while saving assignments', 'error');
    })
    .finally(() => {
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// Remove organizer assignment
function removeOrganizerAssignment(staffId, organizerId) {
    if (!confirm('Are you sure you want to remove this organizer assignment?')) {
        return;
    }

    fetch(`/admin/staff-management/${staffId}/remove-organizer/${organizerId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            refreshData();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while removing assignment', 'error');
    });
}

// Clear all assignments for staff
function clearAllAssignments(staffId, staffName) {
    if (!confirm(`Are you sure you want to clear all organizer assignments for ${staffName}?`)) {
        return;
    }

    fetch(`/admin/staff-management/${staffId}/clear-assignments`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            refreshData();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while clearing assignments', 'error');
    });
}

// Bulk actions
function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.staff-checkbox:checked');
    const count = checkboxes.length;

    selectedStaff = Array.from(checkboxes).map(cb => parseInt(cb.value));

    document.getElementById('selectedCount').textContent = count;

    const bulkActionBar = document.getElementById('bulkActionBar');
    if (count > 0) {
        bulkActionBar.classList.add('active');
    } else {
        bulkActionBar.classList.remove('active');
    }
}

function selectAllStaff() {
    document.querySelectorAll('.staff-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateBulkActions();
}

function clearSelection() {
    document.querySelectorAll('.staff-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateBulkActions();
}

// Organizer selection functions
function selectAllOrganizers() {
    document.querySelectorAll('#organizersList input[type="checkbox"]:not([style*="display: none"])').forEach(checkbox => {
        if (checkbox.closest('.organizer-item').style.display !== 'none') {
            checkbox.checked = true;
        }
    });
    updateSelectedOrganizerCount();
}

function clearOrganizerSelection() {
    document.querySelectorAll('#organizersList input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedOrganizerCount();
}

function updateSelectedOrganizerCount() {
    const count = document.querySelectorAll('#organizersList input[type="checkbox"]:checked').length;
    document.getElementById('selectedOrganizerCount').textContent = count;
}

// Bulk assignment modal
function showBulkAssignmentModal() {
    document.getElementById('bulkAssignmentModal').classList.remove('hidden');
}

function closeBulkAssignmentModal() {
    document.getElementById('bulkAssignmentModal').classList.add('hidden');
    document.getElementById('bulkAssignmentForm').reset();
}

function handleBulkAssignmentSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const staffIds = formData.getAll('staff_ids[]');
    const organizerIds = formData.getAll('organizer_ids[]');
    const assignmentMode = formData.get('assignment_mode');

    if (staffIds.length === 0) {
        showNotification('Please select at least one staff member', 'warning');
        return;
    }

    if (organizerIds.length === 0) {
        showNotification('Please select at least one organizer', 'warning');
        return;
    }

    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i data-lucide="loader" class="w-4 h-4 inline mr-2 animate-spin"></i>Processing...';
    submitButton.disabled = true;

    fetch('/admin/staff-management/bulk-assign', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            staff_ids: staffIds.map(id => parseInt(id)),
            organizer_ids: organizerIds.map(id => parseInt(id)),
            assignment_mode: assignmentMode
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            closeBulkAssignmentModal();
            refreshData();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred during bulk assignment', 'error');
    })
    .finally(() => {
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// Utility functions
function refreshData() {
    window.location.reload();
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-black',
        info: 'bg-blue-500 text-white'
    };

    notification.className += ` ${colors[type] || colors.info}`;
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                <i data-lucide="x" class="w-4 h-4"></i>
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}
</script>
@endpush
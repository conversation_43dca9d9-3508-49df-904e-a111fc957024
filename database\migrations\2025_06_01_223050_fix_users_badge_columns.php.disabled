 <?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Get existing columns
            $columns = Schema::getColumnListing('users');

            // Add badge_level_id if it doesn't exist
            if (!in_array('badge_level_id', $columns)) {
                $table->unsignedBigInteger('badge_level_id')->nullable()->after('role');
            }

            // Add badge_assigned_at if it doesn't exist
            if (!in_array('badge_assigned_at', $columns)) {
                $table->timestamp('badge_assigned_at')->nullable()->after('badge_level_id');
            }

            // Add badge_expires_at if it doesn't exist
            if (!in_array('badge_expires_at', $columns)) {
                $table->timestamp('badge_expires_at')->nullable()->after('badge_assigned_at');
            }

            // Add badge_duration_days if it doesn't exist
            if (!in_array('badge_duration_days', $columns)) {
                $table->integer('badge_duration_days')->nullable()->comment('Duration in days, null for permanent')->after('badge_expires_at');
            }

            // Add badge_auto_renew if it doesn't exist
            if (!in_array('badge_auto_renew', $columns)) {
                $table->boolean('badge_auto_renew')->default(false)->after('badge_duration_days');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Get existing columns
            $columns = Schema::getColumnListing('users');

            // Drop columns if they exist
            $columnsToRemove = [];

            if (in_array('badge_auto_renew', $columns)) {
                $columnsToRemove[] = 'badge_auto_renew';
            }
            if (in_array('badge_duration_days', $columns)) {
                $columnsToRemove[] = 'badge_duration_days';
            }
            if (in_array('badge_expires_at', $columns)) {
                $columnsToRemove[] = 'badge_expires_at';
            }
            if (in_array('badge_assigned_at', $columns)) {
                $columnsToRemove[] = 'badge_assigned_at';
            }
            if (in_array('badge_level_id', $columns)) {
                $columnsToRemove[] = 'badge_level_id';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};

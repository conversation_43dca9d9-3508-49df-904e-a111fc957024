<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Template Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f1f5f9;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .ticket {
            width: 450px;
            height: 280px;
            background: {{ request('background_color', '#ffffff') }};
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .ticket-header {
            background: linear-gradient(135deg, {{ request('primary_color', '#1e40af') }}, {{ request('accent_color', '#3b82f6') }});
            color: white;
            padding: 20px 24px;
            position: relative;
            overflow: hidden;
        }

        .ticket-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .ticket-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            transform: translate(-20px, 20px);
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .company-logo {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .event-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .event-type {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ticket-body {
            padding: 24px;
            flex: 1;
            display: flex;
            gap: 24px;
        }

        .main-info {
            flex: 1;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .info-label {
            font-size: 11px;
            font-weight: 600;
            color: {{ request('secondary_color', '#64748b') }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 600;
            color: {{ request('text_color', '#1e293b') }};
        }

        .attendee-section {
            background: {{ request('secondary_color', '#f8fafc') }};
            border-radius: 8px;
            padding: 12px;
            border-left: 3px solid {{ request('primary_color', '#1e40af') }};
        }

        .attendee-label {
            font-size: 10px;
            font-weight: 600;
            color: {{ request('secondary_color', '#64748b') }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }

        .attendee-name {
            font-size: 16px;
            font-weight: 700;
            color: {{ request('primary_color', '#1e40af') }};
        }

        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            background: {{ request('secondary_color', '#f1f5f9') }};
            border: 2px solid {{ request('primary_color', '#1e40af') }};
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: {{ request('primary_color', '#1e40af') }};
            text-align: center;
            font-weight: 600;
        }

        .qr-instructions {
            font-size: 9px;
            color: {{ request('secondary_color', '#64748b') }};
            text-align: center;
            line-height: 1.3;
        }

        .ticket-footer {
            border-top: 1px solid {{ request('secondary_color', '#e2e8f0') }};
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: {{ request('secondary_color', '#f8fafc') }};
        }

        .ticket-id {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .ticket-id-label {
            font-size: 9px;
            color: {{ request('secondary_color', '#64748b') }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ticket-id-value {
            font-size: 12px;
            font-weight: 600;
            color: {{ request('text_color', '#1e293b') }};
            font-family: 'Courier New', monospace;
        }

        .price-section {
            display: flex;
            flex-direction: column;
            align-items: end;
            gap: 2px;
        }

        .price-label {
            font-size: 9px;
            color: {{ request('secondary_color', '#64748b') }};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .price {
            font-size: 18px;
            font-weight: 700;
            color: {{ request('primary_color', '#1e40af') }};
        }

        .security-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                radial-gradient(circle at 20% 20%, rgba(30, 64, 175, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(30, 64, 175, 0.02) 0%, transparent 50%);
            pointer-events: none;
        }

        .validity-indicator {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="security-pattern"></div>
        <div class="validity-indicator"></div>
        
        <div class="ticket-header">
            <div class="header-content">
                <div class="company-logo">T</div>
                <div class="event-title">{{ request('event_title', 'Sample Event') }}</div>
                <div class="event-type">Professional Conference</div>
            </div>
        </div>
        
        <div class="ticket-body">
            <div class="main-info">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Event Date</div>
                        <div class="info-value">{{ request('start_date', now()->addDays(7)->format('M d, Y')) }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Start Time</div>
                        <div class="info-value">{{ request('start_date', now()->addDays(7)->format('H:i')) }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Venue</div>
                        <div class="info-value">{{ request('venue_name', 'Sample Venue') }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Seat</div>
                        <div class="info-value">{{ request('seat_number', 'A001') }}</div>
                    </div>
                </div>
                
                <div class="attendee-section">
                    <div class="attendee-label">Attendee</div>
                    <div class="attendee-name">{{ request('attendee_name', 'John Doe') }}</div>
                </div>
            </div>
            
            <div class="qr-section">
                <div class="qr-code">
                    SECURE<br>QR<br>CODE
                </div>
                <div class="qr-instructions">
                    Scan at<br>entrance
                </div>
            </div>
        </div>
        
        <div class="ticket-footer">
            <div class="ticket-id">
                <div class="ticket-id-label">Ticket ID</div>
                <div class="ticket-id-value">{{ request('ticket_number', 'TIK-PREVIEW-001') }}</div>
            </div>
            
            <div class="price-section">
                <div class="price-label">Total Paid</div>
                <div class="price">{{ request('price', 'Rp 150.000') }}</div>
            </div>
        </div>
    </div>
</body>
</html>

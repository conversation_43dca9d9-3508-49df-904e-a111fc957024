<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('platform_settings')) {
            Schema::create('platform_settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->text('value');
                $table->string('type')->default('string'); // string, number, boolean, json
                $table->string('group')->default('general'); // general, fees, subscriptions, etc.
                $table->string('description')->nullable();
                $table->boolean('is_public')->default(false); // Can be accessed by non-admin users
                $table->timestamps();

                $table->index(['group']);
                $table->index(['is_public']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('platform_settings');
    }
};

<?php

echo "🔧 Fixing migration syntax errors...\n";

$migrationPath = 'database/migrations/';
$migrationFiles = glob($migrationPath . '*.php');

$fixedCount = 0;
$errorCount = 0;

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    echo "Checking {$filename}...\n";
    
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Common syntax error patterns to fix
    $fixes = [
        // Fix missing closing braces for if statements and functions
        '/(\s+}\);\s+}\s+\/\*\*\s+}\s+\*\s+Reverse)/s' => '$1',
        
        // Fix malformed comment blocks
        '/\/\*\*\s+}\s+\*\s+Reverse the migrations\.\s+\*\//' => "/**\n     * Reverse the migrations.\n     */",
        
        // Fix missing closing brace for up() function
        '/(\s+}\);\s+}\s+\/\*\*\s+\*\s+Reverse)/' => '$1',
        
        // Fix double closing braces
        '/}\s+}\s+\/\*\*/' => "}\n\n    /**",
        
        // Fix missing indentation in table creation
        '/Schema::create\([^,]+,\s*function\s*\([^)]+\)\s*{\s*\$table/' => function($matches) {
            return str_replace('$table', '            $table', $matches[0]);
        }
    ];
    
    $hasChanges = false;
    
    foreach ($fixes as $pattern => $replacement) {
        if (is_callable($replacement)) {
            $newContent = preg_replace_callback($pattern, $replacement, $content);
        } else {
            $newContent = preg_replace($pattern, $replacement, $content);
        }
        
        if ($newContent !== $content) {
            $content = $newContent;
            $hasChanges = true;
        }
    }
    
    // Additional specific fixes for common issues
    
    // Fix missing closing brace for if (!Schema::hasTable()) blocks
    if (preg_match('/if\s*\(\s*!\s*Schema::hasTable\([^)]+\)\s*\)\s*{[^}]*Schema::create[^}]*}\);\s*}\s*\/\*\*/', $content)) {
        // This pattern indicates missing closing brace for if statement
        $content = preg_replace(
            '/(if\s*\(\s*!\s*Schema::hasTable\([^)]+\)\s*\)\s*{[^}]*Schema::create[^}]*}\);\s*)(}\s*\/\*\*)/',
            '$1' . "\n        }\n    }\n\n    /**",
            $content
        );
        $hasChanges = true;
    }
    
    // Fix indentation issues
    if (strpos($content, 'Schema::create') !== false) {
        // Fix table column definitions that are not properly indented
        $content = preg_replace('/(\$table->[^;]+;)/', '                $1', $content);
        $content = preg_replace('/\s{12,}\$table/', '                $table', $content); // Fix over-indentation
        $hasChanges = true;
    }
    
    // Validate PHP syntax
    $tempFile = tempnam(sys_get_temp_dir(), 'migration_check');
    file_put_contents($tempFile, $content);
    
    $output = [];
    $returnCode = 0;
    exec("php -l {$tempFile} 2>&1", $output, $returnCode);
    unlink($tempFile);
    
    if ($returnCode === 0) {
        if ($hasChanges) {
            file_put_contents($file, $content);
            echo "  ✅ Fixed {$filename}\n";
            $fixedCount++;
        } else {
            echo "  ✅ {$filename} is OK\n";
        }
    } else {
        echo "  ❌ Syntax error in {$filename}: " . implode(' ', $output) . "\n";
        $errorCount++;
        
        // Try to restore original content if our fixes made it worse
        if ($originalContent !== $content) {
            file_put_contents($file, $originalContent);
            echo "  🔄 Restored original content for {$filename}\n";
        }
    }
}

echo "\n📊 Summary:\n";
echo "  Fixed: {$fixedCount} files\n";
echo "  Errors: {$errorCount} files\n";

if ($errorCount > 0) {
    echo "\n⚠️  Some files still have syntax errors. Please check them manually.\n";
} else {
    echo "\n🎉 All migration files are now syntax-error free!\n";
}

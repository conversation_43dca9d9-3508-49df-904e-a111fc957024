<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Concert Festival Ticket - {{ $ticket->event->title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Roboto:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #434343 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .festival-ticket {
            background: #000;
            border-radius: 20px;
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            position: relative;
            box-shadow: 0 0 50px rgba(255, 0, 150, 0.3);
            border: 2px solid #ff0096;
        }
        
        .neon-border {
            position: absolute;
            inset: -2px;
            background: linear-gradient(45deg, #ff0096, #00d4ff, #ff0096, #00d4ff);
            border-radius: 22px;
            z-index: -1;
            animation: neonGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes neonGlow {
            0% { box-shadow: 0 0 20px rgba(255, 0, 150, 0.5); }
            100% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.5); }
        }
        
        .ticket-header {
            background: linear-gradient(135deg, #ff0096 0%, #00d4ff 100%);
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .ticket-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 2px,
                rgba(255, 255, 255, 0.1) 2px,
                rgba(255, 255, 255, 0.1) 4px
            );
            animation: moveStripes 20s linear infinite;
        }
        
        @keyframes moveStripes {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(100%) translateY(100%); }
        }
        
        .festival-logo {
            font-family: 'Bebas Neue', cursive;
            font-size: 48px;
            font-weight: 400;
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            letter-spacing: 3px;
        }
        
        .event-title {
            font-family: 'Bebas Neue', cursive;
            font-size: 36px;
            color: #fff;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }
        
        .event-subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            position: relative;
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .ticket-body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            padding: 40px;
            color: #fff;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }
        
        .event-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .info-block {
            background: rgba(255, 255, 255, 0.05);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 0, 150, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .info-block:hover {
            border-color: rgba(0, 212, 255, 0.5);
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
        }
        
        .info-icon {
            font-size: 24px;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #ff0096 0%, #00d4ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .info-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #fff;
            line-height: 1.4;
        }
        
        .attendee-section {
            background: linear-gradient(135deg, rgba(255, 0, 150, 0.1) 0%, rgba(0, 212, 255, 0.1) 100%);
            padding: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255, 0, 150, 0.3);
            margin-bottom: 25px;
        }
        
        .attendee-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            font-weight: 700;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .attendee-name {
            font-family: 'Bebas Neue', cursive;
            font-size: 28px;
            color: #fff;
            margin-bottom: 5px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .attendee-email {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .ticket-number {
            background: linear-gradient(135deg, #ff0096 0%, #00d4ff 100%);
            color: #fff;
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Bebas Neue', cursive;
            font-size: 16px;
            letter-spacing: 2px;
            margin-bottom: 20px;
            box-shadow: 0 0 20px rgba(255, 0, 150, 0.3);
        }
        
        .qr-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .qr-code {
            background: #fff;
            padding: 20px;
            border-radius: 15px;
            position: relative;
            z-index: 2;
        }
        
        .qr-code::before {
            content: '';
            position: absolute;
            inset: -3px;
            background: linear-gradient(45deg, #ff0096, #00d4ff, #ff0096);
            border-radius: 18px;
            z-index: -1;
            animation: rotateGradient 3s linear infinite;
        }
        
        @keyframes rotateGradient {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .qr-code img {
            width: 150px;
            height: 150px;
            display: block;
        }
        
        .qr-instructions {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.5;
            max-width: 180px;
        }
        
        .ticket-footer {
            background: #000;
            padding: 25px 40px;
            border-top: 1px solid rgba(255, 0, 150, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .footer-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .status-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: #fff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
        }
        
        .status-badge.used {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            box-shadow: 0 0 15px rgba(220, 53, 69, 0.3);
        }
        
        .status-badge.cancelled {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            box-shadow: 0 0 15px rgba(108, 117, 125, 0.3);
        }
        
        .footer-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .footer-right {
            text-align: right;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .footer-brand {
            font-family: 'Bebas Neue', cursive;
            font-size: 16px;
            color: #fff;
            margin-bottom: 5px;
            letter-spacing: 1px;
        }
        
        .security-hologram {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff0096, #00d4ff, #ff0096);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #fff;
            font-weight: 700;
            text-align: center;
            animation: hologramSpin 4s linear infinite;
        }
        
        @keyframes hologramSpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .festival-ticket {
                box-shadow: none;
                max-width: none;
                border: 2px solid #000;
            }
            
            .neon-border {
                display: none;
            }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .event-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .ticket-footer {
                flex-direction: column;
                text-align: center;
            }
            
            .footer-right {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="festival-ticket">
        <div class="neon-border"></div>
        <div class="security-hologram">
            SECURE<br>HOLO
        </div>
        
        <!-- Header -->
        <div class="ticket-header">
            <div class="festival-logo">TIXARA</div>
            <div class="event-title">{{ $ticket->event->title }}</div>
            <div class="event-subtitle">Festival • Concert • Live Event</div>
        </div>
        
        <!-- Body -->
        <div class="ticket-body">
            <!-- Attendee Section -->
            <div class="attendee-section">
                <div class="attendee-label">Ticket Holder</div>
                <div class="attendee-name">{{ $ticket->attendee_name }}</div>
                <div class="attendee-email">{{ $ticket->order->customer_email }}</div>
            </div>
            
            <!-- Main Content -->
            <div class="main-content">
                <!-- Event Info -->
                <div class="event-info">
                    <div class="info-block">
                        <div class="info-icon">🗓️</div>
                        <div class="info-label">Event Date</div>
                        <div class="info-value">{{ $ticket->event->start_date->format('D, M j, Y') }}</div>
                    </div>
                    
                    <div class="info-block">
                        <div class="info-icon">⏰</div>
                        <div class="info-label">Show Time</div>
                        <div class="info-value">{{ $ticket->event->start_date->format('g:i A') }}</div>
                    </div>
                    
                    <div class="info-block">
                        <div class="info-icon">📍</div>
                        <div class="info-label">Venue</div>
                        <div class="info-value">{{ $ticket->event->location }}</div>
                    </div>
                    
                    <div class="info-block">
                        <div class="info-icon">🎫</div>
                        <div class="info-label">Section</div>
                        <div class="info-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                    </div>
                    
                    <div class="info-block">
                        <div class="info-icon">🎵</div>
                        <div class="info-label">Category</div>
                        <div class="info-value">{{ $ticket->event->category->name ?? 'Live Music' }}</div>
                    </div>
                    
                    <div class="info-block">
                        <div class="info-icon">💸</div>
                        <div class="info-label">Price</div>
                        <div class="info-value">Rp {{ number_format($ticket->order->unit_price, 0, ',', '.') }}</div>
                    </div>
                </div>
            </div>
            
            <!-- QR Section -->
            <div class="qr-section">
                <div class="ticket-number">{{ $ticket->ticket_number }}</div>
                <div class="qr-container">
                    <div class="qr-code">
                        {!! QrCode::size(150)->generate($ticket->qr_code) !!}
                    </div>
                </div>
                <div class="qr-instructions">
                    Show this QR code at the venue entrance. Keep your ticket safe and arrive early!
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="ticket-footer">
            <div class="footer-left">
                <span class="status-badge {{ strtolower($ticket->status) }}">
                    {{ ucfirst($ticket->status) }}
                </span>
                <div class="footer-info">
                    <div>Order: {{ $ticket->order->order_number }}</div>
                    <div>Issued: {{ $ticket->order->created_at->format('M j, Y') }}</div>
                </div>
            </div>
            <div class="footer-right">
                <div class="footer-brand">TiXara Festival</div>
                <div>Digital Ticketing Platform</div>
                <div>Generated: {{ now()->format('M j, Y g:i A') }}</div>
            </div>
        </div>
    </div>
</body>
</html>

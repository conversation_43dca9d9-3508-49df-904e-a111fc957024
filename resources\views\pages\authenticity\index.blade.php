@extends('layouts.app')

@section('title', 'Ticket Authenticity Checker')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-shield-alt mr-3 text-green-600"></i>
                Ticket Authenticity Checker
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Verifikasi keaslian Boarding E-Tiket TiXara</p>
            <div class="mt-4 inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2"></i>
                <span class="text-sm text-blue-800 dark:text-blue-200">
                    Masukkan ID Boarding E-Tiket untuk mengecek keasliannya
                </span>
            </div>
        </div>

        <!-- Authenticity Check Form -->
        <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
            <form id="authenticityForm" method="POST" action="{{ route('tickets.check-authenticity') }}">
                @csrf

                <!-- Input Field -->
                <div class="mb-6">
                    <label for="identifier" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <i class="fas fa-ticket-alt mr-1"></i>
                        ID Boarding E-Tiket / QR Code
                    </label>
                    <div class="relative">
                        <input type="text" id="identifier" name="identifier" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white text-lg"
                               placeholder="Masukkan nomor tiket atau scan QR code...">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <button type="button" id="scanButton"
                                    class="text-green-600 hover:text-green-800 focus:outline-none">
                                <i class="fas fa-qrcode text-xl"></i>
                            </button>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        Format: TIK-YYYYMMDD-XXXXX, BP-YYYYMMDD-XXXXX, atau scan QR code pada tiket
                    </p>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="checkButton"
                        class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <i class="fas fa-search mr-2"></i>
                    <span>Cek Keaslian Tiket</span>
                    <div class="hidden ml-2" id="loadingSpinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            </form>
        </div>

        <!-- QR Scanner Modal -->
        <div id="scannerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Scan QR Code</h3>
                    <button id="closeScannerButton" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="qr-reader" class="w-full"></div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-4 text-center">
                    Arahkan kamera ke QR Code pada tiket
                </p>
            </div>
        </div>

        <!-- Authenticity Result -->
        <div id="authenticityResult" class="max-w-4xl mx-auto hidden">
            <!-- Result will be populated by JavaScript -->
        </div>

        <!-- How It Works -->
        <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mt-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-question-circle mr-2"></i>Cara Kerja Authenticity Checker
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-ticket-alt text-2xl text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">1. Input ID Tiket</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Masukkan nomor tiket atau scan QR code dari Boarding E-Tiket
                    </p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-database text-2xl text-green-600 dark:text-green-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">2. Verifikasi Database</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Sistem mengecek keberadaan tiket di database TiXara
                    </p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shield-check text-2xl text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">3. Hasil Verifikasi</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Dapatkan konfirmasi apakah tiket authentic atau palsu
                    </p>
                </div>
            </div>
        </div>

        <!-- Security Features -->
        <div class="max-w-4xl mx-auto bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 rounded-xl shadow-lg p-6 mt-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-lock mr-2"></i>Fitur Keamanan TiXara
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-start">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Unique Ticket ID</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Setiap tiket memiliki ID unik yang tidak dapat diduplikasi
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <i class="fas fa-qrcode text-blue-600 dark:text-blue-400 text-xl mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Encrypted QR Code</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            QR Code terenkripsi dengan data verifikasi khusus
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <i class="fas fa-database text-purple-600 dark:text-purple-400 text-xl mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Database Verification</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Verifikasi real-time dengan database TiXara yang aman
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <i class="fas fa-history text-orange-600 dark:text-orange-400 text-xl mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-gray-900 dark:text-white mb-1">Usage Tracking</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Pelacakan penggunaan tiket untuk mencegah duplikasi
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
let html5QrcodeScanner = null;

// Handle form submission
document.getElementById('authenticityForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = document.getElementById('checkButton');
    const spinner = document.getElementById('loadingSpinner');

    // Show loading state
    button.disabled = true;
    spinner.classList.remove('hidden');

    try {
        const response = await fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const result = await response.json();
        showAuthenticityResult(result);

    } catch (error) {
        console.error('Authenticity check error:', error);
        showAuthenticityResult({
            success: false,
            message: 'Terjadi kesalahan jaringan',
            is_authentic: false
        });
    } finally {
        button.disabled = false;
        spinner.classList.add('hidden');
    }
});

// QR Scanner functionality
document.getElementById('scanButton').addEventListener('click', function() {
    document.getElementById('scannerModal').classList.remove('hidden');
    startQRScanner();
});

document.getElementById('closeScannerButton').addEventListener('click', function() {
    stopQRScanner();
    document.getElementById('scannerModal').classList.add('hidden');
});

function startQRScanner() {
    html5QrcodeScanner = new Html5Qrcode("qr-reader");

    html5QrcodeScanner.start(
        { facingMode: "environment" },
        {
            fps: 10,
            qrbox: { width: 250, height: 250 }
        },
        (decodedText, decodedResult) => {
            document.getElementById('identifier').value = decodedText;
            stopQRScanner();
            document.getElementById('scannerModal').classList.add('hidden');
        },
        (errorMessage) => {
            // Handle scan error
        }
    ).catch(err => {
        console.error('QR Scanner error:', err);
    });
}

function stopQRScanner() {
    if (html5QrcodeScanner) {
        html5QrcodeScanner.stop().then(() => {
            html5QrcodeScanner.clear();
        }).catch(err => {
            console.error('Error stopping scanner:', err);
        });
    }
}

function showAuthenticityResult(result) {
    const resultDiv = document.getElementById('authenticityResult');
    const isAuthentic = result.is_authentic;

    let statusIcon, statusColor, statusText, bgClass;

    if (isAuthentic) {
        statusIcon = 'fas fa-shield-check';
        statusColor = 'green';
        statusText = 'AUTHENTIC';
        bgClass = 'bg-green-50 dark:bg-green-900 border-green-500';
    } else {
        statusIcon = 'fas fa-shield-times';
        statusColor = 'red';
        statusText = 'PALSU / TIDAK VALID';
        bgClass = 'bg-red-50 dark:bg-red-900 border-red-500';
    }

    resultDiv.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="${bgClass} p-6 border-l-4">
                <div class="flex items-center mb-4">
                    <i class="${statusIcon} text-3xl text-${statusColor}-600 mr-4"></i>
                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">${statusText}</h3>
                        <p class="text-${statusColor}-600 font-medium text-lg">${result.message}</p>
                    </div>
                </div>
            </div>

            ${result.data ? `
                <div class="p-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Detail Tiket</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Nomor Tiket</p>
                            <p class="font-semibold text-gray-900 dark:text-white text-lg">${result.data.ticket_number}</p>
                        </div>
                        ${result.data.boarding_pass_id ? `
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Boarding Pass ID</p>
                            <p class="font-semibold text-blue-600 dark:text-blue-400 text-lg">${result.data.boarding_pass_id}</p>
                        </div>
                        ` : ''}
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Event</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.event_title}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Nama Peserta</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.attendee_name}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Email Peserta</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.attendee_email}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Venue</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.venue_name}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Tanggal Event</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.event_date}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Status Tiket</p>
                            <p class="font-semibold ${result.data.is_used ? 'text-orange-600' : 'text-green-600'}">${result.data.ticket_status}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Pembeli</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.buyer_name}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Dibuat Pada</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.created_at}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Dicek Pada</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.checked_at}</p>
                        </div>
                        ${result.data.template_used ? `
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Template E-Ticket</p>
                            <p class="font-semibold text-purple-600 dark:text-purple-400">${result.data.template_used.toUpperCase()}</p>
                        </div>
                        ` : ''}
                        ${result.data.generated_at ? `
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">E-Ticket Generated</p>
                            <p class="font-semibold text-gray-900 dark:text-white">${result.data.generated_at}</p>
                        </div>
                        ` : ''}
                        ${result.data.used_at ? `
                            <div class="md:col-span-2">
                                <p class="text-sm text-gray-600 dark:text-gray-400">Digunakan Pada</p>
                                <p class="font-semibold text-orange-600">${result.data.used_at}</p>
                            </div>
                        ` : ''}
                    </div>

                    <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <h5 class="font-semibold text-gray-900 dark:text-white mb-2">Statistik Penggunaan</h5>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Download</p>
                                <p class="font-semibold text-gray-900 dark:text-white">${result.data.download_count} kali</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Print</p>
                                <p class="font-semibold text-gray-900 dark:text-white">${result.data.print_count} kali</p>
                            </div>
                        </div>
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    resultDiv.classList.remove('hidden');
    resultDiv.scrollIntoView({ behavior: 'smooth' });
}
</script>
@endpush
@endsection

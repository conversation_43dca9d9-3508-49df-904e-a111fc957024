<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\UserBadgeLevel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BadgeLevelController extends Controller
{
    /**
     * Display badge level management
     */
    public function index(Request $request)
    {
        $query = UserBadgeLevel::query();

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $badgelevel = $query->withCount('users')->orderBy('sort_order')->paginate(20);

        // Statistics
        $stats = UserBadgeLevel::getStatistics();

        return view('pages.admin.badge-level.index', compact('badgelevel', 'stats'));
    }

    /**
     * Show the form for creating a new badge level
     */
    public function create()
    {
        return view('pages.admin.badge-level.create');
    }

    /**
     * Store a newly created badge level
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:user_badge_level,name',
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'icon' => 'nullable|string|max:255',
            'badge_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'min_spent_amount' => 'required|numeric|min:0',
            'min_uangtix_balance' => 'required|numeric|min:0',
            'min_transactions' => 'required|integer|min:0',
            'min_events_attended' => 'required|integer|min:0',
            'discount_percentage' => 'required|numeric|min:0|max:100',
            'cashback_percentage' => 'required|numeric|min:0|max:100',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->boolean('is_active');

        // Handle badge image upload
        if ($request->hasFile('badge_image')) {
            $image = $request->file('badge_image');
            $filename = time() . '_' . Str::slug($request->name) . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('images/badges'), $filename);
            $data['badge_image'] = 'images/badges/' . $filename;
        }

        // Handle benefits and requirements
        $data['benefits'] = $request->input('benefits', []);
        $data['requirements'] = $request->input('requirements', []);

        UserBadgeLevel::create($data);

        return redirect()->route('admin.badge-level.index')
                        ->with('success', 'Badge level created successfully!');
    }

    /**
     * Display the specified badge level
     */
    public function show(UserBadgeLevel $badgelevel)
    {
        $badgeLevel->load('users');
        return view('pages.admin.badge-level.show', compact('badgeLevel'));
    }

    /**
     * Show the form for editing the specified badge level
     */
    public function edit(UserBadgeLevel $badgelevel)
    {
        return view('pages.admin.badge-level.edit', compact('badgeLevel'));
    }

    /**
     * Update the specified badge level
     */
    public function update(Request $request, UserBadgeLevel $badgelevel)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:user_badge_level,name,' . $badgeLevel->id,
            'description' => 'nullable|string',
            'color' => 'required|string|max:7',
            'icon' => 'nullable|string|max:255',
            'badge_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'min_spent_amount' => 'required|numeric|min:0',
            'min_uangtix_balance' => 'required|numeric|min:0',
            'min_transactions' => 'required|integer|min:0',
            'min_events_attended' => 'required|integer|min:0',
            'discount_percentage' => 'required|numeric|min:0|max:100',
            'cashback_percentage' => 'required|numeric|min:0|max:100',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->name);
        $data['is_active'] = $request->boolean('is_active');

        // Handle badge image upload
        if ($request->hasFile('badge_image')) {
            // Delete old image if exists
            if ($badgeLevel->badge_image && file_exists(public_path($badgeLevel->badge_image))) {
                unlink(public_path($badgeLevel->badge_image));
            }

            $image = $request->file('badge_image');
            $filename = time() . '_' . Str::slug($request->name) . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('images/badges'), $filename);
            $data['badge_image'] = 'images/badges/' . $filename;
        }

        // Handle benefits and requirements
        $data['benefits'] = $request->input('benefits', []);
        $data['requirements'] = $request->input('requirements', []);

        $badgeLevel->update($data);

        // Run auto-upgrade if requested
        if ($request->boolean('run_auto_upgrade')) {
            $upgradedCount = UserBadgeLevel::autoUpgradeUsers();
            return redirect()->route('admin.badge-level.show', $badgelevel)
                            ->with('success', "Badge level updated successfully! Auto-upgraded {$upgradedCount} users.");
        }

        return redirect()->route('admin.badge-level.show', $badgelevel)
                        ->with('success', 'Badge level updated successfully!');
    }

    /**
     * Remove the specified badge level
     */
    public function destroy(UserBadgeLevel $badgelevel)
    {
        // Check if badge level has users
        if ($badgeLevel->users()->count() > 0) {
            return redirect()->back()
                            ->with('error', 'Cannot delete badge level that has users assigned to it.');
        }

        // Delete badge image if exists
        if ($badgeLevel->badge_image && file_exists(public_path($badgeLevel->badge_image))) {
            unlink(public_path($badgeLevel->badge_image));
        }

        $badgeLevel->delete();

        return redirect()->route('admin.badge-level.index')
                        ->with('success', 'Badge level deleted successfully!');
    }

    /**
     * Toggle badge level status
     */
    public function toggleStatus(UserBadgeLevel $badgelevel)
    {
        $badgeLevel->update([
            'is_active' => !$badgeLevel->is_active
        ]);

        $status = $badgeLevel->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
                        ->with('success', "Badge level {$status} successfully!");
    }



    /**
     * Auto-upgrade users
     */
    public function autoUpgrade()
    {
        try {
            $upgradedCount = UserBadgeLevel::autoUpgradeUsers();

            return response()->json([
                'success' => true,
                'message' => "Successfully upgraded {$upgradedCount} users to higher badge level!",
                'upgraded_count' => $upgradedCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-upgrade users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update sort order
     */
    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:user_badge_level,id',
            'items.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->items as $item) {
            UserBadgeLevel::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Sort order updated successfully!'
        ]);
    }

    /**
     * Assign badge to user
     */
    public function assignBadge(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'badge_level_id' => 'nullable|exists:user_badge_level,id',
            'duration_days' => 'nullable|integer|min:1',
            'auto_renew' => 'boolean',
        ]);

        $user = User::findOrFail($validated['user_id']);

        if ($validated['badge_level_id']) {
            $badgelevel = UserBadgeLevel::findOrFail($validated['badge_level_id']);
            $user->assignBadge(
                $badgeLevel,
                $validated['duration_days'] ?? null,
                $validated['auto_renew'] ?? false
            );

            // Create notification
            \App\Models\Notification::create([
                'user_id' => $user->id,
                'title' => 'Badge Level Upgraded!',
                'message' => "Congratulations! You have been awarded the {$badgeLevel->name} badge level.",
                'type' => 'badge_upgrade',
                'data' => [
                    'badge_level_id' => $badgeLevel->id,
                    'badge_name' => $badgeLevel->name,
                ]
            ]);

            $message = "Badge '{$badgeLevel->name}' assigned to {$user->name} successfully!";
        } else {
            $user->removeBadge();
            $message = "Badge removed from {$user->name} successfully!";
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Get users for badge assignment
     */
    public function getUsers(Request $request)
    {
        $search = $request->get('search');
        $role = $request->get('role');

        $query = User::query();

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($role) {
            $query->where('role', $role);
        }

        $users = $query->with('badgeLevel')
                      ->select('id', 'name', 'email', 'role', 'badge_level_id')
                      ->limit(20)
                      ->get();

        return response()->json($users);
    }

    /**
     * Bulk assign badges
     */
    public function bulkAssignBadge(Request $request)
    {
        $validated = $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'badge_level_id' => 'nullable|exists:user_badge_level,id',
        ]);

        $users = User::whereIn('id', $validated['user_ids']);

        if ($validated['badge_level_id']) {
            $badgelevel = UserBadgeLevel::findOrFail($validated['badge_level_id']);
            $users->update(['badge_level_id' => $badgeLevel->id]);
            $message = "Badge '{$badgeLevel->name}' berhasil diberikan kepada " . count($validated['user_ids']) . " pengguna!";
        } else {
            $users->update(['badge_level_id' => null]);
            $message = "Badge berhasil dihapus dari " . count($validated['user_ids']) . " pengguna!";
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Remove badge from user
     */
    public function removeBadge(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($validated['user_id']);
        $user->update(['badge_level_id' => null]);

        return response()->json([
            'success' => true,
            'message' => "Badge berhasil dihapus dari {$user->name}!"
        ]);
    }

    /**
     * Downgrade user badge
     */
    public function downgradeBadge(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'target_badge_id' => 'nullable|exists:user_badge_level,id',
        ]);

        $user = User::findOrFail($validated['user_id']);

        if ($validated['target_badge_id']) {
            $targetBadge = UserBadgeLevel::findOrFail($validated['target_badge_id']);
            $user->assignBadge($targetBadge);
            $message = "User {$user->name} downgraded to {$targetBadge->name} badge!";
        } else {
            $user->downgradeToBronze();
            $message = "User {$user->name} downgraded to Bronze badge!";
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Extend badge duration
     */
    public function extendBadge(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'additional_days' => 'required|integer|min:1',
        ]);

        $user = User::findOrFail($validated['user_id']);

        if (!$user->badge_level_id) {
            return response()->json([
                'success' => false,
                'message' => 'User does not have a badge to extend!'
            ]);
        }

        $currentExpiry = $user->badge_expires_at ?? now();
        $newExpiry = $currentExpiry->addDays($validated['additional_days']);

        $user->update([
            'badge_expires_at' => $newExpiry,
            'badge_duration_days' => ($user->badge_duration_days ?? 0) + $validated['additional_days']
        ]);

        return response()->json([
            'success' => true,
            'message' => "Badge duration extended by {$validated['additional_days']} days for {$user->name}!"
        ]);
    }

    /**
     * Get expired badges
     */
    public function getExpiredBadges()
    {
        $expiredUsers = User::whereNotNull('badge_expires_at')
                           ->where('badge_expires_at', '<', now())
                           ->with('badgeLevel')
                           ->get();

        return response()->json($expiredUsers);
    }

    /**
     * Auto-renew expired badges
     */
    public function autoRenewBadges()
    {
        $renewableUsers = User::whereNotNull('badge_expires_at')
                             ->where('badge_expires_at', '<', now())
                             ->where('badge_auto_renew', true)
                             ->whereNotNull('badge_duration_days')
                             ->get();

        $renewedCount = 0;
        foreach ($renewableUsers as $user) {
            $user->renewBadge();
            $renewedCount++;
        }

        return response()->json([
            'success' => true,
            'message' => "Auto-renewed {$renewedCount} badges successfully!"
        ]);
    }
}

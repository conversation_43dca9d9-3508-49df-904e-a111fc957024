<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('payment_methods')) {
            Schema::create('payment_methods', function (Blueprint $table) {
                $table->id();
                $table->string('code')->unique();
                $table->string('name');
                $table->text('description')->nullable();
                $table->string('icon_class')->nullable(); // CSS class for icon
                $table->string('icon_bg_class')->nullable(); // Background color class
                $table->foreignId('gateway_id')->nullable()->constrained('payment_gateways')->onDelete('set null');
                $table->decimal('fee', 10, 2)->default(0); // Fee amount or percentage
                $table->enum('fee_type', ['fixed', 'percentage'])->default('fixed');
                $table->decimal('min_amount', 15, 2)->nullable(); // Minimum transaction amount
                $table->decimal('max_amount', 15, 2)->nullable(); // Maximum transaction amount
                $table->string('processing_time')->nullable(); // e.g., "Instant", "1-3 hours"
                $table->text('instructions')->nullable(); // Payment instructions
                $table->boolean('is_active')->default(true);
                $table->integer('sort_order')->default(0);
                $table->timestamps();

                $table->index(['is_active', 'sort_order']);
                $table->index(['gateway_id', 'is_active']);
            });
        } else {
            // Table exists, check if we need to add missing columns
            Schema::table('payment_methods', function (Blueprint $table) {
                $columns = Schema::getColumnListing('payment_methods');

                if (!in_array('icon_class', $columns)) {
                    $table->string('icon_class')->nullable()->after('description');
                }
                if (!in_array('icon_bg_class', $columns)) {
                    $table->string('icon_bg_class')->nullable()->after('icon_class');
                }
                if (!in_array('gateway_id', $columns)) {
                    $table->foreignId('gateway_id')->nullable()->after('icon_bg_class');
                }
                if (!in_array('fee_type', $columns)) {
                    // Check if 'fee' column exists, if not add it first
                    if (!in_array('fee', $columns)) {
                        $table->decimal('fee', 10, 2)->default(0)->after('gateway_id');
                    }
                    $table->enum('fee_type', ['fixed', 'percentage'])->default('fixed')->after('fee');
                }
                if (!in_array('processing_time', $columns)) {
                    $table->string('processing_time')->nullable()->after('max_amount');
                }
                if (!in_array('instructions', $columns)) {
                    $table->text('instructions')->nullable()->after('processing_time');
                }
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};

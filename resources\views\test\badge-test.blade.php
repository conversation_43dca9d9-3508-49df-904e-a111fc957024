@extends('layouts.app')

@section('title', 'Badge Test')

@section('content')
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Badge Component Test</h1>
    
    @auth
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Original Badge Component -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Original Badge Component</h2>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-medium mb-2">Size XS</h3>
                        <x-badge-level :user="auth()->user()" size="xs" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size SM</h3>
                        <x-badge-level :user="auth()->user()" size="sm" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size MD</h3>
                        <x-badge-level :user="auth()->user()" size="md" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size LG</h3>
                        <x-badge-level :user="auth()->user()" size="lg" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Without Tooltip</h3>
                        <x-badge-level :user="auth()->user()" :showTooltip="false" />
                    </div>
                </div>
            </div>

            <!-- Simple Badge Component -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-4">Simple Badge Component</h2>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-medium mb-2">Size XS</h3>
                        <x-simple-badge :user="auth()->user()" size="xs" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size SM</h3>
                        <x-simple-badge :user="auth()->user()" size="sm" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size MD</h3>
                        <x-simple-badge :user="auth()->user()" size="md" />
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Size LG</h3>
                        <x-simple-badge :user="auth()->user()" size="lg" />
                    </div>
                </div>
            </div>
        </div>

        <!-- User Info -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">User Information</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium mb-2">Basic Info</h3>
                    <ul class="space-y-1 text-sm">
                        <li><strong>ID:</strong> {{ auth()->user()->id }}</li>
                        <li><strong>Name:</strong> {{ auth()->user()->name }}</li>
                        <li><strong>Email:</strong> {{ auth()->user()->email }}</li>
                        <li><strong>Role:</strong> {{ auth()->user()->role }}</li>
                        <li><strong>Badge Level ID:</strong> {{ auth()->user()->badge_level_id ?? 'NULL' }}</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Badge Level Data</h3>
                    @if(auth()->user()->badgelevel)
                        <ul class="space-y-1 text-sm">
                            <li><strong>Badge ID:</strong> {{ auth()->user()->badgeLevel->id }}</li>
                            <li><strong>Name:</strong> {{ auth()->user()->badgeLevel->name }}</li>
                            <li><strong>Color:</strong> 
                                <span class="inline-block w-4 h-4 rounded" style="background-color: {{ auth()->user()->badgeLevel->color }}"></span>
                                {{ auth()->user()->badgeLevel->color }}
                            </li>
                            <li><strong>Icon:</strong> <i class="{{ auth()->user()->badgeLevel->icon }}"></i> {{ auth()->user()->badgeLevel->icon }}</li>
                            <li><strong>Description:</strong> {{ auth()->user()->badgeLevel->description }}</li>
                            <li><strong>Is Active:</strong> {{ auth()->user()->badgeLevel->is_active ? 'Yes' : 'No' }}</li>
                        </ul>
                    @else
                        <p class="text-red-500">No badge level found!</p>
                        @if(auth()->user()->badge_level_id)
                            <p class="text-yellow-600">User has badge_level_id: {{ auth()->user()->badge_level_id }} but no relation</p>
                        @endif
                    @endif
                </div>
            </div>
        </div>

        <!-- Manual Badge HTML -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Manual Badge Implementation</h2>
            
            @php
                $badgeName = 'Bronze';
                $badgeColor = '#CD7F32';
                $badgeIcon = 'fas fa-medal';
                
                if (auth()->user()->badgelevel) {
                    $badgeName = auth()->user()->badgeLevel->name;
                    $badgeColor = auth()->user()->badgeLevel->color;
                    $badgeIcon = auth()->user()->badgeLevel->icon;
                }
            @endphp
            
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium mb-2">Manual HTML (XS)</h3>
                    <span class="inline-flex items-center px-2 py-1 text-xs rounded-full font-semibold text-white shadow-sm"
                          style="background-color: {{ $badgeColor }};">
                        <i class="{{ $badgeIcon }} text-xs mr-1"></i>
                        {{ $badgeName }}
                    </span>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Manual HTML (SM)</h3>
                    <span class="inline-flex items-center px-2 py-1 text-sm rounded-full font-semibold text-white shadow-sm"
                          style="background-color: {{ $badgeColor }};">
                        <i class="{{ $badgeIcon }} text-sm mr-1"></i>
                        {{ $badgeName }}
                    </span>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Manual HTML (MD)</h3>
                    <span class="inline-flex items-center px-3 py-1 text-sm rounded-full font-semibold text-white shadow-sm"
                          style="background-color: {{ $badgeColor }};">
                        <i class="{{ $badgeIcon }} text-sm mr-2"></i>
                        {{ $badgeName }}
                    </span>
                </div>
            </div>
        </div>

        <!-- All Available Badges -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">All Available Badge Level</h2>
            
            @php
                $allBadges = \App\Models\UserBadgeLevel::all();
            @endphp
            
            @if($allBadges->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($allBadges as $badge)
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="inline-flex items-center px-3 py-1 text-sm rounded-full font-semibold text-white shadow-sm"
                                      style="background-color: {{ $badge->color }};">
                                    <i class="{{ $badge->icon ?? 'fas fa-medal' }} text-sm mr-2"></i>
                                    {{ $badge->name }}
                                </span>
                                @if($badge->is_default)
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Default</span>
                                @endif
                            </div>
                            <div class="text-sm text-gray-600">
                                <p><strong>ID:</strong> {{ $badge->id }}</p>
                                <p><strong>Color:</strong> {{ $badge->color }}</p>
                                <p><strong>Icon:</strong> {{ $badge->icon }}</p>
                                <p><strong>Active:</strong> {{ $badge->is_active ? 'Yes' : 'No' }}</p>
                                <p><strong>Users:</strong> {{ $badge->users()->count() }}</p>
                                <p><strong>Description:</strong> {{ Str::limit($badge->description, 50) }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-8">
                    <p class="text-red-500 mb-4">No badge level found in database!</p>
                    <a href="{{ route('fix.badges') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Fix Badges
                    </a>
                </div>
            @endif
        </div>

        <!-- Actions -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Actions</h2>
            
            <div class="flex space-x-4">
                <a href="{{ route('fix.badges') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    Fix User Badges
                </a>
                <a href="{{ route('debug.badge') }}" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Debug Badge
                </a>
                <button onclick="location.reload()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                    Refresh Page
                </button>
            </div>
        </div>
        
    @else
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Please login to test badge components.
        </div>
    @endauth
</div>
@endsection

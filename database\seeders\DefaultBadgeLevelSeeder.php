<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserBadgeLevel;

class DefaultBadgeLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Bronze badge as default
        $bronzeBadge = UserBadgeLevel::firstOrCreate(
            ['name' => 'Bronze'],
            [
                'description' => 'Default badge for all new users',
                'color' => '#CD7F32',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                ],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]
        );

        // Create Silver badge
        UserBadgeLevel::firstOrCreate(
            ['name' => 'Silver'],
            [
                'description' => 'For users with moderate activity',
                'color' => '#C0C0C0',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 500000,
                'min_uangtix_balance' => 100000,
                'min_transactions' => 5,
                'min_events_attended' => 3,
                'discount_percentage' => 5,
                'cashback_percentage' => 2,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 2,
            ]
        );

        // Create Gold badge
        UserBadgeLevel::firstOrCreate(
            ['name' => 'Gold'],
            [
                'description' => 'For highly active users',
                'color' => '#FFD700',
                'icon' => 'fas fa-crown',
                'min_spent_amount' => 2000000,
                'min_uangtix_balance' => 500000,
                'min_transactions' => 15,
                'min_events_attended' => 10,
                'discount_percentage' => 10,
                'cashback_percentage' => 5,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                    'priority_support' => true,
                    'exclusive_events' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 3,
            ]
        );

        // Create Platinum badge
        UserBadgeLevel::firstOrCreate(
            ['name' => 'Platinum'],
            [
                'description' => 'For VIP users',
                'color' => '#E5E4E2',
                'icon' => 'fas fa-gem',
                'min_spent_amount' => 5000000,
                'min_uangtix_balance' => 1000000,
                'min_transactions' => 30,
                'min_events_attended' => 25,
                'discount_percentage' => 15,
                'cashback_percentage' => 8,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                    'priority_support' => true,
                    'exclusive_events' => true,
                    'personal_concierge' => true,
                    'vip_lounge' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 4,
            ]
        );

        // Assign Bronze badge to all existing users without badges
        $usersWithoutBadges = \App\Models\User::whereNull('badge_level_id')->get();
        
        foreach ($usersWithoutBadges as $user) {
            $user->update([
                'badge_level_id' => $bronzeBadge->id,
                'badge_assigned_at' => now(),
                'badge_expires_at' => null, // Permanent by default
                'badge_duration_days' => null,
                'badge_auto_renew' => false,
            ]);
        }

        $this->command->info('Default badge level created and assigned to existing users.');
    }
}

<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Carbon\Carbon;

class CleanUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing users
        User::truncate();

        $now = Carbon::now();

        // 1. Super Admin
        User::create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('SuperAdmin@2024'),
            'role' => 'admin',
            'phone' => '081234567890',
            'gender' => 'male',
            'birth_date' => '1985-01-15',
            'address' => 'Jl. Sudirman No. 1, Jakarta Pusat, DKI Jakarta 10110',
            'bio' => 'Super Administrator of Tixara Platform with full system access',
            'is_active' => true,
            'badge_level_id' => 4, // Platinum
            'badge_expires_at' => $now->copy()->addYears(5),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 1825,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
            'company_name' => 'Tixara Indonesia',
            'company_address' => 'Jl. Sudirman No. 1, Jakarta Pusat',
            'organization' => 'Tixara Platform',
            'website' => 'https://tixara.my.id',
        ]);

        // 2. Admin
        User::create([
            'name' => 'Admin Tixara',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Admin@2024'),
            'role' => 'admin',
            'phone' => '081234567891',
            'gender' => 'female',
            'birth_date' => '1990-05-15',
            'address' => 'Jl. Gatot Subroto No. 25, Jakarta Selatan',
            'bio' => 'Platform Administrator responsible for system management',
            'is_active' => true,
            'badge_level_id' => 3, // Gold
            'badge_expires_at' => $now->copy()->addYears(2),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 730,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
            'company_name' => 'Tixara Indonesia',
            'company_address' => 'Jl. Gatot Subroto No. 25, Jakarta Selatan',
            'organization' => 'Tixara Platform',
            'website' => 'https://tixara.my.id',
        ]);

        // 3. Staff 1
        User::create([
            'name' => 'Budi Staff',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Staff@2024'),
            'role' => 'staff',
            'phone' => '081234567892',
            'gender' => 'male',
            'birth_date' => '1995-08-20',
            'address' => 'Jl. Kemang Raya No. 15, Jakarta Selatan',
            'bio' => 'Ticket validation staff specializing in QR code scanning',
            'is_active' => true,
            'badge_level_id' => 2, // Silver
            'badge_expires_at' => $now->copy()->addMonths(6),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 180,
            'badge_auto_renew' => false,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
        ]);

        // 4. Staff 2
        User::create([
            'name' => 'Sari Staff',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Staff@2024'),
            'role' => 'staff',
            'phone' => '081234567893',
            'gender' => 'female',
            'birth_date' => '1993-12-10',
            'address' => 'Jl. Senopati No. 88, Jakarta Selatan',
            'bio' => 'Senior ticket validation staff with crowd management expertise',
            'is_active' => true,
            'badge_level_id' => 2, // Silver
            'badge_expires_at' => $now->copy()->addMonths(8),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 240,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
        ]);

        // 5. Organizer Platinum (Custom Template Access)
        User::create([
            'name' => 'Ahmad Platinum Events',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Platinum@2024'),
            'role' => 'penjual',
            'phone' => '081234567894',
            'gender' => 'male',
            'birth_date' => '1985-03-12',
            'address' => 'Jl. HR Rasuna Said No. 100, Jakarta Selatan',
            'bio' => 'Premium event organizer with Platinum badge access. Specializes in large-scale corporate events.',
            'is_active' => true,
            'total_revenue' => *********,
            'total_tickets_sold' => 3500,
            'total_events_created' => 45,
            'badge_level_id' => 4, // Platinum
            'badge_expires_at' => $now->copy()->addYears(2),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 730,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
            'company_name' => 'Platinum Events Indonesia',
            'company_address' => 'Jl. HR Rasuna Said No. 100, Jakarta Selatan',
            'organization' => 'Platinum Events Indonesia',
            'website' => 'https://platinumevents.co.id',
        ]);

        // 6. Organizer Gold
        User::create([
            'name' => 'Sinta Gold Productions',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Gold@2024'),
            'role' => 'penjual',
            'phone' => '081234567895',
            'gender' => 'female',
            'birth_date' => '1988-07-25',
            'address' => 'Jl. Thamrin No. 45, Jakarta Pusat',
            'bio' => 'Professional event organizer with Gold badge. Focuses on premium entertainment events.',
            'is_active' => true,
            'total_revenue' => 75000000,
            'total_tickets_sold' => 2100,
            'total_events_created' => 28,
            'badge_level_id' => 3, // Gold
            'badge_expires_at' => $now->copy()->addYear(),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 365,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
            'company_name' => 'Gold Productions',
            'company_address' => 'Jl. Thamrin No. 45, Jakarta Pusat',
            'organization' => 'Gold Productions',
            'website' => 'https://goldproductions.co.id',
        ]);

        // 7. Organizer Silver
        User::create([
            'name' => 'Budi Silver Events',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Silver@2024'),
            'role' => 'penjual',
            'phone' => '081234567896',
            'gender' => 'male',
            'birth_date' => '1992-11-08',
            'address' => 'Jl. Asia Afrika No. 88, Bandung',
            'bio' => 'Growing event organizer with Silver badge. Specializes in community events.',
            'is_active' => true,
            'total_revenue' => 25000000,
            'total_tickets_sold' => 850,
            'total_events_created' => 15,
            'badge_level_id' => 2, // Silver
            'badge_expires_at' => $now->copy()->addMonths(6),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 180,
            'badge_auto_renew' => false,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
            'company_name' => 'Silver Events Bandung',
            'company_address' => 'Jl. Asia Afrika No. 88, Bandung',
            'organization' => 'Silver Events',
            'website' => 'https://silverevents.co.id',
        ]);

        // 8. Organizer Bronze
        User::create([
            'name' => 'Rina Bronze Organizer',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Bronze@2024'),
            'role' => 'penjual',
            'phone' => '081234567897',
            'gender' => 'female',
            'birth_date' => '1995-09-15',
            'address' => 'Jl. Malioboro No. 25, Yogyakarta',
            'bio' => 'New event organizer with Bronze badge. Starting journey in event management.',
            'is_active' => true,
            'total_revenue' => 5000000,
            'total_tickets_sold' => 150,
            'total_events_created' => 3,
            'badge_level_id' => 1, // Bronze
            'badge_expires_at' => $now->copy()->addMonths(3),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 90,
            'badge_auto_renew' => false,
            'email_notifications' => true,
            'push_notifications' => false,
            'sms_notifications' => false,
            'company_name' => 'Bronze Events Yogya',
            'company_address' => 'Jl. Malioboro No. 25, Yogyakarta',
            'organization' => 'Bronze Events',
        ]);

        // 9. Customer Premium
        User::create([
            'name' => 'Diana Premium Customer',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Customer@2024'),
            'role' => 'pembeli',
            'phone' => '081234567898',
            'gender' => 'female',
            'birth_date' => '1990-04-20',
            'address' => 'Jl. Sudirman No. 200, Jakarta Pusat',
            'bio' => 'Premium customer with Gold badge. Frequent event attendee and VIP ticket buyer.',
            'is_active' => true,
            'badge_level_id' => 3, // Gold
            'badge_expires_at' => $now->copy()->addYear(),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 365,
            'badge_auto_renew' => true,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => false,
        ]);

        // 10. Customer Regular
        User::create([
            'name' => 'Eko Regular Customer',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Customer@2024'),
            'role' => 'pembeli',
            'phone' => '081234567899',
            'gender' => 'male',
            'birth_date' => '1993-08-14',
            'address' => 'Jl. Diponegoro No. 75, Surabaya',
            'bio' => 'Regular customer with Silver badge. Enjoys music concerts and cultural events.',
            'is_active' => true,
            'badge_level_id' => 2, // Silver
            'badge_expires_at' => $now->copy()->addMonths(6),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 180,
            'badge_auto_renew' => false,
            'email_notifications' => true,
            'push_notifications' => true,
            'sms_notifications' => true,
        ]);

        // 11. Customer New
        User::create([
            'name' => 'Fitri New Customer',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Customer@2024'),
            'role' => 'pembeli',
            'phone' => '081234567800',
            'gender' => 'female',
            'birth_date' => '1998-12-05',
            'address' => 'Jl. Gajah Mada No. 150, Medan',
            'bio' => 'New customer with Bronze badge. College student exploring events.',
            'is_active' => true,
            'badge_level_id' => 1, // Bronze
            'badge_expires_at' => $now->copy()->addMonths(3),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 90,
            'badge_auto_renew' => false,
            'email_notifications' => true,
            'push_notifications' => false,
            'sms_notifications' => false,
        ]);

        // 12. Test Customer
        User::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'email_verified_at' => $now,
            'password' => Hash::make('Test@2024'),
            'role' => 'pembeli',
            'phone' => '************',
            'gender' => 'male',
            'birth_date' => '1995-06-30',
            'address' => 'Jl. Testing No. 123, Jakarta',
            'bio' => 'Test account for development and testing purposes.',
            'is_active' => true,
            'badge_level_id' => 1, // Bronze
            'badge_expires_at' => $now->copy()->addMonths(1),
            'badge_assigned_at' => $now,
            'badge_duration_days' => 30,
            'badge_auto_renew' => false,
            'email_notifications' => false,
            'push_notifications' => false,
            'sms_notifications' => false,
        ]);

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('✅ Users seeded successfully!');
        $this->command->info('');
        $this->command->info('🔐 LOGIN CREDENTIALS:');
        $this->command->info('');
        $this->command->info('👑 ADMIN ACCOUNTS:');
        $this->command->info('Super Admin: <EMAIL> / SuperAdmin@2024');
        $this->command->info('Admin: <EMAIL> / Admin@2024');
        $this->command->info('');
        $this->command->info('👥 STAFF ACCOUNTS:');
        $this->command->info('Staff 1: <EMAIL> / Staff@2024');
        $this->command->info('Staff 2: <EMAIL> / Staff@2024');
        $this->command->info('');
        $this->command->info('🎪 ORGANIZER ACCOUNTS:');
        $this->command->info('Platinum: <EMAIL> / Platinum@2024 (Custom Templates)');
        $this->command->info('Gold: <EMAIL> / Gold@2024');
        $this->command->info('Silver: <EMAIL> / Silver@2024');
        $this->command->info('Bronze: <EMAIL> / Bronze@2024');
        $this->command->info('');
        $this->command->info('🎫 CUSTOMER ACCOUNTS:');
        $this->command->info('Premium: <EMAIL> / Customer@2024');
        $this->command->info('Regular: <EMAIL> / Customer@2024');
        $this->command->info('New: <EMAIL> / Customer@2024');
        $this->command->info('Test: <EMAIL> / Test@2024');
        $this->command->info('');
        $this->command->info('🏆 BADGE LEVEL:');
        $this->command->info('Bronze (1): Basic access');
        $this->command->info('Silver (2): Enhanced features');
        $this->command->info('Gold (3): Premium features');
        $this->command->info('Platinum (4): Full access + Custom Templates');
        $this->command->info('');
        $this->command->info('🎨 TEMPLATE ACCESS:');
        $this->command->info('Bronze/Silver/Gold: Classic, Unix, Minimal, Pro templates');
        $this->command->info('Platinum: All templates + Custom template editor');
    }
}

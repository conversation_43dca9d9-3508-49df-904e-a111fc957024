<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

try {
    echo "🔍 Checking Badge System Status...\n\n";
    
    // Check users table badge columns
    echo "=== USERS TABLE BADGE COLUMNS ===\n";
    if (Schema::hasTable('users')) {
        $userColumns = Schema::getColumnListing('users');
        $badgeColumns = ['badge_level_id', 'badge_assigned_at', 'badge_expires_at', 'badge_duration_days', 'badge_auto_renew'];
        
        foreach ($badgeColumns as $col) {
            $status = in_array($col, $userColumns) ? '✅ EXISTS' : '❌ MISSING';
            echo "  {$col}: {$status}\n";
        }
    } else {
        echo "❌ Users table does not exist\n";
    }
    
    // Check badge level table
    echo "\n=== BADGE LEVEL TABLE ===\n";
    $badgeTableExists = false;
    if (Schema::hasTable('user_badge_level')) {
        echo "✅ user_badge_level table exists\n";
        $badgeTableExists = true;
        $badgeTableName = 'user_badge_level';
    } elseif (Schema::hasTable('badge_level')) {
        echo "✅ badge_level table exists (needs rename to user_badge_level)\n";
        $badgeTableExists = true;
        $badgeTableName = 'badge_level';
    } else {
        echo "❌ No badge level table found\n";
    }
    
    // Check badge data
    if ($badgeTableExists) {
        echo "\n=== BADGE LEVEL DATA ===\n";
        $badges = DB::table($badgeTableName)->get();
        echo "Found " . $badges->count() . " badge level:\n";
        foreach ($badges as $badge) {
            $default = isset($badge->is_default) && $badge->is_default ? ' (DEFAULT)' : '';
            echo "  - {$badge->name}{$default}\n";
        }
    }
    
    // Check users with badges
    echo "\n=== USERS WITH BADGES ===\n";
    if (Schema::hasTable('users') && in_array('badge_level_id', Schema::getColumnListing('users'))) {
        $usersWithBadges = DB::table('users')->whereNotNull('badge_level_id')->count();
        $totalUsers = DB::table('users')->count();
        echo "Users with badges: {$usersWithBadges} / {$totalUsers}\n";
        
        if ($usersWithBadges > 0) {
            $badgeDistribution = DB::table('users')
                ->select('badge_level_id', DB::raw('count(*) as count'))
                ->whereNotNull('badge_level_id')
                ->groupBy('badge_level_id')
                ->get();
            
            echo "Badge distribution:\n";
            foreach ($badgeDistribution as $dist) {
                echo "  - Badge ID {$dist->badge_level_id}: {$dist->count} users\n";
            }
        }
    } else {
        echo "❌ Cannot check users with badges (badge_level_id column missing)\n";
    }
    
    // Check migration status
    echo "\n=== MIGRATION STATUS ===\n";
    $badgeMigrations = [
        '2025_06_01_222105_create_badge_level_table.php',
        '2024_01_15_000001_add_badge_duration_to_users_table.php',
        '2024_01_15_000002_add_is_default_to_user_badge_level_table.php',
        '2025_06_01_224000_fix_badge_system_migration.php'
    ];
    
    $completedMigrations = DB::table('migrations')->pluck('migration')->toArray();
    
    foreach ($badgeMigrations as $migration) {
        $migrationName = str_replace('.php', '', $migration);
        $status = in_array($migrationName, $completedMigrations) ? '✅ COMPLETED' : '❌ PENDING';
        echo "  {$migration}: {$status}\n";
    }
    
    echo "\n=== RECOMMENDATIONS ===\n";
    
    if (!$badgeTableExists) {
        echo "🔧 Need to create user_badge_level table\n";
    }
    
    if (Schema::hasTable('users') && !in_array('badge_level_id', Schema::getColumnListing('users'))) {
        echo "🔧 Need to add badge columns to users table\n";
    }
    
    if ($badgeTableExists && $badges->count() == 0) {
        echo "🔧 Need to seed badge level data\n";
    }
    
    echo "\n✅ Badge system status check completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

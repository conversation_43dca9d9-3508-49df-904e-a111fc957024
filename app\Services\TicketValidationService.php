<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\User;
use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TicketValidationService
{
    /**
     * Validate ticket by ticket number
     */
    public function validateByTicketNumber(string $ticketNumber, ?User $validator = null): array
    {
        try {
            $ticket = Ticket::where('ticket_number', $ticketNumber)
                ->with(['event', 'buyer', 'order'])
                ->first();

            if (!$ticket) {
                return $this->validationResponse(false, 'Tiket tidak ditemukan', null);
            }

            return $this->performValidation($ticket, $validator);

        } catch (\Exception $e) {
            Log::error('Ticket validation error: ' . $e->getMessage());
            return $this->validationResponse(false, 'Terjadi kesalahan sistem', null);
        }
    }

    /**
     * Validate ticket by QR code
     */
    public function validateByQRCode(string $qrCode, ?User $validator = null): array
    {
        try {
            $ticket = Ticket::where('qr_code', $qrCode)
                ->with(['event', 'buyer', 'order'])
                ->first();

            if (!$ticket) {
                return $this->validationResponse(false, 'QR Code tidak valid', null);
            }

            return $this->performValidation($ticket, $validator);

        } catch (\Exception $e) {
            Log::error('QR Code validation error: ' . $e->getMessage());
            return $this->validationResponse(false, 'Terjadi kesalahan sistem', null);
        }
    }

    /**
     * Perform actual ticket validation
     */
    private function performValidation(Ticket $ticket, ?User $validator = null): array
    {
        // Check authenticity first
        $isAuthentic = $ticket->verifyAuthenticity();
        if (!$isAuthentic) {
            return $this->validationResponse(
                false,
                'Boarding E-Tiket PALSU! Tidak terdaftar di sistem TiXara',
                $ticket,
                'fake'
            );
        }

        // Check ticket status
        if ($ticket->status === 'used' || $ticket->used_at) {
            return $this->validationResponse(
                false,
                'Boarding E-Tiket telah digunakan! Gagal Valid. Digunakan pada: ' . $ticket->used_at->format('d M Y H:i'),
                $ticket,
                'already_used'
            );
        }

        if ($ticket->status === 'cancelled') {
            return $this->validationResponse(
                false,
                'Tiket telah dibatalkan',
                $ticket,
                'cancelled'
            );
        }

        if ($ticket->status === 'refunded') {
            return $this->validationResponse(
                false,
                'Tiket telah di-refund',
                $ticket,
                'refunded'
            );
        }

        // Check event validity
        $eventValidation = $this->validateEvent($ticket->event);
        if (!$eventValidation['valid']) {
            return $this->validationResponse(
                false,
                $eventValidation['message'],
                $ticket,
                $eventValidation['code']
            );
        }

        // All validations passed, mark ticket as used
        $ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => $validator?->id,
            'validation_notes' => 'Validated via ' . ($validator ? 'staff' : 'system'),
        ]);

        // Log validation
        $this->logValidation($ticket, $validator);

        return $this->validationResponse(
            true,
            'Boarding E-Tiket Berhasil Valid! Selamat menikmati event',
            $ticket->fresh(),
            'success'
        );
    }

    /**
     * Validate event conditions
     */
    private function validateEvent(Event $event): array
    {
        $now = now();

        // Check if event is published
        if ($event->status !== 'published') {
            return [
                'valid' => false,
                'message' => 'Event tidak aktif',
                'code' => 'event_inactive'
            ];
        }

        // Check if event has started (allow entry 1 hour before start)
        $allowedEntryTime = $event->start_date->subHour();
        if ($now < $allowedEntryTime) {
            return [
                'valid' => false,
                'message' => 'Event belum dimulai. Pintu masuk dibuka 1 jam sebelum acara.',
                'code' => 'event_not_started'
            ];
        }

        // Check if event has ended (allow entry until 2 hours after start)
        $lateEntryLimit = $event->start_date->addHours(2);
        if ($now > $event->end_date || $now > $lateEntryLimit) {
            return [
                'valid' => false,
                'message' => 'Event sudah berakhir atau waktu masuk sudah habis',
                'code' => 'event_ended'
            ];
        }

        return [
            'valid' => true,
            'message' => 'Event valid',
            'code' => 'valid'
        ];
    }

    /**
     * Create validation response
     */
    private function validationResponse(bool $success, string $message, ?Ticket $ticket = null, string $code = null): array
    {
        $response = [
            'success' => $success,
            'message' => $message,
            'ticket' => $ticket,
            'timestamp' => now()->toISOString(),
        ];

        if ($code) {
            $response['code'] = $code;
        }

        if ($ticket) {
            $response['event'] = $ticket->event;
            $response['buyer'] = $ticket->buyer;
        }

        return $response;
    }

    /**
     * Log validation activity
     */
    private function logValidation(Ticket $ticket, ?User $validator = null): void
    {
        Log::info('Ticket validated', [
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'event_id' => $ticket->event_id,
            'event_title' => $ticket->event->title,
            'buyer_id' => $ticket->buyer_id,
            'buyer_name' => $ticket->buyer->name,
            'validator_id' => $validator?->id,
            'validator_name' => $validator?->name,
            'validated_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get validation statistics for an event
     */
    public function getEventValidationStats(Event $event): array
    {
        $tickets = $event->tickets();

        return [
            'total_tickets' => $tickets->count(),
            'validated_tickets' => $tickets->where('status', 'used')->count(),
            'pending_tickets' => $tickets->where('status', 'active')->count(),
            'cancelled_tickets' => $tickets->where('status', 'cancelled')->count(),
            'validation_rate' => $tickets->count() > 0 ?
                ($tickets->where('status', 'used')->count() / $tickets->count()) * 100 : 0,
            'last_validation' => $tickets->where('status', 'used')
                ->orderBy('used_at', 'desc')
                ->first()?->used_at,
        ];
    }

    /**
     * Get validator statistics
     */
    public function getValidatorStats(User $validator, ?Carbon $startDate = null, ?Carbon $endDate = null): array
    {
        $query = Ticket::where('validated_by', $validator->id);

        if ($startDate) {
            $query->where('used_at', '>=', $startDate);
        }

        if ($endDate) {
            $query->where('used_at', '<=', $endDate);
        }

        $tickets = $query->with(['event'])->get();

        return [
            'total_validations' => $tickets->count(),
            'events_validated' => $tickets->pluck('event_id')->unique()->count(),
            'validation_period' => [
                'start' => $startDate?->toDateString(),
                'end' => $endDate?->toDateString(),
            ],
            'validations_by_event' => $tickets->groupBy('event.title')
                ->map(function ($eventTickets) {
                    return $eventTickets->count();
                }),
        ];
    }

    /**
     * Check ticket authenticity without marking as used
     */
    public function checkAuthenticity(string $identifier): array
    {
        try {
            // Find ticket by QR code, ticket number, or boarding pass ID
            $ticket = Ticket::where('qr_code', $identifier)
                           ->orWhere('ticket_number', $identifier)
                           ->orWhere('boarding_pass_id', $identifier)
                           ->with(['event', 'buyer'])
                           ->first();

            if (!$ticket) {
                return [
                    'success' => false,
                    'message' => 'ID Boarding E-Tiket tidak ditemukan di database TiXara',
                    'status' => 'not_found',
                    'is_authentic' => false,
                    'data' => null
                ];
            }

            // Verify authenticity
            $isAuthentic = $ticket->verifyAuthenticity();

            if (!$isAuthentic) {
                return [
                    'success' => false,
                    'message' => 'Boarding E-Tiket PALSU! Tidak terdaftar di sistem TiXara',
                    'status' => 'fake',
                    'is_authentic' => false,
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'checked_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Return authentic ticket info
            return [
                'success' => true,
                'message' => 'Boarding E-Tiket AUTHENTIC! Terdaftar di sistem TiXara',
                'status' => 'authentic',
                'is_authentic' => true,
                'data' => [
                    'ticket_number' => $ticket->ticket_number,
                    'boarding_pass_id' => $ticket->boarding_pass_id,
                    'template_used' => $ticket->template_used ?? 'unix',
                    'event_title' => $ticket->event->title,
                    'attendee_name' => $ticket->attendee_name,
                    'attendee_email' => $ticket->attendee_email,
                    'venue_name' => $ticket->event->venue_name,
                    'event_date' => Carbon::parse($ticket->event->start_date)->format('d M Y H:i'),
                    'ticket_status' => $ticket->getStatusMessage(),
                    'is_used' => $ticket->status === 'used' || $ticket->used_at !== null,
                    'used_at' => $ticket->used_at?->format('d M Y H:i:s'),
                    'created_at' => $ticket->created_at->format('d M Y H:i:s'),
                    'generated_at' => $ticket->generated_at?->format('d M Y H:i:s'),
                    'checked_at' => now()->format('d M Y H:i:s'),
                    'buyer_name' => $ticket->buyer->name,
                    'buyer_email' => $ticket->buyer->email,
                    'download_count' => $ticket->download_count ?? 0,
                    'print_count' => $ticket->print_count ?? 0
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Authenticity check error', [
                'identifier' => $identifier,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengecek autentisitas tiket',
                'status' => 'error',
                'is_authentic' => false,
                'data' => null
            ];
        }
    }

    /**
     * Staff scan ticket (check validity without marking as used)
     */
    public function staffScanTicket(string $identifier, User $staff): array
    {
        try {
            // Find ticket by QR code, ticket number, or boarding pass ID
            $ticket = Ticket::where('qr_code', $identifier)
                           ->orWhere('ticket_number', $identifier)
                           ->orWhere('boarding_pass_id', $identifier)
                           ->with(['event', 'buyer', 'event.organizer'])
                           ->first();

            if (!$ticket) {
                return [
                    'success' => false,
                    'message' => 'Tiket tidak ditemukan',
                    'status' => 'not_found',
                    'icon' => 'exclamation-triangle',
                    'color' => 'yellow',
                    'data' => null
                ];
            }

            // Check if staff is assigned to this event's organizer
            if (!$this->isStaffAssignedToEvent($staff, $ticket->event)) {
                return [
                    'success' => false,
                    'message' => 'Tiket tidak bisa digunakan karena bukan event Anda',
                    'status' => 'not_assigned',
                    'icon' => 'exclamation-triangle',
                    'color' => 'yellow',
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'boarding_pass_id' => $ticket->boarding_pass_id,
                        'event_title' => $ticket->event->title,
                        'organizer_name' => $ticket->event->organizer->name,
                        'scanned_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Check authenticity
            $isAuthentic = $ticket->verifyAuthenticity();
            if (!$isAuthentic) {
                return [
                    'success' => false,
                    'message' => 'Tiket PALSU! Tidak terdaftar di sistem',
                    'status' => 'fake',
                    'icon' => 'times',
                    'color' => 'red',
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'scanned_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Check if already used
            if ($ticket->status === 'used' || $ticket->used_at) {
                return [
                    'success' => false,
                    'message' => 'Tiket sudah digunakan',
                    'status' => 'already_used',
                    'icon' => 'times',
                    'color' => 'red',
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'boarding_pass_id' => $ticket->boarding_pass_id,
                        'event_title' => $ticket->event->title,
                        'attendee_name' => $ticket->attendee_name,
                        'used_at' => $ticket->used_at?->format('d M Y H:i:s'),
                        'scanned_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Check other statuses
            if ($ticket->status === 'cancelled') {
                return [
                    'success' => false,
                    'message' => 'Tiket telah dibatalkan',
                    'status' => 'cancelled',
                    'icon' => 'times',
                    'color' => 'red',
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'boarding_pass_id' => $ticket->boarding_pass_id,
                        'scanned_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Check event validity
            $eventValidation = $this->validateEvent($ticket->event);
            if (!$eventValidation['valid']) {
                return [
                    'success' => false,
                    'message' => $eventValidation['message'],
                    'status' => $eventValidation['code'],
                    'icon' => 'exclamation-triangle',
                    'color' => 'yellow',
                    'data' => [
                        'ticket_number' => $ticket->ticket_number,
                        'boarding_pass_id' => $ticket->boarding_pass_id,
                        'event_title' => $ticket->event->title,
                        'scanned_at' => now()->format('d M Y H:i:s')
                    ]
                ];
            }

            // Ticket is valid and ready to use
            return [
                'success' => true,
                'message' => 'Tiket valid dan siap digunakan',
                'status' => 'ready_to_use',
                'icon' => 'check',
                'color' => 'green',
                'data' => [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'boarding_pass_id' => $ticket->boarding_pass_id,
                    'template_used' => $ticket->template_used ?? 'unix',
                    'event_title' => $ticket->event->title,
                    'attendee_name' => $ticket->attendee_name,
                    'attendee_email' => $ticket->attendee_email,
                    'venue_name' => $ticket->event->venue_name,
                    'event_date' => Carbon::parse($ticket->event->start_date)->format('d M Y H:i'),
                    'buyer_name' => $ticket->buyer->name,
                    'buyer_email' => $ticket->buyer->email,
                    'scanned_at' => now()->format('d M Y H:i:s'),
                    'can_use' => true
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Staff scan error', [
                'identifier' => $identifier,
                'staff_id' => $staff->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Terjadi kesalahan sistem',
                'status' => 'error',
                'icon' => 'exclamation-triangle',
                'color' => 'red',
                'data' => null
            ];
        }
    }

    /**
     * Staff use ticket (mark as used after scan)
     */
    public function staffUseTicket(int $ticketId, User $staff): array
    {
        try {
            $ticket = Ticket::with(['event', 'buyer', 'event.organizer'])->find($ticketId);

            if (!$ticket) {
                return [
                    'success' => false,
                    'message' => 'Tiket tidak ditemukan'
                ];
            }

            // Check if staff is assigned to this event's organizer
            if (!$this->isStaffAssignedToEvent($staff, $ticket->event)) {
                return [
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk menggunakan tiket ini'
                ];
            }

            // Check if already used
            if ($ticket->status === 'used' || $ticket->used_at) {
                return [
                    'success' => false,
                    'message' => 'Tiket sudah digunakan sebelumnya'
                ];
            }

            // Mark ticket as used
            $ticket->update([
                'status' => 'used',
                'used_at' => now(),
                'validated_by' => $staff->id,
                'validation_notes' => 'Used by staff: ' . $staff->name,
            ]);

            // Log usage
            $this->logValidation($ticket, $staff);

            return [
                'success' => true,
                'message' => 'Tiket berhasil digunakan',
                'data' => [
                    'ticket_number' => $ticket->ticket_number,
                    'boarding_pass_id' => $ticket->boarding_pass_id,
                    'attendee_name' => $ticket->attendee_name,
                    'event_title' => $ticket->event->title,
                    'used_at' => $ticket->used_at->format('d M Y H:i:s'),
                    'validated_by' => $staff->name
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Staff use ticket error', [
                'ticket_id' => $ticketId,
                'staff_id' => $staff->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat menggunakan tiket'
            ];
        }
    }

    /**
     * Check if staff is assigned to event's organizer
     */
    private function isStaffAssignedToEvent(User $staff, Event $event): bool
    {
        // If user is admin, allow access to all events
        if ($staff->role === 'admin') {
            return true;
        }

        // If user is not staff, deny access
        if ($staff->role !== 'staff') {
            return false;
        }

        // Check if staff is assigned to this event's organizer
        return \DB::table('staff_organizer_assignments')
            ->where('staff_id', $staff->id)
            ->where('organizer_id', $event->organizer_id)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Get assigned organizers for staff
     */
    public function getStaffAssignedOrganizers(User $staff): array
    {
        if ($staff->role !== 'staff') {
            return [];
        }

        $assignments = \DB::table('staff_organizer_assignments')
            ->join('users', 'staff_organizer_assignments.organizer_id', '=', 'users.id')
            ->where('staff_organizer_assignments.staff_id', $staff->id)
            ->where('staff_organizer_assignments.is_active', true)
            ->select('users.id', 'users.name', 'users.email', 'staff_organizer_assignments.assigned_at')
            ->get();

        return $assignments->toArray();
    }

    /**
     * Get events that staff can validate
     */
    public function getStaffValidatableEvents(User $staff): array
    {
        if ($staff->role === 'admin') {
            // Admin can validate all events
            return Event::with('organizer')
                ->where('status', 'published')
                ->orderBy('start_date', 'desc')
                ->get()
                ->toArray();
        }

        if ($staff->role !== 'staff') {
            return [];
        }

        // Get organizer IDs that staff is assigned to
        $organizerIds = \DB::table('staff_organizer_assignments')
            ->where('staff_id', $staff->id)
            ->where('is_active', true)
            ->pluck('organizer_id');

        if ($organizerIds->isEmpty()) {
            return [];
        }

        return Event::with('organizer')
            ->whereIn('organizer_id', $organizerIds)
            ->where('status', 'published')
            ->orderBy('start_date', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Bulk validate tickets (for testing or admin purposes)
     */
    public function bulkValidate(array $ticketNumbers, User $validator): array
    {
        $results = [];

        foreach ($ticketNumbers as $ticketNumber) {
            $results[$ticketNumber] = $this->validateByTicketNumber($ticketNumber, $validator);
        }

        return [
            'total_processed' => count($ticketNumbers),
            'successful_validations' => collect($results)->where('success', true)->count(),
            'failed_validations' => collect($results)->where('success', false)->count(),
            'results' => $results,
        ];
    }
}

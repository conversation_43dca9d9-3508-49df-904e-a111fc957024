<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            color: #92400e;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 950px;
            width: 100%;
            background: white;
            border-radius: 0;
            overflow: hidden;
            box-shadow: 
                0 25px 50px -12px rgba(217, 119, 6, 0.25),
                0 0 0 1px #d97706,
                0 0 0 3px #f59e0b,
                0 0 0 5px #fbbf24;
            position: relative;
        }

        .vip-crown {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: #92400e;
            padding: 8px 24px;
            border-radius: 0 0 12px 12px;
            font-size: 12px;
            font-weight: 800;
            letter-spacing: 2px;
            text-transform: uppercase;
            z-index: 10;
            box-shadow: 0 4px 6px -1px rgba(217, 119, 6, 0.3);
        }

        .vip-header {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            color: #fffbeb;
            padding: 40px;
            text-align: center;
            position: relative;
            border-bottom: 4px solid #f59e0b;
        }

        .vip-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="50,10 60,35 85,35 65,55 75,80 50,65 25,80 35,55 15,35 40,35" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 40px 40px;
            opacity: 0.3;
            animation: star-twinkle 4s ease-in-out infinite;
        }

        @keyframes star-twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-size: 42px;
            font-weight: 900;
            margin-bottom: 8px;
            letter-spacing: 4px;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-subtitle {
            font-size: 16px;
            font-weight: 300;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .vip-ornament {
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, transparent, #fbbf24, transparent);
            margin: 16px auto;
            position: relative;
        }

        .vip-ornament::before,
        .vip-ornament::after {
            content: '★';
            position: absolute;
            color: #fbbf24;
            font-size: 16px;
            top: -6px;
        }

        .vip-ornament::before { left: -20px; }
        .vip-ornament::after { right: -20px; }

        .boarding-body {
            padding: 48px;
            background: linear-gradient(135deg, #fefefe 0%, #fffbeb 100%);
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 48px;
            padding: 40px;
            background: white;
            border: 3px solid #f59e0b;
            border-radius: 20px;
            position: relative;
            box-shadow: 
                0 10px 15px -3px rgba(217, 119, 6, 0.1),
                inset 0 0 0 1px #fbbf24;
        }

        .event-showcase::before {
            content: '';
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 1px solid #fde68a;
            border-radius: 16px;
        }

        .event-title {
            font-size: 36px;
            font-weight: 800;
            color: #92400e;
            margin-bottom: 16px;
            line-height: 1.2;
            position: relative;
            z-index: 2;
            text-shadow: 1px 1px 2px rgba(217, 119, 6, 0.2);
        }

        .event-category {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #d97706, #f59e0b);
            color: white;
            padding: 12px 32px;
            border-radius: 30px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
            position: relative;
            z-index: 2;
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 48px;
            align-items: start;
        }

        .details-panel {
            background: white;
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 40px;
            position: relative;
            box-shadow: 0 10px 15px -3px rgba(217, 119, 6, 0.1);
        }

        .details-panel::before {
            content: '';
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 1px solid #fde68a;
            border-radius: 16px;
        }

        .panel-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 32px;
            padding-bottom: 20px;
            border-bottom: 2px solid #fde68a;
            position: relative;
            z-index: 2;
        }

        .panel-icon {
            font-size: 24px;
            color: #d97706;
        }

        .panel-title {
            font-size: 22px;
            font-weight: 800;
            color: #d97706;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .details-grid {
            display: grid;
            gap: 24px;
            position: relative;
            z-index: 2;
        }

        .detail-pair {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .detail-item {
            background: #fffbeb;
            border: 2px solid #fbbf24;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .detail-item::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            right: 6px;
            bottom: 6px;
            border: 1px solid #fde68a;
            border-radius: 12px;
        }

        .detail-item:hover {
            background: #fef3c7;
            border-color: #f59e0b;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(217, 119, 6, 0.2);
        }

        .detail-label {
            font-size: 11px;
            font-weight: 800;
            color: #d97706;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #92400e;
            line-height: 1.3;
            position: relative;
            z-index: 2;
        }

        .detail-value.highlight {
            font-size: 20px;
            font-weight: 800;
            color: #d97706;
            text-shadow: 1px 1px 2px rgba(217, 119, 6, 0.2);
        }

        .qr-panel {
            background: white;
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            position: relative;
            box-shadow: 0 10px 15px -3px rgba(217, 119, 6, 0.1);
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 1px solid #fde68a;
            border-radius: 16px;
        }

        .qr-header {
            margin-bottom: 24px;
            position: relative;
            z-index: 2;
        }

        .qr-title {
            font-size: 20px;
            font-weight: 800;
            color: #d97706;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .qr-subtitle {
            font-size: 12px;
            color: #f59e0b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .qr-code {
            width: 160px;
            height: 160px;
            background: linear-gradient(135deg, #92400e, #d97706);
            border: 4px solid #f59e0b;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fffbeb;
            font-size: 14px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 24px;
            position: relative;
            z-index: 2;
            box-shadow: 
                0 8px 25px rgba(217, 119, 6, 0.4),
                inset 0 0 0 2px #fbbf24;
        }

        .qr-code::before {
            content: '👑';
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 20px;
            animation: crown-glow 2s ease-in-out infinite;
        }

        @keyframes crown-glow {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .boarding-id {
            background: #fffbeb;
            border: 3px solid #f59e0b;
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            font-weight: 800;
            color: #d97706;
            font-family: 'Montserrat', sans-serif;
            margin-top: 20px;
            position: relative;
            z-index: 2;
            letter-spacing: 1px;
            box-shadow: 0 4px 6px -1px rgba(217, 119, 6, 0.2);
        }

        .boarding-id::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px solid #fde68a;
            border-radius: 8px;
        }

        .vip-footer {
            background: linear-gradient(135deg, #fffbeb, #fef3c7);
            padding: 32px 48px;
            border-top: 4px solid #f59e0b;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 24px;
            align-items: center;
        }

        .footer-item {
            text-align: center;
            background: white;
            padding: 16px;
            border-radius: 12px;
            border: 2px solid #fbbf24;
            position: relative;
        }

        .footer-item::before {
            content: '';
            position: absolute;
            top: 4px;
            left: 4px;
            right: 4px;
            bottom: 4px;
            border: 1px solid #fde68a;
            border-radius: 8px;
        }

        .footer-label {
            font-size: 10px;
            color: #d97706;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 800;
            margin-bottom: 4px;
            position: relative;
            z-index: 2;
        }

        .footer-value {
            font-size: 13px;
            font-weight: 600;
            color: #92400e;
            position: relative;
            z-index: 2;
        }

        .tixara-vip {
            font-size: 24px;
            font-weight: 900;
            color: #d97706;
            letter-spacing: 2px;
            text-align: center;
            grid-column: 1 / -1;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 2px solid #fbbf24;
            text-transform: uppercase;
            text-shadow: 2px 2px 4px rgba(217, 119, 6, 0.2);
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 32px;
            }
            
            .detail-pair {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 32px 24px;
            }
            
            .vip-footer {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
                padding: 24px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 3px solid #000;
            }
            
            * {
                animation: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="vip-crown">👑 VIP EXCLUSIVE 👑</div>
        
        <div class="vip-header">
            <div class="header-content">
                <div class="header-title">VIP</div>
                <div class="vip-ornament"></div>
                <div class="header-subtitle">Exclusive Premium Boarding Pass</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">
                    ⭐ {{ $ticket->event->category->name ?? 'VIP Event' }}
                </div>
            </div>

            <div class="boarding-content">
                <div class="details-panel">
                    <div class="panel-header">
                        <div class="panel-icon">👑</div>
                        <div class="panel-title">VIP Details</div>
                    </div>
                    
                    <div class="details-grid">
                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Event Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('l, d F Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Event Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Exclusive Venue</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">VIP Guest</div>
                            <div class="detail-value highlight">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">VIP Ticket</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">VIP Section</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'VIP Lounge' }}</div>
                            </div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Order Reference</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">VIP Investment</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-header">
                        <div class="qr-title">VIP Access</div>
                        <div class="qr-subtitle">Exclusive Entry Code</div>
                    </div>
                    
                    <div class="qr-code">
                        VIP<br>
                        EXCLUSIVE<br>
                        ACCESS
                    </div>
                    
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-VIP-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="vip-footer">
            <div class="footer-item">
                <div class="footer-label">Issued</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y') ?? now()->format('d M Y') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Time</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('H:i') ?? now()->format('H:i') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Access Level</div>
                <div class="footer-value">VIP Premium</div>
            </div>
            <div class="footer-item">
                <div class="footer-label">Priority</div>
                <div class="footer-value">Highest ⭐</div>
            </div>
            <div class="tixara-vip">TiXara VIP Exclusive</div>
        </div>
    </div>
</body>
</html>

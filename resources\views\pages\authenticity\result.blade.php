@extends('layouts.app')

@section('title', 'Hasil Verifikasi Tiket')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-shield-alt mr-3 text-green-600"></i>
                Hasil Verifikasi Tiket
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Hasil pemeriksaan keaslian tiket TiXara</p>
        </div>

        <!-- Result Card -->
        <div class="max-w-4xl mx-auto">
            @if($result['success'] && $result['is_authentic'])
                <!-- Authentic Ticket -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-green-50 dark:bg-green-900 p-6 border-l-4 border-green-500">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shield-check text-4xl text-green-600 mr-4"></i>
                            <div>
                                <h3 class="text-3xl font-bold text-gray-900 dark:text-white">TIKET AUTHENTIC</h3>
                                <p class="text-green-600 font-medium text-lg">{{ $result['message'] }}</p>
                            </div>
                        </div>
                    </div>
                    
                    @if(isset($result['data']))
                        <div class="p-6">
                            <h4 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Detail Tiket</h4>
                            
                            <!-- Ticket Details Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Nomor Tiket</p>
                                        <p class="font-semibold text-gray-900 dark:text-white text-lg">{{ $result['data']['ticket_number'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Event</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['event_title'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Venue</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['venue_name'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Tanggal Event</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['event_date'] }}</p>
                                    </div>
                                </div>
                                
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Nama Peserta</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['attendee_name'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Email Peserta</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['attendee_email'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Pembeli</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['buyer_name'] }}</p>
                                    </div>
                                    
                                    <div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">Status Tiket</p>
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                            @if($result['data']['is_used']) bg-orange-100 text-orange-800
                                            @else bg-green-100 text-green-800 @endif">
                                            @if($result['data']['is_used'])
                                                <i class="fas fa-check-circle mr-1"></i>Sudah Digunakan
                                            @else
                                                <i class="fas fa-clock mr-1"></i>Belum Digunakan
                                            @endif
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Usage Information -->
                            @if($result['data']['is_used'] && isset($result['data']['used_at']))
                                <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mb-6">
                                    <h5 class="font-semibold text-orange-800 dark:text-orange-200 mb-2">
                                        <i class="fas fa-info-circle mr-2"></i>Informasi Penggunaan
                                    </h5>
                                    <p class="text-orange-700 dark:text-orange-300">
                                        Tiket ini telah digunakan pada: <strong>{{ $result['data']['used_at'] }}</strong>
                                    </p>
                                </div>
                            @endif
                            
                            <!-- Timestamps -->
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                                <h5 class="font-semibold text-gray-900 dark:text-white mb-3">Informasi Waktu</h5>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <p class="text-gray-600 dark:text-gray-400">Dibuat Pada</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['created_at'] }}</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-600 dark:text-gray-400">Dicek Pada</p>
                                        <p class="font-semibold text-gray-900 dark:text-white">{{ $result['data']['checked_at'] }}</p>
                                    </div>
                                    @if(isset($result['data']['used_at']))
                                        <div>
                                            <p class="text-gray-600 dark:text-gray-400">Digunakan Pada</p>
                                            <p class="font-semibold text-orange-600">{{ $result['data']['used_at'] }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Usage Statistics -->
                            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                                <h5 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">
                                    <i class="fas fa-chart-bar mr-2"></i>Statistik Penggunaan
                                </h5>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-blue-700 dark:text-blue-300">Download</p>
                                        <p class="font-semibold text-blue-900 dark:text-blue-100">{{ $result['data']['download_count'] ?? 0 }} kali</p>
                                    </div>
                                    <div>
                                        <p class="text-blue-700 dark:text-blue-300">Print</p>
                                        <p class="font-semibold text-blue-900 dark:text-blue-100">{{ $result['data']['print_count'] ?? 0 }} kali</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            @else
                <!-- Invalid/Fake Ticket -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                    <div class="bg-red-50 dark:bg-red-900 p-6 border-l-4 border-red-500">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-shield-times text-4xl text-red-600 mr-4"></i>
                            <div>
                                <h3 class="text-3xl font-bold text-gray-900 dark:text-white">TIKET TIDAK VALID</h3>
                                <p class="text-red-600 font-medium text-lg">{{ $result['message'] }}</p>
                            </div>
                        </div>
                        
                        <div class="bg-red-100 dark:bg-red-800 border border-red-300 dark:border-red-600 rounded-lg p-4 mt-4">
                            <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Peringatan
                            </h4>
                            <ul class="text-red-700 dark:text-red-300 text-sm space-y-1">
                                <li>• Tiket ini tidak ditemukan dalam database TiXara</li>
                                <li>• Kemungkinan tiket palsu atau sudah tidak berlaku</li>
                                <li>• Jangan menerima tiket ini sebagai tiket yang sah</li>
                                <li>• Laporkan jika Anda menduga ada penipuan</li>
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Action Buttons -->
        <div class="max-w-4xl mx-auto mt-8 text-center space-x-4">
            <a href="{{ route('tickets.authenticity-check') }}" 
               class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-search mr-2"></i>
                Cek Tiket Lain
            </a>
            
            <a href="{{ route('home') }}" 
               class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-home mr-2"></i>
                Kembali ke Beranda
            </a>
        </div>

        <!-- Security Notice -->
        <div class="max-w-4xl mx-auto mt-8">
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-yellow-600 dark:text-yellow-400 text-xl mr-3 mt-0.5"></i>
                    <div>
                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Catatan Keamanan</h4>
                        <p class="text-yellow-700 dark:text-yellow-300 text-sm">
                            Verifikasi ini hanya mengecek keaslian tiket dan tidak mengubah status tiket. 
                            Untuk validasi resmi saat masuk event, gunakan sistem validasi khusus yang disediakan oleh penyelenggara.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

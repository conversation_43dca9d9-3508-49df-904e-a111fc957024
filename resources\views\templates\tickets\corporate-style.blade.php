@php
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#1E40AF';
    $secondaryColor = $config['secondary_color'] ?? '#E5E7EB';
    $backgroundColor = $config['background_color'] ?? '#ffffff';
    $textColor = $config['text_color'] ?? '#1F2937';
@endphp

<div class="ticket-container corporate-style" style="
    background: {{ $backgroundColor }};
    color: {{ $textColor }};
    font-family: 'Inter', sans-serif;
    width: 800px;
    height: 300px;
    position: relative;
    border: 2px solid {{ $secondaryColor }};
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
">
    <!-- Header -->
    <div style="
        background: {{ $primaryColor }};
        color: white;
        padding: 20px 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 3px solid {{ $secondaryColor }};
    ">
        <div>
            <h1 style="
                font-size: 24px;
                font-weight: 600;
                margin: 0 0 4px 0;
                letter-spacing: 0.5px;
            ">{{ $ticket['event']['title'] ?? 'Corporate Event' }}</h1>
            <p style="
                font-size: 14px;
                margin: 0;
                opacity: 0.9;
                font-weight: 500;
            ">PROFESSIONAL EVENT TICKET</p>
        </div>
        
        <div style="
            background: white;
            color: {{ $primaryColor }};
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        ">
            ADMIT ONE
        </div>
    </div>
    
    <!-- Content -->
    <div style="
        padding: 24px 32px;
        display: flex;
        gap: 32px;
        height: calc(100% - 140px);
    ">
        <!-- Left Section -->
        <div style="flex: 2;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 20px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Event Date</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('M j, Y') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('l, g:i A') }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Venue</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['event']['venue_name'] ?? 'Conference Center' }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ $ticket['event']['city'] ?? 'Location TBA' }}</p>
                </div>
            </div>
            
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 20px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Attendee</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['attendee_name'] ?? $ticket['buyer']['name'] ?? 'Professional' }}</p>
                    <p style="
                        font-size: 13px;
                        margin: 0;
                        color: #6B7280;
                    ">{{ $ticket['attendee_email'] ?? $ticket['buyer']['email'] ?? 'N/A' }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Registration Fee</h3>
                    <p style="
                        font-size: 18px;
                        font-weight: 700;
                        margin: 0;
                        color: {{ $primaryColor }};
                    ">Rp {{ number_format($ticket['price'] ?? 0, 0, ',', '.') }}</p>
                </div>
            </div>
            
            <!-- Ticket Number -->
            <div style="
                background: {{ $secondaryColor }};
                padding: 16px;
                border-radius: 6px;
                border-left: 4px solid {{ $primaryColor }};
            ">
                <h3 style="
                    font-size: 12px;
                    font-weight: 600;
                    color: {{ $primaryColor }};
                    margin: 0 0 8px 0;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                ">Registration Number</h3>
                <p style="
                    font-size: 16px;
                    font-weight: 700;
                    font-family: 'Courier New', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                    letter-spacing: 1px;
                ">{{ $ticket['ticket_number'] ?? 'CORP-000000' }}</p>
            </div>
        </div>
        
        <!-- Right Section - QR Code -->
        <div style="
            width: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 2px solid {{ $secondaryColor }};
            padding-left: 32px;
        ">
            <div style="
                background: white;
                padding: 16px;
                border-radius: 8px;
                border: 2px solid {{ $primaryColor }};
                margin-bottom: 16px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            ">
                {!! QrCode::size(120)->generate($ticket['qr_code'] ?? $ticket['ticket_number'] ?? 'INVALID') !!}
            </div>
            
            <div style="text-align: center;">
                <p style="
                    font-size: 12px;
                    font-weight: 600;
                    color: {{ $primaryColor }};
                    margin: 0 0 4px 0;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                ">Check-in Code</p>
                <p style="
                    font-size: 11px;
                    color: #6B7280;
                    margin: 0;
                    line-height: 1.4;
                ">Present this QR code<br>for event registration</p>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: {{ $secondaryColor }};
        padding: 12px 32px;
        border-top: 2px solid {{ $primaryColor }};
        display: flex;
        justify-content: space-between;
        align-items: center;
    ">
        <p style="
            font-size: 11px;
            color: #6B7280;
            margin: 0;
            font-weight: 500;
        ">This ticket is non-transferable and valid for single entry only</p>
        
        <div style="display: flex; align-items: center; gap: 16px;">
            <p style="
                font-size: 11px;
                color: #6B7280;
                margin: 0;
                font-weight: 500;
            ">Powered by Tixara</p>
            
            <div style="
                background: {{ $primaryColor }};
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            ">
                PROFESSIONAL
            </div>
        </div>
    </div>
    
    <!-- Company Logo Placeholder -->
    <div style="
        position: absolute;
        top: 24px;
        right: 32px;
        width: 60px;
        height: 60px;
        background: {{ $secondaryColor }};
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid {{ $primaryColor }};
    ">
        <i class="fas fa-building" style="color: {{ $primaryColor }}; font-size: 24px;"></i>
    </div>
    
    <!-- Professional Badge -->
    <div style="
        position: absolute;
        top: 100px;
        right: 32px;
        background: {{ $primaryColor }};
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    ">
        BUSINESS
    </div>
    
    <!-- Decorative Elements -->
    <div style="
        position: absolute;
        bottom: 60px;
        left: 32px;
        width: 100px;
        height: 2px;
        background: linear-gradient(90deg, {{ $primaryColor }} 0%, transparent 100%);
    "></div>
    
    <div style="
        position: absolute;
        bottom: 55px;
        left: 32px;
        width: 80px;
        height: 2px;
        background: linear-gradient(90deg, {{ $secondaryColor }} 0%, transparent 100%);
    "></div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

.ticket-container {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
}

@media print {
    .ticket-container {
        width: 800px !important;
        height: 300px !important;
        margin: 0 !important;
        border: 2px solid {{ $secondaryColor }} !important;
        box-shadow: none !important;
    }
}
</style>

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="theme-color" content="#4f46e5">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @auth
        <meta name="user-authenticated" content="true">
    @endauth

    <!-- PWA Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="TiXara">

    <title>{{ config('app.name', 'TiXara') }}</title>

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles

    <!-- Theme Manager -->
    @vite(['resources/js/theme-manager.js'])

    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
</head>
<body class="font-sans antialiased bg-secondary min-h-screen">
    <div id="app">
        <!-- Header Navigation -->
        @include('layouts.partials.header')

        <!-- Main Content -->
        <main class="pb-20 md:pb-0"> <!-- pb-20 untuk memberikan ruang untuk floating footer -->
            @yield('content')
        </main>

        <!-- Floating Footer Navigation (Mobile Only) -->
        @include('layouts.footer-floating')
    </div>

    <!-- Scripts -->
    @livewireScripts

    <!-- AOS Init -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 800,
            once: true
        });
    </script>

    <!-- Live Notifications -->
    @auth
        @vite(['resources/js/live-notifications.js'])
        <script>
            // Helper functions for notification management
            async function markAllNotificationsAsRead() {
                try {
                    const response = await fetch('/notifications/mark-all-read', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                        }
                    });

                    if (response.ok) {
                        // Update badge
                        const badge = document.querySelector('.notification-badge');
                        if (badge) {
                            badge.textContent = '0';
                            badge.style.display = 'none';
                        }

                        // Mark all notifications in dropdown as read
                        const notifications = document.querySelectorAll('.notification-item .w-2');
                        notifications.forEach(dot => {
                            dot.classList.add('opacity-50');
                        });

                        // Show success message
                        window.dispatchEvent(new CustomEvent('show-notification', {
                            detail: {
                                type: 'success',
                                title: 'Berhasil',
                                message: 'Semua notifikasi telah ditandai sebagai dibaca',
                                duration: 3000
                            }
                        }));
                    }
                } catch (error) {
                    console.error('Error marking all notifications as read:', error);
                }
            }

            // Load initial notifications when dropdown is opened
            document.addEventListener('DOMContentLoaded', function() {
                const notificationButton = document.querySelector('[x-data*="showNotifications"]');
                if (notificationButton) {
                    notificationButton.addEventListener('click', loadNotifications);
                }
            });

            async function loadNotifications() {
                try {
                    const response = await fetch('/notifications', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        updateNotificationDropdown(data.notifications);
                        updateNotificationCount(data.unread_count);
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                }
            }

            function updateNotificationDropdown(notifications) {
                const container = document.querySelector('.notifications-dropdown-content');
                if (!container) return;

                if (notifications.length === 0) {
                    container.innerHTML = `
                        <div class="p-4 text-center text-gray-500 text-sm">
                            <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                            </svg>
                            Tidak ada notifikasi
                        </div>
                    `;
                    return;
                }

                container.innerHTML = notifications.map(notification => {
                    const colorClass = getNotificationColorClass(notification.type);
                    return `
                        <div class="notification-item px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100" data-notification-id="${notification.id}">
                            <div class="flex items-start space-x-3">
                                <div class="w-2 h-2 ${colorClass} rounded-full mt-2 ${notification.is_read ? 'opacity-50' : ''}"></div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                                    <p class="text-sm text-gray-600">${notification.message}</p>
                                    <p class="text-xs text-gray-500 mt-1">${notification.created_at}</p>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600" onclick="removeNotification(${notification.id})">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            function getNotificationColorClass(type) {
                const colorMap = {
                    'system': 'bg-blue-500',
                    'event': 'bg-purple-500',
                    'payment': 'bg-green-500',
                    'order': 'bg-orange-500',
                    'ticket': 'bg-indigo-500'
                };
                return colorMap[type] || 'bg-gray-500';
            }

            function updateNotificationCount(count) {
                const badge = document.querySelector('.notification-badge');
                if (badge) {
                    badge.textContent = count;
                    badge.style.display = count > 0 ? 'flex' : 'none';
                }
            }

            async function removeNotification(notificationId) {
                try {
                    const response = await fetch(`/notifications/${notificationId}`, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json',
                        }
                    });

                    if (response.ok) {
                        const element = document.querySelector(`[data-notification-id="${notificationId}"]`);
                        if (element) {
                            element.remove();
                        }

                        const data = await response.json();
                        updateNotificationCount(data.unread_count);
                    }
                } catch (error) {
                    console.error('Error removing notification:', error);
                }
            }
        </script>
    @endauth

    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('ServiceWorker registered: ', registration);
                    })
                    .catch(error => {
                        console.log('ServiceWorker registration failed: ', error);
                    });
            });
        }
    </script>
</body>
</html>
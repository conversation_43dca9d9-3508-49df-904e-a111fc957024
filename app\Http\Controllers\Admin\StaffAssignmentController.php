<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StaffAssignmentController extends Controller
{
    public function index()
    {
        $assignments = DB::table('staff_organizer_assignments')
            ->join('users as staff', 'staff_organizer_assignments.staff_id', '=', 'staff.id')
            ->join('users as organizers', 'staff_organizer_assignments.organizer_id', '=', 'organizers.id')
            ->select(
                'staff_organizer_assignments.*',
                'staff.name as staff_name',
                'staff.email as staff_email',
                'organizers.name as organizer_name',
                'organizers.email as organizer_email'
            )
            ->orderBy('staff_organizer_assignments.created_at', 'desc')
            ->paginate(20);

        $staff = User::where('role', 'staff')->get();
        $organizers = User::where('role', 'penjual')->get();

        return view('admin.staff-assignments.index', compact('assignments', 'staff', 'organizers'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'staff_id' => 'required|exists:users,id',
            'organizer_id' => 'required|exists:users,id',
            'notes' => 'nullable|string|max:500'
        ]);

        // Check if assignment already exists
        $exists = DB::table('staff_organizer_assignments')
            ->where('staff_id', $request->staff_id)
            ->where('organizer_id', $request->organizer_id)
            ->exists();

        if ($exists) {
            return back()->with('error', 'Assignment already exists for this staff and organizer.');
        }

        DB::table('staff_organizer_assignments')->insert([
            'staff_id' => $request->staff_id,
            'organizer_id' => $request->organizer_id,
            'is_active' => true,
            'notes' => $request->notes,
            'assigned_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return back()->with('success', 'Staff assignment created successfully.');
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'is_active' => 'required|boolean',
            'notes' => 'nullable|string|max:500'
        ]);

        $updateData = [
            'is_active' => $request->is_active,
            'notes' => $request->notes,
            'updated_at' => now()
        ];

        if (!$request->is_active) {
            $updateData['deactivated_at'] = now();
        } else {
            $updateData['deactivated_at'] = null;
        }

        DB::table('staff_organizer_assignments')
            ->where('id', $id)
            ->update($updateData);

        return back()->with('success', 'Staff assignment updated successfully.');
    }

    public function destroy($id)
    {
        DB::table('staff_organizer_assignments')->where('id', $id)->delete();
        return back()->with('success', 'Staff assignment deleted successfully.');
    }
}

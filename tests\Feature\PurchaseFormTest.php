<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\PaymentMethod;
use App\Models\PaymentGateway;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PurchaseFormTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $event;
    protected $paymentMethod;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '***********'
        ]);

        // Create test event
        $this->event = Event::factory()->create([
            'title' => 'Test Event',
            'price' => 100000,
            'max_tickets' => 100,
            'status' => 'published'
        ]);

        // Create payment gateway
        $gateway = PaymentGateway::create([
            'name' => 'Test Gateway',
            'code' => 'test_gateway',
            'type' => 'manual',
            'is_active' => true,
            'config' => []
        ]);

        // Create payment method
        $this->paymentMethod = PaymentMethod::create([
            'name' => 'Bank Transfer',
            'code' => 'bank_transfer',
            'description' => 'Transfer to bank account',
            'gateway_id' => $gateway->id,
            'fee' => 2500,
            'fee_type' => 'fixed',
            'is_active' => true,
            'sort_order' => 1
        ]);
    }

    /** @test */
    public function user_can_view_purchase_form()
    {
        $response = $this->actingAs($this->user)
            ->get(route('tickets.purchase', $this->event));

        $response->assertStatus(200);
        $response->assertViewIs('tickets.purchase');
        $response->assertViewHas('event', $this->event);
    }

    /** @test */
    public function user_can_view_modern_purchase_form()
    {
        $response = $this->actingAs($this->user)
            ->get('/tickets/' . $this->event->id . '/purchase-modern');

        $response->assertStatus(200);
        $response->assertSee('Purchase Ticket');
        $response->assertSee($this->event->title);
    }

    /** @test */
    public function api_returns_available_payment_methods()
    {
        $response = $this->getJson('/api/payment-methods/available');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'payment_methods' => [
                '*' => [
                    'code',
                    'name',
                    'description',
                    'fee',
                    'fee_type',
                    'is_active'
                ]
            ]
        ]);
    }

    /** @test */
    public function api_can_calculate_payment_fee()
    {
        $response = $this->postJson('/api/payment-methods/calculate-fee', [
            'payment_method_code' => 'bank_transfer',
            'amount' => 100000
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'amount',
            'fee',
            'total',
            'fee_type'
        ]);

        $data = $response->json();
        $this->assertEquals(100000, $data['amount']);
        $this->assertEquals(2500, $data['fee']);
        $this->assertEquals(102500, $data['total']);
    }

    /** @test */
    public function user_can_submit_purchase_form_with_valid_data()
    {
        $purchaseData = [
            'quantity' => 2,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '***********',
            'identity_type' => 'ktp',
            'identity_number' => '****************',
            'payment_method' => 'bank_transfer'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), $purchaseData);

        // Should redirect to payment page or success page
        $response->assertRedirect();
        
        // Check if ticket was created
        $this->assertDatabaseHas('tickets', [
            'event_id' => $this->event->id,
            'buyer_id' => $this->user->id,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function purchase_form_validates_required_fields()
    {
        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), []);

        $response->assertSessionHasErrors([
            'quantity',
            'attendee_name',
            'attendee_email',
            'attendee_phone',
            'identity_type',
            'identity_number',
            'payment_method'
        ]);
    }

    /** @test */
    public function purchase_form_validates_email_format()
    {
        $purchaseData = [
            'quantity' => 1,
            'attendee_name' => 'John Doe',
            'attendee_email' => 'invalid-email',
            'attendee_phone' => '***********',
            'identity_type' => 'ktp',
            'identity_number' => '****************',
            'payment_method' => 'bank_transfer'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), $purchaseData);

        $response->assertSessionHasErrors(['attendee_email']);
    }

    /** @test */
    public function purchase_form_validates_phone_format()
    {
        $purchaseData = [
            'quantity' => 1,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '123', // Invalid phone
            'identity_type' => 'ktp',
            'identity_number' => '****************',
            'payment_method' => 'bank_transfer'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), $purchaseData);

        $response->assertSessionHasErrors(['attendee_phone']);
    }

    /** @test */
    public function purchase_form_validates_quantity_limits()
    {
        $purchaseData = [
            'quantity' => 0, // Invalid quantity
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '***********',
            'identity_type' => 'ktp',
            'identity_number' => '****************',
            'payment_method' => 'bank_transfer'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), $purchaseData);

        $response->assertSessionHasErrors(['quantity']);
    }

    /** @test */
    public function purchase_form_validates_payment_method_exists()
    {
        $purchaseData = [
            'quantity' => 1,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'attendee_phone' => '***********',
            'identity_type' => 'ktp',
            'identity_number' => '****************',
            'payment_method' => 'invalid_method'
        ];

        $response = $this->actingAs($this->user)
            ->post(route('tickets.store', $this->event), $purchaseData);

        $response->assertSessionHasErrors(['payment_method']);
    }

    /** @test */
    public function api_handles_invalid_payment_method_for_fee_calculation()
    {
        $response = $this->postJson('/api/payment-methods/calculate-fee', [
            'payment_method_code' => 'invalid_method',
            'amount' => 100000
        ]);

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Payment method not found'
        ]);
    }

    /** @test */
    public function api_validates_fee_calculation_parameters()
    {
        $response = $this->postJson('/api/payment-methods/calculate-fee', [
            'payment_method_code' => 'bank_transfer'
            // Missing amount parameter
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function purchase_form_calculates_total_with_fees()
    {
        $response = $this->postJson('/api/payment-methods/calculate-fee', [
            'payment_method_code' => 'bank_transfer',
            'amount' => 100000
        ]);

        $data = $response->json();
        
        // Base amount + fixed fee
        $this->assertEquals(100000, $data['amount']);
        $this->assertEquals(2500, $data['fee']);
        $this->assertEquals(102500, $data['total']);
    }

    /** @test */
    public function purchase_form_handles_percentage_fees()
    {
        // Create payment method with percentage fee
        $percentageMethod = PaymentMethod::create([
            'name' => 'Credit Card',
            'code' => 'credit_card',
            'description' => 'Pay with credit card',
            'gateway_id' => $this->paymentMethod->gateway_id,
            'fee' => 2.5, // 2.5%
            'fee_type' => 'percentage',
            'is_active' => true,
            'sort_order' => 2
        ]);

        $response = $this->postJson('/api/payment-methods/calculate-fee', [
            'payment_method_code' => 'credit_card',
            'amount' => 100000
        ]);

        $data = $response->json();
        
        // 2.5% of 100000 = 2500
        $this->assertEquals(100000, $data['amount']);
        $this->assertEquals(2500, $data['fee']);
        $this->assertEquals(102500, $data['total']);
    }

    /** @test */
    public function guest_cannot_access_purchase_form()
    {
        $response = $this->get(route('tickets.purchase', $this->event));

        $response->assertRedirect(route('login'));
    }

    /** @test */
    public function purchase_form_auto_fills_user_data()
    {
        $response = $this->actingAs($this->user)
            ->get(route('tickets.purchase', $this->event));

        $response->assertSee('value="' . $this->user->name . '"', false);
        $response->assertSee('value="' . $this->user->email . '"', false);
        $response->assertSee('value="' . $this->user->phone . '"', false);
    }
}

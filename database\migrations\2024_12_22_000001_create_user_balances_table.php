<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('user_balances')) {
            Schema::create('user_balances', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->decimal('balance', 15, 2)->default(0);
                $table->decimal('pending_balance', 15, 2)->default(0); // For pending withdrawals
                $table->decimal('total_earned', 15, 2)->default(0); // Total earnings from events
                $table->decimal('total_deposited', 15, 2)->default(0); // Total deposits
                $table->decimal('total_withdrawn', 15, 2)->default(0); // Total withdrawals
                $table->decimal('total_fees_paid', 15, 2)->default(0); // Total platform fees paid
                $table->timestamps();

                $table->index(['user_id']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('user_balances');
    }
};

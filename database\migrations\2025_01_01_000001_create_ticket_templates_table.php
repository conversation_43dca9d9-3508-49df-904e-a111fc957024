<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organizer_id')->constrained('users')->onDelete('cascade');
            $table->string('name'); // Template name
            $table->string('template_type')->default('custom'); // classic, unix, minimal, pro, custom
            $table->boolean('is_default')->default(false); // Default template for this organizer
            $table->boolean('is_active')->default(true);
            
            // Basic configuration
            $table->json('config')->nullable(); // Basic colors and settings
            
            // Custom configuration (only for platinum users)
            $table->json('custom_config')->nullable(); // Advanced customization options
            
            // Template preview
            $table->string('preview_image')->nullable();
            
            // Usage tracking
            $table->integer('usage_count')->default(0);
            $table->timestamp('last_used_at')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['organizer_id', 'is_active']);
            $table->index(['organizer_id', 'is_default']);
            $table->index('template_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_templates');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $columns = Schema::getColumnListing('users');

            // User level system - only add if it doesn't exist
            if (!in_array('badge_level', $columns)) {
                $table->enum('badge_level', ['star', 'star_plus', 'premium', 'platinum'])
                      ->default('star')
                      ->after('role')
                      ->comment('User level: star, star_plus, premium, platinum');
            }

            // Level metadata - only add if they don't exist
            if (!in_array('level_upgraded_at', $columns)) {
                $table->timestamp('level_upgraded_at')->nullable()->after('badge_level');
            }
            if (!in_array('level_expires_at', $columns)) {
                $table->timestamp('level_expires_at')->nullable()->after('level_upgraded_at');
            }
            if (!in_array('level_benefits', $columns)) {
                $table->json('level_benefits')->nullable()->after('level_expires_at');
            }
            if (!in_array('level_notes', $columns)) {
                $table->text('level_notes')->nullable()->after('level_benefits');
            }

            // Level statistics - only add if they don't exist
            if (!in_array('total_events_created', $columns)) {
                $table->integer('total_events_created')->default(0)->after('level_notes');
            }
            if (!in_array('total_tickets_sold', $columns)) {
                $table->integer('total_tickets_sold')->default(0)->after('total_events_created');
            }
            if (!in_array('total_revenue', $columns)) {
                $table->decimal('total_revenue', 15, 2)->default(0)->after('total_tickets_sold');
            }
            if (!in_array('level_score', $columns)) {
                $table->decimal('level_score', 8, 2)->default(0)->after('total_revenue');
            }
        });

        // Add indexes separately to avoid conflicts
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->index('badge_level');
            });
        } catch (Exception $e) {
            // Index might already exist
        }

        try {
            Schema::table('users', function (Blueprint $table) {
                $table->index(['role', 'badge_level']);
            });
        } catch (Exception $e) {
            // Index might already exist
        }

        try {
            Schema::table('users', function (Blueprint $table) {
                $table->index('level_expires_at');
            });
        } catch (Exception $e) {
            // Index might already exist
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes safely
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex(['badge_level']);
            });
        } catch (Exception $e) {
            // Index might not exist
        }

        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex(['role', 'badge_level']);
            });
        } catch (Exception $e) {
            // Index might not exist
        }

        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex(['level_expires_at']);
            });
        } catch (Exception $e) {
            // Index might not exist
        }

        // Drop columns safely
        Schema::table('users', function (Blueprint $table) {
            $columns = Schema::getColumnListing('users');
            $columnsToRemove = [];

            $possibleColumns = [
                'level_score',
                'total_revenue',
                'total_tickets_sold',
                'total_events_created',
                'level_notes',
                'level_benefits',
                'level_expires_at',
                'level_upgraded_at',
                'badge_level'
            ];

            foreach ($possibleColumns as $column) {
                if (in_array($column, $columns)) {
                    $columnsToRemove[] = $column;
                }
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });
    }
};

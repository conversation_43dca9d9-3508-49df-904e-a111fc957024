<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unix Terminal Template Preview</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
            background: #1a1a1a;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .terminal {
            width: 500px;
            height: 300px;
            background: {{ request('background_color', '#000000') }};
            border: 1px solid {{ request('primary_color', '#00FF00') }};
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
            position: relative;
        }

        .terminal-header {
            background: #333333;
            padding: 8px 12px;
            border-bottom: 1px solid {{ request('primary_color', '#00FF00') }};
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .terminal-buttons {
            display: flex;
            gap: 4px;
        }

        .terminal-button {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .close { background: #ff5f56; }
        .minimize { background: #ffbd2e; }
        .maximize { background: #27ca3f; }

        .terminal-title {
            color: {{ request('primary_color', '#00FF00') }};
            font-size: 12px;
            margin-left: 8px;
        }

        .terminal-body {
            padding: 16px;
            color: {{ request('primary_color', '#00FF00') }};
            font-size: 12px;
            line-height: 1.4;
            height: calc(100% - 40px);
            overflow: hidden;
        }

        .prompt {
            color: {{ request('secondary_color', '#FFFF00') }};
        }

        .command {
            color: {{ request('primary_color', '#00FF00') }};
        }

        .output {
            color: {{ request('text_color', '#FFFFFF') }};
            margin-left: 0;
        }

        .ticket-info {
            border: 1px solid {{ request('primary_color', '#00FF00') }};
            padding: 8px;
            margin: 8px 0;
            background: rgba(0, 255, 0, 0.05);
        }

        .ascii-art {
            color: {{ request('secondary_color', '#FFFF00') }};
            font-size: 10px;
            line-height: 1;
            text-align: center;
            margin: 8px 0;
        }

        .cursor {
            background: {{ request('primary_color', '#00FF00') }};
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .qr-placeholder {
            border: 1px solid {{ request('primary_color', '#00FF00') }};
            width: 60px;
            height: 60px;
            display: inline-block;
            text-align: center;
            line-height: 60px;
            font-size: 8px;
            float: right;
            margin-left: 8px;
        }
    </style>
</head>
<body>
    <div class="terminal">
        <div class="terminal-header">
            <div class="terminal-buttons">
                <div class="terminal-button close"></div>
                <div class="terminal-button minimize"></div>
                <div class="terminal-button maximize"></div>
            </div>
            <div class="terminal-title">tixara@terminal: ~/tickets</div>
        </div>
        
        <div class="terminal-body">
            <div><span class="prompt">user@tixara:~$</span> <span class="command">cat ticket.txt</span></div>
            <div class="output">
                <div class="ascii-art">
╔══════════════════════════════════════════════════════════╗
║                    TIXARA BOARDING PASS                  ║
╚══════════════════════════════════════════════════════════╝
                </div>
                
                <div class="ticket-info">
                    <div class="qr-placeholder">
                        ████<br>
                        █  █<br>
                        ████<br>
                        QR
                    </div>
                    
                    EVENT: {{ request('event_title', 'Sample Event') }}<br>
                    DATE:  {{ request('start_date', now()->addDays(7)->format('Y-m-d H:i')) }}<br>
                    VENUE: {{ request('venue_name', 'Sample Venue') }}<br>
                    USER:  {{ request('attendee_name', 'john.doe') }}<br>
                    SEAT:  {{ request('seat_number', 'A001') }}<br>
                    PRICE: {{ request('price', 'Rp 150.000') }}<br>
                    ID:    {{ request('ticket_number', 'TIK-PREVIEW-001') }}
                </div>
                
                <div class="ascii-art">
┌─────────────────────────────────────────────────────────┐
│ STATUS: VALID | SCANNED: NO | ENTRY: PENDING           │
└─────────────────────────────────────────────────────────┘
                </div>
            </div>
            
            <div><span class="prompt">user@tixara:~$</span> <span class="command">validate_ticket</span></div>
            <div class="output">Ticket validation ready...</div>
            <div><span class="prompt">user@tixara:~$</span> <span class="cursor">&nbsp;</span></div>
        </div>
    </div>
</body>
</html>

@extends('layouts.staff')

@section('title', 'Staff Events - TiXara')
@section('page-title', 'Events')

@push('styles')
<style>
/* Staff Events Styles */
.event-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(16, 185, 129, 0.1);
    backdrop-filter: blur(10px);
    border-left: 4px solid transparent;
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.event-card.active {
    border-left-color: #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, white 100%);
}

.event-card.upcoming {
    border-left-color: #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.05) 0%, white 100%);
}

.event-card.ended {
    border-left-color: #6b7280;
    background: linear-gradient(90deg, rgba(107, 114, 128, 0.05) 0%, white 100%);
}

.event-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.event-status.active {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.event-status.upcoming {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.event-status.ended {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.calendar-view {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-6 lg:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Event Management</h1>
                <p class="text-gray-600 dark:text-gray-400">Monitor and manage assigned events</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    <i data-lucide="calendar-plus" class="w-4 h-4 inline mr-2"></i>
                    Today's Events
                </button>
                <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export Schedule
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Total Events</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">24</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-xl">
                    <i data-lucide="calendar" class="w-6 h-6 text-blue-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Active Events</p>
                    <p class="text-2xl font-bold text-green-600">8</p>
                </div>
                <div class="p-3 bg-green-100 rounded-xl">
                    <i data-lucide="calendar-check" class="w-6 h-6 text-green-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Upcoming Events</p>
                    <p class="text-2xl font-bold text-orange-600">12</p>
                </div>
                <div class="p-3 bg-orange-100 rounded-xl">
                    <i data-lucide="clock" class="w-6 h-6 text-orange-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Completed Events</p>
                    <p class="text-2xl font-bold text-gray-600">4</p>
                </div>
                <div class="p-3 bg-gray-100 rounded-xl">
                    <i data-lucide="check-circle" class="w-6 h-6 text-gray-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- View Toggle -->
    <div class="mb-6" data-aos="fade-up" data-aos-delay="200">
        <div class="flex items-center space-x-4">
            <button id="listView" class="px-4 py-2 bg-green-500 text-white rounded-lg transition-colors">
                <i data-lucide="list" class="w-4 h-4 inline mr-2"></i>
                List View
            </button>
            <button id="calendarView" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors">
                <i data-lucide="calendar" class="w-4 h-4 inline mr-2"></i>
                Calendar View
            </button>
        </div>
    </div>

    <!-- Events List -->
    <div id="eventsList" class="space-y-4" data-aos="fade-up" data-aos-delay="300">
        <!-- Sample Event Cards -->
        @php
            $events = [
                ['name' => 'Concert Music Festival 2024', 'date' => '2024-12-25', 'time' => '19:00', 'venue' => 'Jakarta Arena', 'capacity' => 5000, 'sold' => 4200, 'status' => 'active'],
                ['name' => 'Tech Summit Indonesia', 'date' => '2024-12-30', 'time' => '09:00', 'venue' => 'ICE BSD', 'capacity' => 2000, 'sold' => 1800, 'status' => 'upcoming'],
                ['name' => 'Art Exhibition Modern', 'date' => '2024-12-28', 'time' => '10:00', 'venue' => 'National Gallery', 'capacity' => 500, 'sold' => 450, 'status' => 'upcoming'],
                ['name' => 'Food Festival Jakarta', 'date' => '2024-12-20', 'time' => '11:00', 'venue' => 'PIK Avenue', 'capacity' => 3000, 'sold' => 3000, 'status' => 'ended'],
                ['name' => 'Fashion Week 2024', 'date' => '2024-12-29', 'time' => '18:00', 'venue' => 'JCC Senayan', 'capacity' => 1500, 'sold' => 1200, 'status' => 'upcoming'],
            ];
        @endphp

        @foreach($events as $index => $event)
        <div class="event-card {{ $event['status'] }} bg-white dark:bg-gray-800 rounded-xl p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex-1 mb-4 lg:mb-0">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                            <i data-lucide="calendar-days" class="w-8 h-8 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $event['name'] }}</h3>
                            <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                                <span><i data-lucide="calendar" class="w-4 h-4 inline mr-1"></i>{{ \Carbon\Carbon::parse($event['date'])->format('d M Y') }}</span>
                                <span><i data-lucide="clock" class="w-4 h-4 inline mr-1"></i>{{ $event['time'] }}</span>
                                <span><i data-lucide="map-pin" class="w-4 h-4 inline mr-1"></i>{{ $event['venue'] }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-6">
                    <!-- Capacity Info -->
                    <div class="text-center">
                        <p class="text-sm text-gray-600 dark:text-gray-400">Capacity</p>
                        <p class="text-lg font-bold text-gray-900 dark:text-white">{{ number_format($event['sold']) }}/{{ number_format($event['capacity']) }}</p>
                        <div class="w-20 bg-gray-200 rounded-full h-2 mt-1">
                            <div class="bg-green-500 h-2 rounded-full" style="width: {{ ($event['sold'] / $event['capacity']) * 100 }}%"></div>
                        </div>
                    </div>

                    <!-- Status -->
                    <div class="event-status {{ $event['status'] }}">
                        {{ ucfirst($event['status']) }}
                    </div>

                    <!-- Actions -->
                    <div class="flex space-x-2">
                        <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="View Details">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Scan Tickets">
                            <i data-lucide="scan-line" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-purple-600 hover:bg-purple-50 rounded-lg transition-colors" title="Reports">
                            <i data-lucide="bar-chart-3" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Calendar View (Hidden by default) -->
    <div id="eventsCalendar" class="calendar-view rounded-xl p-6 hidden" data-aos="fade-up" data-aos-delay="300">
        <div class="text-center py-20">
            <i data-lucide="calendar" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">Calendar View</h3>
            <p class="text-gray-600 dark:text-gray-400">Calendar integration will be implemented here</p>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View toggle functionality
    const listViewBtn = document.getElementById('listView');
    const calendarViewBtn = document.getElementById('calendarView');
    const eventsList = document.getElementById('eventsList');
    const eventsCalendar = document.getElementById('eventsCalendar');

    listViewBtn.addEventListener('click', function() {
        // Switch to list view
        listViewBtn.classList.remove('bg-gray-200', 'text-gray-700');
        listViewBtn.classList.add('bg-green-500', 'text-white');
        calendarViewBtn.classList.remove('bg-green-500', 'text-white');
        calendarViewBtn.classList.add('bg-gray-200', 'text-gray-700');
        
        eventsList.classList.remove('hidden');
        eventsCalendar.classList.add('hidden');
    });

    calendarViewBtn.addEventListener('click', function() {
        // Switch to calendar view
        calendarViewBtn.classList.remove('bg-gray-200', 'text-gray-700');
        calendarViewBtn.classList.add('bg-green-500', 'text-white');
        listViewBtn.classList.remove('bg-green-500', 'text-white');
        listViewBtn.classList.add('bg-gray-200', 'text-gray-700');
        
        eventsList.classList.add('hidden');
        eventsCalendar.classList.remove('hidden');
    });
});
</script>
@endpush
@endsection

<?php

require_once 'vendor/autoload.php';

// Load Laravel app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

try {
    echo "🔍 Checking for duplicate table migration issues...\n";
    
    // Get all existing tables
    $existingTables = [];
    $tables = DB::select('SHOW TABLES');
    foreach ($tables as $table) {
        $tableName = array_values((array)$table)[0];
        $existingTables[] = $tableName;
    }
    
    echo "✅ Found " . count($existingTables) . " existing tables\n";
    echo "Tables: " . implode(', ', $existingTables) . "\n\n";
    
    // List of migration files that might have duplicate table issues
    $migrationPath = 'database/migrations/';
    $problematicMigrations = [];
    
    // Scan migration files
    $migrationFiles = glob($migrationPath . '*.php');
    
    foreach ($migrationFiles as $file) {
        $content = file_get_contents($file);
        $filename = basename($file);
        
        // Check if it's a create table migration
        if (preg_match('/Schema::create\([\'"](\w+)[\'"]/', $content, $matches)) {
            $tableName = $matches[1];
            
            // Check if table already exists
            if (in_array($tableName, $existingTables)) {
                $problematicMigrations[] = [
                    'file' => $filename,
                    'table' => $tableName,
                    'path' => $file
                ];
            }
        }
    }
    
    if (empty($problematicMigrations)) {
        echo "✅ No duplicate table migration issues found!\n";
        exit(0);
    }
    
    echo "⚠️  Found " . count($problematicMigrations) . " problematic migrations:\n";
    foreach ($problematicMigrations as $migration) {
        echo "  - {$migration['file']} (table: {$migration['table']})\n";
    }
    
    echo "\n🔧 Fixing migrations...\n";
    
    foreach ($problematicMigrations as $migration) {
        echo "Fixing {$migration['file']}...\n";
        
        $content = file_get_contents($migration['path']);
        $tableName = $migration['table'];
        
        // Replace Schema::create with conditional check
        $pattern = '/Schema::create\([\'"]' . preg_quote($tableName) . '[\'"],\s*function\s*\(\s*Blueprint\s*\$table\s*\)\s*{/';
        $replacement = "if (!Schema::hasTable('{$tableName}')) {\n            Schema::create('{$tableName}', function (Blueprint \$table) {";
        
        $newContent = preg_replace($pattern, $replacement, $content);
        
        // Add closing brace for the if statement
        $pattern2 = '/(\s+}\);\s+}\s+\/\*\*)/';
        $replacement2 = '$1' . "\n        }";
        
        $newContent = preg_replace($pattern2, $replacement2, $newContent);
        
        // Write back to file
        file_put_contents($migration['path'], $newContent);
        echo "  ✅ Fixed {$migration['file']}\n";
    }
    
    echo "\n🎉 All migrations fixed successfully!\n";
    echo "You can now run: php artisan migrate\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('vouchers')) {
            Schema::create('vouchers', function (Blueprint $table) {
                $table->id();
                $table->string('code')->unique(); // Voucher code (e.g., WELCOME10, NEWYEAR2024)
                $table->string('name'); // Display name
                $table->text('description')->nullable(); // Description for admin
            
            // Discount Configuration
                $table->enum('type', ['percentage', 'fixed']); // percentage or fixed amount
                $table->decimal('value', 12, 2); // discount value (percentage or amount)
                $table->decimal('min_order_amount', 12, 2)->default(0); // minimum order amount
                $table->decimal('max_discount_amount', 12, 2)->nullable(); // max discount for percentage type
            
            // Usage Limits
                $table->integer('usage_limit')->nullable(); // total usage limit (null = unlimited)
                $table->integer('usage_limit_per_user')->default(1); // usage limit per user
                $table->integer('used_count')->default(0); // current usage count
            
            // Validity Period
                $table->datetime('starts_at'); // when voucher becomes active
                $table->datetime('expires_at'); // when voucher expires
            
            // Restrictions
                $table->json('applicable_events')->nullable(); // specific event IDs (null = all events)
                $table->json('applicable_categories')->nullable(); // specific category IDs (null = all categories)
                $table->json('applicable_user_roles')->nullable(); // specific user roles (null = all users)
                $table->decimal('min_tickets', 8, 0)->default(1); // minimum tickets to purchase
                $table->decimal('max_tickets', 8, 0)->nullable(); // maximum tickets to purchase
            
            // Status and Metadata
                $table->boolean('is_active')->default(true);
                $table->boolean('is_public')->default(true); // public vouchers vs private/targeted
                $table->string('created_by_type')->default('admin'); // admin, system, organizer
                $table->foreignId('created_by_id')->nullable()->constrained('users')->onDelete('set null');
                $table->json('metadata')->nullable(); // additional data (campaign info, etc.)
            
                $table->timestamps();
            
            // Indexes
                $table->index(['code', 'is_active']);
                $table->index(['starts_at', 'expires_at']);
                $table->index(['type', 'is_active']);
                $table->index(['created_by_id']);
                $table->index(['is_public', 'is_active']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('vouchers');
    }
};

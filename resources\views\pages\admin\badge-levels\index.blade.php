@extends('layouts.admin')

@section('title', 'Badge Level Management')

@push('styles')
<style>
.badge-level-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.badge-level-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.badge-level-card.active {
    border-color: #10B981;
    background: linear-gradient(135deg, #F0FDF4 0%, #ECFDF5 100%);
}

.badge-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.badge-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-item {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.badge-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 14px;
    color: #6B7280;
}

.benefits-list li i {
    color: #10B981;
    font-size: 12px;
}

.user-count-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(99, 102, 241, 0.1);
    color: #6366F1;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.btn-badge {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.btn-edit {
    background: #F59E0B;
    color: white;
    border-color: #F59E0B;
}

.btn-edit:hover {
    background: #D97706;
    color: white;
}

.btn-toggle {
    background: #6B7280;
    color: white;
    border-color: #6B7280;
}

.btn-toggle:hover {
    background: #4B5563;
    color: white;
}

.btn-toggle.active {
    background: #10B981;
    border-color: #10B981;
}

.btn-toggle.active:hover {
    background: #059669;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    border: 2px dashed #E5E7EB;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    background: #F3F4F6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
    color: #9CA3AF;
}

@media (max-width: 768px) {
    .badge-grid {
        grid-template-columns: 1fr;
    }

    .badge-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Badge Level Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Manage user badge level and benefits</p>
        </div>
        <div class="flex gap-3">
            <button onclick="openAssignBadgeModal()" class="btn btn-outline-primary">
                <i data-lucide="user-plus" class="w-4 h-4"></i>
                Assign Badge
            </button>
            <button onclick="autoUpgradeUsers()" class="btn btn-secondary">
                <i data-lucide="zap" class="w-4 h-4"></i>
                Auto Upgrade Users
            </button>
            <a href="{{ route('admin.badge-level.create') }}" class="btn btn-primary">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Add Badge Level
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="badge-stats">
        <div class="stat-item">
            <div class="stat-value">{{ $stats['total_level'] ?? 0 }}</div>
            <div class="stat-label">Total Level</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['active_level'] ?? 0 }}</div>
            <div class="stat-label">Active Level</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['total_users'] ?? 0 }}</div>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['users_with_badges'] ?? 0 }}</div>
            <div class="stat-label">Users with Badges</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['eligible_for_upgrade'] ?? 0 }}</div>
            <div class="stat-label">Eligible for Upgrade</div>
        </div>
    </div>

    <!-- Badge Level Grid -->
    @if($badgelevel->count() > 0)
        <div class="badge-grid">
            @foreach($badgelevel as $level)
                <div class="badge-level-card {{ $level->is_active ? 'active' : '' }} bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <!-- Badge Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="badge-icon" style="background: {{ $level->color }};">
                            <i class="{{ $level->icon }}"></i>
                        </div>
                        <div class="flex items-center gap-2">
                            @if($level->is_active)
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">Active</span>
                            @else
                                <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded-full">Inactive</span>
                            @endif
                            <span class="user-count-badge">
                                <i data-lucide="users" class="w-3 h-3"></i>
                                {{ $level->users_count ?? 0 }} users
                            </span>
                        </div>
                    </div>

                    <!-- Badge Info -->
                    <div class="mb-4">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ $level->name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ $level->description }}</p>

                        <!-- Requirements -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-3">
                            <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Requirements:</h4>
                            <div class="grid grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400">
                                <div>Min Spent: Rp {{ number_format($level->min_spent_amount, 0, ',', '.') }}</div>
                                <div>Min UangTix: Rp {{ number_format($level->min_uangtix_balance, 0, ',', '.') }}</div>
                                <div>Min Transactions: {{ $level->min_transactions }}</div>
                                <div>Min Events: {{ $level->min_events_attended }}</div>
                            </div>
                        </div>

                        <!-- Benefits -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                            <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">Benefits:</h4>
                            <ul class="benefits-list">
                                <li>
                                    <i data-lucide="percent" class="w-3 h-3"></i>
                                    {{ $level->discount_percentage }}% Discount
                                </li>
                                <li>
                                    <i data-lucide="coins" class="w-3 h-3"></i>
                                    {{ $level->cashback_percentage }}% Cashback
                                </li>
                                @if($level->benefits && is_array($level->benefits))
                                    @foreach($level->benefits as $key => $value)
                                        @if($value === true)
                                            <li>
                                                <i data-lucide="check" class="w-3 h-3"></i>
                                                {{ ucfirst(str_replace('_', ' ', $key)) }}
                                            </li>
                                        @endif
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="action-buttons">
                        <a href="{{ route('admin.badge-level.edit', $level) }}" class="btn-badge btn-edit">
                            <i data-lucide="edit" class="w-3 h-3"></i>
                            Edit
                        </a>

                        <form method="POST" action="{{ route('admin.badge-level.toggle-status', $level) }}" class="inline">
                            @csrf
                            <button type="submit" class="btn-badge btn-toggle {{ $level->is_active ? 'active' : '' }}">
                                <i data-lucide="{{ $level->is_active ? 'eye-off' : 'eye' }}" class="w-3 h-3"></i>
                                {{ $level->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>

                        <a href="{{ route('admin.badge-level.show', $level) }}" class="btn-badge" style="background: #6366F1; color: white;">
                            <i data-lucide="eye" class="w-3 h-3"></i>
                            View
                        </a>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($badgelevel->hasPages())
            <div class="flex justify-center">
                {{ $badgelevel->links() }}
            </div>
        @endif
    @else
        <div class="empty-state">
            <div class="empty-state-icon">
                <i data-lucide="award"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Badge Level</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Get started by creating your first badge level</p>
            <a href="{{ route('admin.badge-level.create') }}" class="btn btn-primary">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Create Badge Level
            </a>
        </div>
    @endif

    <!-- Assign Badge Modal -->
    <div id="assignBadgeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Assign Badge to User</h3>
                        <button onclick="closeAssignBadgeModal()" class="text-gray-400 hover:text-gray-600">
                            <i data-lucide="x" class="w-5 h-5"></i>
                        </button>
                    </div>

                    <form id="assignBadgeForm">
                        <div class="space-y-4">
                            <!-- User Search -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Search User
                                </label>
                                <input type="text" id="userSearch" placeholder="Type user name or email..."
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div id="userSearchResults" class="mt-2 max-h-40 overflow-y-auto hidden"></div>
                            </div>

                            <!-- Selected User -->
                            <div id="selectedUserDiv" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Selected User
                                </label>
                                <div id="selectedUser" class="p-3 bg-gray-50 dark:bg-gray-700 rounded-md"></div>
                                <input type="hidden" id="selectedUserId" name="user_id">
                            </div>

                            <!-- Badge Level Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Badge Level
                                </label>
                                <select id="BadgeLevelSelect" name="badge_level_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Remove Badge</option>
                                    @foreach($badgelevel as $level)
                                        <option value="{{ $level->id }}">{{ $level->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Duration Settings -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Duration (Optional)
                                </label>
                                <div class="flex space-x-2">
                                    <input type="number" id="durationDays" name="duration_days" placeholder="Days" min="1"
                                           class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <div class="flex items-center">
                                        <input type="checkbox" id="autoRenew" name="auto_renew" class="mr-2">
                                        <label for="autoRenew" class="text-sm text-gray-700 dark:text-gray-300">Auto Renew</label>
                                    </div>
                                </div>
                                <small class="text-gray-500">Leave empty for permanent badge</small>
                            </div>
                        </div>

                        <div class="flex justify-end gap-3 mt-6">
                            <button type="button" onclick="closeAssignBadgeModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Assign Badge
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Assign Badge Modal Functions
function openAssignBadgeModal() {
    document.getElementById('assignBadgeModal').classList.remove('hidden');
}

function closeAssignBadgeModal() {
    document.getElementById('assignBadgeModal').classList.add('hidden');
    document.getElementById('assignBadgeForm').reset();
    document.getElementById('selectedUserDiv').classList.add('hidden');
    document.getElementById('userSearchResults').classList.add('hidden');
}

// User Search
let searchTimeout;
document.getElementById('userSearch').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    const query = this.value.trim();

    if (query.length < 2) {
        document.getElementById('userSearchResults').classList.add('hidden');
        return;
    }

    searchTimeout = setTimeout(() => {
        searchUsers(query);
    }, 300);
});

function searchUsers(query) {
    fetch(`{{ route('admin.badge-level.users') }}?search=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(users => {
            const resultsDiv = document.getElementById('userSearchResults');

            if (users.length === 0) {
                resultsDiv.innerHTML = '<div class="p-2 text-gray-500">No users found</div>';
            } else {
                resultsDiv.innerHTML = users.map(user => `
                    <div class="p-2 hover:bg-gray-100 cursor-pointer border-b" onclick="selectUser(${user.id}, '${user.name}', '${user.email}', '${user.role}', ${user.badge_level_id || 'null'})">
                        <div class="font-medium">${user.name}</div>
                        <div class="text-sm text-gray-500">${user.email} - ${user.role}</div>
                        ${user.badge_level ? `<div class="text-xs text-blue-600">Current: ${user.badge_level.name}</div>` : ''}
                    </div>
                `).join('');
            }

            resultsDiv.classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error searching users:', error);
        });
}

function selectUser(id, name, email, role, currentBadgeId) {
    document.getElementById('selectedUserId').value = id;
    document.getElementById('selectedUser').innerHTML = `
        <div class="font-medium">${name}</div>
        <div class="text-sm text-gray-500">${email} - ${role}</div>
    `;
    document.getElementById('selectedUserDiv').classList.remove('hidden');
    document.getElementById('userSearchResults').classList.add('hidden');
    document.getElementById('userSearch').value = '';

    // Set current badge level in select
    if (currentBadgeId) {
        document.getElementById('BadgeLevelSelect').value = currentBadgeId;
    }
}

// Assign Badge Form
document.getElementById('assignBadgeForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const userId = document.getElementById('selectedUserId').value;
    const badgeLevelId = document.getElementById('BadgeLevelSelect').value;

    if (!userId) {
        alert('Please select a user');
        return;
    }

    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Assigning...';
    submitBtn.disabled = true;

    const durationDays = document.getElementById('durationDays').value;
    const autoRenew = document.getElementById('autoRenew').checked;

    fetch('{{ route("admin.badge-level.assign-badge") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            user_id: userId,
            badge_level_id: badgeLevelId || null,
            duration_days: durationDays ? parseInt(durationDays) : null,
            auto_renew: autoRenew
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            closeAssignBadgeModal();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while assigning badge');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
});

function autoUpgradeUsers() {
    if (!confirm('Are you sure you want to auto-upgrade all eligible users?')) {
        return;
    }

    const button = event.target.closest('button');
    const originalText = button.innerHTML;

    button.innerHTML = '<i data-lucide="loader" class="w-4 h-4 animate-spin"></i> Upgrading...';
    button.disabled = true;

    fetch('{{ route("admin.badge-level.auto-upgrade") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Successfully upgraded ${data.upgraded_count} users!`);
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while upgrading users');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Auto-refresh user counts every 30 seconds
setInterval(() => {
    fetch('/api/admin/badge-stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update user counts in badges
                document.querySelectorAll('.user-count-badge').forEach((badge, index) => {
                    const levelData = data.stats.level_distribution;
                    if (levelData && Object.values(levelData)[index] !== undefined) {
                        badge.innerHTML = `<i data-lucide="users" class="w-3 h-3"></i> ${Object.values(levelData)[index]} users`;
                    }
                });
            }
        })
        .catch(error => console.error('Error updating badge stats:', error));
}, 30000);
</script>
@endpush

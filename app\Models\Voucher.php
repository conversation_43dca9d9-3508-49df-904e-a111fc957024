<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Voucher extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'min_order_amount',
        'max_discount_amount',
        'usage_limit',
        'usage_limit_per_user',
        'used_count',
        'starts_at',
        'expires_at',
        'applicable_events',
        'applicable_categories',
        'applicable_user_roles',
        'min_tickets',
        'max_tickets',
        'is_active',
        'is_public',
        'created_by_type',
        'created_by_id',
        'metadata',
        'organizer_id',
        'event_id',
        'minimum_purchase',
        'maximum_discount',
        'start_date',
        'end_date',
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'min_order_amount' => 'decimal:2',
        'max_discount_amount' => 'decimal:2',
        'min_tickets' => 'integer',
        'max_tickets' => 'integer',
        'usage_limit' => 'integer',
        'usage_limit_per_user' => 'integer',
        'used_count' => 'integer',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'applicable_events' => 'array',
        'applicable_categories' => 'array',
        'applicable_user_roles' => 'array',
        'is_active' => 'boolean',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    // Relationships
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }

    public function organizer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function usages(): HasMany
    {
        return $this->hasMany(VoucherUsage::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeValid($query)
    {
        $now = now();
        return $query->where('is_active', true)
                    ->where('starts_at', '<=', $now)
                    ->where('expires_at', '>=', $now);
    }

    public function scopeAvailable($query)
    {
        return $query->valid()
                    ->where(function ($q) {
                        $q->whereNull('usage_limit')
                          ->orWhereRaw('used_count < usage_limit');
                    });
    }

    // Helper Methods
    public function isValid(): bool
    {
        $now = now();
        return $this->is_active
            && $this->starts_at <= $now
            && $this->expires_at >= $now;
    }

    public function isAvailable(): bool
    {
        return $this->isValid()
            && (is_null($this->usage_limit) || $this->used_count < $this->usage_limit);
    }

    public function canBeUsedBy(User $user): bool
    {
        if (!$this->isAvailable()) {
            return false;
        }

        // Check user role restrictions
        if (!empty($this->applicable_user_roles) && !in_array($user->role, $this->applicable_user_roles)) {
            return false;
        }

        // Check per-user usage limit
        $userUsageCount = $this->usages()->where('user_id', $user->id)->count();
        if ($userUsageCount >= $this->usage_limit_per_user) {
            return false;
        }

        return true;
    }

    public function canBeAppliedToEvent(Event $event): bool
    {
        // Check event restrictions
        if (!empty($this->applicable_events) && !in_array($event->id, $this->applicable_events)) {
            return false;
        }

        // Check category restrictions
        if (!empty($this->applicable_categories) && !in_array($event->category_id, $this->applicable_categories)) {
            return false;
        }

        return true;
    }

    public function calculateDiscount(float $orderAmount, int $ticketQuantity = 1): array
    {
        // Check minimum order amount
        if ($orderAmount < $this->min_order_amount) {
            return [
                'eligible' => false,
                'reason' => "Minimum order amount is " . number_format($this->min_order_amount, 0, ',', '.'),
                'discount' => 0,
                'final_amount' => $orderAmount
            ];
        }

        // Check ticket quantity restrictions
        if ($ticketQuantity < $this->min_tickets) {
            return [
                'eligible' => false,
                'reason' => "Minimum {$this->min_tickets} tickets required",
                'discount' => 0,
                'final_amount' => $orderAmount
            ];
        }

        if ($this->max_tickets && $ticketQuantity > $this->max_tickets) {
            return [
                'eligible' => false,
                'reason' => "Maximum {$this->max_tickets} tickets allowed",
                'discount' => 0,
                'final_amount' => $orderAmount
            ];
        }

        // Calculate discount
        if ($this->type === 'percentage') {
            $discount = ($orderAmount * $this->value) / 100;

            // Apply maximum discount limit
            if ($this->max_discount_amount && $discount > $this->max_discount_amount) {
                $discount = $this->max_discount_amount;
            }
        } else {
            $discount = $this->value;
        }

        // Ensure discount doesn't exceed order amount
        $discount = min($discount, $orderAmount);
        $finalAmount = $orderAmount - $discount;

        return [
            'eligible' => true,
            'discount' => $discount,
            'final_amount' => $finalAmount,
            'savings_percentage' => $orderAmount > 0 ? round(($discount / $orderAmount) * 100, 1) : 0
        ];
    }

    public function incrementUsage(): void
    {
        $this->increment('used_count');
    }

    public function getFormattedValueAttribute(): string
    {
        if ($this->type === 'percentage') {
            return $this->value . '%';
        }

        return 'Rp ' . number_format($this->value, 0, ',', '.');
    }

    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'inactive';
        }

        $now = now();

        if ($this->starts_at > $now) {
            return 'scheduled';
        }

        if ($this->expires_at < $now) {
            return 'expired';
        }

        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return 'exhausted';
        }

        return 'active';
    }

    public function getUsagePercentageAttribute(): float
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return min(100, ($this->used_count / $this->usage_limit) * 100);
    }

    public function getRemainingUsageAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }

        return max(0, $this->usage_limit - $this->used_count);
    }
}

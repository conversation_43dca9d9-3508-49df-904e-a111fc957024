@extends('layouts.app')

@section('title', 'Detail Saldo UangTix')

@push('styles')
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Chart.js for analytics -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- UangTix Balance CSS -->
<link rel="stylesheet" href="{{ asset('css/uangtix.css') }}?v={{ time() }}">
<style>
/* UangTix Balance Page Styles */
.balance-page-container {
    --uangtix-primary-blue: #0066CC;
    --uangtix-primary-green: #00AA5B;
    --uangtix-secondary-blue: #E6F3FF;
    --uangtix-text-dark: #1A1A1A;
    --uangtix-text-gray: #666666;
    --uangtix-bg-light: #F8FAFC;
    --uangtix-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --uangtix-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --uangtix-border-radius: 16px;

    background: var(--uangtix-bg-light);
    min-height: 100vh;
    padding-bottom: 100px;
    font-family: 'Poppins', 'DM Sans', sans-serif;
}

/* Header Section */
.balance-header {
    background: linear-gradient(135deg, var(--uangtix-primary-blue) 0%, var(--uangtix-primary-green) 100%);
    padding: 24px 16px;
    border-radius: var(--uangtix-border-radius);
    margin-bottom: 24px;
    position: relative;
    overflow: hidden;
}

.balance-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(60px);
}

.balance-header-content {
    position: relative;
    z-index: 2;
}

.balance-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.balance-header-title {
    color: white;
    font-size: 24px;
    font-weight: 700;
    margin: 0;
}

.balance-header-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin: 4px 0 0 0;
}

.balance-header-actions {
    display: flex;
    gap: 12px;
}

.balance-header-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    color: white;
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
}

.balance-header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
}

/* Main Balance Card */
.main-balance-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: var(--uangtix-border-radius);
    padding: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.main-balance-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 12px;
}

.main-balance-amount {
    color: white;
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 8px;
}

.main-balance-currency {
    font-size: 24px;
    opacity: 0.8;
    font-weight: 500;
}

.main-balance-idr {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 24px;
}

.balance-status-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--uangtix-shadow-medium);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--stat-color, var(--uangtix-primary-blue));
}

.stat-card.earned::before { background: #10B981; }
.stat-card.spent::before { background: #EF4444; }
.stat-card.deposited::before { background: #3B82F6; }
.stat-card.withdrawn::before { background: #F59E0B; }

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--uangtix-text-gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: white;
}

.stat-icon.earned { background: #10B981; }
.stat-icon.spent { background: #EF4444; }
.stat-icon.deposited { background: #3B82F6; }
.stat-icon.withdrawn { background: #F59E0B; }

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin-bottom: 4px;
}

.stat-value-idr {
    font-size: 14px;
    color: var(--uangtix-text-gray);
    font-weight: 500;
}

/* Chart Section */
.chart-section {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
    margin-bottom: 32px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.chart-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
}

.chart-filters {
    display: flex;
    gap: 8px;
}

.chart-filter-btn {
    background: transparent;
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
    color: var(--uangtix-text-gray);
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-filter-btn.active,
.chart-filter-btn:hover {
    border-color: var(--uangtix-primary-blue);
    color: var(--uangtix-primary-blue);
    background: var(--uangtix-secondary-blue);
}

/* Info Cards */
.info-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    box-shadow: var(--uangtix-shadow-light);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.info-card-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: var(--uangtix-secondary-blue);
    color: var(--uangtix-primary-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.info-card-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin: 0;
}

.info-table {
    width: 100%;
}

.info-table tr {
    border-bottom: 1px solid #F3F4F6;
}

.info-table tr:last-child {
    border-bottom: none;
}

.info-table td {
    padding: 12px 0;
    font-size: 14px;
}

.info-table td:first-child {
    font-weight: 600;
    color: var(--uangtix-text-gray);
    width: 40%;
}

.info-table td:last-child {
    color: var(--uangtix-text-dark);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .balance-page-container {
        padding: 16px;
    }

    .balance-header {
        margin: 0 -16px 24px;
        border-radius: 0;
    }

    .balance-header-top {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .balance-header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .main-balance-amount {
        font-size: 36px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .chart-filters {
        flex-wrap: wrap;
    }

    .info-cards-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .balance-header-actions {
        flex-direction: column;
        gap: 8px;
    }

    .balance-header-btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
@endpush

@section('content')
<div class="balance-page-container">
    <!-- Header -->
    <div class="balance-header">
        <div class="balance-header-content">
            <div class="balance-header-top">
                <div>
                    <h1 class="balance-header-title">Detail Saldo UangTix</h1>
                    <p class="balance-header-subtitle">Informasi lengkap tentang saldo dan transaksi UangTix Anda</p>
                </div>
                <div class="balance-header-actions">
                    <a href="{{ route('uangtix.index') }}" class="balance-header-btn">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                    <a href="{{ route('uangtix.transactions') }}" class="balance-header-btn">
                        <i class="fas fa-history"></i> Riwayat
                    </a>
                </div>
            </div>

            <!-- Main Balance Display -->
            <div class="main-balance-card">
                <div class="main-balance-label">Saldo UangTix Anda</div>
                <div class="main-balance-amount">
                    {{ number_format($balance->balance ?? 0, 0, ',', '.') }}
                    <span class="main-balance-currency">UTX</span>
                </div>
                <div class="main-balance-idr">
                    ≈ Rp {{ number_format(($balance->balance ?? 0) * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}
                </div>
                <div class="balance-status-badge">
                    <i class="fas fa-{{ $balance->is_active ? 'check-circle' : 'times-circle' }}"></i>
                    {{ $balance->is_active ? 'Akun Aktif' : 'Akun Nonaktif' }}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card earned">
            <div class="stat-header">
                <div class="stat-title">Total Earned</div>
                <div class="stat-icon earned">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>
            <div class="stat-value">{{ number_format($balance->total_earned, 0, ',', '.') }} UTX</div>
            <div class="stat-value-idr">≈ Rp {{ number_format($balance->total_earned * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}</div>
        </div>

        <div class="stat-card spent">
            <div class="stat-header">
                <div class="stat-title">Total Spent</div>
                <div class="stat-icon spent">
                    <i class="fas fa-arrow-down"></i>
                </div>
            </div>
            <div class="stat-value">{{ number_format($balance->total_spent, 0, ',', '.') }} UTX</div>
            <div class="stat-value-idr">≈ Rp {{ number_format($balance->total_spent * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}</div>
        </div>

        <div class="stat-card deposited">
            <div class="stat-header">
                <div class="stat-title">Total Deposited</div>
                <div class="stat-icon deposited">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
            <div class="stat-value">{{ number_format($balance->total_deposited, 0, ',', '.') }} UTX</div>
            <div class="stat-value-idr">≈ Rp {{ number_format($balance->total_deposited * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}</div>
        </div>

        <div class="stat-card withdrawn">
            <div class="stat-header">
                <div class="stat-title">Total Withdrawn</div>
                <div class="stat-icon withdrawn">
                    <i class="fas fa-minus"></i>
                </div>
            </div>
            <div class="stat-value">{{ number_format($balance->total_withdrawn, 0, ',', '.') }} UTX</div>
            <div class="stat-value-idr">≈ Rp {{ number_format($balance->total_withdrawn * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}</div>
        </div>
    </div>

    <!-- Analytics Chart -->
    <div class="chart-section">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-line"></i>
                Analisis Transaksi
            </div>
            <div class="chart-filters">
                <button class="chart-filter-btn active" data-period="7d">7 Hari</button>
                <button class="chart-filter-btn" data-period="30d">30 Hari</button>
                <button class="chart-filter-btn" data-period="90d">90 Hari</button>
            </div>
        </div>
        <div style="position: relative; height: 300px;">
            <canvas id="balanceChart"></canvas>
        </div>
    </div>

    <!-- Monthly Statistics -->
    @if(isset($monthlyStats) && !empty($monthlyStats))
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        Statistik Bulanan
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($monthlyStats as $month => $stats)
                        <div class="col-md-4 mb-3">
                            <div class="border rounded p-3">
                                <h6 class="font-weight-bold text-primary">{{ $month }}</h6>
                                <div class="small">
                                    <div class="d-flex justify-content-between">
                                        <span>Transaksi:</span>
                                        <span class="font-weight-bold">{{ $stats['transactions'] }}</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total In:</span>
                                        <span class="text-success">+{{ number_format($stats['total_in'], 0, ',', '.') }} UTX</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Total Out:</span>
                                        <span class="text-danger">-{{ number_format($stats['total_out'], 0, ',', '.') }} UTX</span>
                                    </div>
                                    <hr class="my-2">
                                    <div class="d-flex justify-content-between">
                                        <span class="font-weight-bold">Net:</span>
                                        <span class="font-weight-bold text-{{ $stats['net'] >= 0 ? 'success' : 'danger' }}">
                                            {{ $stats['net'] >= 0 ? '+' : '' }}{{ number_format($stats['net'], 0, ',', '.') }} UTX
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Information Cards -->
    <div class="info-cards-grid">
        <div class="info-card">
            <div class="info-card-header">
                <div class="info-card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="info-card-title">Informasi Akun</h3>
            </div>
            <table class="info-table">
                <tr>
                    <td>Status Akun:</td>
                    <td>
                        <span style="color: {{ $balance->is_active ? '#10B981' : '#EF4444' }}; font-weight: 600;">
                            <i class="fas fa-{{ $balance->is_active ? 'check-circle' : 'times-circle' }}"></i>
                            {{ $balance->is_active ? 'Aktif' : 'Nonaktif' }}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td>Dibuat:</td>
                    <td>{{ $balance->created_at->format('d/m/Y H:i') }}</td>
                </tr>
                <tr>
                    <td>Terakhir Update:</td>
                    <td>{{ $balance->updated_at->format('d/m/Y H:i') }}</td>
                </tr>
                <tr>
                    <td>ID Akun:</td>
                    <td><code style="background: #F3F4F6; padding: 2px 6px; border-radius: 4px; font-size: 12px;">{{ $balance->id }}</code></td>
                </tr>
            </table>
        </div>

        <div class="info-card">
            <div class="info-card-header">
                <div class="info-card-icon">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h3 class="info-card-title">Informasi Kurs</h3>
            </div>
            <table class="info-table">
                <tr>
                    <td>IDR ke UangTix:</td>
                    <td><strong>1 IDR = {{ number_format($exchangeRate->rate_idr_to_uangtix ?? 1, 4) }} UTX</strong></td>
                </tr>
                <tr>
                    <td>UangTix ke IDR:</td>
                    <td><strong>1 UTX = Rp {{ number_format($exchangeRate->rate_uangtix_to_idr ?? 1, 0, ',', '.') }}</strong></td>
                </tr>
                <tr>
                    <td>Min Deposit:</td>
                    <td>Rp {{ number_format($exchangeRate->min_deposit_idr ?? 10000, 0, ',', '.') }}</td>
                </tr>
                <tr>
                    <td>Min Penarikan:</td>
                    <td>{{ number_format($exchangeRate->min_withdrawal_uangtix ?? 1000, 0, ',', '.') }} UTX</td>
                </tr>
            </table>
        </div>

        <!-- Quick Actions Card -->
        <div class="info-card">
            <div class="info-card-header">
                <div class="info-card-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3 class="info-card-title">Aksi Cepat</h3>
            </div>
            <div style="display: flex; flex-direction: column; gap: 12px;">
                <a href="{{ route('uangtix.index') }}" style="background: var(--uangtix-primary-blue); color: white; padding: 12px 16px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fas fa-plus"></i> Top Up Saldo
                </a>
                <a href="{{ route('uangtix.transactions') }}" style="background: var(--uangtix-primary-green); color: white; padding: 12px 16px; border-radius: 8px; text-decoration: none; text-align: center; font-weight: 600; transition: all 0.3s ease;">
                    <i class="fas fa-history"></i> Lihat Transaksi
                </a>
                <button onclick="exportTransactions()" style="background: #F59E0B; color: white; padding: 12px 16px; border-radius: 8px; border: none; font-weight: 600; cursor: pointer; transition: all 0.3s ease;">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Balance Chart
let balanceChart;

document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    setupChartFilters();
    setupQuickActions();
});

function initializeChart() {
    const ctx = document.getElementById('balanceChart').getContext('2d');

    // Sample data - in real app, this would come from backend
    const chartData = {
        labels: ['7 hari lalu', '6 hari lalu', '5 hari lalu', '4 hari lalu', '3 hari lalu', '2 hari lalu', 'Kemarin', 'Hari ini'],
        datasets: [{
            label: 'Saldo UangTix',
            data: [
                {{ $balance->balance - 50000 }},
                {{ $balance->balance - 30000 }},
                {{ $balance->balance - 20000 }},
                {{ $balance->balance - 10000 }},
                {{ $balance->balance - 5000 }},
                {{ $balance->balance + 10000 }},
                {{ $balance->balance + 15000 }},
                {{ $balance->balance }}
            ],
            borderColor: '#0066CC',
            backgroundColor: 'rgba(0, 102, 204, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#0066CC',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    };

    const config = {
        type: 'line',
        data: chartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#0066CC',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        label: function(context) {
                            return 'Saldo: ' + new Intl.NumberFormat('id-ID').format(context.parsed.y) + ' UTX';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#666666',
                        font: {
                            size: 12
                        }
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        color: '#666666',
                        font: {
                            size: 12
                        },
                        callback: function(value) {
                            return new Intl.NumberFormat('id-ID', {
                                notation: 'compact',
                                compactDisplay: 'short'
                            }).format(value) + ' UTX';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    };

    balanceChart = new Chart(ctx, config);
}

function setupChartFilters() {
    document.querySelectorAll('.chart-filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('.chart-filter-btn').forEach(b => b.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Update chart data based on period
            const period = this.dataset.period;
            updateChartData(period);
        });
    });
}

function updateChartData(period) {
    // In real app, this would fetch data from backend
    let labels, data;

    switch(period) {
        case '7d':
            labels = ['7 hari lalu', '6 hari lalu', '5 hari lalu', '4 hari lalu', '3 hari lalu', '2 hari lalu', 'Kemarin', 'Hari ini'];
            data = [
                {{ $balance->balance - 50000 }},
                {{ $balance->balance - 30000 }},
                {{ $balance->balance - 20000 }},
                {{ $balance->balance - 10000 }},
                {{ $balance->balance - 5000 }},
                {{ $balance->balance + 10000 }},
                {{ $balance->balance + 15000 }},
                {{ $balance->balance }}
            ];
            break;
        case '30d':
            labels = ['30 hari lalu', '25 hari lalu', '20 hari lalu', '15 hari lalu', '10 hari lalu', '5 hari lalu', 'Hari ini'];
            data = [
                {{ $balance->balance - 200000 }},
                {{ $balance->balance - 150000 }},
                {{ $balance->balance - 100000 }},
                {{ $balance->balance - 75000 }},
                {{ $balance->balance - 50000 }},
                {{ $balance->balance - 25000 }},
                {{ $balance->balance }}
            ];
            break;
        case '90d':
            labels = ['90 hari lalu', '75 hari lalu', '60 hari lalu', '45 hari lalu', '30 hari lalu', '15 hari lalu', 'Hari ini'];
            data = [
                {{ $balance->balance - 500000 }},
                {{ $balance->balance - 400000 }},
                {{ $balance->balance - 300000 }},
                {{ $balance->balance - 250000 }},
                {{ $balance->balance - 200000 }},
                {{ $balance->balance - 100000 }},
                {{ $balance->balance }}
            ];
            break;
    }

    balanceChart.data.labels = labels;
    balanceChart.data.datasets[0].data = data;
    balanceChart.update('active');
}

function setupQuickActions() {
    // Add hover effects to quick action buttons
    document.querySelectorAll('.info-card a, .info-card button').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

function exportTransactions() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting...';
    btn.disabled = true;

    // Simulate export process
    setTimeout(() => {
        // In real app, this would trigger actual export
        showToast('Export berhasil! File akan diunduh sebentar lagi.', 'success');

        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
@endpush

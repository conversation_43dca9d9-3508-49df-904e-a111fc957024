@extends('layouts.main')

@section('title', 'Payment Success - Demo')

@push('styles')
<style>
.success-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.success-card {
    background: white;
    border-radius: 2rem;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    text-align: center;
    max-width: 500px;
    width: 100%;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.success-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.success-message {
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.order-details {
    background: #f9fafb;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: left;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.detail-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: #10b981;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
    transform: translateY(-2px);
}

@media (max-width: 640px) {
    .success-card {
        padding: 2rem;
        margin: 1rem;
    }
    
    .success-title {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>
@endpush

@section('content')
<div class="success-container">
    <div class="success-card">
        <!-- Success Icon -->
        <div class="success-icon">
            <i data-lucide="check" class="w-10 h-10 text-white"></i>
        </div>
        
        <!-- Success Title -->
        <h1 class="success-title">Payment Successful!</h1>
        
        <!-- Success Message -->
        <p class="success-message">
            Thank you for your purchase. Your payment has been processed successfully and your tickets have been confirmed.
        </p>
        
        <!-- Order Details -->
        <div class="order-details">
            <h3 class="font-semibold text-gray-900 mb-3">Order Summary</h3>
            
            <div class="detail-row">
                <span class="text-gray-600">Order Number</span>
                <span class="font-mono">TXR-{{ date('Ymd') }}-001</span>
            </div>
            
            <div class="detail-row">
                <span class="text-gray-600">Event</span>
                <span>TechConf 2024</span>
            </div>
            
            <div class="detail-row">
                <span class="text-gray-600">Quantity</span>
                <span>1 ticket</span>
            </div>
            
            <div class="detail-row">
                <span class="text-gray-600">Payment Method</span>
                <span>Bank Transfer</span>
            </div>
            
            <div class="detail-row">
                <span class="font-semibold">Total Paid</span>
                <span class="font-semibold">Rp 255,000</span>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="/demo/purchase-modern" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4"></i>
                New Purchase
            </a>
            
            <a href="#" class="btn btn-primary" onclick="downloadTicket()">
                <i data-lucide="download" class="w-4 h-4"></i>
                Download E-Ticket
            </a>
        </div>
        
        <!-- Additional Info -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-start">
                <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5 mr-2 flex-shrink-0"></i>
                <div class="text-sm text-blue-800">
                    <p class="font-semibold mb-1">What's Next?</p>
                    <ul class="space-y-1">
                        <li>• Your e-ticket has been sent to your email</li>
                        <li>• Present your e-ticket at the event entrance</li>
                        <li>• Arrive 30 minutes before the event starts</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
<script>
// Initialize Lucide icons
document.addEventListener('DOMContentLoaded', function() {
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// Download ticket function
function downloadTicket() {
    // Show loading state
    const btn = event.target.closest('.btn');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i data-lucide="loader" class="w-4 h-4 animate-spin"></i> Generating...';
    
    // Simulate download process
    setTimeout(() => {
        // Create a dummy download
        const link = document.createElement('a');
        link.href = 'data:text/plain;charset=utf-8,This is a demo e-ticket for TechConf 2024';
        link.download = 'TechConf-2024-Ticket.txt';
        link.click();
        
        // Reset button
        btn.innerHTML = originalText;
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        // Show success message
        showNotification('E-ticket downloaded successfully!', 'success');
    }, 2000);
}

// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' : 
        type === 'error' ? 'bg-red-500 text-white' : 
        'bg-blue-500 text-white'
    }`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i data-lucide="${type === 'success' ? 'check-circle' : 'info'}" class="w-5 h-5 mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Initialize icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Show notification
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Hide and remove notification
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 300);
    }, 3000);
}
</script>
@endpush

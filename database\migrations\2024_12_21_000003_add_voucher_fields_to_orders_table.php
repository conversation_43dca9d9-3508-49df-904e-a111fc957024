<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Add voucher-related fields
            $table->foreignId('voucher_id')->nullable()->after('discount_code')->constrained()->onDelete('set null');
            $table->decimal('voucher_discount', 12, 2)->default(0)->after('voucher_id');
            $table->string('voucher_code')->nullable()->after('voucher_discount');
            
            // Update discount_amount to be voucher_discount for clarity
            // Keep both for backward compatibility
            $table->index(['voucher_id']);
            $table->index(['voucher_code']);
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['voucher_id']);
            $table->dropIndex(['voucher_id']);
            $table->dropIndex(['voucher_code']);
            $table->dropColumn(['voucher_id', 'voucher_discount', 'voucher_code']);
        });
    }
};

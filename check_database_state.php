<?php

require_once 'vendor/autoload.php';

// Load <PERSON> app
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

try {
    echo "Checking current database state...\n";
    
    // Check if users table exists and what columns it has
    if (Schema::hasTable('users')) {
        echo "✓ Users table exists\n";
        $userColumns = Schema::getColumnListing('users');
        echo "User columns: " . implode(', ', $userColumns) . "\n";
        
        // Check specifically for badge columns
        $badgeColumns = ['badge_level_id', 'badge_assigned_at', 'badge_expires_at', 'badge_duration_days', 'badge_auto_renew'];
        echo "\nBadge columns in users table:\n";
        foreach ($badgeColumns as $col) {
            echo "  $col: " . (in_array($col, $userColumns) ? 'EXISTS' : 'MISSING') . "\n";
        }
    } else {
        echo "✗ Users table does not exist\n";
    }
    
    // Check badge level tables
    echo "\nBadge level tables:\n";
    echo "  user_badge_level: " . (Schema::hasTable('user_badge_level') ? 'EXISTS' : 'MISSING') . "\n";
    echo "  badge_level: " . (Schema::hasTable('badge_level') ? 'EXISTS' : 'MISSING') . "\n";
    echo "  badge_level: " . (Schema::hasTable('badge_level') ? 'EXISTS' : 'MISSING') . "\n";
    
    // Check migrations table
    if (Schema::hasTable('migrations')) {
        echo "\n✓ Migrations table exists\n";
        $migrations = DB::table('migrations')->orderBy('batch')->get();
        echo "Recent migrations:\n";
        foreach ($migrations->take(-10) as $migration) {
            echo "  - {$migration->migration} (batch: {$migration->batch})\n";
        }
    } else {
        echo "\n✗ Migrations table does not exist\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

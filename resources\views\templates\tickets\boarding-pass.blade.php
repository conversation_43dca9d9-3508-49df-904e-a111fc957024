<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Ticket Boarding Pass - {{ $ticket->event->title }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .boarding-pass {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            position: relative;
        }
        
        .boarding-pass::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                #e5e7eb 0px,
                #e5e7eb 10px,
                transparent 10px,
                transparent 20px
            );
            transform: translateY(-1px);
            z-index: 10;
        }
        
        .boarding-pass::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -10px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            transform: translateY(-50%);
            z-index: 11;
        }
        
        .boarding-pass-right::after {
            left: auto;
            right: -10px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            text-align: center;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 10px solid #764ba2;
        }
        
        .airline-logo {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
            letter-spacing: 2px;
        }
        
        .boarding-pass-title {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            min-height: 300px;
        }
        
        .left-section {
            padding: 30px;
            border-right: 2px dashed #e5e7eb;
        }
        
        .right-section {
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
        }
        
        .passenger-info {
            margin-bottom: 25px;
        }
        
        .passenger-name {
            font-size: 24px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .passenger-email {
            color: #6b7280;
            font-size: 14px;
        }
        
        .flight-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .detail-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .detail-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }
        
        .event-info {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        
        .event-title {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .event-location {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .event-datetime {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }
        
        .qr-section {
            text-align: center;
        }
        
        .qr-code {
            background: white;
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 15px;
            display: inline-block;
        }
        
        .qr-code img {
            width: 120px;
            height: 120px;
            display: block;
        }
        
        .qr-instructions {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.5;
            max-width: 150px;
        }
        
        .ticket-number {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 1px;
            margin-bottom: 15px;
            display: inline-block;
        }
        
        .footer {
            background: #f8fafc;
            padding: 20px 30px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6b7280;
        }
        
        .footer-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .status-badge {
            background: #10b981;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 10px;
            letter-spacing: 0.5px;
        }
        
        .status-badge.used {
            background: #ef4444;
        }
        
        .status-badge.cancelled {
            background: #6b7280;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 60px;
            font-weight: 900;
            color: rgba(102, 126, 234, 0.05);
            pointer-events: none;
            z-index: 1;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-pass {
                box-shadow: none;
                max-width: none;
                border: 1px solid #e5e7eb;
            }
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .left-section {
                border-right: none;
                border-bottom: 2px dashed #e5e7eb;
            }
            
            .flight-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .footer {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-pass">
        <div class="watermark">TIXARA</div>
        
        <!-- Header -->
        <div class="header">
            <div class="airline-logo">TiXara</div>
            <div class="boarding-pass-title">E-Ticket Boarding Pass</div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Section -->
            <div class="left-section">
                <!-- Passenger Info -->
                <div class="passenger-info">
                    <div class="passenger-name">{{ $ticket->attendee_name }}</div>
                    <div class="passenger-email">{{ $ticket->order->customer_email }}</div>
                </div>
                
                <!-- Event Info -->
                <div class="event-info">
                    <div class="event-title">{{ $ticket->event->title }}</div>
                    <div class="event-location">
                        <i class="fas fa-map-marker-alt"></i>
                        {{ $ticket->event->location }}
                    </div>
                    <div class="event-datetime">
                        <span>
                            <i class="fas fa-calendar"></i>
                            {{ $ticket->event->start_date->format('d M Y') }}
                        </span>
                        <span>
                            <i class="fas fa-clock"></i>
                            {{ $ticket->event->start_date->format('H:i') }} WIB
                        </span>
                    </div>
                </div>
                
                <!-- Flight Details -->
                <div class="flight-details">
                    <div class="detail-item">
                        <div class="detail-label">Ticket Type</div>
                        <div class="detail-value">{{ $ticket->event->category->name ?? 'General' }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Seat/Gate</div>
                        <div class="detail-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Order Date</div>
                        <div class="detail-value">{{ $ticket->order->created_at->format('d M Y') }}</div>
                    </div>
                    
                    <div class="detail-item">
                        <div class="detail-label">Price</div>
                        <div class="detail-value">Rp {{ number_format($ticket->order->unit_price, 0, ',', '.') }}</div>
                    </div>
                </div>
            </div>
            
            <!-- Right Section -->
            <div class="right-section">
                <div class="ticket-number">{{ $ticket->ticket_number }}</div>
                
                <div class="qr-section">
                    <div class="qr-code">
                        {!! QrCode::size(120)->generate($ticket->qr_code) !!}
                    </div>
                    <div class="qr-instructions">
                        Scan this QR code at the venue entrance for quick check-in
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-left">
                <span class="status-badge {{ strtolower($ticket->status) }}">
                    {{ ucfirst($ticket->status) }}
                </span>
                <span>Generated: {{ now()->format('d M Y H:i') }}</span>
            </div>
            <div class="footer-right">
                <span>TiXara.my.id | Customer Service: +62 812-3456-7890</span>
            </div>
        </div>
    </div>
</body>
</html>

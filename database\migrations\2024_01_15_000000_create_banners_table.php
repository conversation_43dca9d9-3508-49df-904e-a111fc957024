<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('banners', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('subtitle')->nullable();
            $table->string('image_path');
            $table->string('mobile_image_path')->nullable();
            $table->string('link_url')->nullable();
            $table->string('button_text')->default('Lihat Detail');
            $table->string('button_color')->default('primary');
            $table->boolean('is_active')->default(true);
            $table->datetime('start_date')->nullable();
            $table->datetime('end_date')->nullable();
            $table->integer('sort_order')->default(0);
            $table->string('text_position')->default('left'); // left, center, right
            $table->string('text_color')->default('white'); // white, dark
            $table->boolean('show_overlay')->default(true);
            $table->string('overlay_color')->default('dark'); // dark, light, gradient
            $table->decimal('overlay_opacity', 3, 2)->default(0.50);
            $table->json('animation_settings')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('banners');
    }
};

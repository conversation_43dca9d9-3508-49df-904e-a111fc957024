<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist E-Ticket</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #fafafa;
            color: #1a1a1a;
            padding: 40px 20px;
            line-height: 1.5;
        }

        .ticket-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 2px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ticket-header {
            padding: 32px 32px 24px;
            border-bottom: 1px solid #e5e5e5;
        }

        .brand {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 1px;
            text-transform: uppercase;
            color: #666;
            margin-bottom: 16px;
        }

        .event-title {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .event-subtitle {
            font-size: 14px;
            color: #666;
            font-weight: 400;
        }

        .ticket-body {
            padding: 32px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            color: #999;
            margin-bottom: 4px;
        }

        .info-value {
            font-size: 14px;
            font-weight: 400;
            color: #1a1a1a;
        }

        .ticket-number-section {
            text-align: center;
            padding: 24px 0;
            border-top: 1px solid #e5e5e5;
            border-bottom: 1px solid #e5e5e5;
            margin: 24px 0;
        }

        .ticket-number-label {
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            color: #999;
            margin-bottom: 8px;
        }

        .ticket-number {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            letter-spacing: 1px;
        }

        .qr-section {
            text-align: center;
            margin: 32px 0;
        }

        .qr-code {
            display: inline-block;
            padding: 16px;
            background: #f8f8f8;
            border-radius: 2px;
            margin-bottom: 16px;
        }

        .qr-instruction {
            font-size: 12px;
            color: #666;
            font-weight: 400;
        }

        .ticket-footer {
            padding: 24px 32px;
            background: #f8f8f8;
            border-top: 1px solid #e5e5e5;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            font-size: 11px;
            color: #666;
        }

        .footer-item {
            display: flex;
            flex-direction: column;
        }

        .footer-label {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .footer-value {
            font-weight: 400;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            background: #f0f9ff;
            color: #0369a1;
            border-radius: 2px;
            font-size: 10px;
            font-weight: 500;
            letter-spacing: 0.5px;
            text-transform: uppercase;
        }

        @media (max-width: 640px) {
            body {
                padding: 20px 16px;
            }

            .ticket-header,
            .ticket-body,
            .ticket-footer {
                padding: 24px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .ticket-container {
                box-shadow: none;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <!-- Header -->
        <div class="ticket-header">
            <div class="brand">TiXara E-Ticket</div>
            <h1 class="event-title">{{ $ticket['event_title'] ?? 'Sample Event Title' }}</h1>
            <p class="event-subtitle">{{ $ticket['venue_name'] ?? 'Sample Venue Location' }}</p>
        </div>

        <!-- Body -->
        <div class="ticket-body">
            <!-- Event Information -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Date</div>
                    <div class="info-value">{{ $ticket['event_date'] ?? 'December 31, 2024' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Time</div>
                    <div class="info-value">{{ $ticket['event_time'] ?? '7:00 PM' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Ticket Holder</div>
                    <div class="info-value">{{ $ticket['buyer_name'] ?? 'John Doe' }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Price</div>
                    <div class="info-value">{{ $ticket['formatted_price'] ?? 'Rp 150.000' }}</div>
                </div>
            </div>

            <!-- Ticket Number -->
            <div class="ticket-number-section">
                <div class="ticket-number-label">Ticket Number</div>
                <div class="ticket-number">{{ $ticket['ticket_number'] ?? 'TIX-MIN-001' }}</div>
                <div class="ticket-number-label" style="margin-top: 8px;">Boarding Pass ID</div>
                <div class="ticket-number" style="font-size: 14px; color: #666;">{{ $ticket['boarding_pass_id'] ?? 'BP250127ABC123' }}</div>
            </div>

            <!-- QR Code -->
            <div class="qr-section">
                <div class="qr-code">
                    {!! QrCode::size(120)->generate($ticket['qr_code'] ?? 'SAMPLE-QR-CODE') !!}
                </div>
                <div class="qr-instruction">
                    Present this QR code at the venue entrance
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="ticket-footer">
            <div class="footer-grid">
                <div class="footer-item">
                    <div class="footer-label">Status</div>
                    <div class="footer-value">
                        <span class="status-badge">Valid</span>
                    </div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Generated</div>
                    <div class="footer-value">{{ now()->format('M d, Y H:i') }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Contact</div>
                    <div class="footer-value"><EMAIL></div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Website</div>
                    <div class="footer-value">tixara.my.id</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

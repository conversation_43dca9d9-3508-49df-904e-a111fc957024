@extends('layouts.admin')

@section('title', 'Ad Subscriptions Management')

@push('styles')
<style>
.subscription-card {
    transition: all 0.3s ease;
    border: 1px solid #E5E7EB;
}

.subscription-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.plan-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.plan-basic { background: rgba(59, 130, 246, 0.1); color: #3B82F6; border: 1px solid rgba(59, 130, 246, 0.2); }
.plan-premium { background: rgba(168, 85, 247, 0.1); color: #A855F7; border: 1px solid rgba(168, 85, 247, 0.2); }
.plan-enterprise { background: rgba(251, 191, 36, 0.1); color: #F59E0B; border: 1px solid rgba(251, 191, 36, 0.2); }

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active { background: rgba(16, 185, 129, 0.1); color: #10B981; border: 1px solid rgba(16, 185, 129, 0.2); }
.status-expired { background: rgba(239, 68, 68, 0.1); color: #EF4444; border: 1px solid rgba(239, 68, 68, 0.2); }
.status-cancelled { background: rgba(107, 114, 128, 0.1); color: #6B7280; border: 1px solid rgba(107, 114, 128, 0.2); }
.status-suspended { background: rgba(251, 191, 36, 0.1); color: #F59E0B; border: 1px solid rgba(251, 191, 36, 0.2); }

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.filters-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #E5E7EB;
    margin-bottom: 24px;
}

.subscription-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-top: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #F9FAFB;
    border-radius: 6px;
    font-size: 14px;
}

.detail-label {
    color: #6B7280;
    font-weight: 500;
}

.detail-value {
    color: #1F2937;
    font-weight: 600;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .subscription-details {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Ad Subscriptions Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Manage advertising subscription plans and billing</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('admin.ads.index') }}" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4"></i>
                Back to Ads
            </a>
            <button onclick="exportSubscriptions()" class="btn btn-secondary">
                <i data-lucide="download" class="w-4 h-4"></i>
                Export
            </button>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon bg-blue-100 text-blue-600">
                <i data-lucide="credit-card" class="w-5 h-5"></i>
            </div>
            <div class="stat-value">{{ number_format($stats['total_subscriptions'] ?? 0) }}</div>
            <div class="stat-label">Total Subscriptions</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon bg-green-100 text-green-600">
                <i data-lucide="check-circle" class="w-5 h-5"></i>
            </div>
            <div class="stat-value">{{ number_format($stats['active_subscriptions'] ?? 0) }}</div>
            <div class="stat-label">Active Subscriptions</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon bg-red-100 text-red-600">
                <i data-lucide="x-circle" class="w-5 h-5"></i>
            </div>
            <div class="stat-value">{{ number_format($stats['expired_subscriptions'] ?? 0) }}</div>
            <div class="stat-label">Expired Subscriptions</div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon bg-emerald-100 text-emerald-600">
                <i data-lucide="dollar-sign" class="w-5 h-5"></i>
            </div>
            <div class="stat-value">Rp {{ number_format($stats['monthly_revenue'] ?? 0, 0, ',', '.') }}</div>
            <div class="stat-label">Monthly Revenue</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="Search subscriptions..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="expired" {{ request('status') === 'expired' ? 'selected' : '' }}>Expired</option>
                    <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Plan Type</label>
                <select name="plan_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Plans</option>
                    <option value="basic" {{ request('plan_type') === 'basic' ? 'selected' : '' }}>Basic</option>
                    <option value="premium" {{ request('plan_type') === 'premium' ? 'selected' : '' }}>Premium</option>
                    <option value="enterprise" {{ request('plan_type') === 'enterprise' ? 'selected' : '' }}>Enterprise</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Billing Cycle</label>
                <select name="billing_cycle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">All Cycles</option>
                    <option value="monthly" {{ request('billing_cycle') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                    <option value="yearly" {{ request('billing_cycle') === 'yearly' ? 'selected' : '' }}>Yearly</option>
                </select>
            </div>
            
            <div class="flex items-end gap-2">
                <button type="submit" class="btn btn-primary flex-1">
                    <i data-lucide="search" class="w-4 h-4"></i>
                    Filter
                </button>
                <a href="{{ route('admin.ads.subscriptions') }}" class="btn btn-secondary">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </a>
            </div>
        </form>
    </div>

    <!-- Subscriptions List -->
    @if($subscriptions->count() > 0)
        <div class="space-y-4">
            @foreach($subscriptions as $subscription)
                <div class="subscription-card bg-white rounded-xl shadow-sm p-6">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <!-- Subscription Header -->
                            <div class="flex items-start justify-between mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $subscription->plan_name }}</h3>
                                    <p class="text-gray-600 text-sm mb-2">{{ $subscription->plan_description }}</p>
                                    <div class="flex items-center gap-3 mb-2">
                                        <span class="plan-badge plan-{{ $subscription->plan_type }}">
                                            <i data-lucide="package" class="w-3 h-3"></i>
                                            {{ ucfirst($subscription->plan_type) }}
                                        </span>
                                        <span class="status-badge status-{{ $subscription->status }}">
                                            <i data-lucide="circle" class="w-3 h-3"></i>
                                            {{ ucfirst($subscription->status) }}
                                        </span>
                                        <span class="text-xs text-gray-500">
                                            {{ ucfirst($subscription->billing_cycle) }} Billing
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <span>User: {{ $subscription->user->name }}</span>
                                        <span class="ml-3">Email: {{ $subscription->user->email }}</span>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-900">
                                        Rp {{ number_format($subscription->billing_cycle === 'monthly' ? $subscription->monthly_price : $subscription->yearly_price, 0, ',', '.') }}
                                    </div>
                                    <div class="text-sm text-gray-500">per {{ $subscription->billing_cycle === 'monthly' ? 'month' : 'year' }}</div>
                                </div>
                            </div>
                            
                            <!-- Subscription Details -->
                            <div class="subscription-details">
                                <div class="detail-item">
                                    <span class="detail-label">Max Ads:</span>
                                    <span class="detail-value">{{ number_format($subscription->max_ads) }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Max Impressions/Day:</span>
                                    <span class="detail-value">{{ number_format($subscription->max_impressions_per_day) }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Max Clicks/Day:</span>
                                    <span class="detail-value">{{ number_format($subscription->max_clicks_per_day) }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Priority Placement:</span>
                                    <span class="detail-value">
                                        @if($subscription->priority_placement)
                                            <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                                        @else
                                            <i data-lucide="x" class="w-4 h-4 text-red-600"></i>
                                        @endif
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Analytics Access:</span>
                                    <span class="detail-value">
                                        @if($subscription->analytics_access)
                                            <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                                        @else
                                            <i data-lucide="x" class="w-4 h-4 text-red-600"></i>
                                        @endif
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Custom Targeting:</span>
                                    <span class="detail-value">
                                        @if($subscription->custom_targeting)
                                            <i data-lucide="check" class="w-4 h-4 text-green-600"></i>
                                        @else
                                            <i data-lucide="x" class="w-4 h-4 text-red-600"></i>
                                        @endif
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Start Date:</span>
                                    <span class="detail-value">{{ $subscription->starts_at->format('M d, Y') }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Expires:</span>
                                    <span class="detail-value">{{ $subscription->expires_at->format('M d, Y') }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Amount Paid:</span>
                                    <span class="detail-value">Rp {{ number_format($subscription->amount_paid, 0, ',', '.') }}</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Auto Renewal:</span>
                                    <span class="detail-value">
                                        @if($subscription->auto_renewal)
                                            <span class="text-green-600 font-medium">Enabled</span>
                                        @else
                                            <span class="text-red-600 font-medium">Disabled</span>
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex flex-col gap-2 ml-6">
                            <a href="{{ route('admin.users.show', $subscription->user) }}" class="btn btn-sm btn-secondary">
                                <i data-lucide="user" class="w-3 h-3"></i>
                                View User
                            </a>
                            
                            @if($subscription->status === 'active')
                                <button onclick="suspendSubscription({{ $subscription->id }})" class="btn btn-sm btn-warning">
                                    <i data-lucide="pause" class="w-3 h-3"></i>
                                    Suspend
                                </button>
                            @elseif($subscription->status === 'suspended')
                                <button onclick="activateSubscription({{ $subscription->id }})" class="btn btn-sm btn-success">
                                    <i data-lucide="play" class="w-3 h-3"></i>
                                    Activate
                                </button>
                            @endif
                            
                            @if($subscription->status !== 'cancelled')
                                <button onclick="cancelSubscription({{ $subscription->id }})" class="btn btn-sm btn-danger">
                                    <i data-lucide="x-circle" class="w-3 h-3"></i>
                                    Cancel
                                </button>
                            @endif
                            
                            <button onclick="viewSubscriptionDetails({{ $subscription->id }})" class="btn btn-sm btn-primary">
                                <i data-lucide="eye" class="w-3 h-3"></i>
                                Details
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($subscriptions->hasPages())
            <div class="flex justify-center">
                {{ $subscriptions->appends(request()->query())->links() }}
            </div>
        @endif
    @else
        <div class="text-center py-12 bg-white rounded-xl shadow-sm">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="credit-card" class="w-8 h-8 text-gray-400"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No subscriptions found</h3>
            <p class="text-gray-500">No ad subscriptions match your current filters.</p>
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function suspendSubscription(id) {
    if (!confirm('Are you sure you want to suspend this subscription?')) return;
    
    fetch(`/admin/ads/subscriptions/${id}/suspend`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    });
}

function activateSubscription(id) {
    if (!confirm('Are you sure you want to activate this subscription?')) return;
    
    fetch(`/admin/ads/subscriptions/${id}/activate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    });
}

function cancelSubscription(id) {
    const reason = prompt('Please provide a reason for cancellation:');
    if (!reason) return;
    
    fetch(`/admin/ads/subscriptions/${id}/cancel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    });
}

function viewSubscriptionDetails(id) {
    // Open subscription details in modal or new page
    window.open(`/admin/ads/subscriptions/${id}`, '_blank');
}

function exportSubscriptions() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '/admin/ads/subscriptions?' + params.toString();
}

// Auto-refresh every 60 seconds
setInterval(() => {
    location.reload();
}, 60000);
</script>
@endpush

@props([
    'id' => 'qrScanner',
    'onScan' => null,
    'onError' => null,
    'width' => 300,
    'height' => 300,
    'facingMode' => 'environment', // 'user' for front camera, 'environment' for back camera
    'showToggleCamera' => true,
    'showTorch' => true,
    'autoStart' => false
])

<div class="qr-scanner-container" id="{{ $id }}Container">
    <!-- Scanner Interface -->
    <div class="relative bg-black rounded-xl overflow-hidden" style="width: {{ $width }}px; height: {{ $height }}px;">
        <!-- Video Element -->
        <video id="{{ $id }}Video" 
               class="w-full h-full object-cover" 
               style="width: {{ $width }}px; height: {{ $height }}px;"
               playsinline>
        </video>
        
        <!-- Scanning Overlay -->
        <div class="absolute inset-0 pointer-events-none">
            <!-- Corner Brackets -->
            <div class="absolute top-4 left-4 w-8 h-8 border-l-4 border-t-4 border-green-400 rounded-tl-lg"></div>
            <div class="absolute top-4 right-4 w-8 h-8 border-r-4 border-t-4 border-green-400 rounded-tr-lg"></div>
            <div class="absolute bottom-4 left-4 w-8 h-8 border-l-4 border-b-4 border-green-400 rounded-bl-lg"></div>
            <div class="absolute bottom-4 right-4 w-8 h-8 border-r-4 border-b-4 border-green-400 rounded-br-lg"></div>
            
            <!-- Scanning Line -->
            <div class="absolute inset-x-4 top-1/2 h-0.5 bg-green-400 opacity-75 animate-pulse"></div>
            
            <!-- Center Target -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div class="w-32 h-32 border-2 border-green-400 rounded-lg opacity-50"></div>
            </div>
        </div>
        
        <!-- Status Overlay -->
        <div id="{{ $id }}Status" class="absolute inset-0 bg-black bg-opacity-75 flex items-center justify-center hidden">
            <div class="text-center text-white">
                <div class="mb-4">
                    <i class="fas fa-spinner fa-spin text-3xl"></i>
                </div>
                <p class="text-sm">Initializing camera...</p>
            </div>
        </div>
        
        <!-- Error Overlay -->
        <div id="{{ $id }}Error" class="absolute inset-0 bg-red-500 bg-opacity-90 flex items-center justify-center hidden">
            <div class="text-center text-white p-4">
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle text-3xl"></i>
                </div>
                <p class="text-sm font-medium mb-2">Camera Error</p>
                <p class="text-xs opacity-90" id="{{ $id }}ErrorMessage">Unable to access camera</p>
            </div>
        </div>
        
        <!-- Success Overlay -->
        <div id="{{ $id }}Success" class="absolute inset-0 bg-green-500 bg-opacity-90 flex items-center justify-center hidden">
            <div class="text-center text-white">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-3xl"></i>
                </div>
                <p class="text-sm font-medium">QR Code Detected!</p>
            </div>
        </div>
    </div>
    
    <!-- Controls -->
    <div class="flex justify-center space-x-4 mt-4">
        <!-- Start/Stop Button -->
        <button id="{{ $id }}StartBtn" 
                class="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
            <i class="fas fa-play mr-2"></i>
            <span>Start Scanner</span>
        </button>
        
        <button id="{{ $id }}StopBtn" 
                class="flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 hidden">
            <i class="fas fa-stop mr-2"></i>
            <span>Stop Scanner</span>
        </button>
        
        @if($showToggleCamera)
        <!-- Camera Toggle -->
        <button id="{{ $id }}ToggleCamera" 
                class="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 hidden">
            <i class="fas fa-camera-rotate mr-2"></i>
            <span>Flip Camera</span>
        </button>
        @endif
        
        @if($showTorch)
        <!-- Torch Toggle -->
        <button id="{{ $id }}ToggleTorch" 
                class="flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors duration-200 hidden">
            <i class="fas fa-flashlight mr-2"></i>
            <span>Torch</span>
        </button>
        @endif
    </div>
    
    <!-- Scanner Info -->
    <div class="mt-4 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
            Position the QR code within the frame to scan
        </p>
        <div id="{{ $id }}Info" class="mt-2 text-xs text-gray-500 dark:text-gray-500">
            Camera: <span id="{{ $id }}CameraInfo">Not initialized</span>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scannerId = '{{ $id }}';
    const videoElement = document.getElementById(scannerId + 'Video');
    const startBtn = document.getElementById(scannerId + 'StartBtn');
    const stopBtn = document.getElementById(scannerId + 'StopBtn');
    const toggleCameraBtn = document.getElementById(scannerId + 'ToggleCamera');
    const toggleTorchBtn = document.getElementById(scannerId + 'ToggleTorch');
    const statusOverlay = document.getElementById(scannerId + 'Status');
    const errorOverlay = document.getElementById(scannerId + 'Error');
    const successOverlay = document.getElementById(scannerId + 'Success');
    const errorMessage = document.getElementById(scannerId + 'ErrorMessage');
    const cameraInfo = document.getElementById(scannerId + 'CameraInfo');
    
    let html5QrCode = null;
    let currentFacingMode = '{{ $facingMode }}';
    let isScanning = false;
    let torchEnabled = false;
    let availableCameras = [];
    let currentCameraIndex = 0;
    
    // Initialize scanner
    async function initializeScanner() {
        try {
            // Get available cameras
            const cameras = await Html5Qrcode.getCameras();
            availableCameras = cameras;
            
            if (cameras.length === 0) {
                throw new Error('No cameras found');
            }
            
            // Show camera toggle if multiple cameras available
            if (cameras.length > 1 && toggleCameraBtn) {
                toggleCameraBtn.classList.remove('hidden');
            }
            
            updateCameraInfo();
            
        } catch (error) {
            console.error('Failed to initialize scanner:', error);
            showError('Failed to initialize camera: ' + error.message);
        }
    }
    
    // Start scanning
    async function startScanning() {
        if (isScanning) return;
        
        try {
            showStatus('Starting camera...');
            
            html5QrCode = new Html5Qrcode(scannerId + 'Video');
            
            const cameraId = availableCameras.length > 0 ? 
                availableCameras[currentCameraIndex].id : 
                { facingMode: currentFacingMode };
            
            const config = {
                fps: 10,
                qrbox: { width: 200, height: 200 },
                aspectRatio: 1.0,
                disableFlip: false,
            };
            
            await html5QrCode.start(
                cameraId,
                config,
                onScanSuccess,
                onScanFailure
            );
            
            isScanning = true;
            hideStatus();
            updateButtons();
            updateCameraInfo();
            
            // Show torch button if supported
            if (toggleTorchBtn) {
                checkTorchSupport();
            }
            
        } catch (error) {
            console.error('Failed to start scanning:', error);
            showError('Failed to start camera: ' + error.message);
            isScanning = false;
            updateButtons();
        }
    }
    
    // Stop scanning
    async function stopScanning() {
        if (!isScanning || !html5QrCode) return;
        
        try {
            await html5QrCode.stop();
            html5QrCode.clear();
            html5QrCode = null;
            isScanning = false;
            torchEnabled = false;
            updateButtons();
            updateCameraInfo();
            hideAllOverlays();
            
        } catch (error) {
            console.error('Failed to stop scanning:', error);
        }
    }
    
    // Toggle camera
    async function toggleCamera() {
        if (!isScanning || availableCameras.length <= 1) return;
        
        await stopScanning();
        currentCameraIndex = (currentCameraIndex + 1) % availableCameras.length;
        await startScanning();
    }
    
    // Toggle torch
    async function toggleTorch() {
        if (!isScanning || !html5QrCode) return;
        
        try {
            const capabilities = html5QrCode.getRunningTrackCapabilities();
            if (capabilities.torch) {
                torchEnabled = !torchEnabled;
                await html5QrCode.applyVideoConstraints({
                    advanced: [{ torch: torchEnabled }]
                });
                updateTorchButton();
            }
        } catch (error) {
            console.error('Failed to toggle torch:', error);
        }
    }
    
    // Check torch support
    function checkTorchSupport() {
        if (!html5QrCode) return;
        
        try {
            const capabilities = html5QrCode.getRunningTrackCapabilities();
            if (capabilities.torch && toggleTorchBtn) {
                toggleTorchBtn.classList.remove('hidden');
            }
        } catch (error) {
            console.log('Torch not supported');
        }
    }
    
    // Scan success callback
    function onScanSuccess(decodedText, decodedResult) {
        console.log('QR Code detected:', decodedText);
        
        showSuccess();
        
        // Execute custom callback if provided
        @if($onScan)
            {!! $onScan !!}(decodedText, decodedResult);
        @else
            // Default behavior - you can customize this
            if (window.handleQRScan) {
                window.handleQRScan(decodedText, decodedResult);
            } else {
                alert('QR Code detected: ' + decodedText);
            }
        @endif
        
        // Hide success overlay after 2 seconds
        setTimeout(() => {
            hideSuccess();
        }, 2000);
    }
    
    // Scan failure callback
    function onScanFailure(error) {
        // This is called continuously when no QR code is detected
        // We don't want to show errors for this
        @if($onError)
            {!! $onError !!}(error);
        @endif
    }
    
    // UI Helper functions
    function showStatus(message = 'Initializing camera...') {
        hideAllOverlays();
        statusOverlay.querySelector('p').textContent = message;
        statusOverlay.classList.remove('hidden');
    }
    
    function hideStatus() {
        statusOverlay.classList.add('hidden');
    }
    
    function showError(message) {
        hideAllOverlays();
        errorMessage.textContent = message;
        errorOverlay.classList.remove('hidden');
    }
    
    function hideError() {
        errorOverlay.classList.add('hidden');
    }
    
    function showSuccess() {
        hideAllOverlays();
        successOverlay.classList.remove('hidden');
    }
    
    function hideSuccess() {
        successOverlay.classList.add('hidden');
    }
    
    function hideAllOverlays() {
        statusOverlay.classList.add('hidden');
        errorOverlay.classList.add('hidden');
        successOverlay.classList.add('hidden');
    }
    
    function updateButtons() {
        if (isScanning) {
            startBtn.classList.add('hidden');
            stopBtn.classList.remove('hidden');
            if (toggleCameraBtn && availableCameras.length > 1) {
                toggleCameraBtn.classList.remove('hidden');
            }
        } else {
            startBtn.classList.remove('hidden');
            stopBtn.classList.add('hidden');
            if (toggleCameraBtn) {
                toggleCameraBtn.classList.add('hidden');
            }
            if (toggleTorchBtn) {
                toggleTorchBtn.classList.add('hidden');
            }
        }
    }
    
    function updateTorchButton() {
        if (toggleTorchBtn) {
            const icon = toggleTorchBtn.querySelector('i');
            if (torchEnabled) {
                icon.className = 'fas fa-flashlight mr-2';
                toggleTorchBtn.classList.remove('bg-yellow-600', 'hover:bg-yellow-700');
                toggleTorchBtn.classList.add('bg-orange-600', 'hover:bg-orange-700');
            } else {
                icon.className = 'fas fa-flashlight mr-2';
                toggleTorchBtn.classList.remove('bg-orange-600', 'hover:bg-orange-700');
                toggleTorchBtn.classList.add('bg-yellow-600', 'hover:bg-yellow-700');
            }
        }
    }
    
    function updateCameraInfo() {
        if (availableCameras.length > 0 && currentCameraIndex < availableCameras.length) {
            const camera = availableCameras[currentCameraIndex];
            cameraInfo.textContent = camera.label || `Camera ${currentCameraIndex + 1}`;
        } else {
            cameraInfo.textContent = isScanning ? 'Active' : 'Not initialized';
        }
    }
    
    // Event listeners
    startBtn.addEventListener('click', startScanning);
    stopBtn.addEventListener('click', stopScanning);
    
    if (toggleCameraBtn) {
        toggleCameraBtn.addEventListener('click', toggleCamera);
    }
    
    if (toggleTorchBtn) {
        toggleTorchBtn.addEventListener('click', toggleTorch);
    }
    
    // Initialize on load
    initializeScanner();
    
    @if($autoStart)
        // Auto start if enabled
        setTimeout(startScanning, 1000);
    @endif
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (isScanning) {
            stopScanning();
        }
    });
});
</script>
@endpush

<?php

namespace Database\Seeders;

use App\Models\Feedback;
use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use Illuminate\Database\Seeder;

class FeedbackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some users and events
        $users = User::where('role', 'pembeli')->take(10)->get();
        $events = Event::take(5)->get();

        if ($users->isEmpty() || $events->isEmpty()) {
            $this->command->info('No users or events found. Skipping feedback seeder.');
            return;
        }

        $feedbackData = [
            [
                'rating' => 5,
                'comment' => 'Event yang sangat luar biasa! Organisasi yang rapi dan acara yang menarik. Pasti akan datang lagi ke event berikutnya.',
                'status' => 'approved',
                'is_anonymous' => false,
            ],
            [
                'rating' => 4,
                'comment' => 'Secara keseluruhan bagus, tapi sound system agak kurang jelas di bagian belakang. Selain itu semua oke!',
                'status' => 'approved',
                'is_anonymous' => false,
            ],
            [
                'rating' => 5,
                'comment' => 'Perfect! Venue bagus, makanan enak, dan speaker sangat inspiratif. Worth it banget!',
                'status' => 'approved',
                'is_anonymous' => true,
            ],
            [
                'rating' => 3,
                'comment' => 'Event cukup bagus tapi parkir susah dan antrian masuk agak lama. Mungkin bisa diperbaiki untuk event selanjutnya.',
                'status' => 'pending',
                'is_anonymous' => false,
            ],
            [
                'rating' => 4,
                'comment' => 'Konten event sangat bermanfaat dan networking session juga bagus. Terima kasih untuk pengalaman yang berkesan!',
                'status' => 'approved',
                'is_anonymous' => false,
            ],
            [
                'rating' => 2,
                'comment' => 'Agak kecewa dengan kualitas makanan dan minuman. Event content bagus tapi fasilitas kurang memuaskan.',
                'status' => 'rejected',
                'is_anonymous' => true,
            ],
            [
                'rating' => 5,
                'comment' => 'Event terbaik yang pernah saya hadiri! Semua aspek sangat well-organized. Kudos untuk tim organizer!',
                'status' => 'approved',
                'is_anonymous' => false,
            ],
            [
                'rating' => 4,
                'comment' => 'Great event overall. Speakers were knowledgeable and the venue was comfortable. Looking forward to more events like this.',
                'status' => 'pending',
                'is_anonymous' => false,
            ],
            [
                'rating' => 3,
                'comment' => 'Event bagus tapi AC kurang dingin dan kursi agak tidak nyaman untuk duduk lama. Content wise sangat bagus.',
                'status' => 'approved',
                'is_anonymous' => true,
            ],
            [
                'rating' => 5,
                'comment' => 'Absolutely amazing! Everything was perfect from registration to closing. Will definitely recommend to friends.',
                'status' => 'responded',
                'is_anonymous' => false,
                'admin_response' => 'Thank you so much for your wonderful feedback! We\'re thrilled that you enjoyed the event. We look forward to seeing you at our future events!',
            ],
        ];

        foreach ($feedbackData as $index => $data) {
            $user = $users->random();
            $event = $events->random();

            // Create or find an order for this user and event
            $quantity = rand(1, 3);
            $unitPrice = $event->price ?? 50000;
            $subtotal = $unitPrice * $quantity;
            $order = Order::firstOrCreate([
                'user_id' => $user->id,
                'event_id' => $event->id,
            ], [
                'order_number' => 'ORD-' . now()->format('Ymd') . '-' . strtoupper(\Str::random(6)),
                'customer_name' => $user->name,
                'customer_email' => $user->email,
                'customer_phone' => $user->phone ?? '081234567890',
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'subtotal' => $subtotal,
                'total_amount' => $subtotal,
                'status' => 'confirmed',
                'payment_status' => 'paid',
                'paid_at' => now()->subDays(rand(1, 30)),
                'confirmed_at' => now()->subDays(rand(1, 30)),
            ]);

            Feedback::create([
                'user_id' => $user->id,
                'event_id' => $event->id,
                'order_id' => $order->id,
                'rating' => $data['rating'],
                'comment' => $data['comment'],
                'status' => $data['status'],
                'is_anonymous' => $data['is_anonymous'],
                'admin_response' => $data['admin_response'] ?? null,
                'responded_at' => isset($data['admin_response']) ? now()->subDays(rand(1, 5)) : null,
                'responded_by' => isset($data['admin_response']) ? User::where('role', 'admin')->first()?->id : null,
                'created_at' => now()->subDays(rand(1, 30)),
                'updated_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        $this->command->info('Feedback seeder completed successfully!');
    }
}

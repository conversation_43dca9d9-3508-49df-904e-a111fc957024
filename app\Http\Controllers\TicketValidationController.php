<?php

namespace App\Http\Controllers;

use App\Services\TicketValidationService;
use App\Models\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TicketValidationController extends Controller
{
    protected $validationService;

    public function __construct(TicketValidationService $validationService)
    {
        $this->validationService = $validationService;
    }

    /**
     * Show ticket validation page for staff
     */
    public function index()
    {
        return view('pages.validation.index');
    }

    /**
     * Validate ticket by scanning QR code or entering ticket number
     */
    public function validateTicket(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string',
            'type' => 'required|in:qr_code,ticket_number'
        ]);

        $identifier = $request->identifier;
        $validator = Auth::user();

        if ($request->type === 'qr_code') {
            $result = $this->validationService->validateByQRCode($identifier, $validator);
        } else {
            $result = $this->validationService->validateByTicketNumber($identifier, $validator);
        }

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return back()->with('success', $result['message'])->with('ticket_data', $result);
        } else {
            return back()->with('error', $result['message'])->with('ticket_data', $result);
        }
    }

    /**
     * Check ticket authenticity without marking as used
     */
    public function checkAuthenticity(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string'
        ]);

        $result = $this->validationService->checkAuthenticity($request->identifier);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        return view('tickets.authenticity.result', compact('result'));
    }

    /**
     * Show authenticity checking page
     */
    public function authenticityCheck()
    {
        return view('pages.authenticity.index');
    }

    /**
     * Download ticket PDF
     */
    public function download(Ticket $ticket)
    {
        // Check if user owns the ticket or is admin/staff
        if (!$this->canAccessTicket($ticket)) {
            abort(403, 'Unauthorized access to ticket');
        }

        // Track download
        $ticket->trackDownload();

        // Generate PDF if not exists
        if (!$ticket->pdf_path || !file_exists(storage_path('app/public/' . $ticket->pdf_path))) {
            $ticketGenerator = new \App\Services\TicketGeneratorService();
            $ticketGenerator->generateTicketPdf($ticket);
        }

        $filePath = storage_path('app/public/' . $ticket->pdf_path);

        if (!file_exists($filePath)) {
            abort(404, 'Ticket PDF not found');
        }

        return response()->download($filePath, "ticket-{$ticket->ticket_number}.pdf");
    }

    /**
     * Print ticket (track print activity)
     */
    public function print(Ticket $ticket)
    {
        // Check if user owns the ticket or is admin/staff
        if (!$this->canAccessTicket($ticket)) {
            abort(403, 'Unauthorized access to ticket');
        }

        // Track print
        $ticket->trackPrint();

        // Generate PDF if not exists
        if (!$ticket->pdf_path || !file_exists(storage_path('app/public/' . $ticket->pdf_path))) {
            $ticketGenerator = new \App\Services\TicketGeneratorService();
            $ticketGenerator->generateTicketPdf($ticket);
        }

        $filePath = storage_path('app/public/' . $ticket->pdf_path);

        if (!file_exists($filePath)) {
            abort(404, 'Ticket PDF not found');
        }

        return response()->file($filePath, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'inline; filename="ticket-' . $ticket->ticket_number . '.pdf"'
        ]);
    }

    /**
     * Show ticket details
     */
    public function show(Ticket $ticket)
    {
        // Check if user owns the ticket or is admin/staff
        if (!$this->canAccessTicket($ticket)) {
            abort(403, 'Unauthorized access to ticket');
        }

        $ticket->load(['event', 'buyer', 'validator']);

        return view('tickets.show', compact('ticket'));
    }

    /**
     * Get validation statistics
     */
    public function stats()
    {
        $stats = [
            'total_tickets' => Ticket::count(),
            'active_tickets' => Ticket::where('status', 'active')->whereNull('used_at')->count(),
            'used_tickets' => Ticket::where('status', 'used')->orWhereNotNull('used_at')->count(),
            'authentic_tickets' => Ticket::where('is_authentic', true)->count(),
            'fake_attempts' => Ticket::where('is_authentic', false)->count(),
            'today_validations' => Ticket::whereDate('used_at', today())->count(),
            'this_week_validations' => Ticket::whereBetween('used_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count()
        ];

        return response()->json($stats);
    }

    /**
     * Get recent validation activities
     */
    public function recentValidations()
    {
        $validations = Ticket::whereNotNull('used_at')
                           ->with(['event', 'buyer', 'validator'])
                           ->orderBy('used_at', 'desc')
                           ->limit(10)
                           ->get();

        $data = $validations->map(function ($ticket) {
            return [
                'ticket_number' => $ticket->ticket_number,
                'event_title' => $ticket->event->title,
                'attendee_name' => $ticket->attendee_name,
                'validated_at' => $ticket->used_at->format('d M Y H:i:s'),
                'validator' => $ticket->validator?->name ?? 'System',
                'status' => $ticket->status
            ];
        });

        return response()->json($data);
    }

    /**
     * Bulk validation for admin/staff
     */
    public function bulkValidate(Request $request)
    {
        $request->validate([
            'ticket_numbers' => 'required|array',
            'ticket_numbers.*' => 'required|string'
        ]);

        $validator = Auth::user();
        $result = $this->validationService->bulkValidate($request->ticket_numbers, $validator);

        return response()->json($result);
    }

    /**
     * Check if user can access ticket
     */
    private function canAccessTicket(Ticket $ticket): bool
    {
        $user = Auth::user();

        if (!$user) {
            return false;
        }

        // Admin and staff can access all tickets
        if (in_array($user->role, ['admin', 'staff'])) {
            return true;
        }

        // Organizer can access tickets for their events
        if ($user->role === 'penjual' && $ticket->event->organizer_id === $user->id) {
            return true;
        }

        // User can access their own tickets
        if ($ticket->buyer_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Generate QR code for ticket
     */
    public function qrCode(Ticket $ticket)
    {
        // Check if user owns the ticket or is admin/staff
        if (!$this->canAccessTicket($ticket)) {
            abort(403, 'Unauthorized access to ticket');
        }

        $qrCodeUrl = $ticket->qr_code_url;

        if (!$qrCodeUrl) {
            abort(404, 'QR Code not found');
        }

        return response()->file(storage_path('app/public/' . str_replace('/storage/', '', $qrCodeUrl)));
    }

    /**
     * Verify ticket by QR code (public endpoint for verification)
     */
    public function verify(string $qrCode)
    {
        $result = $this->validationService->checkAuthenticity($qrCode);

        return view('tickets.verify', compact('result', 'qrCode'));
    }

    /**
     * Show enhanced scanner page
     */
    public function enhancedScanner()
    {
        return view('pages.validation.enhanced-scanner');
    }

    /**
     * Staff scan ticket (check validity without marking as used)
     */
    public function staffScan(Request $request)
    {
        $request->validate([
            'identifier' => 'required|string'
        ]);

        $staff = Auth::user();

        // Check if user is staff
        if ($staff->role !== 'staff') {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak. Hanya staff yang dapat menggunakan fitur ini.'
            ], 403);
        }

        $result = $this->validationService->staffScanTicket($request->identifier, $staff);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        return back()->with('scan_result', $result);
    }

    /**
     * Staff use ticket (mark as used after scan)
     */
    public function staffUse(Request $request)
    {
        $request->validate([
            'ticket_id' => 'required|integer|exists:tickets,id'
        ]);

        $staff = Auth::user();

        // Check if user is staff
        if ($staff->role !== 'staff') {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak. Hanya staff yang dapat menggunakan fitur ini.'
            ], 403);
        }

        $result = $this->validationService->staffUseTicket($request->ticket_id, $staff);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return back()->with('success', $result['message'])->with('use_result', $result);
        } else {
            return back()->with('error', $result['message']);
        }
    }
}

@extends('layouts.main')

@section('title', 'Purchase Ticket - ' . $event->title)

@push('styles')
<style>
/* CSS Variables for Theme Support */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --background-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    --background-primary: #1f2937;
    --background-secondary: #111827;
    --background-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --glass-bg: rgba(31, 41, 55, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Base Styles */
.purchase-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
}

.purchase-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* Header Styles */
.purchase-header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    position: relative;
    z-index: 1;
}

.purchase-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--primary-color));
    border-radius: 1.5rem 1.5rem 0 0;
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Form Cards */
.form-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px var(--shadow-color);
}

/* Payment Method Cards */
.payment-method-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.payment-method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.payment-method-card:hover::before {
    left: 100%;
}

.payment-method-card.selected {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.payment-method-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

/* Gateway Badge */
.gateway-badge {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.gateway-badge.xendit {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.gateway-badge.midtrans {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.gateway-badge.tripay {
    background: rgba(147, 51, 234, 0.1);
    color: #9333ea;
    border: 1px solid rgba(147, 51, 234, 0.2);
}

.gateway-badge.manual {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Input Styles */
.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    font-size: 1rem;
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Quantity Selector */
.quantity-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--background-secondary);
    border-radius: 1rem;
    padding: 0.5rem;
}

.quantity-btn {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
    background: var(--background-primary);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.quantity-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.quantity-display {
    flex: 1;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Summary Card */
.summary-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    position: sticky;
    top: 2rem;
    z-index: 1;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--primary-color);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .purchase-container {
        padding: 1rem;
    }

    .form-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .summary-card {
        position: static;
        margin-top: 2rem;
    }

    .payment-method-card {
        padding: 1rem;
    }

    .quantity-selector {
        justify-content: center;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* Success Animation */
.success-animation {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Error Animation */
.error-animation {
    animation: errorShake 0.6s ease-in-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--background-tertiary);
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 0.25rem;
    transition: width 0.3s ease;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 100;
    padding: 1rem 1.5rem;
    border-radius: 1rem;
    box-shadow: 0 10px 30px var(--shadow-color);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: rgba(16, 185, 129, 0.9);
    color: white;
}

.notification.error {
    background: rgba(239, 68, 68, 0.9);
    color: white;
}

.notification.warning {
    background: rgba(245, 158, 11, 0.9);
    color: white;
}
</style>
@endpush

@section('content')
<div class="purchase-container" data-theme="light" id="purchaseContainer">
    <div class="container mx-auto px-4 py-8">
        <!-- Progress Bar -->
        <div class="progress-bar mb-8">
            <div class="progress-fill" style="width: 33%"></div>
        </div>

        <!-- Header -->
        <div class="purchase-header fade-in">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Purchase Ticket</h1>
                    <p class="text-lg text-gray-600">Complete your ticket purchase for {{ $event->title }}</p>
                </div>
                <div class="flex gap-3">
                    <button id="themeToggle" class="btn-primary">
                        <i data-lucide="sun" class="w-4 h-4 mr-2"></i>
                        Theme
                    </button>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('tickets.store', $event->id ?? $event) }}"
              x-data="modernTicketPurchase()"
              @submit="handleSubmit"
              novalidate>
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Form -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Event Summary -->
                    <div class="form-card fade-in" style="animation-delay: 0.1s;">
                        <h2 class="text-xl font-bold mb-4">Event Details</h2>
                        <div class="flex items-start gap-4">
                            <img src="{{ $event->poster_url }}"
                                 alt="{{ $event->title }}"
                                 class="w-20 h-20 object-cover rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-semibold text-lg mb-2">{{ $event->title }}</h3>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                        {{ $event->start_date->format('d M Y, H:i') }} WIB
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="map-pin" class="w-4 h-4 mr-2"></i>
                                        {{ $event->venue_name }}, {{ $event->city }}
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                                        {{ $event->organizer->name ?? 'TiXara' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Selection -->
                    <div class="form-card fade-in" style="animation-delay: 0.2s;">
                        <h2 class="text-xl font-bold mb-4">Select Quantity</h2>
                        <div class="quantity-selector">
                            <button type="button"
                                    @click="decreaseQuantity()"
                                    :disabled="quantity <= 1"
                                    class="quantity-btn">
                                <i data-lucide="minus" class="w-5 h-5"></i>
                            </button>
                            <div class="quantity-display" x-text="quantity"></div>
                            <button type="button"
                                    @click="increaseQuantity()"
                                    :disabled="quantity >= maxQuantity"
                                    class="quantity-btn">
                                <i data-lucide="plus" class="w-5 h-5"></i>
                            </button>
                        </div>
                        <input type="hidden" name="quantity" x-model="quantity">
                        <p class="text-sm text-gray-500 text-center mt-2">
                            Maximum {{ $maxQuantity }} tickets per purchase
                        </p>
                    </div>

                    <!-- Personal Information -->
                    <div class="form-card fade-in" style="animation-delay: 0.3s;">
                        <h2 class="text-xl font-bold mb-4">Personal Information</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium mb-2">Full Name *</label>
                                <input type="text" name="attendee_name"
                                       value="{{ old('attendee_name', auth()->user()->name ?? '') }}"
                                       class="form-input" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Email *</label>
                                <input type="email" name="attendee_email"
                                       value="{{ old('attendee_email', auth()->user()->email ?? '') }}"
                                       class="form-input" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Phone Number *</label>
                                <input type="tel" name="attendee_phone"
                                       value="{{ old('attendee_phone', auth()->user()->phone ?? '') }}"
                                       class="form-input" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Identity Type *</label>
                                <select name="identity_type" class="form-input" required>
                                    <option value="">Select Identity Type</option>
                                    <option value="ktp">KTP (ID Card)</option>
                                    <option value="passport">Passport</option>
                                    <option value="sim">Driver License</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="form-card fade-in" style="animation-delay: 0.4s;">
                        <h2 class="text-xl font-bold mb-4">Payment Method</h2>

                        <!-- Loading Payment Methods -->
                        <div x-show="loadingPaymentMethods" class="text-center py-8">
                            <div class="loading"></div>
                            <p class="text-sm text-gray-600 mt-2">Loading payment methods...</p>
                        </div>

                        <!-- Payment Methods List -->
                        <div x-show="!loadingPaymentMethods" class="space-y-3">
                            <template x-for="method in availablePaymentMethods" :key="method.code">
                                <label class="payment-method-card"
                                       :class="selectedPaymentMethod === method.code ? 'selected' : ''">
                                    <input type="radio" name="payment_method"
                                           :value="method.code"
                                           x-model="selectedPaymentMethod"
                                           :disabled="!method.is_active"
                                           class="sr-only">

                                    <div class="gateway-badge"
                                         :class="method.gateway_name.toLowerCase()"
                                         x-text="method.gateway_name"></div>

                                    <div class="flex items-center">
                                        <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4"
                                             :class="method.icon_bg_class">
                                            <i :class="method.icon_class" class="text-xl"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold" x-text="method.name"></h4>
                                            <p class="text-sm text-gray-600" x-text="method.description"></p>
                                            <div x-show="method.fee > 0" class="text-xs text-orange-600 mt-1">
                                                Fee: <span x-text="formatCurrency(method.fee)"></span>
                                            </div>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                            <div class="w-2 h-2 bg-blue-600 rounded-full"
                                                 x-show="selectedPaymentMethod === method.code"></div>
                                        </div>
                                    </div>
                                </label>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="summary-card fade-in" style="animation-delay: 0.5s;">
                        <h3 class="text-xl font-bold mb-4">Order Summary</h3>

                        <div class="summary-item">
                            <span>Ticket Price</span>
                            <span x-text="formatCurrency(ticketPrice)"></span>
                        </div>

                        <div class="summary-item">
                            <span>Quantity</span>
                            <span x-text="quantity + ' tickets'"></span>
                        </div>

                        <div class="summary-item">
                            <span>Subtotal</span>
                            <span x-text="formatCurrency(subtotal)"></span>
                        </div>

                        <div class="summary-item" x-show="paymentFee > 0">
                            <span>Payment Fee</span>
                            <span x-text="formatCurrency(paymentFee)"></span>
                        </div>

                        <div class="summary-item">
                            <span>Total</span>
                            <span x-text="formatCurrency(total)"></span>
                        </div>

                        <button type="submit"
                                class="btn-primary w-full mt-6"
                                :disabled="!selectedPaymentMethod || processing">
                            <span x-show="!processing">
                                <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                Purchase Now
                            </span>
                            <span x-show="processing">
                                <i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>
                                Processing...
                            </span>
                        </button>

                        <div class="mt-4 text-xs text-gray-500 text-center">
                            <p>By purchasing, you agree to our Terms & Conditions</p>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Notification Container -->
<div id="notificationContainer"></div>
@endsection

@push('scripts')
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
<script>
function modernTicketPurchase() {
    return {
        // Basic properties
        quantity: 1,
        maxQuantity: {{ $maxQuantity }},
        ticketPrice: {{ $event->price }},
        selectedPaymentMethod: '',
        processing: false,

        // Payment methods
        availablePaymentMethods: [],
        loadingPaymentMethods: true,
        paymentFee: 0,

        // Theme
        currentTheme: 'light',

        // Computed properties
        get subtotal() {
            return this.ticketPrice * this.quantity;
        },

        get total() {
            return this.subtotal + this.paymentFee;
        },

        // Initialize
        init() {
            this.loadPaymentMethods();
            this.setupThemeToggle();
            this.setupFormValidation();
            this.updateProgressBar(33);
        },

        // Load payment methods from API
        async loadPaymentMethods() {
            try {
                const response = await fetch('/api/payment-methods/available', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.availablePaymentMethods = data.payment_methods || [];

                    // Set default payment method
                    if (this.availablePaymentMethods.length > 0) {
                        this.selectedPaymentMethod = this.availablePaymentMethods[0].code;
                        this.calculatePaymentFee();
                    }
                } else {
                    console.error('Failed to load payment methods');
                    this.availablePaymentMethods = this.getDefaultPaymentMethods();
                }
            } catch (error) {
                console.error('Error loading payment methods:', error);
                this.availablePaymentMethods = this.getDefaultPaymentMethods();
            } finally {
                this.loadingPaymentMethods = false;
            }
        },

        // Get default payment methods as fallback
        getDefaultPaymentMethods() {
            return [
                {
                    code: 'bank_transfer',
                    name: 'Bank Transfer',
                    description: 'Transfer to bank account',
                    icon_class: 'fas fa-university',
                    icon_bg_class: 'bg-blue-100 text-blue-600',
                    gateway_name: 'Manual',
                    fee: 0,
                    is_active: true
                },
                {
                    code: 'e_wallet',
                    name: 'E-Wallet',
                    description: 'GoPay, OVO, DANA',
                    icon_class: 'fas fa-mobile-alt',
                    icon_bg_class: 'bg-green-100 text-green-600',
                    gateway_name: 'Xendit',
                    fee: 2500,
                    is_active: true
                }
            ];
        },

        // Calculate payment fee when method changes
        async calculatePaymentFee() {
            if (!this.selectedPaymentMethod) {
                this.paymentFee = 0;
                return;
            }

            try {
                const response = await fetch('/api/payment-methods/calculate-fee', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        payment_method_code: this.selectedPaymentMethod,
                        amount: this.subtotal
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.paymentFee = data.fee || 0;
                } else {
                    // Fallback to method fee
                    const method = this.availablePaymentMethods.find(m => m.code === this.selectedPaymentMethod);
                    this.paymentFee = method ? method.fee : 0;
                }
            } catch (error) {
                console.error('Error calculating fee:', error);
                this.paymentFee = 0;
            }
        },

        // Quantity controls
        increaseQuantity() {
            if (this.quantity < this.maxQuantity) {
                this.quantity++;
                this.calculatePaymentFee();
                this.updateProgressBar(50);
            }
        },

        decreaseQuantity() {
            if (this.quantity > 1) {
                this.quantity--;
                this.calculatePaymentFee();
            }
        },

        // Theme toggle
        setupThemeToggle() {
            document.getElementById('themeToggle').addEventListener('click', () => {
                this.toggleTheme();
            });
        },

        toggleTheme() {
            this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
            document.getElementById('purchaseContainer').setAttribute('data-theme', this.currentTheme);

            const icon = document.querySelector('#themeToggle i');
            icon.setAttribute('data-lucide', this.currentTheme === 'light' ? 'sun' : 'moon');

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            this.showNotification(`Switched to ${this.currentTheme} theme`, 'success');
        },

        // Form validation
        setupFormValidation() {
            // Real-time validation
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });

                input.addEventListener('input', () => {
                    if (input.classList.contains('error')) {
                        this.validateField(input);
                    }
                });
            });
        },

        validateField(field) {
            const value = field.value.trim();
            const isRequired = field.hasAttribute('required');
            const type = field.type;

            let isValid = true;
            let errorMessage = '';

            // Required validation
            if (isRequired && !value) {
                isValid = false;
                errorMessage = 'This field is required';
            }

            // Email validation
            else if (type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }
            }

            // Phone validation
            else if (type === 'tel' && value) {
                const phoneRegex = /^[0-9]{10,13}$/;
                if (!phoneRegex.test(value.replace(/\D/g, ''))) {
                    isValid = false;
                    errorMessage = 'Please enter a valid phone number';
                }
            }

            // Update field appearance
            if (isValid) {
                field.classList.remove('error');
                this.removeFieldError(field);
            } else {
                field.classList.add('error');
                this.showFieldError(field, errorMessage);
            }

            return isValid;
        },

        showFieldError(field, message) {
            this.removeFieldError(field);

            const errorElement = document.createElement('p');
            errorElement.className = 'text-red-500 text-sm mt-1 field-error';
            errorElement.textContent = message;

            field.parentNode.appendChild(errorElement);
        },

        removeFieldError(field) {
            const existingError = field.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        },

        // Form submission
        async handleSubmit(event) {
            event.preventDefault();

            if (this.processing) return;

            // Validate all fields
            const inputs = document.querySelectorAll('.form-input[required]');
            let isFormValid = true;

            inputs.forEach(input => {
                if (!this.validateField(input)) {
                    isFormValid = false;
                }
            });

            if (!this.selectedPaymentMethod) {
                this.showNotification('Please select a payment method', 'error');
                isFormValid = false;
            }

            if (!isFormValid) {
                this.showNotification('Please fix the errors in the form', 'error');
                return;
            }

            this.processing = true;
            this.updateProgressBar(75);

            try {
                // Submit form
                const form = event.target;
                const formData = new FormData(form);

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    this.updateProgressBar(100);
                    this.showNotification('Purchase successful! Redirecting...', 'success');

                    // Redirect after success
                    setTimeout(() => {
                        window.location.href = response.url || '/tickets/my-tickets';
                    }, 2000);
                } else {
                    const errorData = await response.json();
                    this.showNotification(errorData.message || 'Purchase failed', 'error');
                }
            } catch (error) {
                console.error('Purchase error:', error);
                this.showNotification('Network error. Please try again.', 'error');
            } finally {
                this.processing = false;
            }
        },

        // Utility functions
        formatCurrency(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },

        updateProgressBar(percentage) {
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = percentage + '%';
            }
        },

        showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i data-lucide="${this.getNotificationIcon(type)}" class="w-5 h-5 mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            container.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            // Hide and remove notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        },

        getNotificationIcon(type) {
            switch (type) {
                case 'success': return 'check-circle';
                case 'error': return 'x-circle';
                case 'warning': return 'alert-triangle';
                default: return 'info';
            }
        },

        // Watch for payment method changes
        $watch: {
            selectedPaymentMethod() {
                this.calculatePaymentFee();
            }
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add form auto-save (optional)
    const form = document.querySelector('form');
    if (form) {
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                // Auto-save form data to localStorage
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                localStorage.setItem('purchase_form_data', JSON.stringify(data));
            });
        });

        // Restore form data on page load
        const savedData = localStorage.getItem('purchase_form_data');
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input && input.type !== 'hidden') {
                        input.value = data[key];
                    }
                });
            } catch (error) {
                console.error('Error restoring form data:', error);
            }
        }
    }
});

// Clear saved form data when purchase is successful
window.addEventListener('beforeunload', function() {
    // Only clear if we're navigating to success page
    if (window.location.href.includes('success') || window.location.href.includes('my-tickets')) {
        localStorage.removeItem('purchase_form_data');
    }
});
</script>
@endpush

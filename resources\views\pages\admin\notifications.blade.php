@extends('layouts.admin')

@section('title', 'Manaj<PERSON>en Notifikasi')
@section('subtitle', '<PERSON><PERSON><PERSON> dan kirim notifikasi ke pengguna')

@push('styles')
<style>
.notification-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.notification-card.unread {
    border-left-color: #3B82F6;
    background: linear-gradient(135deg, #EBF4FF 0%, #FFFFFF 100%);
}

.notification-card.read {
    border-left-color: #10B981;
}

.notification-type-system { @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400; }
.notification-type-user { @apply bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400; }
.notification-type-event { @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400; }
.notification-type-payment { @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400; }

.filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}
</style>
@endpush

@section('content')
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4" data-aos="fade-up">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-bell mr-3 text-blue-600"></i>
                Manajemen Notifikasi
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Kelola dan kirim notifikasi ke seluruh pengguna platform</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-3">
            <button onclick="refreshNotifications()" class="flex items-center justify-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh
            </button>
            <a href="{{ route('admin.notifications.create') }}"
               class="flex items-center justify-center px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                <i class="fas fa-plus mr-2"></i>
                Kirim Notifikasi
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="100">
        <!-- Total Notifications -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Notifikasi</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{{ number_format($stats['total_notifications'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-chart-line text-green-500 mr-1"></i>
                        <span class="text-sm text-green-600 dark:text-green-400 font-medium">+12% dari bulan lalu</span>
                    </div>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-bell text-2xl text-white"></i>
                </div>
            </div>
        </div>

        <!-- Unread Notifications -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Belum Dibaca</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{{ number_format($stats['unread_notifications'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        @php
                            $unreadPercentage = $stats['total_notifications'] > 0 ? round(($stats['unread_notifications'] / $stats['total_notifications']) * 100, 1) : 0;
                        @endphp
                        <i class="fas fa-percentage text-orange-500 mr-1"></i>
                        <span class="text-sm text-orange-600 dark:text-orange-400 font-medium">{{ $unreadPercentage }}% dari total</span>
                    </div>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-envelope text-2xl text-white"></i>
                </div>
            </div>
        </div>

        <!-- System Notifications -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Notifikasi System</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{{ number_format($stats['system_notifications'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-cog text-green-500 mr-1"></i>
                        <span class="text-sm text-green-600 dark:text-green-400 font-medium">Otomatis</span>
                    </div>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-server text-2xl text-white"></i>
                </div>
            </div>
        </div>

        <!-- User Notifications -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Notifikasi User</p>
                    <p class="text-3xl font-bold text-gray-900 dark:text-white mt-2">{{ number_format($stats['user_notifications'], 0, ',', '.') }}</p>
                    <div class="flex items-center mt-2">
                        <i class="fas fa-users text-purple-500 mr-1"></i>
                        <span class="text-sm text-purple-600 dark:text-purple-400 font-medium">Manual</span>
                    </div>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-friends text-2xl text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
        <!-- Send Notification Card -->
        <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Kirim Notifikasi Cepat</h3>
                <i class="fas fa-paper-plane text-2xl opacity-80"></i>
            </div>
            <p class="text-blue-100 mb-4">Kirim notifikasi ke semua pengguna atau grup tertentu</p>
            <div class="space-y-2">
                <button onclick="sendQuickNotification('all')" class="w-full bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-broadcast-tower mr-2"></i>Broadcast ke Semua
                </button>
                <button onclick="sendQuickNotification('organizers')" class="w-full bg-white/20 hover:bg-white/30 text-white py-2 px-4 rounded-lg transition-colors duration-200">
                    <i class="fas fa-user-tie mr-2"></i>Ke Organizer
                </button>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Aktivitas Terbaru</h3>
                <i class="fas fa-clock text-gray-400"></i>
            </div>
            <div class="space-y-3" id="recentActivity">
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse-slow"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">5 notifikasi terkirim</span>
                    <span class="text-xs text-gray-400">2 menit lalu</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Notifikasi sistem dibuat</span>
                    <span class="text-xs text-gray-400">10 menit lalu</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">12 notifikasi dibaca</span>
                    <span class="text-xs text-gray-400">1 jam lalu</span>
                </div>
            </div>
        </div>

        <!-- Notification Templates -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Template Cepat</h3>
                <i class="fas fa-templates text-gray-400"></i>
            </div>
            <div class="space-y-2">
                <button onclick="useTemplate('welcome')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                    <div class="font-medium text-gray-900 dark:text-white">Selamat Datang</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Template untuk user baru</div>
                </button>
                <button onclick="useTemplate('event')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                    <div class="font-medium text-gray-900 dark:text-white">Event Reminder</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Pengingat event</div>
                </button>
                <button onclick="useTemplate('maintenance')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                    <div class="font-medium text-gray-900 dark:text-white">Maintenance</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Pemberitahuan maintenance</div>
                </button>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-xl shadow-sm p-6 mb-8" data-aos="fade-up" data-aos-delay="500">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tipe</label>
                <select class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                    <option value="">Semua Tipe</option>
                    <option value="system">System</option>
                    <option value="user">User</option>
                    <option value="event">Event</option>
                    <option value="payment">Payment</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                    <option value="">Semua Status</option>
                    <option value="read">Dibaca</option>
                    <option value="unread">Belum Dibaca</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Mulai</label>
                <input type="date" class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tanggal Akhir</label>
                <input type="date" class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
            </div>
        </div>
        <div class="mt-4 flex justify-end">
            <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                Filter
            </button>
        </div>
    </div>

    <!-- Notifications Table -->
    <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="600">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">Daftar Notifikasi</h2>
                <div class="flex space-x-2">
                    <input type="text"
                           placeholder="Cari notifikasi..."
                           class="border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                    <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors">
                        Cari
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Penerima
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Judul
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pesan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tipe
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tanggal
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($notifications as $notification)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-600">
                                            {{ substr($notification->user->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $notification->user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $notification->user->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ $notification->title }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 max-w-xs truncate">{{ $notification->message }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $notification->type === 'system' ? 'bg-blue-100 text-blue-800' :
                                       ($notification->type === 'event' ? 'bg-green-100 text-green-800' :
                                       ($notification->type === 'payment' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                                    {{ ucfirst($notification->type) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $notification->read_at ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800' }}">
                                    {{ $notification->read_at ? 'Dibaca' : 'Belum Dibaca' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $notification->created_at->format('d M Y, H:i') }}
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"/>
                                </svg>
                                <p>Belum ada notifikasi</p>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($notifications->hasPages())
            <div class="mt-6">
                {{ $notifications->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

<?php

echo "🔧 Fixing migration brace syntax errors...\n";

$migrationPath = 'database/migrations/';
$migrationFiles = glob($migrationPath . '*.php');

$fixedCount = 0;

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Pattern 1: Fix missing closing braces and malformed comments
    // Look for pattern: });    }    /**        }     * Reverse the migrations.     */
    $pattern1 = '/(\s+}\);\s+}\s+\/\*\*\s+}\s+\*\s+Reverse\s+the\s+migrations\.\s+\*\/)/s';
    $replacement1 = "\n        }\n    }\n\n    /**\n     * Reverse the migrations.\n     */";
    
    if (preg_match($pattern1, $content)) {
        $content = preg_replace($pattern1, $replacement1, $content);
        echo "  🔧 Fixed pattern 1 in {$filename}\n";
    }
    
    // Pattern 2: Fix missing closing brace for if statement and up() function
    // Look for pattern: });    }    /**        }     * Reverse
    $pattern2 = '/(\s+}\);\s+}\s+\/\*\*\s+}\s+\*\s+Reverse)/s';
    $replacement2 = "\n        }\n    }\n\n    /**\n     * Reverse";
    
    if (preg_match($pattern2, $content)) {
        $content = preg_replace($pattern2, $replacement2, $content);
        echo "  🔧 Fixed pattern 2 in {$filename}\n";
    }
    
    // Pattern 3: Fix indentation for table columns
    $pattern3 = '/(\$table->[^;]+;)/';
    $lines = explode("\n", $content);
    $inTableCreation = false;
    $fixedLines = [];
    
    foreach ($lines as $line) {
        if (strpos($line, 'Schema::create') !== false) {
            $inTableCreation = true;
        } elseif (strpos($line, '});') !== false && $inTableCreation) {
            $inTableCreation = false;
        }
        
        if ($inTableCreation && strpos($line, '$table->') !== false) {
            // Fix indentation for table columns
            $line = preg_replace('/^\s*(\$table->.*)/', '                $1', $line);
        }
        
        $fixedLines[] = $line;
    }
    
    $newContent = implode("\n", $fixedLines);
    if ($newContent !== $content) {
        $content = $newContent;
        echo "  🔧 Fixed indentation in {$filename}\n";
    }
    
    // Only write if content changed
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        $fixedCount++;
        echo "  ✅ Fixed {$filename}\n";
    }
}

echo "\n📊 Summary: Fixed {$fixedCount} migration files\n";

// Test a few key files
$testFiles = [
    'database/migrations/2014_10_12_000000_create_users_table.php',
    'database/migrations/2014_10_12_100000_create_password_reset_tokens_table.php'
];

echo "\n🧪 Testing syntax of key files:\n";
foreach ($testFiles as $testFile) {
    if (file_exists($testFile)) {
        $output = [];
        $returnCode = 0;
        exec("php -l \"{$testFile}\" 2>&1", $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "  ✅ " . basename($testFile) . " - Syntax OK\n";
        } else {
            echo "  ❌ " . basename($testFile) . " - " . implode(' ', $output) . "\n";
        }
    }
}

echo "\n🎉 Migration syntax fix completed!\n";

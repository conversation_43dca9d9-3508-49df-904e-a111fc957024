<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\AdSubscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdsController extends Controller
{
    /**
     * Display ads management dashboard
     */
    public function index(Request $request)
    {
        $query = Advertisement::with(['advertiser', 'event']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('advertiser', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by active status
        if ($request->filled('active')) {
            $query->where('is_active', $request->active === 'true');
        }

        // Filter by approval status
        if ($request->filled('approved')) {
            $query->where('is_approved', $request->approved === 'true');
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'title', 'status', 'spent_amount', 'impressions', 'clicks'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $advertisements = $query->paginate(20);

        // Statistics
        $stats = $this->getAdvertisementStats();

        return view('pages.admin.ads.index', compact('advertisements', 'stats'));
    }

    /**
     * Display ad subscriptions
     */
    public function subscriptions(Request $request)
    {
        $query = AdSubscription::with('user');

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('plan_name', 'like', "%{$search}%")
                  ->orWhereHas('user', function($q) use ($search) {
                      $q->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by plan type
        if ($request->filled('plan_type')) {
            $query->where('plan_type', $request->plan_type);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->paginate(20);

        // Subscription statistics
        $subscriptionStats = $this->getSubscriptionStats();

        return view('pages.admin.ads.subscriptions', compact('subscriptions', 'subscriptionStats'));
    }

    /**
     * Show advertisement details
     */
    public function show(Advertisement $advertisement)
    {
        $advertisement->load(['advertiser', 'event']);
        
        // Get performance data for the last 30 days
        $performanceData = $this->getAdPerformanceData($advertisement);

        return view('pages.admin.ads.show', compact('advertisement', 'performanceData'));
    }

    /**
     * Approve advertisement
     */
    public function approve(Advertisement $advertisement)
    {
        $advertisement->update([
            'is_approved' => true,
            'status' => 'approved',
            'admin_notes' => 'Approved by admin on ' . now()->format('Y-m-d H:i:s')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Advertisement approved successfully!'
        ]);
    }

    /**
     * Reject advertisement
     */
    public function reject(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $advertisement->update([
            'is_approved' => false,
            'status' => 'rejected',
            'admin_notes' => $request->reason
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Advertisement rejected successfully!'
        ]);
    }

    /**
     * Pause advertisement
     */
    public function pause(Advertisement $advertisement)
    {
        $advertisement->update([
            'status' => 'paused',
            'admin_notes' => 'Paused by admin on ' . now()->format('Y-m-d H:i:s')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Advertisement paused successfully!'
        ]);
    }

    /**
     * Resume advertisement
     */
    public function resume(Advertisement $advertisement)
    {
        $advertisement->update([
            'status' => 'approved',
            'admin_notes' => 'Resumed by admin on ' . now()->format('Y-m-d H:i:s')
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Advertisement resumed successfully!'
        ]);
    }

    /**
     * Toggle advertisement active status
     */
    public function toggleActive(Advertisement $advertisement)
    {
        $advertisement->update([
            'is_active' => !$advertisement->is_active
        ]);

        $status = $advertisement->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Advertisement {$status} successfully!",
            'is_active' => $advertisement->is_active
        ]);
    }

    /**
     * Delete advertisement
     */
    public function destroy(Advertisement $advertisement)
    {
        // Delete image if exists
        if ($advertisement->image_url && Storage::disk('public')->exists($advertisement->image_url)) {
            Storage::disk('public')->delete($advertisement->image_url);
        }

        $advertisement->delete();

        return response()->json([
            'success' => true,
            'message' => 'Advertisement deleted successfully!'
        ]);
    }

    /**
     * Bulk actions for advertisements
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject,pause,resume,delete,activate,deactivate',
            'advertisement_ids' => 'required|array',
            'advertisement_ids.*' => 'exists:advertisements,id',
            'reason' => 'required_if:action,reject|string|max:500'
        ]);

        $advertisements = Advertisement::whereIn('id', $request->advertisement_ids);
        $count = $advertisements->count();

        switch ($request->action) {
            case 'approve':
                $advertisements->update([
                    'is_approved' => true,
                    'status' => 'approved',
                    'admin_notes' => 'Bulk approved by admin on ' . now()->format('Y-m-d H:i:s')
                ]);
                $message = "Successfully approved {$count} advertisements";
                break;

            case 'reject':
                $advertisements->update([
                    'is_approved' => false,
                    'status' => 'rejected',
                    'admin_notes' => $request->reason
                ]);
                $message = "Successfully rejected {$count} advertisements";
                break;

            case 'pause':
                $advertisements->update([
                    'status' => 'paused',
                    'admin_notes' => 'Bulk paused by admin on ' . now()->format('Y-m-d H:i:s')
                ]);
                $message = "Successfully paused {$count} advertisements";
                break;

            case 'resume':
                $advertisements->update([
                    'status' => 'approved',
                    'admin_notes' => 'Bulk resumed by admin on ' . now()->format('Y-m-d H:i:s')
                ]);
                $message = "Successfully resumed {$count} advertisements";
                break;

            case 'activate':
                $advertisements->update(['is_active' => true]);
                $message = "Successfully activated {$count} advertisements";
                break;

            case 'deactivate':
                $advertisements->update(['is_active' => false]);
                $message = "Successfully deactivated {$count} advertisements";
                break;

            case 'delete':
                // Delete images
                foreach ($advertisements->get() as $ad) {
                    if ($ad->image_url && Storage::disk('public')->exists($ad->image_url)) {
                        Storage::disk('public')->delete($ad->image_url);
                    }
                }
                $advertisements->delete();
                $message = "Successfully deleted {$count} advertisements";
                break;
        }

        return response()->json([
            'success' => true,
            'message' => $message
        ]);
    }

    /**
     * Get advertisement statistics
     */
    private function getAdvertisementStats()
    {
        return [
            'total_ads' => Advertisement::count(),
            'active_ads' => Advertisement::where('is_active', true)->count(),
            'approved_ads' => Advertisement::where('is_approved', true)->count(),
            'pending_ads' => Advertisement::where('status', 'pending')->count(),
            'running_ads' => Advertisement::active()->approved()->current()->count(),
            'total_impressions' => Advertisement::sum('impressions'),
            'total_clicks' => Advertisement::sum('clicks'),
            'total_spent' => Advertisement::sum('spent_amount'),
            'avg_ctr' => $this->calculateAverageCTR(),
            'revenue_this_month' => $this->getMonthlyRevenue(),
        ];
    }

    /**
     * Get subscription statistics
     */
    private function getSubscriptionStats()
    {
        return [
            'total_subscriptions' => AdSubscription::count(),
            'active_subscriptions' => AdSubscription::where('status', 'active')->count(),
            'expired_subscriptions' => AdSubscription::where('status', 'expired')->count(),
            'monthly_revenue' => AdSubscription::where('status', 'active')
                                              ->where('billing_cycle', 'monthly')
                                              ->sum('monthly_price'),
            'yearly_revenue' => AdSubscription::where('status', 'active')
                                             ->where('billing_cycle', 'yearly')
                                             ->sum('yearly_price'),
            'plan_distribution' => AdSubscription::selectRaw('plan_type, COUNT(*) as count')
                                                ->groupBy('plan_type')
                                                ->pluck('count', 'plan_type'),
        ];
    }

    /**
     * Calculate average CTR
     */
    private function calculateAverageCTR()
    {
        $totalImpressions = Advertisement::sum('impressions');
        $totalClicks = Advertisement::sum('clicks');

        if ($totalImpressions == 0) {
            return 0;
        }

        return round(($totalClicks / $totalImpressions) * 100, 2);
    }

    /**
     * Get monthly revenue
     */
    private function getMonthlyRevenue()
    {
        return Advertisement::whereMonth('created_at', now()->month)
                          ->whereYear('created_at', now()->year)
                          ->sum('spent_amount');
    }

    /**
     * Get ad performance data
     */
    private function getAdPerformanceData(Advertisement $advertisement)
    {
        // This would typically come from ad_analytics table
        // For now, return basic data
        return [
            'daily_impressions' => [],
            'daily_clicks' => [],
            'daily_spend' => [],
            'total_impressions' => $advertisement->impressions,
            'total_clicks' => $advertisement->clicks,
            'total_spend' => $advertisement->spent_amount,
            'ctr' => $advertisement->ctr,
            'cpc' => $advertisement->cpc,
            'cpm' => $advertisement->cpm,
        ];
    }
}

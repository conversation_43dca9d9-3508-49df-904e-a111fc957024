@extends('layouts.app')

@section('title', 'Boarding Pass Preview')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-plane mr-3 text-blue-600"></i>
                Boarding Pass Preview
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Preview template boarding pass E-Tiket TiXara</p>
        </div>

        <!-- Template Selection -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-palette mr-2"></i>
                    Pilih Template
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button onclick="loadTemplate('boarding-pass')"
                            class="template-btn active p-4 border-2 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center transition-all duration-200 hover:shadow-lg">
                        <div class="text-blue-600 dark:text-blue-400 mb-2">
                            <i class="fas fa-plane text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">Boarding Pass</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Modern airline style</p>
                    </button>

                    <button onclick="loadTemplate('classic-ticket')"
                            class="template-btn p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400">
                        <div class="text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-ticket-alt text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">Classic Ticket</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Traditional event ticket</p>
                    </button>

                    <button onclick="loadTemplate('modern-card')"
                            class="template-btn p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400">
                        <div class="text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-id-card text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">Modern Card</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Sleek card design</p>
                    </button>

                    <button onclick="loadTemplate('concert-festival')"
                            class="template-btn p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg text-center transition-all duration-200 hover:shadow-lg hover:border-blue-400">
                        <div class="text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-music text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-900 dark:text-white">Concert Festival</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Music & festival theme</p>
                    </button>
                </div>
            </div>
        </div>

        <!-- Sample Data Controls -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-edit mr-2"></i>
                    Sample Data
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Name</label>
                        <input type="text" id="eventName" value="TechConf 2024 - Future of Technology"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Attendee Name</label>
                        <input type="text" id="attendeeName" value="John Doe"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location</label>
                        <input type="text" id="location" value="Jakarta Convention Center, Jakarta"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date & Time</label>
                        <input type="datetime-local" id="eventDateTime" value="2024-12-15T09:00"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ticket Number</label>
                        <input type="text" id="ticketNumber" value="TIK-20241215-ABC123"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price</label>
                        <input type="text" id="price" value="Rp 250.000"
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                </div>

                <div class="mt-4 flex justify-end">
                    <button onclick="updatePreview()"
                            class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Update Preview
                    </button>
                </div>
            </div>
        </div>

        <!-- Preview Area -->
        <div class="max-w-6xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-eye mr-2"></i>
                        Template Preview
                    </h2>

                    <div class="flex space-x-3">
                        <button onclick="downloadPDF()"
                                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-file-pdf mr-2"></i>
                            Download PDF
                        </button>

                        <button onclick="printPreview()"
                                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-print mr-2"></i>
                            Print
                        </button>
                    </div>
                </div>

                <div id="previewContainer" class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                    <!-- Template will be loaded here -->
                    <iframe id="previewFrame"
                            src="/templates/boarding-pass-preview"
                            class="w-full h-96 border-0"
                            style="min-height: 600px;">
                    </iframe>
                </div>
            </div>
        </div>

        <!-- Template Info -->
        <div class="max-w-4xl mx-auto mt-8">
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-1"></i>
                    <div>
                        <h3 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">Template Features</h3>
                        <ul class="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                            <li>• Responsive design untuk desktop dan mobile</li>
                            <li>• QR Code terintegrasi untuk validasi</li>
                            <li>• Informasi event lengkap dan terstruktur</li>
                            <li>• Desain modern dengan branding TiXara</li>
                            <li>• Support untuk print dan download PDF</li>
                            <li>• Watermark keamanan untuk mencegah pemalsuan</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let currentTemplate = 'boarding-pass';

// Template configurations
const templateConfigs = {
    'boarding-pass': {
        name: 'Boarding Pass',
        route: 'templates.boarding-pass-preview',
        defaultEvent: 'TechConf 2024 - Future of Technology',
        defaultLocation: 'Jakarta Convention Center, Jakarta'
    },
    'classic-ticket': {
        name: 'Classic Ticket',
        route: 'templates.classic-ticket-preview',
        defaultEvent: 'Classic Event 2024',
        defaultLocation: 'Grand Ballroom, Hotel Indonesia'
    },
    'modern-card': {
        name: 'Modern Card',
        route: 'templates.modern-card-preview',
        defaultEvent: 'Modern Tech Summit 2024',
        defaultLocation: 'ICE BSD Convention Center'
    },
    'concert-festival': {
        name: 'Concert Festival',
        route: 'templates.concert-festival-preview',
        defaultEvent: 'Summer Music Festival 2024',
        defaultLocation: 'Gelora Bung Karno Stadium, Jakarta'
    }
};

function loadTemplate(templateName) {
    currentTemplate = templateName;

    // Update active button
    document.querySelectorAll('.template-btn').forEach(btn => {
        btn.classList.remove('active', 'border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
        btn.classList.add('border-gray-300', 'dark:border-gray-600');
    });

    event.target.closest('.template-btn').classList.add('active', 'border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
    event.target.closest('.template-btn').classList.remove('border-gray-300', 'dark:border-gray-600');

    // Update default values based on template
    const config = templateConfigs[templateName];
    if (config) {
        document.getElementById('eventName').value = config.defaultEvent;
        document.getElementById('location').value = config.defaultLocation;

        // Update datetime for concert/festival to evening
        if (templateName === 'concert-festival') {
            document.getElementById('eventDateTime').value = '2024-12-15T19:00';
        } else {
            document.getElementById('eventDateTime').value = '2024-12-15T09:00';
        }
    }

    // Load template preview
    updatePreview();
}

function updatePreview() {
    const data = {
        template: currentTemplate,
        eventName: document.getElementById('eventName').value,
        attendeeName: document.getElementById('attendeeName').value,
        location: document.getElementById('location').value,
        eventDateTime: document.getElementById('eventDateTime').value,
        ticketNumber: document.getElementById('ticketNumber').value,
        price: document.getElementById('price').value
    };

    const queryString = new URLSearchParams(data).toString();
    const previewUrl = `/templates/${currentTemplate}-preview?${queryString}`;

    document.getElementById('previewFrame').src = previewUrl;
}

function downloadPDF() {
    const data = {
        template: currentTemplate,
        eventName: document.getElementById('eventName').value,
        attendeeName: document.getElementById('attendeeName').value,
        location: document.getElementById('location').value,
        eventDateTime: document.getElementById('eventDateTime').value,
        ticketNumber: document.getElementById('ticketNumber').value,
        price: document.getElementById('price').value,
        format: 'pdf'
    };

    const queryString = new URLSearchParams(data).toString();
    const downloadUrl = `/templates/${currentTemplate}-download?${queryString}`;

    window.open(downloadUrl, '_blank');
}

function printPreview() {
    const previewFrame = document.getElementById('previewFrame');
    previewFrame.contentWindow.print();
}

// Auto-update preview when inputs change
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['eventName', 'attendeeName', 'location', 'eventDateTime', 'ticketNumber', 'price'];

    inputs.forEach(inputId => {
        document.getElementById(inputId).addEventListener('input', debounce(updatePreview, 500));
    });
});

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
@endpush
@endsection

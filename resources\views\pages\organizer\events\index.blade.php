@extends('layouts.organizer')

@section('title', 'My Events - Organizer Dashboard')

@push('styles')
<style>
/* Modern Events Dashboard Styles */
.events-dashboard {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.event-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.event-card.published {
    border-left-color: #10b981;
}

.event-card.draft {
    border-left-color: #f59e0b;
}

.event-card.completed {
    border-left-color: #6b7280;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-published {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.status-draft {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.status-completed {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.search-filter-bar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
}

.btn-modern {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-secondary-modern {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    border: 2px solid #e5e7eb;
}

.btn-secondary-modern:hover {
    background: rgba(107, 114, 128, 0.2);
    border-color: #d1d5db;
}

.floating-action {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 50;
}

.floating-btn {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
    transition: all 0.3s ease;
}

.floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.5);
}

.metric-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-blue {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.icon-green {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.icon-yellow {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.icon-gray {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 2px dashed #d1d5db;
    border-radius: 16px;
}

@media (max-width: 768px) {
    .floating-action {
        bottom: 5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
}
</style>
@endpush

@section('content')
<div class="events-dashboard">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-up">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div>
                    <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-2">My Events</h1>
                    <p class="text-gray-600 dark:text-gray-400 text-lg">Manage your events and track performance</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('organizer.events.create') }}"
                       class="btn-modern btn-primary-modern">
                        <i data-lucide="plus" class="w-5 h-5"></i>
                        Create New Event
                    </a>
                    <button class="btn-modern btn-secondary-modern">
                        <i data-lucide="download" class="w-5 h-5"></i>
                        Export Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8" data-aos="fade-up" data-aos-delay="100">
            <!-- Total Events -->
            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Events</p>
                        <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ $events->count() ?? 24 }}</p>
                    </div>
                    <div class="metric-icon icon-blue">
                        <i data-lucide="calendar" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600">+15% from last month</span>
                </div>
            </div>

            <!-- Published Events -->
            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Published Events</p>
                        <p class="text-3xl font-bold text-green-600">{{ $events->where('status', 'published')->count() ?? 18 }}</p>
                    </div>
                    <div class="metric-icon icon-green">
                        <i data-lucide="check-circle" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600">{{ round(($events->where('status', 'published')->count() / max($events->count(), 1)) * 100) }}% of total</span>
                </div>
            </div>

            <!-- Total Tickets Sold -->
            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Tickets Sold</p>
                        <p class="text-3xl font-bold text-blue-600">{{ number_format($events->sum('total_capacity') - $events->sum('available_capacity')) ?? '12,847' }}</p>
                    </div>
                    <div class="metric-icon icon-blue">
                        <i data-lucide="ticket" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-blue-600">{{ round((($events->sum('total_capacity') - $events->sum('available_capacity')) / max($events->sum('total_capacity'), 1)) * 100) }}% sold rate</span>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="stats-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Revenue</p>
                        <p class="text-3xl font-bold text-yellow-600">Rp {{ number_format($events->sum(function($event) { return $event->price * ($event->total_capacity - $event->available_capacity); }) ?? 2847500000, 0, ',', '.') }}</p>
                    </div>
                    <div class="metric-icon icon-yellow">
                        <i data-lucide="dollar-sign" class="w-6 h-6"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-yellow-600">+23% from last month</span>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="search-filter-bar rounded-xl p-6 mb-8" data-aos="fade-up" data-aos-delay="200">
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Box -->
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" id="search" placeholder="Search events by title, venue, or city..."
                               class="w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-green-500 transition-colors">
                        <i data-lucide="search" class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
                    </div>
                </div>

                <!-- Filter Buttons -->
                <div class="flex gap-2">
                    <button class="filter-btn active px-4 py-3 rounded-xl text-sm font-medium border-2 border-green-500 bg-green-500 text-white" data-filter="all">
                        All Events
                    </button>
                    <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium border-2 border-gray-200 text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors" data-filter="published">
                        Published
                    </button>
                    <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium border-2 border-gray-200 text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors" data-filter="draft">
                        Draft
                    </button>
                    <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium border-2 border-gray-200 text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors" data-filter="upcoming">
                        Upcoming
                    </button>
                </div>
            </div>
        </div>

        <!-- Events Grid -->
        <div class="space-y-6" data-aos="fade-up" data-aos-delay="300">
            @php
                // Sample events data for 2025 (this would come from controller)
                $sampleEvents = [
                    [
                        'id' => 1,
                        'title' => 'Jakarta Music Festival 2025',
                        'venue_name' => 'Gelora Bung Karno',
                        'city' => 'Jakarta',
                        'start_date' => '2025-03-15 18:00:00',
                        'price' => 350000,
                        'total_capacity' => 50000,
                        'available_capacity' => 35000,
                        'status' => 'published',
                        'category' => 'Musik',
                        'poster' => 'events/poster-1.jpg'
                    ],
                    [
                        'id' => 2,
                        'title' => 'Tech Summit Indonesia 2025',
                        'venue_name' => 'Jakarta Convention Center',
                        'city' => 'Jakarta',
                        'start_date' => '2025-04-20 09:00:00',
                        'price' => 750000,
                        'total_capacity' => 2000,
                        'available_capacity' => 800,
                        'status' => 'published',
                        'category' => 'Teknologi',
                        'poster' => 'events/poster-2.jpg'
                    ],
                    [
                        'id' => 3,
                        'title' => 'Bandung Art Exhibition 2025',
                        'venue_name' => 'Gedung Sate',
                        'city' => 'Bandung',
                        'start_date' => '2025-05-10 10:00:00',
                        'price' => 50000,
                        'total_capacity' => 500,
                        'available_capacity' => 200,
                        'status' => 'published',
                        'category' => 'Seni',
                        'poster' => 'events/poster-3.jpg'
                    ],
                    [
                        'id' => 4,
                        'title' => 'Surabaya Marathon 2025',
                        'venue_name' => 'Tugu Pahlawan',
                        'city' => 'Surabaya',
                        'start_date' => '2025-06-08 05:00:00',
                        'price' => 200000,
                        'total_capacity' => 10000,
                        'available_capacity' => 3000,
                        'status' => 'published',
                        'category' => 'Olahraga',
                        'poster' => 'events/poster-4.jpg'
                    ]
                ];

                // Use actual events if available, otherwise use sample data
                $displayEvents = $events ?? collect($sampleEvents);
            @endphp

            @forelse($displayEvents as $event)
            <div class="event-card {{ $event['status'] ?? $event->status ?? 'published' }} rounded-xl p-6" data-status="{{ $event['status'] ?? $event->status ?? 'published' }}">
                <div class="flex flex-col lg:flex-row gap-6">
                    <!-- Event Image -->
                    <div class="lg:w-48 lg:flex-shrink-0">
                        <div class="w-full h-32 lg:h-full bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center">
                            <i data-lucide="calendar-days" class="w-12 h-12 text-white"></i>
                        </div>
                    </div>

                    <!-- Event Details -->
                    <div class="flex-1">
                        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ $event['title'] ?? $event->title ?? 'Event Title' }}</h3>
                                    <span class="status-badge status-{{ $event['status'] ?? $event->status ?? 'published' }}">
                                        {{ ucfirst($event['status'] ?? $event->status ?? 'published') }}
                                    </span>
                                </div>

                                <div class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                    <div class="flex items-center gap-2">
                                        <i data-lucide="map-pin" class="w-4 h-4"></i>
                                        <span>{{ $event['venue_name'] ?? $event->venue_name ?? 'Venue Name' }}, {{ $event['city'] ?? $event->city ?? 'City' }}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i data-lucide="calendar" class="w-4 h-4"></i>
                                        <span>{{ \Carbon\Carbon::parse($event['start_date'] ?? $event->start_date ?? now())->format('d M Y, H:i') }}</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i data-lucide="tag" class="w-4 h-4"></i>
                                        <span>{{ $event['category'] ?? $event->category->name ?? 'Category' }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Event Stats -->
                            <div class="lg:text-right">
                                <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                                    Rp {{ number_format($event['price'] ?? $event->price ?? 0, 0, ',', '.') }}
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                                    {{ number_format(($event['total_capacity'] ?? $event->total_capacity ?? 0) - ($event['available_capacity'] ?? $event->available_capacity ?? 0)) }} / {{ number_format($event['total_capacity'] ?? $event->total_capacity ?? 0) }} sold
                                </div>

                                <!-- Progress Bar -->
                                <div class="w-32 lg:w-24 bg-gray-200 rounded-full h-2 mb-4">
                                    @php
                                        $totalCap = $event['total_capacity'] ?? $event->total_capacity ?? 1;
                                        $availableCap = $event['available_capacity'] ?? $event->available_capacity ?? 0;
                                        $soldPercentage = (($totalCap - $availableCap) / $totalCap) * 100;
                                    @endphp
                                    <div class="bg-green-500 h-2 rounded-full" style="width: {{ $soldPercentage }}%"></div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex lg:flex-col gap-2">
                                    <a href="{{ route('organizer.events.show', $event['id'] ?? $event->id ?? 1) }}"
                                       class="btn-modern btn-secondary-modern text-xs px-3 py-2">
                                        <i data-lucide="eye" class="w-3 h-3"></i>
                                        View
                                    </a>
                                    <a href="{{ route('organizer.events.edit', $event['id'] ?? $event->id ?? 1) }}"
                                       class="btn-modern btn-secondary-modern text-xs px-3 py-2">
                                        <i data-lucide="edit" class="w-3 h-3"></i>
                                        Edit
                                    </a>
                                    <button onclick="openTemplateModal({{ $event['id'] ?? $event->id ?? 1 }})"
                                            class="btn-modern text-xs px-3 py-2 bg-purple-100 text-purple-600 border-2 border-purple-200 hover:bg-purple-200">
                                        <i data-lucide="layout-template" class="w-3 h-3"></i>
                                        Template
                                    </button>
                                    <button onclick="deleteEvent({{ $event['id'] ?? $event->id ?? 1 }})"
                                            class="btn-modern text-xs px-3 py-2 bg-red-100 text-red-600 border-2 border-red-200 hover:bg-red-200">
                                        <i data-lucide="trash-2" class="w-3 h-3"></i>
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="empty-state">
                <div class="max-w-md mx-auto">
                    <i data-lucide="calendar-plus" class="w-16 h-16 text-gray-400 mx-auto mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No events found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">Get started by creating your first event and start selling tickets.</p>
                    <a href="{{ route('organizer.events.create') }}" class="btn-modern btn-primary-modern">
                        <i data-lucide="plus" class="w-5 h-5"></i>
                        Create Your First Event
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!-- Floating Action Button (Mobile) -->
        <div class="floating-action lg:hidden">
            <a href="{{ route('organizer.events.create') }}" class="floating-btn">
                <i data-lucide="plus" class="w-6 h-6"></i>
            </a>
        </div>
    </div>
</div>

<!-- Template Selection Modal -->
<div id="templateModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeTemplateModal()"></div>

        <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-2xl font-bold text-gray-900">Select E-Ticket Template</h3>
                <button onclick="closeTemplateModal()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- Unix Template -->
                <div class="template-option cursor-pointer" data-template="unix">
                    <div class="template-card border-2 border-gray-200 rounded-xl p-4 hover:border-purple-500 transition-all duration-200">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gray-900 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="terminal" class="w-8 h-8 text-green-400"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Unix Terminal</h4>
                            <p class="text-sm text-gray-600">Terminal-style design with monospace fonts</p>
                        </div>
                    </div>
                </div>

                <!-- Minimalist Template -->
                <div class="template-option cursor-pointer" data-template="minimalist">
                    <div class="template-card border-2 border-gray-200 rounded-xl p-4 hover:border-purple-500 transition-all duration-200">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gray-50 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="minimize-2" class="w-8 h-8 text-gray-600"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Minimalist</h4>
                            <p class="text-sm text-gray-600">Clean and simple design</p>
                        </div>
                    </div>
                </div>

                <!-- Pro Template -->
                <div class="template-option cursor-pointer" data-template="pro">
                    <div class="template-card border-2 border-gray-200 rounded-xl p-4 hover:border-purple-500 transition-all duration-200">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="crown" class="w-8 h-8 text-white"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Pro Boarding Pass</h4>
                            <p class="text-sm text-gray-600">Professional airline-style design</p>
                        </div>
                    </div>
                </div>

                <!-- Custom Template -->
                <div class="template-option cursor-pointer" data-template="custom">
                    <div class="template-card border-2 border-gray-200 rounded-xl p-4 hover:border-purple-500 transition-all duration-200">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i data-lucide="palette" class="w-8 h-8 text-white"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">Custom Template</h4>
                            <p class="text-sm text-gray-600">Fully customizable design</p>
                            <span class="inline-block mt-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Platinum Only</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <label class="flex items-center gap-2">
                        <input type="checkbox" id="autoGenerate" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="text-sm text-gray-700">Auto-generate tickets after payment</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" id="emailTickets" checked class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
                        <span class="text-sm text-gray-700">Email tickets to buyers</span>
                    </label>
                </div>
                <div class="flex gap-3">
                    <button onclick="closeTemplateModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button onclick="saveTemplate()" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                        Save Template
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('search');
    const filterBtns = document.querySelectorAll('.filter-btn');

    // Search events
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const eventCards = document.querySelectorAll('.event-card');

        eventCards.forEach(card => {
            const title = card.querySelector('h3').textContent.toLowerCase();
            const venue = card.querySelector('.flex.items-center.gap-2 span').textContent.toLowerCase();

            if (title.includes(searchTerm) || venue.includes(searchTerm)) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
    });

    // Filter events by status
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.dataset.filter;

            // Update active button
            filterBtns.forEach(b => {
                b.classList.remove('active', 'bg-green-500', 'text-white', 'border-green-500');
                b.classList.add('border-gray-200', 'text-gray-600');
            });

            this.classList.add('active', 'bg-green-500', 'text-white', 'border-green-500');
            this.classList.remove('border-gray-200', 'text-gray-600');

            // Filter events
            const eventCards = document.querySelectorAll('.event-card');
            eventCards.forEach(card => {
                if (filter === 'all') {
                    card.style.display = '';
                } else if (filter === 'upcoming') {
                    // Show events that are in the future
                    card.style.display = '';
                } else {
                    const status = card.dataset.status;
                    if (status === filter) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                }
            });
        });
    });
});

function deleteEvent(eventId) {
    if (confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
        // Make AJAX request to delete event
        fetch(`/organizer/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting event: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting event');
        });
    }
}

// Template management functions
let currentEventId = null;
let selectedTemplate = null;

function openTemplateModal(eventId) {
    currentEventId = eventId;
    selectedTemplate = null;

    // Reset template selection
    document.querySelectorAll('.template-option').forEach(option => {
        option.querySelector('.template-card').classList.remove('border-purple-500', 'bg-purple-50');
        option.querySelector('.template-card').classList.add('border-gray-200');
    });

    // Show modal
    document.getElementById('templateModal').classList.remove('hidden');

    // Initialize Lucide icons for modal
    if (window.lucide) {
        window.lucide.createIcons();
    }
}

function closeTemplateModal() {
    document.getElementById('templateModal').classList.add('hidden');
    currentEventId = null;
    selectedTemplate = null;
}

function saveTemplate() {
    if (!selectedTemplate) {
        alert('Please select a template first');
        return;
    }

    if (!currentEventId) {
        alert('No event selected');
        return;
    }

    const autoGenerate = document.getElementById('autoGenerate').checked;
    const emailTickets = document.getElementById('emailTickets').checked;

    // Make AJAX request to save template
    fetch(`/organizer/events/${currentEventId}/template`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify({
            boarding_pass_template: selectedTemplate,
            auto_generate_tickets: autoGenerate,
            email_tickets_to_buyers: emailTickets
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeTemplateModal();
            // Show success message
            showNotification('Template updated successfully!', 'success');
        } else {
            alert('Error updating template: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating template');
    });
}

function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// Template selection handling
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for template options
    document.querySelectorAll('.template-option').forEach(option => {
        option.addEventListener('click', function() {
            const template = this.dataset.template;

            // Check if custom template and user doesn't have platinum badge
            if (template === 'custom') {
                // You can add badge check here
                // For now, we'll allow all users to select custom template
            }

            // Remove selection from all templates
            document.querySelectorAll('.template-option').forEach(opt => {
                opt.querySelector('.template-card').classList.remove('border-purple-500', 'bg-purple-50');
                opt.querySelector('.template-card').classList.add('border-gray-200');
            });

            // Add selection to clicked template
            this.querySelector('.template-card').classList.remove('border-gray-200');
            this.querySelector('.template-card').classList.add('border-purple-500', 'bg-purple-50');

            selectedTemplate = template;
        });
    });
});
</script>
@endpush
@endsection

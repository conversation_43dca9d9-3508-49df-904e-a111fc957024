@extends('layouts.organizer')

@section('title', $event->title . ' - Event Details')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $event->title }}</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">Event Details & Management</p>
                </div>
                <div class="flex space-x-3">
                    @if($event->status === 'draft')
                        <form action="{{ route('organizer.events.publish', $event) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200"
                                    onclick="return confirm('Are you sure you want to publish this event?')">
                                <i data-lucide="send" class="w-4 h-4 inline mr-2"></i>
                                Publish Event
                            </button>
                        </form>
                    @elseif($event->status === 'published')
                        <form action="{{ route('organizer.events.unpublish', $event) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit"
                                    class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors duration-200"
                                    onclick="return confirm('Are you sure you want to unpublish this event?')">
                                <i data-lucide="eye-off" class="w-4 h-4 inline mr-2"></i>
                                Unpublish Event
                            </button>
                        </form>
                    @endif

                    <a href="{{ route('organizer.events.edit', $event) }}"
                       class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                        <i data-lucide="edit" class="w-4 h-4 inline mr-2"></i>
                        Edit Event
                    </a>

                    <a href="{{ route('organizer.events.index') }}"
                       class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        <i data-lucide="arrow-left" class="w-4 h-4 inline mr-2"></i>
                        Back to Events
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Event Overview -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div class="aspect-w-16 aspect-h-9">
                        <img src="{{ $event->poster_url }}"
                             alt="{{ $event->title }}"
                             class="w-full h-64 object-cover">
                    </div>

                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <span class="px-3 py-1 text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">
                                    {{ $event->category->name }}
                                </span>
                                @if($event->status == 'published')
                                    <span class="px-3 py-1 text-sm font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded-full">Published</span>
                                @elseif($event->status == 'draft')
                                    <span class="px-3 py-1 text-sm font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 rounded-full">Draft</span>
                                @else
                                    <span class="px-3 py-1 text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">{{ ucfirst($event->status) }}</span>
                                @endif
                            </div>

                            <div class="text-right">
                                @if($event->is_free)
                                    <span class="text-2xl font-bold text-green-600 dark:text-green-400">Free</span>
                                @else
                                    <span class="text-2xl font-bold text-gray-900 dark:text-white">Rp {{ number_format($event->price, 0, ',', '.') }}</span>
                                @endif
                            </div>
                        </div>

                        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Description</h2>
                        <div class="prose prose-gray dark:prose-invert max-w-none">
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">{{ $event->description }}</p>
                        </div>
                    </div>
                </div>

                <!-- Event Details -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Event Details</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date & Time</label>
                            <div class="flex items-center text-gray-900 dark:text-white">
                                <i data-lucide="calendar" class="w-5 h-5 mr-2 text-gray-400"></i>
                                {{ $event->start_date->format('l, F j, Y') }}
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400 mt-1">
                                <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-400"></i>
                                {{ $event->start_date->format('g:i A') }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date & Time</label>
                            <div class="flex items-center text-gray-900 dark:text-white">
                                <i data-lucide="calendar" class="w-5 h-5 mr-2 text-gray-400"></i>
                                {{ $event->end_date->format('l, F j, Y') }}
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400 mt-1">
                                <i data-lucide="clock" class="w-5 h-5 mr-2 text-gray-400"></i>
                                {{ $event->end_date->format('g:i A') }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Venue</label>
                            <div class="flex items-start text-gray-900 dark:text-white">
                                <i data-lucide="map-pin" class="w-5 h-5 mr-2 text-gray-400 mt-0.5"></i>
                                <div>
                                    <div class="font-medium">{{ $event->venue_name }}</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $event->venue_address }}</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $event->city }}, {{ $event->province }}</div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Capacity</label>
                            <div class="flex items-center text-gray-900 dark:text-white">
                                <i data-lucide="users" class="w-5 h-5 mr-2 text-gray-400"></i>
                                <span class="font-medium">{{ $event->available_capacity }}</span>
                                <span class="text-gray-600 dark:text-gray-400 mx-1">/</span>
                                <span>{{ $event->total_capacity }} available</span>
                            </div>
                            <div class="mt-2">
                                <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="bg-primary rounded-full h-2"
                                         style="width: {{ ($event->total_capacity - $event->available_capacity) / $event->total_capacity * 100 }}%"></div>
                                </div>
                                <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    {{ $event->total_capacity - $event->available_capacity }} tickets sold
                                </div>
                            </div>
                        </div>

                        @if($event->sale_start_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sale Period</label>
                            <div class="text-gray-900 dark:text-white">
                                <div class="flex items-center">
                                    <i data-lucide="play-circle" class="w-5 h-5 mr-2 text-green-500"></i>
                                    <span class="text-sm">{{ $event->sale_start_date->format('M j, Y g:i A') }}</span>
                                </div>
                                @if($event->sale_end_date)
                                <div class="flex items-center mt-1">
                                    <i data-lucide="stop-circle" class="w-5 h-5 mr-2 text-red-500"></i>
                                    <span class="text-sm">{{ $event->sale_end_date->format('M j, Y g:i A') }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                        @endif

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Settings</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <i data-lucide="{{ $event->requires_approval ? 'check-circle' : 'x-circle' }}"
                                       class="w-5 h-5 mr-2 {{ $event->requires_approval ? 'text-green-500' : 'text-gray-400' }}"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $event->requires_approval ? 'Requires Approval' : 'Auto Approval' }}
                                    </span>
                                </div>
                                <div class="flex items-center">
                                    <i data-lucide="{{ $event->is_free ? 'gift' : 'credit-card' }}"
                                       class="w-5 h-5 mr-2 {{ $event->is_free ? 'text-green-500' : 'text-blue-500' }}"></i>
                                    <span class="text-sm text-gray-600 dark:text-gray-400">
                                        {{ $event->is_free ? 'Free Event' : 'Paid Event' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gallery -->
                @if($event->gallery && count($event->gallery) > 0)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Gallery</h2>

                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        @foreach($event->gallery as $image)
                            <div class="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
                                <img src="{{ Storage::url($image) }}"
                                     alt="Gallery Image"
                                     class="w-full h-32 object-cover hover:scale-105 transition-transform duration-200 cursor-pointer"
                                     onclick="openImageModal('{{ Storage::url($image) }}')">
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Quick Stats -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Stats</h3>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Total Views</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($event->views ?? 0) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Tickets Sold</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($event->total_capacity - $event->available_capacity) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Revenue</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">
                                @if($event->is_free)
                                    Free Event
                                @else
                                    Rp {{ number_format(($event->total_capacity - $event->available_capacity) * $event->price, 0, ',', '.') }}
                                @endif
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Created</span>
                            <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $event->created_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>

                    <div class="space-y-3">
                        <a href="{{ route('tickets.show', $event->slug) }}"
                           target="_blank"
                           class="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors inline-block text-center">
                            <i data-lucide="external-link" class="w-4 h-4 inline mr-2"></i>
                            View Public Page
                        </a>

                        <button onclick="copyEventLink()"
                                class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i data-lucide="link" class="w-4 h-4 inline mr-2"></i>
                            Copy Event Link
                        </button>

                        <button onclick="shareEvent()"
                                class="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <i data-lucide="share-2" class="w-4 h-4 inline mr-2"></i>
                            Share Event
                        </button>

                        @if($event->status === 'published')
                        <a href="#"
                           class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors inline-block text-center">
                            <i data-lucide="bar-chart" class="w-4 h-4 inline mr-2"></i>
                            View Analytics
                        </a>
                        @endif

                        <button onclick="downloadQR()"
                                class="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                            <i data-lucide="qr-code" class="w-4 h-4 inline mr-2"></i>
                            Download QR Code
                        </button>
                    </div>
                </div>

                <!-- Event Status -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Event Status</h3>

                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Status</span>
                            @if($event->status == 'published')
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400 rounded-full">Published</span>
                            @elseif($event->status == 'draft')
                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400 rounded-full">Draft</span>
                            @else
                                <span class="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-full">{{ ucfirst($event->status) }}</span>
                            @endif
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Sale Status</span>
                            @php
                                $now = now();
                                $saleStart = $event->sale_start_date;
                                $saleEnd = $event->sale_end_date;

                                if ($saleStart && $now < $saleStart) {
                                    $saleStatus = 'Not Started';
                                    $saleColor = 'gray';
                                } elseif ($saleEnd && $now > $saleEnd) {
                                    $saleStatus = 'Ended';
                                    $saleColor = 'red';
                                } elseif ($event->available_capacity <= 0) {
                                    $saleStatus = 'Sold Out';
                                    $saleColor = 'red';
                                } else {
                                    $saleStatus = 'Active';
                                    $saleColor = 'green';
                                }
                            @endphp
                            <span class="px-2 py-1 text-xs font-medium bg-{{ $saleColor }}-100 dark:bg-{{ $saleColor }}-900/20 text-{{ $saleColor }}-800 dark:text-{{ $saleColor }}-400 rounded-full">
                                {{ $saleStatus }}
                            </span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600 dark:text-gray-400">Event Date</span>
                            @php
                                $eventStart = $event->start_date;
                                if ($now < $eventStart) {
                                    $eventStatus = 'Upcoming';
                                    $eventColor = 'blue';
                                } elseif ($now > $event->end_date) {
                                    $eventStatus = 'Completed';
                                    $eventColor = 'gray';
                                } else {
                                    $eventStatus = 'Ongoing';
                                    $eventColor = 'green';
                                }
                            @endphp
                            <span class="px-2 py-1 text-xs font-medium bg-{{ $eventColor }}-100 dark:bg-{{ $eventColor }}-900/20 text-{{ $eventColor }}-800 dark:text-{{ $eventColor }}-400 rounded-full">
                                {{ $eventStatus }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center p-4">
    <div class="relative max-w-4xl max-h-full">
        <img id="modalImage" src="" alt="Gallery Image" class="max-w-full max-h-full object-contain">
        <button onclick="closeImageModal()"
                class="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors">
            <i data-lucide="x" class="w-8 h-8"></i>
        </button>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Image Modal Functions
function openImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}

// Copy Event Link
function copyEventLink() {
    const eventUrl = '{{ route("tickets.show", $event->slug) }}';
    navigator.clipboard.writeText(eventUrl).then(function() {
        // Show success message
        showNotification('Event link copied to clipboard!', 'success');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showNotification('Failed to copy link', 'error');
    });
}

// Share Event
function shareEvent() {
    const eventData = {
        title: '{{ $event->title }}',
        text: '{{ Str::limit($event->description, 100) }}',
        url: '{{ route("tickets.show", $event->slug) }}'
    };

    if (navigator.share) {
        navigator.share(eventData).catch(console.error);
    } else {
        // Fallback to copy link
        copyEventLink();
    }
}

// Download QR Code
function downloadQR() {
    const downloadUrl = '{{ route("organizer.events.qr-code", $event) }}';

    // Show loading notification
    showNotification('Generating QR Code...', 'info');

    // Create a temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `qr-code-{{ $event->slug }}.svg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success notification after a short delay
    setTimeout(() => {
        showNotification('QR Code downloaded successfully!', 'success');
    }, 1000);
}

// Show Notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full`;

    // Set color based on type
    switch(type) {
        case 'success':
            notification.className += ' bg-green-500 text-white';
            break;
        case 'error':
            notification.className += ' bg-red-500 text-white';
            break;
        case 'info':
        default:
            notification.className += ' bg-blue-500 text-white';
            break;
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeImageModal();
    }
});

// Close modal on background click
document.getElementById('imageModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeImageModal();
    }
});
</script>
@endpush

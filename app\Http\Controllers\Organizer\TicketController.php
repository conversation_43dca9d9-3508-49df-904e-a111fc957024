<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Ticket;
use App\Models\Event;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TicketController extends Controller
{
    /**
     * Display organizer's tickets
     */
    public function index(Request $request)
    {
        $query = Ticket::whereHas('event', function($q) {
                    $q->where('organizer_id', Auth::id());
                })
                ->with(['event', 'order.user']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('ticket_number', 'like', "%{$search}%")
                  ->orWhereHas('order.user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('event', function($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by event
        if ($request->filled('event_id')) {
            $query->whereHas('event', function($q) use ($request) {
                $q->where('id', $request->event_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $tickets = $query->latest()->paginate(20);

        // Get organizer's events for filter
        $events = Event::where('organizer_id', Auth::id())
                      ->select('id', 'title')
                      ->get();

        // Statistics
        $stats = [
            'total_tickets' => Ticket::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->count(),
            'active_tickets' => Ticket::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'active')->count(),
            'used_tickets' => Ticket::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'used')->count(),
            'cancelled_tickets' => Ticket::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'cancelled')->count(),
        ];

        return view('pages.organizer.events.index', compact('tickets', 'events', 'stats'));
    }

    /**
     * Show ticket details
     */
    public function show(Ticket $ticket)
    {
        // Ensure ticket belongs to organizer's event
        if ($ticket->event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this ticket.');
        }

        $ticket->load(['event', 'order.user', 'order.payment']);

        return view('pages.organizer.events.show', compact('ticket'));
    }

    /**
     * Validate ticket (mark as used)
     */
    public function validateTicket(Request $request, Ticket $ticket)
    {
        // Ensure ticket belongs to organizer's event
        if ($ticket->event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this ticket.');
        }

        if ($ticket->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'Ticket is not active and cannot be validated.'
            ]);
        }

        // Check if event has started
        if ($ticket->event->start_date > now()) {
            return response()->json([
                'success' => false,
                'message' => 'Event has not started yet.'
            ]);
        }

        // Check if event has ended
        if ($ticket->event->end_date < now()) {
            return response()->json([
                'success' => false,
                'message' => 'Event has already ended.'
            ]);
        }

        $ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => Auth::id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Ticket validated successfully!',
            'ticket' => [
                'number' => $ticket->ticket_number,
                'holder' => $ticket->order->user->name,
                'event' => $ticket->event->title,
                'used_at' => $ticket->used_at->format('Y-m-d H:i:s')
            ]
        ]);
    }

    /**
     * Cancel ticket
     */
    public function cancel(Request $request, Ticket $ticket)
    {
        // Ensure ticket belongs to organizer's event
        if ($ticket->event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this ticket.');
        }

        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        if ($ticket->status === 'used') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel a ticket that has already been used.'
            ]);
        }

        $ticket->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $request->reason,
            'cancelled_by' => Auth::id(),
        ]);

        // Update event capacity
        $ticket->event->increment('available_capacity');

        return response()->json([
            'success' => true,
            'message' => 'Ticket cancelled successfully!'
        ]);
    }

    /**
     * Bulk validate tickets
     */
    public function bulkValidate(Request $request)
    {
        $request->validate([
            'ticket_ids' => 'required|array',
            'ticket_ids.*' => 'exists:tickets,id'
        ]);

        $tickets = Ticket::whereIn('id', $request->ticket_ids)
                        ->whereHas('event', function($q) {
                            $q->where('organizer_id', Auth::id());
                        })
                        ->where('status', 'active')
                        ->get();

        $validatedCount = 0;
        $errors = [];

        foreach ($tickets as $ticket) {
            // Check if event is active
            if ($ticket->event->start_date > now()) {
                $errors[] = "Ticket {$ticket->ticket_number}: Event has not started yet.";
                continue;
            }

            if ($ticket->event->end_date < now()) {
                $errors[] = "Ticket {$ticket->ticket_number}: Event has already ended.";
                continue;
            }

            $ticket->update([
                'status' => 'used',
                'used_at' => now(),
                'validated_by' => Auth::id(),
            ]);

            $validatedCount++;
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully validated {$validatedCount} tickets.",
            'validated_count' => $validatedCount,
            'errors' => $errors
        ]);
    }

    /**
     * Export tickets
     */
    public function export(Request $request)
    {
        $query = Ticket::whereHas('event', function($q) {
                    $q->where('organizer_id', Auth::id());
                })
                ->with(['event', 'order.user']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('event_id')) {
            $query->whereHas('event', function($q) use ($request) {
                $q->where('id', $request->event_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $tickets = $query->get();

        $csvData = [];
        $csvData[] = [
            'Ticket Number',
            'Event Title',
            'Customer Name',
            'Customer Email',
            'Status',
            'Purchase Date',
            'Used Date',
            'Order Number',
            'Price'
        ];

        foreach ($tickets as $ticket) {
            $csvData[] = [
                $ticket->ticket_number,
                $ticket->event->title,
                $ticket->order->user->name,
                $ticket->order->user->email,
                ucfirst($ticket->status),
                $ticket->created_at->format('Y-m-d H:i:s'),
                $ticket->used_at ? $ticket->used_at->format('Y-m-d H:i:s') : 'N/A',
                $ticket->order->order_number,
                'Rp ' . number_format($ticket->event->price, 0, ',', '.')
            ];
        }

        $filename = 'tickets_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Scan ticket by QR code
     */
    public function scan(Request $request)
    {
        $request->validate([
            'ticket_number' => 'required|string'
        ]);

        $ticket = Ticket::where('ticket_number', $request->ticket_number)
                       ->whereHas('event', function($q) {
                           $q->where('organizer_id', Auth::id());
                       })
                       ->with(['event', 'order.user'])
                       ->first();

        if (!$ticket) {
            return response()->json([
                'success' => false,
                'message' => 'Ticket not found or does not belong to your events.'
            ]);
        }

        // Check ticket status
        if ($ticket->status === 'used') {
            return response()->json([
                'success' => false,
                'message' => 'Ticket has already been used.',
                'ticket' => [
                    'number' => $ticket->ticket_number,
                    'holder' => $ticket->order->user->name,
                    'event' => $ticket->event->title,
                    'status' => $ticket->status,
                    'used_at' => $ticket->used_at->format('Y-m-d H:i:s')
                ]
            ]);
        }

        if ($ticket->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'Ticket has been cancelled.',
                'ticket' => [
                    'number' => $ticket->ticket_number,
                    'holder' => $ticket->order->user->name,
                    'event' => $ticket->event->title,
                    'status' => $ticket->status,
                    'cancelled_at' => $ticket->cancelled_at->format('Y-m-d H:i:s')
                ]
            ]);
        }

        // Check event timing
        if ($ticket->event->start_date > now()) {
            return response()->json([
                'success' => false,
                'message' => 'Event has not started yet.',
                'ticket' => [
                    'number' => $ticket->ticket_number,
                    'holder' => $ticket->order->user->name,
                    'event' => $ticket->event->title,
                    'status' => $ticket->status,
                    'event_start' => $ticket->event->start_date->format('Y-m-d H:i:s')
                ]
            ]);
        }

        if ($ticket->event->end_date < now()) {
            return response()->json([
                'success' => false,
                'message' => 'Event has already ended.',
                'ticket' => [
                    'number' => $ticket->ticket_number,
                    'holder' => $ticket->order->user->name,
                    'event' => $ticket->event->title,
                    'status' => $ticket->status,
                    'event_end' => $ticket->event->end_date->format('Y-m-d H:i:s')
                ]
            ]);
        }

        // Ticket is valid
        return response()->json([
            'success' => true,
            'message' => 'Ticket is valid and ready to be validated.',
            'ticket' => [
                'number' => $ticket->ticket_number,
                'holder' => $ticket->order->user->name,
                'event' => $ticket->event->title,
                'status' => $ticket->status,
                'purchase_date' => $ticket->created_at->format('Y-m-d H:i:s'),
                'event_start' => $ticket->event->start_date->format('Y-m-d H:i:s'),
                'event_end' => $ticket->event->end_date->format('Y-m-d H:i:s')
            ]
        ]);
    }
}

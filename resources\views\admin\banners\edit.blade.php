@extends('layouts.admin')

@section('title', 'Edit Banner')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4">
                <a href="{{ route('admin.banners.index') }}" 
                   class="inline-flex items-center px-4 py-2 bg-white text-gray-700 rounded-xl hover:bg-gray-50 transition-colors border border-gray-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                    Kembali
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Banner</h1>
                    <p class="text-gray-600">Perbarui banner "{{ $banner->title }}"</p>
                </div>
            </div>
        </div>

        <!-- Form -->
        <form action="{{ route('admin.banners.update', $banner) }}" method="POST" enctype="multipart/form-data" id="bannerForm">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Form -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Information -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Informasi Dasar</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Judul Banner *</label>
                                <input type="text" 
                                       id="title" 
                                       name="title" 
                                       value="{{ old('title', $banner->title) }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="Masukkan judul banner"
                                       required>
                                @error('title')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="subtitle" class="block text-sm font-medium text-gray-700 mb-2">Subtitle</label>
                                <input type="text" 
                                       id="subtitle" 
                                       name="subtitle" 
                                       value="{{ old('subtitle', $banner->subtitle) }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="Subtitle opsional">
                                @error('subtitle')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Deskripsi</label>
                                <textarea id="description" 
                                          name="description" 
                                          rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                          placeholder="Deskripsi banner">{{ old('description', $banner->description) }}</textarea>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Images -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Gambar Banner</h2>
                        
                        <div class="space-y-6">
                            <!-- Current Desktop Image -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Gambar Desktop Saat Ini</label>
                                <div class="w-full h-32 bg-gray-100 rounded-xl overflow-hidden border border-gray-200">
                                    <img src="{{ $banner->image_url }}" alt="{{ $banner->title }}" class="w-full h-full object-cover">
                                </div>
                            </div>

                            <!-- Desktop Image Upload -->
                            <div>
                                <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Ganti Gambar Desktop (1920x800px)
                                </label>
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-green-400 transition-colors">
                                    <div class="space-y-1 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500">
                                                <span>Upload gambar baru</span>
                                                <input id="image" name="image" type="file" class="sr-only" accept="image/*">
                                            </label>
                                            <p class="pl-1">atau drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, WEBP hingga 5MB</p>
                                    </div>
                                </div>
                                <div id="imagePreview" class="mt-4 hidden">
                                    <img class="w-full h-32 object-cover rounded-xl border border-gray-200" alt="Preview">
                                </div>
                                @error('image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Current Mobile Image -->
                            @if($banner->mobile_image_path)
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Gambar Mobile Saat Ini</label>
                                    <div class="w-32 h-42 bg-gray-100 rounded-xl overflow-hidden border border-gray-200 mx-auto">
                                        <img src="{{ $banner->mobile_image_url }}" alt="{{ $banner->title }}" class="w-full h-full object-cover">
                                    </div>
                                </div>
                            @endif

                            <!-- Mobile Image Upload -->
                            <div>
                                <label for="mobile_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    {{ $banner->mobile_image_path ? 'Ganti' : 'Upload' }} Gambar Mobile (768x1024px)
                                </label>
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-xl hover:border-green-400 transition-colors">
                                    <div class="space-y-1 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="mobile_image" class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500">
                                                <span>Upload gambar mobile</span>
                                                <input id="mobile_image" name="mobile_image" type="file" class="sr-only" accept="image/*">
                                            </label>
                                            <p class="pl-1">atau drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">Opsional - akan menggunakan gambar desktop jika kosong</p>
                                    </div>
                                </div>
                                <div id="mobileImagePreview" class="mt-4 hidden">
                                    <img class="w-32 h-42 object-cover rounded-xl border border-gray-200 mx-auto" alt="Mobile Preview">
                                </div>
                                @error('mobile_image')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Button & Link -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Tombol & Link</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="button_text" class="block text-sm font-medium text-gray-700 mb-2">Teks Tombol *</label>
                                <input type="text" 
                                       id="button_text" 
                                       name="button_text" 
                                       value="{{ old('button_text', $banner->button_text) }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="Teks yang ditampilkan pada tombol"
                                       required>
                                @error('button_text')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="link_url" class="block text-sm font-medium text-gray-700 mb-2">URL Tujuan</label>
                                <input type="url" 
                                       id="link_url" 
                                       name="link_url" 
                                       value="{{ old('link_url', $banner->link_url) }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                       placeholder="https://example.com">
                                @error('link_url')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="button_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Tombol</label>
                                <select id="button_color" 
                                        name="button_color" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="primary" {{ old('button_color', $banner->button_color) == 'primary' ? 'selected' : '' }}>Primary</option>
                                    <option value="pastel-green" {{ old('button_color', $banner->button_color) == 'pastel-green' ? 'selected' : '' }}>Pastel Green</option>
                                    <option value="pastel-pink" {{ old('button_color', $banner->button_color) == 'pastel-pink' ? 'selected' : '' }}>Pastel Pink</option>
                                    <option value="pastel-purple" {{ old('button_color', $banner->button_color) == 'pastel-purple' ? 'selected' : '' }}>Pastel Purple</option>
                                    <option value="secondary" {{ old('button_color', $banner->button_color) == 'secondary' ? 'selected' : '' }}>Secondary</option>
                                    <option value="success" {{ old('button_color', $banner->button_color) == 'success' ? 'selected' : '' }}>Success</option>
                                    <option value="warning" {{ old('button_color', $banner->button_color) == 'warning' ? 'selected' : '' }}>Warning</option>
                                    <option value="danger" {{ old('button_color', $banner->button_color) == 'danger' ? 'selected' : '' }}>Danger</option>
                                </select>
                                @error('button_color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Status & Schedule -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Status & Jadwal</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="is_active" 
                                           value="1" 
                                           {{ old('is_active', $banner->is_active) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Banner Aktif</span>
                                </label>
                            </div>

                            <div>
                                <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">Tanggal Mulai</label>
                                <input type="datetime-local" 
                                       id="start_date" 
                                       name="start_date" 
                                       value="{{ old('start_date', $banner->start_date ? $banner->start_date->format('Y-m-d\TH:i') : '') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                @error('start_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">Tanggal Berakhir</label>
                                <input type="datetime-local" 
                                       id="end_date" 
                                       name="end_date" 
                                       value="{{ old('end_date', $banner->end_date ? $banner->end_date->format('Y-m-d\TH:i') : '') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                @error('end_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-2">Urutan Tampil</label>
                                <input type="number" 
                                       id="sort_order" 
                                       name="sort_order" 
                                       value="{{ old('sort_order', $banner->sort_order) }}"
                                       min="0"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                <p class="mt-1 text-xs text-gray-500">Semakin kecil angka, semakin awal ditampilkan</p>
                                @error('sort_order')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Text Settings -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Pengaturan Teks</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="text_position" class="block text-sm font-medium text-gray-700 mb-2">Posisi Teks</label>
                                <select id="text_position" 
                                        name="text_position" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="left" {{ old('text_position', $banner->text_position) == 'left' ? 'selected' : '' }}>Kiri</option>
                                    <option value="center" {{ old('text_position', $banner->text_position) == 'center' ? 'selected' : '' }}>Tengah</option>
                                    <option value="right" {{ old('text_position', $banner->text_position) == 'right' ? 'selected' : '' }}>Kanan</option>
                                </select>
                                @error('text_position')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="text_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Teks</label>
                                <select id="text_color" 
                                        name="text_color" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="white" {{ old('text_color', $banner->text_color) == 'white' ? 'selected' : '' }}>Putih</option>
                                    <option value="dark" {{ old('text_color', $banner->text_color) == 'dark' ? 'selected' : '' }}>Gelap</option>
                                </select>
                                @error('text_color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Overlay Settings -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-6">Pengaturan Overlay</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" 
                                           name="show_overlay" 
                                           value="1" 
                                           {{ old('show_overlay', $banner->show_overlay) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Tampilkan Overlay</span>
                                </label>
                            </div>

                            <div>
                                <label for="overlay_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Overlay</label>
                                <select id="overlay_color" 
                                        name="overlay_color" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="dark" {{ old('overlay_color', $banner->overlay_color) == 'dark' ? 'selected' : '' }}>Gelap</option>
                                    <option value="light" {{ old('overlay_color', $banner->overlay_color) == 'light' ? 'selected' : '' }}>Terang</option>
                                    <option value="gradient" {{ old('overlay_color', $banner->overlay_color) == 'gradient' ? 'selected' : '' }}>Gradient</option>
                                </select>
                                @error('overlay_color')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="overlay_opacity" class="block text-sm font-medium text-gray-700 mb-2">Opacity Overlay</label>
                                <input type="range" 
                                       id="overlay_opacity" 
                                       name="overlay_opacity" 
                                       min="0" 
                                       max="1" 
                                       step="0.1" 
                                       value="{{ old('overlay_opacity', $banner->overlay_opacity) }}"
                                       class="w-full">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>0%</span>
                                    <span id="opacityValue">{{ old('overlay_opacity', $banner->overlay_opacity) * 100 }}%</span>
                                    <span>100%</span>
                                </div>
                                @error('overlay_opacity')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                        <div class="space-y-3">
                            <button type="submit" 
                                    class="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl">
                                Perbarui Banner
                            </button>
                            <a href="{{ route('admin.banners.index') }}" 
                               class="w-full px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-semibold text-center block">
                                Batal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image preview functionality
    function setupImagePreview(inputId, previewId) {
        const input = document.getElementById(inputId);
        const preview = document.getElementById(previewId);
        
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    setupImagePreview('image', 'imagePreview');
    setupImagePreview('mobile_image', 'mobileImagePreview');
    
    // Overlay opacity slider
    const opacitySlider = document.getElementById('overlay_opacity');
    const opacityValue = document.getElementById('opacityValue');
    
    opacitySlider.addEventListener('input', function() {
        opacityValue.textContent = Math.round(this.value * 100) + '%';
    });
    
    // Form validation
    const form = document.getElementById('bannerForm');
    form.addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        
        if (!title) {
            e.preventDefault();
            alert('Judul banner harus diisi');
            return;
        }
    });
});
</script>
@endpush

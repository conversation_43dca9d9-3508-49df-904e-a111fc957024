<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TiXara - Enhanced GoPay/OVO Style Hero Preview</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* GoPay/OVO Style Animations and Floating Tickets */

        /* GoPay/OVO Gradient Background */
        .gopay-gradient-bg {
            background: linear-gradient(135deg,
                #00AA5B 0%,     /* GoPay Green */
                #0066CC 25%,    /* OVO Blue */
                #4F46E5 50%,    /* Modern Purple */
                #06B6D4 75%,    /* Cyan */
                #10B981 100%    /* Emerald */
            );
            background-size: 400% 400%;
            animation: gopayGradient 15s ease infinite;
            opacity: 0.1;
        }

        @keyframes gopayGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating Tickets Container */
        .floating-tickets-container {
            overflow: hidden;
        }

        /* Individual Floating Tickets */
        .floating-ticket {
            position: absolute;
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.7));
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            animation: floatTicket 20s linear infinite;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .floating-ticket:hover {
            transform: scale(1.1) !important;
            opacity: 1;
            box-shadow: 0 12px 40px rgba(0,0,0,0.2);
        }

        .ticket-content {
            padding: 8px 12px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
        }

        .ticket-header {
            font-size: 20px;
            line-height: 1;
        }

        .ticket-title {
            font-size: 10px;
            font-weight: 600;
            color: #1f2937;
            margin: 2px 0;
        }

        .ticket-price {
            font-size: 9px;
            font-weight: 700;
            color: #059669;
            background: rgba(16, 185, 129, 0.1);
            padding: 2px 6px;
            border-radius: 6px;
        }

        /* Floating Animation */
        @keyframes floatTicket {
            0% {
                transform: translateY(100vh) translateX(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-120px) translateX(50px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Individual Ticket Positions and Animations */
        .ticket-1 {
            left: 10%;
            animation-delay: 0s;
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(16, 185, 129, 0.7));
        }

        .ticket-2 {
            left: 25%;
            animation-delay: 3s;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(59, 130, 246, 0.7));
        }

        .ticket-3 {
            left: 40%;
            animation-delay: 6s;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.9), rgba(245, 158, 11, 0.7));
        }

        .ticket-4 {
            left: 55%;
            animation-delay: 9s;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.9), rgba(139, 92, 246, 0.7));
        }

        .ticket-5 {
            left: 70%;
            animation-delay: 12s;
            background: linear-gradient(135deg, rgba(236, 72, 153, 0.9), rgba(236, 72, 153, 0.7));
        }

        .ticket-6 {
            left: 85%;
            animation-delay: 15s;
            background: linear-gradient(135deg, rgba(6, 182, 212, 0.9), rgba(6, 182, 212, 0.7));
        }

        /* GoPay/OVO Style Cards for Content */
        .gopay-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 24px;
            transition: all 0.3s ease;
        }

        .gopay-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        /* GoPay/OVO Style Buttons */
        .gopay-btn {
            background: linear-gradient(135deg, #00AA5B, #059669);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 170, 91, 0.3);
            transition: all 0.3s ease;
        }

        .gopay-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 170, 91, 0.4);
        }

        .ovo-btn {
            background: linear-gradient(135deg, #0066CC, #0056b3);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
            transition: all 0.3s ease;
        }

        .ovo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 102, 204, 0.4);
        }

        /* Mobile Responsive Tickets */
        @media (max-width: 768px) {
            .floating-ticket {
                width: 80px;
                height: 60px;
            }

            .ticket-content {
                padding: 4px 6px;
            }

            .ticket-header {
                font-size: 14px;
            }

            .ticket-title {
                font-size: 8px;
            }

            .ticket-price {
                font-size: 7px;
                padding: 1px 4px;
            }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* 3D Interactive Tickets Styling */
        .tickets-3d-container {
            perspective: 1000px;
            overflow: hidden;
        }

        .ticket-3d {
            position: absolute;
            width: 160px;
            height: 100px;
            cursor: pointer;
            transition: all 0.3s ease;
            transform-style: preserve-3d;
            animation: float3D 8s ease-in-out infinite;
        }

        .ticket-3d-inner {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .ticket-3d:hover .ticket-3d-inner {
            transform: rotateY(180deg);
        }

        .ticket-3d-front,
        .ticket-3d-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.95) 0%,
                rgba(255, 255, 255, 0.85) 100%
            );
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .ticket-3d-back {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.95) 0%,
                rgba(6, 182, 212, 0.95) 100%
            );
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transform: rotateY(180deg);
        }

        .ticket-3d-content {
            padding: 12px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
        }

        .ticket-3d-header {
            font-size: 24px;
            line-height: 1;
            margin-bottom: 4px;
        }

        .ticket-3d-title {
            font-size: 12px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 2px;
        }

        .ticket-3d-price {
            font-size: 11px;
            font-weight: 600;
            color: #059669;
            background: rgba(16, 185, 129, 0.1);
            padding: 2px 6px;
            border-radius: 6px;
            margin-bottom: 2px;
        }

        .ticket-3d-date {
            font-size: 9px;
            color: #6b7280;
            font-weight: 500;
        }

        /* Back side styling */
        .ticket-3d-back .ticket-3d-content {
            color: white;
        }

        .ticket-3d-qr {
            font-size: 28px;
            margin-bottom: 4px;
        }

        .ticket-3d-code {
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 4px;
            letter-spacing: 1px;
        }

        .ticket-3d-venue {
            font-size: 10px;
            opacity: 0.9;
        }

        /* Individual 3D Ticket Positions and Animations */
        .ticket-3d-1 {
            top: 15%;
            left: 10%;
            animation-delay: 0s;
        }

        .ticket-3d-2 {
            top: 25%;
            right: 15%;
            animation-delay: 1.5s;
        }

        .ticket-3d-3 {
            top: 45%;
            left: 20%;
            animation-delay: 3s;
        }

        .ticket-3d-4 {
            top: 35%;
            right: 25%;
            animation-delay: 4.5s;
        }

        .ticket-3d-5 {
            top: 65%;
            left: 15%;
            animation-delay: 6s;
        }

        .ticket-3d-6 {
            top: 55%;
            right: 10%;
            animation-delay: 7.5s;
        }

        /* 3D Float Animation */
        @keyframes float3D {
            0%, 100% {
                transform: translateY(0px) rotateX(0deg) rotateZ(0deg);
            }
            25% {
                transform: translateY(-10px) rotateX(5deg) rotateZ(2deg);
            }
            50% {
                transform: translateY(-5px) rotateX(-3deg) rotateZ(-1deg);
            }
            75% {
                transform: translateY(-15px) rotateX(2deg) rotateZ(3deg);
            }
        }

        /* Category-based colors for 3D tickets */
        .ticket-3d[data-category="musik"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(16, 185, 129, 0.95) 0%,
                rgba(16, 185, 129, 0.85) 100%
            );
        }

        .ticket-3d[data-category="teknologi"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(59, 130, 246, 0.95) 0%,
                rgba(59, 130, 246, 0.85) 100%
            );
        }

        .ticket-3d[data-category="seni"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.95) 0%,
                rgba(139, 92, 246, 0.85) 100%
            );
        }

        .ticket-3d[data-category="olahraga"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(245, 158, 11, 0.95) 0%,
                rgba(245, 158, 11, 0.85) 100%
            );
        }

        .ticket-3d[data-category="kuliner"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(236, 72, 153, 0.95) 0%,
                rgba(236, 72, 153, 0.85) 100%
            );
        }

        .ticket-3d[data-category="fashion"] .ticket-3d-front {
            background: linear-gradient(135deg,
                rgba(168, 85, 247, 0.95) 0%,
                rgba(168, 85, 247, 0.85) 100%
            );
        }

        /* Mobile Responsive 3D Tickets */
        @media (max-width: 768px) {
            .ticket-3d {
                width: 120px;
                height: 80px;
            }

            .ticket-3d-content {
                padding: 8px;
            }

            .ticket-3d-header {
                font-size: 18px;
            }

            .ticket-3d-title {
                font-size: 10px;
            }

            .ticket-3d-price {
                font-size: 9px;
                padding: 1px 4px;
            }

            .ticket-3d-date {
                font-size: 8px;
            }

            .ticket-3d-qr {
                font-size: 20px;
            }

            .ticket-3d-code {
                font-size: 11px;
            }

            .ticket-3d-venue {
                font-size: 8px;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Hero Section Preview -->
    <div class="hero-container relative min-h-screen overflow-hidden">
        <!-- 3D Interactive Tickets Animation -->
        <div class="tickets-3d-container absolute inset-0 pointer-events-auto z-6" id="tickets3DContainer">
            <div class="ticket-3d ticket-3d-1" data-category="musik">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">🎵</div>
                            <div class="ticket-3d-title">Konser Rock</div>
                            <div class="ticket-3d-price">Rp 250K</div>
                            <div class="ticket-3d-date">25 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX001</div>
                            <div class="ticket-3d-venue">Jakarta Arena</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-3d ticket-3d-2" data-category="teknologi">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">💻</div>
                            <div class="ticket-3d-title">Tech Summit</div>
                            <div class="ticket-3d-price">Rp 150K</div>
                            <div class="ticket-3d-date">30 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX002</div>
                            <div class="ticket-3d-venue">ICE BSD</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-3d ticket-3d-3" data-category="seni">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">🎭</div>
                            <div class="ticket-3d-title">Theater Show</div>
                            <div class="ticket-3d-price">Rp 100K</div>
                            <div class="ticket-3d-date">28 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX003</div>
                            <div class="ticket-3d-venue">Teater Kecil</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-3d ticket-3d-4" data-category="olahraga">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">⚽</div>
                            <div class="ticket-3d-title">Final Liga</div>
                            <div class="ticket-3d-price">Rp 300K</div>
                            <div class="ticket-3d-date">31 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX004</div>
                            <div class="ticket-3d-venue">GBK Stadium</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-3d ticket-3d-5" data-category="kuliner">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">🍔</div>
                            <div class="ticket-3d-title">Food Festival</div>
                            <div class="ticket-3d-price">Rp 75K</div>
                            <div class="ticket-3d-date">26 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX005</div>
                            <div class="ticket-3d-venue">PIK Avenue</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ticket-3d ticket-3d-6" data-category="fashion">
                <div class="ticket-3d-inner">
                    <div class="ticket-3d-front">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-header">👗</div>
                            <div class="ticket-3d-title">Fashion Week</div>
                            <div class="ticket-3d-price">Rp 400K</div>
                            <div class="ticket-3d-date">29 Des 2024</div>
                        </div>
                    </div>
                    <div class="ticket-3d-back">
                        <div class="ticket-3d-content">
                            <div class="ticket-3d-qr">📱</div>
                            <div class="ticket-3d-code">TIX006</div>
                            <div class="ticket-3d-venue">JCC Senayan</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Tickets Background Animation -->
        <div class="floating-tickets-container absolute inset-0 pointer-events-none z-5">
            <div class="floating-ticket ticket-1" data-speed="2">
                <div class="ticket-content">
                    <div class="ticket-header">🎵</div>
                    <div class="ticket-title">Konser</div>
                    <div class="ticket-price">Rp 150K</div>
                </div>
            </div>
            <div class="floating-ticket ticket-2" data-speed="3">
                <div class="ticket-content">
                    <div class="ticket-header">🎭</div>
                    <div class="ticket-title">Theater</div>
                    <div class="ticket-price">Rp 75K</div>
                </div>
            </div>
            <div class="floating-ticket ticket-3" data-speed="1.5">
                <div class="ticket-content">
                    <div class="ticket-header">🏃</div>
                    <div class="ticket-title">Marathon</div>
                    <div class="ticket-price">Rp 200K</div>
                </div>
            </div>
            <div class="floating-ticket ticket-4" data-speed="2.5">
                <div class="ticket-content">
                    <div class="ticket-header">🎨</div>
                    <div class="ticket-title">Workshop</div>
                    <div class="ticket-price">Rp 100K</div>
                </div>
            </div>
            <div class="floating-ticket ticket-5" data-speed="1.8">
                <div class="ticket-content">
                    <div class="ticket-header">🍔</div>
                    <div class="ticket-title">Food Fest</div>
                    <div class="ticket-price">Rp 50K</div>
                </div>
            </div>
            <div class="floating-ticket ticket-6" data-speed="2.2">
                <div class="ticket-content">
                    <div class="ticket-header">💼</div>
                    <div class="ticket-title">Seminar</div>
                    <div class="ticket-price">Rp 125K</div>
                </div>
            </div>
        </div>

        <!-- GoPay/OVO Style Gradient Background -->
        <div class="gopay-gradient-bg absolute inset-0 z-0"></div>

        <!-- Main Content -->
        <div class="relative z-10 flex items-center min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-green-900">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="text-white space-y-8">
                        <!-- GoPay/OVO-style status badge -->
                        <div class="inline-flex items-center px-4 py-2 gopay-card text-sm font-bold">
                            <span class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                            <span class="text-green-600">[ONLINE]</span>
                            <span class="text-gray-800 ml-2">Platform Tiket Event #1 Indonesia</span>
                        </div>

                        <!-- Main title -->
                        <div>
                            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight text-white">
                                <span class="block">Temukan</span>
                                <span class="block text-green-300">Event Impian</span>
                                <span class="block">Anda</span>
                            </h1>
                        </div>

                        <!-- Description -->
                        <div class="gopay-card max-w-2xl">
                            <div class="text-gray-800 leading-relaxed">
                                Jelajahi ribuan event menarik: konser musik, seminar bisnis, workshop kreatif, festival kuliner.
                                <span class="text-green-600 font-semibold">Semua dalam satu platform!</span>
                            </div>
                        </div>

                        <!-- GoPay/OVO-style buttons -->
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button class="gopay-btn group px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                <span class="flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                    </svg>
                                    <span>Cari Event</span>
                                </span>
                            </button>
                            <button class="ovo-btn group px-6 py-3 font-bold rounded-xl transition-all duration-300 shadow-lg">
                                <span class="flex items-center justify-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                    </svg>
                                    <span>Kategori</span>
                                </span>
                            </button>
                        </div>

                        <!-- UangTix wallet -->
                        <div class="mt-6">
                            <div class="gopay-card group inline-flex items-center px-6 py-4 rounded-xl font-bold transition-all duration-300 hover:scale-105 cursor-pointer">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mr-3">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-gray-800 font-bold">UangTix Wallet</div>
                                        <div class="text-green-600 font-bold text-lg">UTX 50,000</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <!-- Search card -->
                        <div class="gopay-card p-6 shadow-xl">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Cari Event Favorit</h3>
                            <div class="relative mb-6">
                                <input type="text" class="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-green-500" placeholder="Cari konser, seminar, workshop...">
                                <button class="absolute right-2 top-2 gopay-btn px-4 py-2 text-sm">
                                    Cari
                                </button>
                            </div>
                        </div>

                        <!-- Stats card -->
                        <div class="gopay-card p-6">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">Platform Terpercaya</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">1000+</div>
                                    <div class="text-sm text-gray-600">Events</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">50K+</div>
                                    <div class="text-sm text-gray-600">Users</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize floating tickets animation and 3D tickets
        document.addEventListener('DOMContentLoaded', function() {
            const ticketsContainer = document.querySelector('.floating-tickets-container');
            const tickets = ticketsContainer.querySelectorAll('.floating-ticket');

            // Add click interactions for floating tickets
            tickets.forEach(ticket => {
                ticket.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Create ripple effect
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.6)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';
                    ripple.style.marginLeft = '-10px';
                    ripple.style.marginTop = '-10px';

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Initialize 3D Interactive Tickets
            init3DTickets();
        });

        // Initialize 3D Interactive Tickets System
        function init3DTickets() {
            const tickets3DContainer = document.querySelector('.tickets-3d-container');
            if (!tickets3DContainer) return;

            const tickets3D = tickets3DContainer.querySelectorAll('.ticket-3d');
            let mouseX = 0;
            let mouseY = 0;
            let containerRect = tickets3DContainer.getBoundingClientRect();

            // Update container rect on resize
            window.addEventListener('resize', () => {
                containerRect = tickets3DContainer.getBoundingClientRect();
            });

            // Mouse movement handler for 3D effects
            function handleMouseMove(e) {
                const clientX = e.clientX || (e.touches && e.touches[0] ? e.touches[0].clientX : 0);
                const clientY = e.clientY || (e.touches && e.touches[0] ? e.touches[0].clientY : 0);

                mouseX = clientX - containerRect.left;
                mouseY = clientY - containerRect.top;

                const centerX = containerRect.width / 2;
                const centerY = containerRect.height / 2;

                const rotateX = (mouseY - centerY) / centerY * 15; // Max 15 degrees
                const rotateY = (mouseX - centerX) / centerX * 15; // Max 15 degrees

                tickets3D.forEach((ticket, index) => {
                    const ticketRect = ticket.getBoundingClientRect();
                    const ticketCenterX = ticketRect.left + ticketRect.width / 2 - containerRect.left;
                    const ticketCenterY = ticketRect.top + ticketRect.height / 2 - containerRect.top;

                    // Calculate distance from mouse to ticket center
                    const distanceX = mouseX - ticketCenterX;
                    const distanceY = mouseY - ticketCenterY;
                    const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

                    // Apply stronger effect to closer tickets
                    const maxDistance = 300;
                    const influence = Math.max(0, 1 - distance / maxDistance);

                    const ticketRotateX = -rotateX * influence * 0.5;
                    const ticketRotateY = rotateY * influence * 0.5;
                    const ticketTranslateZ = influence * 20;

                    // Apply transform with smooth transition
                    ticket.style.transform = `
                        translateZ(${ticketTranslateZ}px)
                        rotateX(${ticketRotateX}deg)
                        rotateY(${ticketRotateY}deg)
                        scale(${1 + influence * 0.1})
                    `;

                    // Add glow effect based on proximity
                    const glowIntensity = influence * 0.3;
                    ticket.style.filter = `drop-shadow(0 0 ${glowIntensity * 20}px rgba(16, 185, 129, ${glowIntensity}))`;
                });
            }

            // Reset tickets to original position
            function resetTickets() {
                tickets3D.forEach(ticket => {
                    ticket.style.transform = '';
                    ticket.style.filter = '';
                });
            }

            // Enhanced click/touch interactions for 3D tickets
            tickets3D.forEach((ticket, index) => {
                let isFlipped = false;
                let touchStartTime = 0;

                // Mouse events
                ticket.addEventListener('mouseenter', function() {
                    this.style.zIndex = '1000';
                    this.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                });

                ticket.addEventListener('mouseleave', function() {
                    this.style.zIndex = '';
                    this.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                });

                // Click/Touch to flip
                ticket.addEventListener('click', function(e) {
                    e.preventDefault();
                    flipTicket(this);
                });

                // Touch events for mobile
                ticket.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    touchStartTime = Date.now();
                    this.style.zIndex = '1000';

                    // Add touch feedback
                    this.style.transform += ' scale(0.95)';
                });

                ticket.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    const touchDuration = Date.now() - touchStartTime;

                    // Reset scale
                    this.style.transform = this.style.transform.replace(' scale(0.95)', '');

                    // Flip if touch was quick (tap)
                    if (touchDuration < 300) {
                        flipTicket(this);
                    }

                    setTimeout(() => {
                        this.style.zIndex = '';
                    }, 300);
                });

                // Double-click for special effect
                ticket.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    createTicketExplosion(this);
                });

                function flipTicket(ticketElement) {
                    const inner = ticketElement.querySelector('.ticket-3d-inner');
                    isFlipped = !isFlipped;

                    if (isFlipped) {
                        inner.style.transform = 'rotateY(180deg)';
                        ticketElement.style.transform += ' scale(1.1)';
                    } else {
                        inner.style.transform = 'rotateY(0deg)';
                        ticketElement.style.transform = ticketElement.style.transform.replace(' scale(1.1)', '');
                    }
                }

                function createTicketExplosion(ticketElement) {
                    // Create multiple particles
                    for (let i = 0; i < 12; i++) {
                        const particle = document.createElement('div');
                        particle.style.position = 'absolute';
                        particle.style.width = '6px';
                        particle.style.height = '6px';
                        particle.style.background = `hsl(${Math.random() * 360}, 70%, 60%)`;
                        particle.style.borderRadius = '50%';
                        particle.style.pointerEvents = 'none';
                        particle.style.zIndex = '9999';

                        const rect = ticketElement.getBoundingClientRect();
                        particle.style.left = rect.left + rect.width / 2 + 'px';
                        particle.style.top = rect.top + rect.height / 2 + 'px';

                        document.body.appendChild(particle);

                        // Animate particle
                        const angle = (i / 12) * Math.PI * 2;
                        const velocity = 100 + Math.random() * 50;
                        const vx = Math.cos(angle) * velocity;
                        const vy = Math.sin(angle) * velocity;

                        particle.animate([
                            {
                                transform: 'translate(0, 0) scale(1)',
                                opacity: 1
                            },
                            {
                                transform: `translate(${vx}px, ${vy}px) scale(0)`,
                                opacity: 0
                            }
                        ], {
                            duration: 800,
                            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
                        }).onfinish = () => {
                            particle.remove();
                        };
                    }

                    // Shake the ticket
                    ticketElement.animate([
                        { transform: 'translateX(0)' },
                        { transform: 'translateX(-5px)' },
                        { transform: 'translateX(5px)' },
                        { transform: 'translateX(-3px)' },
                        { transform: 'translateX(3px)' },
                        { transform: 'translateX(0)' }
                    ], {
                        duration: 300,
                        easing: 'ease-in-out'
                    });
                }
            });

            // Mouse/Touch movement tracking
            tickets3DContainer.addEventListener('mousemove', handleMouseMove);
            tickets3DContainer.addEventListener('touchmove', function(e) {
                e.preventDefault();
                handleMouseMove(e);
            });

            // Reset on mouse leave
            tickets3DContainer.addEventListener('mouseleave', resetTickets);
            tickets3DContainer.addEventListener('touchend', function(e) {
                if (!e.touches.length) {
                    resetTickets();
                }
            });

            // Auto-flip tickets periodically
            function autoFlipTickets() {
                tickets3D.forEach((ticket, index) => {
                    setTimeout(() => {
                        const inner = ticket.querySelector('.ticket-3d-inner');
                        const isCurrentlyFlipped = inner.style.transform.includes('180deg');

                        if (!isCurrentlyFlipped && Math.random() > 0.7) {
                            inner.style.transform = 'rotateY(180deg)';
                            setTimeout(() => {
                                inner.style.transform = 'rotateY(0deg)';
                            }, 2000);
                        }
                    }, index * 1000 + Math.random() * 3000);
                });
            }

            // Start auto-flip every 10 seconds
            setInterval(autoFlipTickets, 10000);
        }
    </script>
</body>
</html>

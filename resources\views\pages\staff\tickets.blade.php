@extends('layouts.staff')

@section('title', 'Staff Tickets - TiXara')
@section('page-title', 'Tickets')

@push('styles')
<style>
/* Staff Tickets Styles */
.ticket-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(16, 185, 129, 0.1);
    backdrop-filter: blur(10px);
}

.ticket-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.ticket-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.ticket-status.valid {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.ticket-status.used {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.ticket-status.invalid {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.search-box {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.filter-btn {
    transition: all 0.3s ease;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.filter-btn.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.stats-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-6 lg:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Ticket Management</h1>
                <p class="text-gray-600 dark:text-gray-400">Manage and validate event tickets</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    <i data-lucide="scan-line" class="w-4 h-4 inline mr-2"></i>
                    Quick Scan
                </button>
                <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" data-aos="fade-up" data-aos-delay="100">
        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Total Tickets</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">1,234</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-xl">
                    <i data-lucide="ticket" class="w-6 h-6 text-blue-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Valid Tickets</p>
                    <p class="text-2xl font-bold text-green-600">987</p>
                </div>
                <div class="p-3 bg-green-100 rounded-xl">
                    <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Used Tickets</p>
                    <p class="text-2xl font-bold text-gray-600">156</p>
                </div>
                <div class="p-3 bg-gray-100 rounded-xl">
                    <i data-lucide="check-square" class="w-6 h-6 text-gray-600"></i>
                </div>
            </div>
        </div>

        <div class="stats-card rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Invalid Tickets</p>
                    <p class="text-2xl font-bold text-red-600">91</p>
                </div>
                <div class="p-3 bg-red-100 rounded-xl">
                    <i data-lucide="x-circle" class="w-6 h-6 text-red-600"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-8" data-aos="fade-up" data-aos-delay="200">
        <div class="flex flex-col lg:flex-row gap-4">
            <!-- Search Box -->
            <div class="flex-1">
                <div class="relative">
                    <input type="text" 
                           placeholder="Search tickets by ID, event name, or customer..." 
                           class="search-box w-full pl-10 pr-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500">
                    <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"></i>
                </div>
            </div>

            <!-- Filter Buttons -->
            <div class="flex gap-2">
                <button class="filter-btn active px-4 py-3 rounded-xl text-sm font-medium">All</button>
                <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium">Valid</button>
                <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium">Used</button>
                <button class="filter-btn px-4 py-3 rounded-xl text-sm font-medium">Invalid</button>
            </div>
        </div>
    </div>

    <!-- Tickets List -->
    <div class="space-y-4" data-aos="fade-up" data-aos-delay="300">
        <!-- Sample Ticket Cards -->
        @for($i = 1; $i <= 10; $i++)
        <div class="ticket-card bg-white dark:bg-gray-800 rounded-xl p-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <div class="flex-1 mb-4 lg:mb-0">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                            <i data-lucide="ticket" class="w-6 h-6 text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">TIX{{ str_pad($i, 6, '0', STR_PAD_LEFT) }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Concert Music Festival 2024</p>
                            <p class="text-xs text-gray-500 dark:text-gray-500">Customer: John Doe #{{ $i }}</p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <p class="text-lg font-bold text-gray-900 dark:text-white">Rp 250,000</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ now()->subDays(rand(1, 30))->format('d M Y') }}</p>
                    </div>

                    <div class="ticket-status {{ $i % 4 == 0 ? 'invalid' : ($i % 3 == 0 ? 'used' : 'valid') }}">
                        {{ $i % 4 == 0 ? 'Invalid' : ($i % 3 == 0 ? 'Used' : 'Valid') }}
                    </div>

                    <div class="flex space-x-2">
                        <button class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <i data-lucide="eye" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                            <i data-lucide="scan-line" class="w-4 h-4"></i>
                        </button>
                        <button class="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                            <i data-lucide="more-vertical" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        @endfor
    </div>

    <!-- Pagination -->
    <div class="mt-8 flex justify-center" data-aos="fade-up" data-aos-delay="400">
        <nav class="flex items-center space-x-2">
            <button class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                <i data-lucide="chevron-left" class="w-4 h-4"></i>
            </button>
            <button class="px-3 py-2 bg-green-500 text-white rounded-lg">1</button>
            <button class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">2</button>
            <button class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">3</button>
            <button class="px-3 py-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                <i data-lucide="chevron-right" class="w-4 h-4"></i>
            </button>
        </nav>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Search functionality
    const searchInput = document.querySelector('.search-box');
    searchInput.addEventListener('input', function() {
        // Implement search logic here
        console.log('Searching for:', this.value);
    });
});
</script>
@endpush
@endsection

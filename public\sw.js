const CACHE_NAME = 'tixara-v1';
const OFFLINE_URL = '/offline';

// Assets to cache
const assetsToCache = [
    '/',
    '/offline',
    '/uangtix',
    '/css/app.css',
    '/js/app.js',
    '/manifest.json',
    '/icons/icon-72x72.png',
    '/icons/icon-96x96.png',
    '/icons/icon-128x128.png',
    '/icons/icon-144x144.png',
    '/icons/icon-152x152.png',
    '/icons/icon-192x192.png',
    '/icons/icon-384x384.png',
    '/icons/icon-512x512.png',
    'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap',
    'https://unpkg.com/aos@next/dist/aos.css',
    'https://unpkg.com/aos@next/dist/aos.js',
    'https://cdn.jsdelivr.net/npm/qrcode@1.4.4/build/qrcode.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'
];

// Install event - cache assets
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                return cache.addAll(assetsToCache);
            })
            .then(() => {
                return self.skipWaiting();
            })
    );
});

// Activate event - clean old caches
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames
                    .filter(cacheName => cacheName.startsWith('tixara-'))
                    .filter(cacheName => cacheName !== CACHE_NAME)
                    .map(cacheName => caches.delete(cacheName))
            );
        })
        .then(() => {
            return self.clients.claim();
        })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    // Skip cross-origin requests
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }

    // Handle API requests
    if (event.request.url.includes('/api/')) {
        return handleApiRequest(event);
    }

    // Handle UangTix API requests
    if (event.request.url.includes('/uangtix/')) {
        return handleUangTixRequest(event);
    }

    // Handle navigation requests
    if (event.request.mode === 'navigate') {
        return handleNavigationRequest(event);
    }

    // Handle asset requests
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                if (response) {
                    return response;
                }

                return fetch(event.request).then(response => {
                    // Cache successful responses
                    if (response.ok && response.type === 'basic') {
                        const responseToCache = response.clone();
                        caches.open(CACHE_NAME).then(cache => {
                            cache.put(event.request, responseToCache);
                        });
                    }
                    return response;
                });
            })
    );
});

// Handle API requests
function handleApiRequest(event) {
    event.respondWith(
        fetch(event.request)
            .catch(() => {
                // If offline, try to return cached data
                return caches.match(event.request);
            })
    );
}

// Handle UangTix requests
function handleUangTixRequest(event) {
    event.respondWith(
        fetch(event.request)
            .then(response => {
                // Cache successful UangTix responses
                if (response.ok && event.request.method === 'GET') {
                    const responseToCache = response.clone();
                    caches.open(CACHE_NAME).then(cache => {
                        cache.put(event.request, responseToCache);
                    });
                }
                return response;
            })
            .catch(() => {
                // If offline, try to return cached data
                return caches.match(event.request)
                    .then(response => {
                        if (response) {
                            return response;
                        }

                        // Return offline response for UangTix balance
                        if (event.request.url.includes('/uangtix/balance')) {
                            return new Response(JSON.stringify({
                                success: false,
                                message: 'Offline - Tidak dapat memuat saldo terbaru',
                                offline: true,
                                data: {
                                    balance: 0,
                                    balance_idr: 0,
                                    formatted_balance: 'UTX 0',
                                    formatted_balance_idr: 'Rp 0'
                                }
                            }), {
                                headers: { 'Content-Type': 'application/json' }
                            });
                        }

                        // Return offline UangTix page
                        if (event.request.url.includes('/uangtix') && !event.request.url.includes('/uangtix/')) {
                            return new Response(`
                                <!DOCTYPE html>
                                <html>
                                <head>
                                    <title>UangTix - Offline</title>
                                    <meta name="viewport" content="width=device-width, initial-scale=1">
                                    <style>
                                        body {
                                            font-family: Arial, sans-serif;
                                            text-align: center;
                                            padding: 50px;
                                            background: #F8FAFC;
                                            margin: 0;
                                        }
                                        .offline-container {
                                            max-width: 400px;
                                            margin: 0 auto;
                                            background: white;
                                            padding: 40px;
                                            border-radius: 16px;
                                            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                                        }
                                        .offline-icon {
                                            font-size: 64px;
                                            margin-bottom: 20px;
                                        }
                                        .offline-title {
                                            font-size: 24px;
                                            font-weight: 700;
                                            color: #1A1A1A;
                                            margin-bottom: 12px;
                                        }
                                        .offline-message {
                                            color: #666666;
                                            margin-bottom: 24px;
                                        }
                                        .retry-btn {
                                            background: #0066CC;
                                            color: white;
                                            border: none;
                                            padding: 12px 24px;
                                            border-radius: 8px;
                                            font-weight: 600;
                                            cursor: pointer;
                                        }
                                    </style>
                                </head>
                                <body>
                                    <div class="offline-container">
                                        <div class="offline-icon">💳</div>
                                        <div class="offline-title">UangTix Offline</div>
                                        <div class="offline-message">
                                            Tidak dapat terhubung ke server. Periksa koneksi internet Anda.
                                        </div>
                                        <button class="retry-btn" onclick="window.location.reload()">
                                            Coba Lagi
                                        </button>
                                    </div>
                                </body>
                                </html>
                            `, {
                                headers: { 'Content-Type': 'text/html' }
                            });
                        }

                        return new Response('Offline - UangTix not available', {
                            status: 503,
                            statusText: 'Service Unavailable'
                        });
                    });
            })
    );
}

// Handle navigation requests
function handleNavigationRequest(event) {
    event.respondWith(
        fetch(event.request)
            .catch(() => {
                return caches.match(event.request)
                    .then(response => {
                        if (response) {
                            return response;
                        }
                        // If no cached response, return offline page
                        return caches.match(OFFLINE_URL);
                    });
            })
    );
}

// Cache tickets for offline access
self.addEventListener('message', event => {
    if (event.data.type === 'CACHE_TICKETS') {
        event.waitUntil(
            caches.open(CACHE_NAME)
                .then(cache => {
                    const ticketUrls = event.data.tickets.map(ticket => {
                        return [
                            ticket.url,
                            `/api/tickets/${ticket.id}`,
                            ticket.qrCodeUrl,
                            ticket.eventImageUrl
                        ];
                    }).flat();
                    return cache.addAll(ticketUrls);
                })
        );
    }
});

// Periodic sync for background updates
self.addEventListener('periodicsync', event => {
    if (event.tag === 'update-tickets') {
        event.waitUntil(updateTickets());
    }
});

// Background sync for offline purchases
self.addEventListener('sync', event => {
    if (event.tag === 'sync-purchases') {
        event.waitUntil(syncPurchases());
    }
});

// Push notification handler
self.addEventListener('push', event => {
    const options = {
        body: event.data.text(),
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'view-ticket',
                title: 'Lihat Tiket',
                icon: '/icons/icon-72x72.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('TiXara', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'view-ticket') {
        event.waitUntil(
            clients.openWindow('/my-tickets')
        );
    }
});

// Function to update cached tickets
async function updateTickets() {
    try {
        const response = await fetch('/api/tickets');
        const tickets = await response.json();
        const cache = await caches.open(CACHE_NAME);

        // Update cached tickets
        await Promise.all(
            tickets.map(ticket => {
                return cache.put(`/api/tickets/${ticket.id}`, new Response(JSON.stringify(ticket)));
            })
        );

        // Notify clients about the update
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'TICKETS_UPDATED',
                tickets: tickets
            });
        });
    } catch (error) {
        console.error('Error updating tickets:', error);
    }
}

// Function to sync offline purchases
async function syncPurchases() {
    try {
        const cache = await caches.open(CACHE_NAME);
        const requests = await cache.keys();
        const purchaseRequests = requests.filter(request =>
            request.url.includes('/api/purchases') &&
            request.method === 'POST'
        );

        await Promise.all(
            purchaseRequests.map(async request => {
                try {
                    const response = await fetch(request.clone());
                    if (response.ok) {
                        await cache.delete(request);
                        // Show success notification
                        self.registration.showNotification('TiXara', {
                            body: 'Pembelian tiket berhasil disinkronkan',
                            icon: '/icons/icon-192x192.png',
                            badge: '/icons/icon-72x72.png'
                        });
                    }
                } catch (error) {
                    console.error('Error syncing purchase:', error);
                }
            })
        );
    } catch (error) {
        console.error('Error in sync purchases:', error);
    }
}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('balance_transactions')) {
            Schema::create('balance_transactions', function (Blueprint $table) {
                $table->id();
                $table->string('transaction_number')->unique();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->enum('type', ['deposit', 'withdrawal', 'earning', 'fee', 'refund', 'adjustment']);
                $table->decimal('amount', 15, 2);
                $table->decimal('balance_before', 15, 2);
                $table->decimal('balance_after', 15, 2);
                $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending');
                $table->string('description');
                $table->json('metadata')->nullable(); // Store additional data like order_id, event_id, etc.
            
            // For deposits and withdrawals
                $table->string('payment_method')->nullable();
                $table->string('payment_reference')->nullable();
                $table->json('payment_data')->nullable();
            
            // Admin approval for withdrawals
                $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamp('approved_at')->nullable();
                $table->text('admin_notes')->nullable();
            
                $table->timestamps();

                $table->index(['user_id', 'type']);
                $table->index(['status']);
                $table->index(['created_at']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('balance_transactions');
    }
};

<?php

namespace App\Models\CyberGuard;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CyberGuardSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get setting value with proper type casting
     */
    public function getTypedValue()
    {
        switch ($this->type) {
            case 'boolean':
                return filter_var($this->value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $this->value;
            case 'float':
                return (float) $this->value;
            case 'json':
                return json_decode($this->value, true);
            default:
                return $this->value;
        }
    }

    /**
     * Set setting value with proper type handling
     */
    public function setTypedValue($value)
    {
        switch ($this->type) {
            case 'boolean':
                $this->value = $value ? '1' : '0';
                break;
            case 'json':
                $this->value = json_encode($value);
                break;
            default:
                $this->value = (string) $value;
        }
    }

    /**
     * Get setting by key
     */
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->where('is_active', true)->first();
        return $setting ? $setting->getTypedValue() : $default;
    }

    /**
     * Set setting value
     */
    public static function set($key, $value, $type = 'string', $description = null, $category = 'general')
    {
        $setting = static::firstOrNew(['key' => $key]);
        $setting->type = $type;
        $setting->description = $description;
        $setting->category = $category;
        $setting->is_active = true;
        $setting->setTypedValue($value);
        $setting->save();

        return $setting;
    }

    /**
     * Get all settings by category
     */
    public static function getByCategory($category)
    {
        return static::where('category', $category)
                    ->where('is_active', true)
                    ->get()
                    ->pluck('value', 'key')
                    ->map(function($value, $key) {
                        $setting = static::where('key', $key)->first();
                        return $setting ? $setting->getTypedValue() : $value;
                    });
    }

    /**
     * Check if feature is enabled
     */
    public static function isEnabled($key)
    {
        return static::get($key, false) === true;
    }

    /**
     * Get default settings
     */
    public static function getDefaults()
    {
        return [
            // DDoS Protection
            'ddos_protection_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'ddos', 'description' => 'Enable DDoS protection'],
            'ddos_max_requests_per_minute' => ['value' => 60, 'type' => 'integer', 'category' => 'ddos', 'description' => 'Maximum requests per minute per IP'],
            'ddos_block_duration_minutes' => ['value' => 15, 'type' => 'integer', 'category' => 'ddos', 'description' => 'Block duration in minutes'],
            'ddos_whitelist_ips' => ['value' => [], 'type' => 'json', 'category' => 'ddos', 'description' => 'Whitelisted IP addresses'],

            // SQL Injection Protection
            'sql_injection_protection_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'sql_injection', 'description' => 'Enable SQL injection protection'],
            'sql_injection_patterns' => ['value' => [
                'union.*select', 'select.*from', 'insert.*into', 'delete.*from', 'update.*set',
                'drop.*table', 'create.*table', 'alter.*table', 'exec.*sp_', 'xp_cmdshell'
            ], 'type' => 'json', 'category' => 'sql_injection', 'description' => 'SQL injection patterns to detect'],
            'sql_injection_action' => ['value' => 'block', 'type' => 'string', 'category' => 'sql_injection', 'description' => 'Action to take: log, block, or alert'],

            // Rate Limiting
            'rate_limiting_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'rate_limiting', 'description' => 'Enable rate limiting'],
            'rate_limit_api_requests_per_minute' => ['value' => 100, 'type' => 'integer', 'category' => 'rate_limiting', 'description' => 'API requests per minute'],
            'rate_limit_login_attempts_per_hour' => ['value' => 5, 'type' => 'integer', 'category' => 'rate_limiting', 'description' => 'Login attempts per hour'],
            'rate_limit_registration_per_hour' => ['value' => 3, 'type' => 'integer', 'category' => 'rate_limiting', 'description' => 'Registration attempts per hour'],

            // Firewall
            'firewall_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'firewall', 'description' => 'Enable firewall'],
            'firewall_block_tor_exit_nodes' => ['value' => true, 'type' => 'boolean', 'category' => 'firewall', 'description' => 'Block Tor exit nodes'],
            'firewall_block_vpn_proxies' => ['value' => false, 'type' => 'boolean', 'category' => 'firewall', 'description' => 'Block VPN/Proxy IPs'],
            'firewall_allowed_countries' => ['value' => ['ID'], 'type' => 'json', 'category' => 'firewall', 'description' => 'Allowed country codes'],

            // Monitoring
            'monitoring_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'monitoring', 'description' => 'Enable security monitoring'],
            'monitoring_log_all_requests' => ['value' => false, 'type' => 'boolean', 'category' => 'monitoring', 'description' => 'Log all requests'],
            'monitoring_alert_email' => ['value' => '<EMAIL>', 'type' => 'string', 'category' => 'monitoring', 'description' => 'Alert email address'],
            'monitoring_alert_threshold' => ['value' => 10, 'type' => 'integer', 'category' => 'monitoring', 'description' => 'Alert threshold per hour'],

            // XSS Protection
            'xss_protection_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'xss', 'description' => 'Enable XSS protection'],
            'xss_sanitize_input' => ['value' => true, 'type' => 'boolean', 'category' => 'xss', 'description' => 'Sanitize user input'],
            'xss_patterns' => ['value' => [
                '<script', 'javascript:', 'onload=', 'onerror=', 'onclick=', 'onmouseover=',
                'eval(', 'alert(', 'confirm(', 'prompt('
            ], 'type' => 'json', 'category' => 'xss', 'description' => 'XSS patterns to detect'],

            // CSRF Protection
            'csrf_protection_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'csrf', 'description' => 'Enable CSRF protection'],
            'csrf_token_lifetime' => ['value' => 120, 'type' => 'integer', 'category' => 'csrf', 'description' => 'CSRF token lifetime in minutes'],

            // File Upload Security
            'file_upload_security_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'file_upload', 'description' => 'Enable file upload security'],
            'file_upload_allowed_extensions' => ['value' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'], 'type' => 'json', 'category' => 'file_upload', 'description' => 'Allowed file extensions'],
            'file_upload_max_size_mb' => ['value' => 10, 'type' => 'integer', 'category' => 'file_upload', 'description' => 'Maximum file size in MB'],
            'file_upload_scan_viruses' => ['value' => true, 'type' => 'boolean', 'category' => 'file_upload', 'description' => 'Scan uploaded files for viruses'],

            // Session Security
            'session_security_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'session', 'description' => 'Enable session security'],
            'session_timeout_minutes' => ['value' => 120, 'type' => 'integer', 'category' => 'session', 'description' => 'Session timeout in minutes'],
            'session_regenerate_on_login' => ['value' => true, 'type' => 'boolean', 'category' => 'session', 'description' => 'Regenerate session ID on login'],
            'session_ip_validation' => ['value' => true, 'type' => 'boolean', 'category' => 'session', 'description' => 'Validate session IP address'],

            // Brute Force Protection
            'brute_force_protection_enabled' => ['value' => true, 'type' => 'boolean', 'category' => 'brute_force', 'description' => 'Enable brute force protection'],
            'brute_force_max_attempts' => ['value' => 5, 'type' => 'integer', 'category' => 'brute_force', 'description' => 'Maximum login attempts'],
            'brute_force_lockout_duration' => ['value' => 30, 'type' => 'integer', 'category' => 'brute_force', 'description' => 'Lockout duration in minutes'],
            'brute_force_progressive_delay' => ['value' => true, 'type' => 'boolean', 'category' => 'brute_force', 'description' => 'Enable progressive delay'],
        ];
    }

    /**
     * Initialize default settings
     */
    public static function initializeDefaults()
    {
        $defaults = static::getDefaults();
        $created = 0;

        foreach ($defaults as $key => $config) {
            if (!static::where('key', $key)->exists()) {
                static::set(
                    $key,
                    $config['value'],
                    $config['type'],
                    $config['description'],
                    $config['category']
                );
                $created++;
            }
        }

        return $created;
    }

    /**
     * Get security status overview
     */
    public static function getSecurityStatus()
    {
        $categories = ['ddos', 'sql_injection', 'rate_limiting', 'firewall', 'monitoring', 'xss', 'csrf'];
        $status = [];

        foreach ($categories as $category) {
            $settings = static::where('category', $category)->where('is_active', true)->get();
            $enabledCount = $settings->where('key', 'like', '%_enabled')->where('value', '1')->count();
            $totalCount = $settings->where('key', 'like', '%_enabled')->count();

            $status[$category] = [
                'enabled' => $enabledCount,
                'total' => $totalCount,
                'percentage' => $totalCount > 0 ? round(($enabledCount / $totalCount) * 100) : 0,
                'status' => $enabledCount === $totalCount ? 'protected' : ($enabledCount > 0 ? 'partial' : 'vulnerable')
            ];
        }

        return $status;
    }
}

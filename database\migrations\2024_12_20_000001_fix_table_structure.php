<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        try {
            // Check if tickets table exists with wrong name (tickets)
            if (Schema::hasTable('tickets') && !Schema::hasTable('tickets')) {
                // Check if this is actually the tickets table by looking for event-specific columns
                $columns = Schema::getColumnListing('tickets');
                $eventColumns = ['title', 'description', 'venue_name', 'venue_address', 'start_date', 'end_date'];
                $hasEventColumns = count(array_intersect($eventColumns, $columns)) >= 4;

                if ($hasEventColumns) {
                    // This is the tickets table with wrong name, rename it
                    Schema::rename('tickets', 'tickets');
                    echo "Renamed 'tickets' table to 'tickets'\n";
                }
            }

            // Ensure events table exists with correct structure
            if (!Schema::hasTable('events')) {
                Schema::create('events', function (Blueprint $table) {
                    $table->id();
                    $table->string('title');
                    $table->string('slug')->unique();
                    $table->text('description');
                    $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
                    $table->foreignId('organizer_id')->constrained('users')->onDelete('cascade');
                    $table->string('venue_name');
                    $table->text('venue_address');
                    $table->string('city');
                    $table->string('province')->default('DKI Jakarta');
                    $table->datetime('start_date');
                    $table->datetime('end_date');
                    $table->decimal('price', 12, 2)->default(0);
                    $table->integer('total_capacity');
                    $table->integer('available_capacity');
                    $table->string('poster')->nullable();
                    $table->boolean('is_free')->default(false);
                    $table->boolean('is_featured')->default(false);
                    $table->enum('status', ['draft', 'published', 'cancelled'])->default('draft');
                    $table->text('terms_conditions')->nullable();
                    $table->json('additional_info')->nullable();
                    $table->datetime('sale_start_date')->nullable();
                    $table->datetime('sale_end_date')->nullable();
                    $table->timestamps();

                    // Indexes
                    $table->index(['status', 'start_date']);
                    $table->index(['category_id', 'status']);
                    $table->index(['organizer_id', 'status']);
                    $table->index(['city', 'status']);
                    $table->index('is_featured');
                });
                echo "Created 'events' table\n";
            }

            // Ensure tickets table exists with correct structure (for actual tickets)
            if (!Schema::hasTable('tickets')) {
                Schema::create('tickets', function (Blueprint $table) {
                    $table->id();
                    $table->string('ticket_code')->unique();
                    $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
                    $table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
                    $table->foreignId('order_id')->nullable()->constrained('orders')->onDelete('set null');
                    $table->string('buyer_name');
                    $table->string('buyer_email');
                    $table->string('buyer_phone')->nullable();
                    $table->decimal('price', 10, 2);
                    $table->enum('status', ['active', 'used', 'cancelled', 'refunded'])->default('active');
                    $table->datetime('purchased_at');
                    $table->datetime('used_at')->nullable();
                    $table->foreignId('validated_by')->nullable()->constrained('users');
                    $table->text('notes')->nullable();
                    $table->json('metadata')->nullable();
                    $table->timestamps();

                    // Indexes
                    $table->index(['event_id', 'status']);
                    $table->index(['buyer_id', 'status']);
                    $table->index(['order_id', 'status']);
                    $table->index('ticket_code');
                    $table->index('status');
                });
                echo "Created 'tickets' table\n";
            }

            // Fix any remaining foreign key issues
            $this->fixForeignKeys();

        } catch (Exception $e) {
            echo "Error during table structure fix: " . $e->getMessage() . "\n";
            // Don't throw the exception to prevent migration failure
        } finally {
            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is designed to fix structure, not to be reversed
        // Reversing could cause data loss
    }

    /**
     * Fix foreign key constraints
     */
    private function fixForeignKeys(): void
    {
        try {
            // Fix tickets table foreign keys
            if (Schema::hasTable('tickets')) {
                if (!$this->foreignKeyExists('tickets', 'tickets_category_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_organizer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('organizer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }
            }

            // Fix tickets table foreign keys
            if (Schema::hasTable('tickets')) {
                if (!$this->foreignKeyExists('tickets', 'tickets_event_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('event_id')->references('id')->on('events')->onDelete('cascade');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_buyer_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('buyer_id')->references('id')->on('users')->onDelete('cascade');
                    });
                }

                if (Schema::hasTable('orders') && !$this->foreignKeyExists('tickets', 'tickets_order_id_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
                    });
                }

                if (!$this->foreignKeyExists('tickets', 'tickets_validated_by_foreign')) {
                    Schema::table('tickets', function (Blueprint $table) {
                        $table->foreign('validated_by')->references('id')->on('users');
                    });
                }
            }

        } catch (Exception $e) {
            echo "Error fixing foreign keys: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Check if foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $constraintName): bool
    {
        try {
            $result = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.TABLE_CONSTRAINTS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
                AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            ", [$table, $constraintName]);

            return count($result) > 0;
        } catch (Exception $e) {
            return false;
        }
    }
};

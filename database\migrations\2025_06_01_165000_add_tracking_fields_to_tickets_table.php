<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->integer('print_count')->default(0)->after('download_count');
            $table->timestamp('last_printed_at')->nullable()->after('print_count');
            $table->boolean('is_authentic')->default(true)->after('last_printed_at');
            $table->timestamp('authenticity_verified_at')->nullable()->after('is_authentic');
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn([
                'print_count',
                'last_printed_at',
                'is_authentic',
                'authenticity_verified_at'
            ]);
        });
    }
};

<?php

namespace Database\Seeders;

use App\Models\CyberGuard\CyberGuardSetting;
use Illuminate\Database\Seeder;

class CyberGuardSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Initialize default CyberGuard settings
        $created = CyberGuardSetting::initializeDefaults();
        
        $this->command->info("CyberGuard settings initialized! Created {$created} default settings.");
    }
}

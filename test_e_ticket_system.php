<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Services\TicketGeneratorService;
use App\Services\TicketValidationService;

echo "=== TESTING E-TICKET BOARDING PASS SYSTEM ===\n\n";

// 1. Create test users
echo "1. Creating test users...\n";
$organizer = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Test Organizer',
        'password' => bcrypt('password'),
        'role' => 'penjual',
        'badge_level_id' => 4,
        'email_verified_at' => now()
    ]
);

$customer = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Test Customer',
        'password' => bcrypt('password'),
        'role' => 'customer',
        'badge_level_id' => 1,
        'email_verified_at' => now()
    ]
);

$staff = User::firstOrCreate(
    ['email' => '<EMAIL>'],
    [
        'name' => 'Test Staff',
        'password' => bcrypt('password'),
        'role' => 'staff',
        'badge_level_id' => 3,
        'email_verified_at' => now()
    ]
);

echo "✓ Users created successfully\n\n";

// 2. Create test event with different templates
echo "2. Creating test events with different templates...\n";

$templates = ['unix', 'minimalist', 'pro', 'custom'];
$events = [];

foreach ($templates as $template) {
    $event = Event::create([
        'title' => "Test Event - {$template} Template",
        'description' => "Testing event untuk {$template} boarding pass template",
        'venue_name' => 'Jakarta Convention Center',
        'venue_address' => 'Jl. Gatot Subroto, Jakarta',
        'city' => 'Jakarta',
        'province' => 'DKI Jakarta',
        'start_date' => now()->addDays(7),
        'end_date' => now()->addDays(7)->addHours(3),
        'price' => 150000 + (array_search($template, $templates) * 50000),
        'organizer_id' => $organizer->id,
        'boarding_pass_template' => $template,
        'auto_generate_tickets' => true,
        'email_tickets_to_buyers' => true,
        'status' => 'published'
    ]);
    
    $events[$template] = $event;
    echo "✓ Event created: {$event->title} (ID: {$event->id})\n";
}

echo "\n";

// 3. Create orders and tickets
echo "3. Creating orders and generating tickets...\n";

$ticketGenerator = new TicketGeneratorService();
$tickets = [];

foreach ($events as $template => $event) {
    // Create order
    $order = Order::create([
        'order_number' => 'ORD-' . now()->format('Ymd') . '-' . str_pad(array_search($template, $templates) + 1, 3, '0', STR_PAD_LEFT),
        'user_id' => $customer->id,
        'event_id' => $event->id,
        'total_amount' => $event->price,
        'status' => 'completed',
        'payment_status' => 'paid'
    ]);
    
    // Create ticket
    $ticket = Ticket::create([
        'ticket_number' => 'TIK-' . now()->format('Ymd') . '-' . str_pad(array_search($template, $templates) + 1, 3, '0', STR_PAD_LEFT),
        'qr_code' => 'QR-' . strtoupper(substr($template, 0, 3)) . '-' . \Str::random(8),
        'event_id' => $event->id,
        'buyer_id' => $customer->id,
        'user_id' => $customer->id,
        'order_id' => $order->id,
        'attendee_name' => $customer->name,
        'attendee_email' => $customer->email,
        'price' => $event->price,
        'total_paid' => $event->price,
        'status' => 'active'
    ]);
    
    // Generate boarding pass ID and PDF
    try {
        $pdfPath = $ticketGenerator->generateTicketPdfFromEvent($ticket);
        $tickets[$template] = $ticket->fresh();
        echo "✓ Ticket generated: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})\n";
        echo "  PDF: {$pdfPath}\n";
    } catch (Exception $e) {
        echo "✗ Error generating ticket: {$e->getMessage()}\n";
    }
}

echo "\n";

// 4. Test authenticity checker
echo "4. Testing Authenticity Checker...\n";

$validationService = new TicketValidationService();

foreach ($tickets as $template => $ticket) {
    echo "Testing {$template} template ticket:\n";
    
    // Test with ticket number
    $result = $validationService->checkAuthenticity($ticket->ticket_number);
    echo "  Ticket Number Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED') . "\n";
    
    // Test with boarding pass ID
    $result = $validationService->checkAuthenticity($ticket->boarding_pass_id);
    echo "  Boarding Pass ID Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED') . "\n";
    
    // Test with QR code
    $result = $validationService->checkAuthenticity($ticket->qr_code);
    echo "  QR Code Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED') . "\n";
    
    echo "\n";
}

// 5. Test validation (marking as used)
echo "5. Testing ticket validation (marking as used)...\n";

$testTicket = $tickets['unix']; // Use unix template ticket for validation test
$result = $validationService->validateByTicketNumber($testTicket->ticket_number, $staff);

echo "Validation Result: " . ($result['success'] ? '✓ SUCCESS' : '✗ FAILED') . "\n";
echo "Message: {$result['message']}\n";

// Test authenticity check on used ticket
echo "\nTesting authenticity check on used ticket:\n";
$result = $validationService->checkAuthenticity($testTicket->ticket_number);
echo "Used Ticket Authenticity: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED') . "\n";
echo "Status: {$result['data']['ticket_status']}\n";

echo "\n";

// 6. Test fake ticket
echo "6. Testing fake ticket detection...\n";

$fakeTicketNumber = 'TIK-20250127-FAKE';
$result = $validationService->checkAuthenticity($fakeTicketNumber);
echo "Fake Ticket Check: " . ($result['success'] ? '✗ FAILED (Should be fake)' : '✓ CORRECTLY DETECTED AS FAKE') . "\n";
echo "Message: {$result['message']}\n";

echo "\n";

// 7. Display test URLs
echo "7. Test URLs for manual testing:\n";
echo "Home: http://localhost:8000\n";
echo "Login (Organizer): http://localhost:8000/login (<EMAIL> / password)\n";
echo "Login (Customer): http://localhost:8000/login (<EMAIL> / password)\n";
echo "Login (Staff): http://localhost:8000/login (<EMAIL> / password)\n";
echo "Authenticity Checker: http://localhost:8000/authenticity-check\n";
echo "Staff Validation: http://localhost:8000/validation\n";

echo "\nTemplate Preview URLs:\n";
foreach ($templates as $template) {
    echo "- {$template}: http://localhost:8000/organizer/events/templates/boarding-pass/{$template}\n";
}

echo "\nTest Ticket Numbers for Authenticity Check:\n";
foreach ($tickets as $template => $ticket) {
    echo "- {$template}: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})\n";
}

echo "\n=== TESTING COMPLETED ===\n";
echo "You can now test the system manually using the URLs above.\n";

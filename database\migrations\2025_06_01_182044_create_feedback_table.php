<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('feedback')) {
            if (!Schema::hasTable('feedback')) {
            Schema::create('feedback', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('event_id')->constrained()->onDelete('cascade');
                $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
                $table->integer('rating')->default(5); // 1-5 stars
                $table->text('comment')->nullable();
                $table->boolean('is_anonymous')->default(false);
                $table->enum('status', ['pending', 'approved', 'rejected', 'responded'])->default('pending');
                $table->text('admin_response')->nullable();
                $table->timestamp('responded_at')->nullable();
                $table->foreignId('responded_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();

                $table->index(['event_id', 'status']);
                $table->index(['user_id', 'created_at']);
                $table->index(['rating', 'status']);
            });
        }

        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};

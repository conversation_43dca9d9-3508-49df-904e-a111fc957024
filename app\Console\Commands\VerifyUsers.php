<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class VerifyUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:verify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify users data and display statistics';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== VERIFIKASI DATA USERS ===');
        $this->newLine();

        // Total users
        $totalUsers = User::count();
        $this->info("📊 TOTAL USERS: {$totalUsers}");
        $this->newLine();

        // Users by role
        $this->info('👥 USERS BY ROLE:');
        $roles = ['admin', 'staff', 'penjual', 'pembeli'];
        foreach ($roles as $role) {
            $count = User::where('role', $role)->count();
            $this->line("  {$role}: {$count}");
        }
        $this->newLine();

        // Users by badge level
        $this->info('🏆 USERS BY BADGE LEVEL:');
        for ($i = 1; $i <= 4; $i++) {
            $count = User::where('badge_level_id', $i)->count();
            $badgeName = match($i) {
                1 => 'Bronze',
                2 => 'Silver', 
                3 => 'Gold',
                4 => 'Platinum'
            };
            $this->line("  Badge {$i} ({$badgeName}): {$count}");
        }
        $this->newLine();

        // Detailed user list
        $this->info('📋 DETAILED USER LIST:');
        $users = User::orderBy('role')->orderBy('badge_level_id', 'desc')->get();

        $headers = ['Name', 'Role', 'Badge', 'Email'];
        $rows = [];

        foreach ($users as $user) {
            $badgeName = match($user->badge_level_id) {
                1 => 'Bronze',
                2 => 'Silver', 
                3 => 'Gold',
                4 => 'Platinum',
                default => 'Unknown'
            };
            
            $rows[] = [
                $user->name,
                $user->role,
                $badgeName,
                $user->email
            ];
        }

        $this->table($headers, $rows);
        $this->newLine();

        // Platinum users (can access custom templates)
        $this->info('💎 PLATINUM USERS (Custom Template Access):');
        $platinumUsers = User::where('badge_level_id', 4)->get();
        foreach ($platinumUsers as $user) {
            $this->line("  ✅ {$user->name} ({$user->role}) - {$user->email}");
        }
        $this->newLine();

        // Organizers by badge level
        $this->info('🎪 ORGANIZERS BY BADGE LEVEL:');
        $organizers = User::where('role', 'penjual')->orderBy('badge_level_id', 'desc')->get();
        foreach ($organizers as $organizer) {
            $badgeName = match($organizer->badge_level_id) {
                1 => 'Bronze',
                2 => 'Silver', 
                3 => 'Gold',
                4 => 'Platinum',
                default => 'Unknown'
            };
            
            $customAccess = $organizer->badge_level_id >= 4 ? '✅ Custom Templates' : '❌ No Custom';
            $this->line("  {$badgeName}: {$organizer->name} - {$customAccess}");
        }

        $this->newLine();
        $this->info('🔐 LOGIN CREDENTIALS:');
        $this->newLine();
        
        $this->comment('👑 ADMIN ACCOUNTS:');
        $this->line('Super Admin: <EMAIL> / SuperAdmin@2024');
        $this->line('Admin: <EMAIL> / Admin@2024');
        $this->newLine();
        
        $this->comment('👥 STAFF ACCOUNTS:');
        $this->line('Staff 1: <EMAIL> / Staff@2024');
        $this->line('Staff 2: <EMAIL> / Staff@2024');
        $this->newLine();
        
        $this->comment('🎪 ORGANIZER ACCOUNTS:');
        $this->line('Platinum: <EMAIL> / Platinum@2024 (Custom Templates)');
        $this->line('Gold: <EMAIL> / Gold@2024');
        $this->line('Silver: <EMAIL> / Silver@2024');
        $this->line('Bronze: <EMAIL> / Bronze@2024');
        $this->newLine();
        
        $this->comment('🎫 CUSTOMER ACCOUNTS:');
        $this->line('Premium: <EMAIL> / Customer@2024');
        $this->line('Regular: <EMAIL> / Customer@2024');
        $this->line('New: <EMAIL> / Customer@2024');
        $this->line('Test: <EMAIL> / Test@2024');

        $this->newLine();
        $this->info('=== VERIFICATION COMPLETE ===');

        return 0;
    }
}

@extends('layouts.admin')

@section('title', 'Payment Gateway Management')

@push('styles')
<style>
.gateway-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.gateway-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.gateway-status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.connection-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.connection-indicator.connected {
    background-color: #10b981;
    animation: pulse 2s infinite;
}

.connection-indicator.disconnected {
    background-color: #ef4444;
}

.connection-indicator.testing {
    background-color: #f59e0b;
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.method-toggle {
    transition: all 0.3s ease;
}

.method-toggle:checked {
    background-color: #10b981;
    border-color: #10b981;
}
</style>
@endpush

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Payment Gateway Management</h1>
            <p class="text-gray-600 mt-2">Configure and manage payment gateways and methods</p>
        </div>
        <div class="flex space-x-3">
            <button id="testAllGateways" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-plug mr-2"></i>
                Test All Connections
            </button>
            <button id="refreshStats" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-credit-card text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Gateways</p>
                    <p class="text-2xl font-bold text-gray-900" id="totalGateways">{{ $gateways->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-check-circle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Gateways</p>
                    <p class="text-2xl font-bold text-gray-900" id="activeGateways">{{ $gateways->where('is_active', true)->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                    <i class="fas fa-cogs text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Payment Methods</p>
                    <p class="text-2xl font-bold text-gray-900" id="totalMethods">{{ $paymentMethods->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-toggle-on text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Methods</p>
                    <p class="text-2xl font-bold text-gray-900" id="activeMethods">{{ $paymentMethods->where('is_active', true)->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Gateways Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        @foreach($gateways as $gateway)
        <div class="gateway-card bg-white rounded-lg shadow-md p-6 relative">
            <!-- Status Badge -->
            <div class="gateway-status-badge">
                <span class="px-3 py-1 rounded-full text-xs font-medium {{ $gateway->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                    {{ $gateway->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>

            <!-- Gateway Header -->
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                    {{ strtoupper(substr($gateway->name, 0, 2)) }}
                </div>
                <div class="ml-4">
                    <h3 class="text-xl font-semibold text-gray-900">{{ $gateway->name }}</h3>
                    <p class="text-gray-600 text-sm">{{ $gateway->description }}</p>
                </div>
            </div>

            <!-- Connection Status -->
            <div class="mb-4">
                <div class="flex items-center">
                    <span class="connection-indicator disconnected" id="indicator-{{ $gateway->id }}"></span>
                    <span class="text-sm text-gray-600" id="status-{{ $gateway->id }}">Not tested</span>
                </div>
            </div>

            <!-- Gateway Info -->
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div>
                    <p class="text-xs text-gray-500 uppercase tracking-wide">Provider</p>
                    <p class="text-sm font-medium text-gray-900">{{ ucfirst($gateway->provider) }}</p>
                </div>
                <div>
                    <p class="text-xs text-gray-500 uppercase tracking-wide">Environment</p>
                    <p class="text-sm font-medium text-gray-900">
                        {{ $gateway->is_production ? 'Production' : 'Sandbox' }}
                    </p>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Payment Methods ({{ $gateway->paymentMethods->count() }})</h4>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                    @forelse($gateway->paymentMethods as $method)
                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div class="flex items-center">
                            <i class="{{ $method->icon_class ?? 'fas fa-credit-card' }} text-gray-600 mr-2"></i>
                            <span class="text-sm text-gray-900">{{ $method->name }}</span>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   class="sr-only method-toggle" 
                                   {{ $method->is_active ? 'checked' : '' }}
                                   data-method-id="{{ $method->id }}">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    @empty
                    <p class="text-sm text-gray-500 italic">No payment methods configured</p>
                    @endforelse
                </div>
            </div>

            <!-- Actions -->
            <div class="flex space-x-2">
                <button class="test-connection flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                        data-gateway-id="{{ $gateway->id }}">
                    <i class="fas fa-plug mr-2"></i>
                    Test Connection
                </button>
                <button class="toggle-gateway bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
                        data-gateway-id="{{ $gateway->id }}"
                        data-active="{{ $gateway->is_active ? 'true' : 'false' }}">
                    <i class="fas fa-power-off mr-2"></i>
                    {{ $gateway->is_active ? 'Disable' : 'Enable' }}
                </button>
                <a href="{{ route('admin.payment-gateways.show', $gateway) }}" 
                   class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    <i class="fas fa-cog mr-2"></i>
                    Configure
                </a>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Quick Actions -->
    <div class="mt-8 bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button id="enableAllGateways" class="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors">
                <i class="fas fa-check-circle mr-2"></i>
                Enable All Gateways
            </button>
            <button id="disableAllGateways" class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg transition-colors">
                <i class="fas fa-times-circle mr-2"></i>
                Disable All Gateways
            </button>
            <button id="exportConfig" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors">
                <i class="fas fa-download mr-2"></i>
                Export Configuration
            </button>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div id="testResultsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Connection Test Results</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="testResults" class="space-y-3">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Test individual gateway connection
    document.querySelectorAll('.test-connection').forEach(button => {
        button.addEventListener('click', function() {
            const gatewayId = this.dataset.gatewayId;
            testGatewayConnection(gatewayId);
        });
    });

    // Toggle gateway status
    document.querySelectorAll('.toggle-gateway').forEach(button => {
        button.addEventListener('click', function() {
            const gatewayId = this.dataset.gatewayId;
            const isActive = this.dataset.active === 'true';
            toggleGatewayStatus(gatewayId, !isActive);
        });
    });

    // Toggle payment method status
    document.querySelectorAll('.method-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const methodId = this.dataset.methodId;
            const isActive = this.checked;
            updateMethodStatus(methodId, isActive);
        });
    });

    // Test all gateways
    document.getElementById('testAllGateways').addEventListener('click', function() {
        testAllGateways();
    });

    // Quick actions
    document.getElementById('enableAllGateways').addEventListener('click', function() {
        bulkUpdateGatewayStatus(true);
    });

    document.getElementById('disableAllGateways').addEventListener('click', function() {
        bulkUpdateGatewayStatus(false);
    });

    // Modal controls
    document.getElementById('closeModal').addEventListener('click', function() {
        document.getElementById('testResultsModal').classList.add('hidden');
    });

    // Functions
    async function testGatewayConnection(gatewayId) {
        const indicator = document.getElementById(`indicator-${gatewayId}`);
        const status = document.getElementById(`status-${gatewayId}`);
        
        // Show testing state
        indicator.className = 'connection-indicator testing';
        status.textContent = 'Testing connection...';

        try {
            const response = await fetch(`/admin/payment-gateways/${gatewayId}/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                indicator.className = 'connection-indicator connected';
                status.textContent = 'Connected';
                showNotification('Connection test successful', 'success');
            } else {
                indicator.className = 'connection-indicator disconnected';
                status.textContent = 'Connection failed';
                showNotification(result.message || 'Connection test failed', 'error');
            }
        } catch (error) {
            indicator.className = 'connection-indicator disconnected';
            status.textContent = 'Test failed';
            showNotification('Network error during test', 'error');
        }
    }

    async function toggleGatewayStatus(gatewayId, newStatus) {
        try {
            const response = await fetch(`/admin/payment-gateways/${gatewayId}/toggle`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                location.reload(); // Refresh page to update UI
            } else {
                showNotification(result.message || 'Failed to update gateway status', 'error');
            }
        } catch (error) {
            showNotification('Network error', 'error');
        }
    }

    async function updateMethodStatus(methodId, isActive) {
        try {
            const response = await fetch(`/admin/payment-gateways/${methodId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ is_active: isActive })
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Payment method status updated', 'success');
            } else {
                showNotification(result.message || 'Failed to update method status', 'error');
            }
        } catch (error) {
            showNotification('Network error', 'error');
        }
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});
</script>
@endpush

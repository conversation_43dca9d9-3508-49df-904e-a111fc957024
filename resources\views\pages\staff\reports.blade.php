@extends('layouts.staff')

@section('title', 'Staff Reports - TiXara')
@section('page-title', 'Reports')

@push('styles')
<style>
/* Staff Reports Styles */
.report-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
}

.chart-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
}

.metric-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.performance-ring {
    position: relative;
    width: 120px;
    height: 120px;
}

.performance-ring svg {
    transform: rotate(-90deg);
}

.performance-ring .ring-bg {
    fill: none;
    stroke: #e5e7eb;
    stroke-width: 8;
}

.performance-ring .ring-progress {
    fill: none;
    stroke: #10b981;
    stroke-width: 8;
    stroke-linecap: round;
    transition: stroke-dasharray 1s ease;
}

.date-picker {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.2);
}
</style>
@endpush

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8" data-aos="fade-up">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="mb-6 lg:mb-0">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Performance Reports</h1>
                <p class="text-gray-600 dark:text-gray-400">Track your validation performance and statistics</p>
            </div>
            <div class="flex flex-col sm:flex-row gap-3">
                <input type="date" class="date-picker px-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500" value="{{ date('Y-m-d') }}">
                <button class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                    <i data-lucide="download" class="w-4 h-4 inline mr-2"></i>
                    Export Report
                </button>
            </div>
        </div>
    </div>

    <!-- Performance Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- Performance Score -->
        <div class="metric-card rounded-xl p-6" data-aos="fade-up" data-aos-delay="100">
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Overall Performance</h3>
                <div class="performance-ring mx-auto mb-4">
                    <svg class="w-30 h-30">
                        <circle class="ring-bg" cx="60" cy="60" r="50"></circle>
                        <circle class="ring-progress" cx="60" cy="60" r="50"
                                stroke-dasharray="{{ 2 * pi() * 50 }}"
                                stroke-dashoffset="{{ 2 * pi() * 50 * (1 - 0.92) }}"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900 dark:text-white">92%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">Score</div>
                        </div>
                    </div>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">Excellent performance this month!</p>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="lg:col-span-2 space-y-4" data-aos="fade-up" data-aos-delay="200">
            <div class="grid grid-cols-2 gap-4">
                <div class="report-card rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Today's Scans</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
                        </div>
                        <div class="p-3 bg-blue-100 rounded-xl">
                            <i data-lucide="scan-line" class="w-6 h-6 text-blue-600"></i>
                        </div>
                    </div>
                </div>

                <div class="report-card rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                            <p class="text-2xl font-bold text-green-600">98.5%</p>
                        </div>
                        <div class="p-3 bg-green-100 rounded-xl">
                            <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                        </div>
                    </div>
                </div>

                <div class="report-card rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Avg. Scan Time</p>
                            <p class="text-2xl font-bold text-purple-600">2.3s</p>
                        </div>
                        <div class="p-3 bg-purple-100 rounded-xl">
                            <i data-lucide="clock" class="w-6 h-6 text-purple-600"></i>
                        </div>
                    </div>
                </div>

                <div class="report-card rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Events Handled</p>
                            <p class="text-2xl font-bold text-orange-600">8</p>
                        </div>
                        <div class="p-3 bg-orange-100 rounded-xl">
                            <i data-lucide="calendar" class="w-6 h-6 text-orange-600"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Daily Performance Chart -->
        <div class="chart-container rounded-xl p-6" data-aos="fade-up" data-aos-delay="300">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Daily Validation Trend</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center">
                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-gray-400 mx-auto mb-2"></i>
                    <p class="text-gray-600">Chart will be rendered here</p>
                </div>
            </div>
        </div>

        <!-- Event Distribution Chart -->
        <div class="chart-container rounded-xl p-6" data-aos="fade-up" data-aos-delay="400">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Event Type Distribution</h3>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center">
                    <i data-lucide="pie-chart" class="w-12 h-12 text-gray-400 mx-auto mb-2"></i>
                    <p class="text-gray-600">Pie chart will be rendered here</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="report-card rounded-xl p-6" data-aos="fade-up" data-aos-delay="500">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Recent Validation Activity</h3>
        <div class="space-y-4">
            @for($i = 1; $i <= 8; $i++)
            <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                        <i data-lucide="check" class="w-5 h-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-900 dark:text-white">Ticket TIX{{ str_pad($i * 123, 6, '0', STR_PAD_LEFT) }} validated</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Concert Music Festival 2024</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ now()->subMinutes($i * 5)->format('H:i') }}</p>
                    <p class="text-xs text-gray-600 dark:text-gray-400">{{ now()->subMinutes($i * 5)->diffForHumans() }}</p>
                </div>
            </div>
            @endfor
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8" data-aos="fade-up" data-aos-delay="600">
        <div class="report-card rounded-xl p-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Weekly Summary</h4>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Total Scans</span>
                    <span class="font-semibold text-gray-900 dark:text-white">1,247</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Valid Scans</span>
                    <span class="font-semibold text-green-600">1,228</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Invalid Scans</span>
                    <span class="font-semibold text-red-600">19</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-400">Accuracy</span>
                    <span class="font-semibold text-blue-600">98.5%</span>
                </div>
            </div>
        </div>

        <div class="report-card rounded-xl p-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monthly Goals</h4>
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Scans Target</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">2,847/3,000</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: 94.9%"></div>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between mb-1">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Accuracy Target</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">98.5%/95%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-500 h-2 rounded-full" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="report-card rounded-xl p-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Achievements</h4>
            <div class="space-y-3">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                        <i data-lucide="award" class="w-4 h-4 text-yellow-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Speed Demon</p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">Avg scan time under 3s</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i data-lucide="target" class="w-4 h-4 text-green-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Accuracy Master</p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">98%+ accuracy rate</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i data-lucide="zap" class="w-4 h-4 text-blue-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Productivity Pro</p>
                        <p class="text-xs text-gray-600 dark:text-gray-400">1000+ scans this month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Date picker functionality
    const datePicker = document.querySelector('.date-picker');
    datePicker.addEventListener('change', function() {
        console.log('Date changed to:', this.value);
        // Implement date filtering logic here
    });

    // Export functionality
    const exportBtn = document.querySelector('button[title="Export Report"]');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            console.log('Exporting report...');
            // Implement export logic here
        });
    }
});
</script>
@endpush
@endsection

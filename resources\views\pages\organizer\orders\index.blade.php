@extends('layouts.organizer')

@section('title', 'Orders')
@section('subtitle', 'Manage customer orders for your events')

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Order Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Track and manage customer orders</p>
        </div>
        
        <div class="mt-4 md:mt-0">
            <x-button variant="outline" icon="download" onclick="exportOrders()">
                Export Orders
            </x-button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <x-stats-card 
            title="Total Orders"
            :value="number_format($stats['total_orders'], 0, ',', '.')"
            icon="shopping-cart"
            icon-color="blue"
            aos="fade-up"
            aos-delay="100"
        />
        
        <x-stats-card 
            title="Completed Orders"
            :value="number_format($stats['completed_orders'], 0, ',', '.')"
            icon="check-circle"
            icon-color="green"
            aos="fade-up"
            aos-delay="200"
        />
        
        <x-stats-card 
            title="Pending Orders"
            :value="number_format($stats['pending_orders'], 0, ',', '.')"
            icon="clock"
            icon-color="yellow"
            aos="fade-up"
            aos-delay="300"
        />
        
        <x-stats-card 
            title="Total Revenue"
            :value="'Rp ' . number_format($stats['total_revenue'], 0, ',', '.')"
            icon="dollar-sign"
            icon-color="purple"
            aos="fade-up"
            aos-delay="400"
        />
    </div>

    <!-- Filters -->
    <x-modern-card aos="fade-up" aos-delay="500">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Status Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                    <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    <option value="refunded" {{ request('status') == 'refunded' ? 'selected' : '' }}>Refunded</option>
                </select>
            </div>

            <!-- Event Filter -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event</label>
                <select name="event_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                    <option value="">All Events</option>
                    @foreach($events as $event)
                        <option value="{{ $event->id }}" {{ request('event_id') == $event->id ? 'selected' : '' }}>
                            {{ $event->title }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Date From -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Date</label>
                <input type="date" name="date_from" value="{{ request('date_from') }}" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Date To -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Date</label>
                <input type="date" name="date_to" value="{{ request('date_to') }}" 
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            </div>

            <!-- Search -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                <div class="flex space-x-2">
                    <input type="text" name="search" value="{{ request('search') }}" placeholder="Order number, customer..." 
                           class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
                    <x-button type="submit" icon="search">Filter</x-button>
                </div>
            </div>
        </form>
    </x-modern-card>

    <!-- Orders Table -->
    <x-modern-card aos="fade-up" aos-delay="600">
        @if($orders->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-700">
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Order</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Customer</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Event</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Quantity</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Amount</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Status</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Date</th>
                            <th class="text-left py-3 px-4 font-semibold text-gray-900 dark:text-white">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($orders as $order)
                        <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                            <td class="py-4 px-4">
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $order->order_number }}</p>
                                </div>
                            </td>
                            <td class="py-4 px-4">
                                <div>
                                    <p class="font-medium text-gray-900 dark:text-white">{{ $order->user->name }}</p>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->user->email }}</p>
                                </div>
                            </td>
                            <td class="py-4 px-4">
                                <p class="font-medium text-gray-900 dark:text-white">{{ $order->event->title }}</p>
                            </td>
                            <td class="py-4 px-4">
                                <p class="text-gray-900 dark:text-white">{{ $order->quantity }}</p>
                            </td>
                            <td class="py-4 px-4">
                                <p class="font-medium text-gray-900 dark:text-white">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</p>
                            </td>
                            <td class="py-4 px-4">
                                <x-badge :variant="$order->status === 'completed' ? 'success' : ($order->status === 'pending' ? 'warning' : ($order->status === 'cancelled' ? 'error' : 'default'))">
                                    {{ ucfirst($order->status) }}
                                </x-badge>
                            </td>
                            <td class="py-4 px-4">
                                <p class="text-gray-900 dark:text-white">{{ $order->created_at->format('M d, Y') }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->created_at->format('H:i') }}</p>
                            </td>
                            <td class="py-4 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('organizer.orders.show', $order) }}" 
                                       class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i data-lucide="eye" class="w-4 h-4"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-6">
                {{ $orders->links() }}
            </div>
        @else
            <x-empty-state 
                icon="shopping-cart"
                title="No orders found"
                description="Orders from customers will appear here"
                action-text="View All Events"
                action-url="{{ route('organizer.events.index') }}"
            />
        @endif
    </x-modern-card>
</div>
@endsection

@push('scripts')
<script>
function exportOrders() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '{{ route("organizer.orders.export") }}?' + params.toString();
}
</script>
@endpush

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Check if column doesn't exist before adding
            if (!Schema::hasColumn('users', 'assigned_organizers')) {
                // JSON column to store assigned organizer IDs for staff
                $table->json('assigned_organizers')->nullable()->after('role');
            }

            // Add index for better performance when querying by role
            if (!Schema::hasIndex('users', 'users_role_index')) {
                $table->index(['role'], 'users_role_index');
            }
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'assigned_organizers')) {
                $table->dropColumn('assigned_organizers');
            }

            if (Schema::hasIndex('users', 'users_role_index')) {
                $table->dropIndex('users_role_index');
            }
        });
    }
};

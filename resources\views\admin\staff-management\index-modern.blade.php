@extends('layouts.admin')

@section('title', 'Staff Management - Modern Dashboard')

@push('styles')
<style>
/* CSS Variables for Theme Support */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --background-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    --background-primary: #1f2937;
    --background-secondary: #111827;
    --background-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --glass-bg: rgba(31, 41, 55, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Base Styles */
.staff-management-container {
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    min-height: 100vh;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.staff-management-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* Header Styles */
.page-header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    position: relative;
    z-index: 1;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--primary-color));
    border-radius: 1.5rem 1.5rem 0 0;
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.page-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

/* Statistics Cards */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

.stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--card-gradient);
    border-radius: 1.5rem 1.5rem 0 0;
}

.stat-card.primary::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.stat-card.success::before {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.stat-card.warning::before {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.stat-card.error::before {
    background: linear-gradient(90deg, var(--error-color), #dc2626);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px var(--shadow-color);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    background: var(--icon-bg);
    color: var(--icon-color);
}

.stat-card.primary .stat-icon {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.stat-card.success .stat-icon {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stat-card.warning .stat-icon {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stat-card.error .stat-icon {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

/* Search and Filter Section */
.search-filter-section {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px var(--shadow-color);
    position: relative;
    z-index: 1;
}

.search-box {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    font-size: 1rem;
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    background: var(--background-primary);
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Staff Cards */
.staff-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    position: relative;
    z-index: 1;
}

.staff-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 1.5rem;
    box-shadow: 0 10px 30px var(--shadow-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.staff-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--card-status-color);
    border-radius: 1.5rem 1.5rem 0 0;
}

.staff-card.assigned::before {
    background: linear-gradient(90deg, var(--success-color), #059669);
}

.staff-card.unassigned::before {
    background: linear-gradient(90deg, var(--warning-color), #d97706);
}

.staff-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px var(--shadow-color);
}

.staff-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.staff-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.25rem;
}

.staff-info h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.staff-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.staff-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.staff-status.assigned {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.staff-status.unassigned {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.staff-assignments {
    margin-bottom: 1rem;
}

.assignment-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--background-secondary);
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.staff-actions {
    display: flex;
    gap: 0.75rem;
}

.action-btn {
    flex: 1;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.action-btn.danger {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .staff-grid {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        padding: 1.5rem;
    }
    
    .page-title {
        font-size: 1.75rem;
    }
    
    .search-filter-section {
        padding: 1rem;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .staff-actions {
        flex-direction: column;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
</style>
@endpush

@section('content')
<div class="staff-management-container">
    <div class="container mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="page-header fade-in">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="page-title">Staff Management</h1>
                    <p class="page-subtitle">Manage staff assignments and organizer relationships</p>
                </div>
                <div class="flex gap-3">
                    <button id="refreshData" class="action-btn primary">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        Refresh
                    </button>
                    <button id="exportData" class="action-btn primary">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid fade-in" style="animation-delay: 0.1s;">
            <div class="stat-card primary">
                <div class="stat-icon">
                    <i data-lucide="users" class="w-6 h-6"></i>
                </div>
                <div class="stat-number" id="totalStaff">{{ $staffMembers->count() }}</div>
                <div class="stat-label">Total Staff</div>
                <div class="stat-change positive">
                    <i data-lucide="trending-up" class="w-4 h-4"></i>
                    +2 this month
                </div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon">
                    <i data-lucide="user-check" class="w-6 h-6"></i>
                </div>
                <div class="stat-number" id="assignedStaff">{{ $staffMembers->where('assigned_organizers_count', '>', 0)->count() }}</div>
                <div class="stat-label">Assigned Staff</div>
                <div class="stat-change positive">
                    <i data-lucide="trending-up" class="w-4 h-4"></i>
                    +5 this week
                </div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon">
                    <i data-lucide="user-x" class="w-6 h-6"></i>
                </div>
                <div class="stat-number" id="unassignedStaff">{{ $staffMembers->where('assigned_organizers_count', 0)->count() }}</div>
                <div class="stat-label">Unassigned Staff</div>
                <div class="stat-change negative">
                    <i data-lucide="trending-down" class="w-4 h-4"></i>
                    -3 this week
                </div>
            </div>

            <div class="stat-card error">
                <div class="stat-icon">
                    <i data-lucide="building" class="w-6 h-6"></i>
                </div>
                <div class="stat-number" id="totalOrganizers">{{ $organizers->count() }}</div>
                <div class="stat-label">Total Organizers</div>
                <div class="stat-change positive">
                    <i data-lucide="trending-up" class="w-4 h-4"></i>
                    +1 this month
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-filter-section fade-in" style="animation-delay: 0.2s;">
            <div class="flex flex-col lg:flex-row gap-4 items-center">
                <div class="search-box flex-1 max-w-md">
                    <i data-lucide="search" class="search-icon w-5 h-5"></i>
                    <input type="text" id="searchInput" class="search-input" 
                           placeholder="Search staff or organizers...">
                </div>
                
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">All Staff</button>
                    <button class="filter-btn" data-filter="assigned">Assigned</button>
                    <button class="filter-btn" data-filter="unassigned">Unassigned</button>
                </div>
            </div>
        </div>

        <!-- Staff Grid -->
        <div class="staff-grid fade-in" style="animation-delay: 0.3s;" id="staffGrid">
            @foreach($staffMembers as $staff)
            <div class="staff-card {{ $staff['assigned_organizers_count'] > 0 ? 'assigned' : 'unassigned' }}"
                 data-staff-id="{{ $staff['id'] }}"
                 data-filter="{{ $staff['assigned_organizers_count'] > 0 ? 'assigned' : 'unassigned' }}">
                
                <div class="staff-status {{ $staff['assigned_organizers_count'] > 0 ? 'assigned' : 'unassigned' }}">
                    {{ $staff['assigned_organizers_count'] > 0 ? 'Assigned' : 'Unassigned' }}
                </div>

                <div class="staff-header">
                    <div class="staff-avatar">
                        {{ strtoupper(substr($staff['name'], 0, 2)) }}
                    </div>
                    <div class="staff-info">
                        <h3>{{ $staff['name'] }}</h3>
                        <p>{{ $staff['email'] }}</p>
                    </div>
                </div>

                <div class="staff-assignments">
                    @if($staff['assigned_organizers_count'] > 0)
                        @foreach($staff['assigned_organizers'] as $organizer)
                        <div class="assignment-item">
                            <i data-lucide="building" class="w-4 h-4"></i>
                            <span>{{ $organizer['name'] }}</span>
                            <span class="ml-auto text-xs">({{ $organizer['events_count'] }} events)</span>
                        </div>
                        @endforeach
                    @else
                        <div class="assignment-item">
                            <i data-lucide="alert-circle" class="w-4 h-4"></i>
                            <span>No assignments</span>
                        </div>
                    @endif
                </div>

                <div class="staff-actions">
                    <button onclick="showAssignmentModal({{ $staff['id'] }}, '{{ $staff['name'] }}')"
                            class="action-btn primary">
                        <i data-lucide="user-plus" class="w-4 h-4"></i>
                        Manage
                    </button>
                    
                    @if($staff['assigned_organizers_count'] > 0)
                    <button onclick="clearAllAssignments({{ $staff['id'] }}, '{{ $staff['name'] }}')"
                            class="action-btn danger">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                        Clear
                    </button>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection

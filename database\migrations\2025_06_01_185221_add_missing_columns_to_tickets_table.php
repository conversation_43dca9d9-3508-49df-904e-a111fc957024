<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            if (!Schema::hasColumn('tickets', 'cancelled_at')) {
                $table->timestamp('cancelled_at')->nullable()->after('used_at');
            }
            if (!Schema::hasColumn('tickets', 'cancellation_reason')) {
                $table->text('cancellation_reason')->nullable()->after('cancelled_at');
            }
            if (!Schema::hasColumn('tickets', 'qr_code_path')) {
                $table->string('qr_code_path')->nullable()->after('qr_code');
            }
            if (!Schema::hasColumn('tickets', 'seat_number')) {
                $table->string('seat_number')->nullable()->after('attendee_phone');
            }
            if (!Schema::hasColumn('tickets', 'user_id')) {
                $table->foreignId('user_id')->nullable()->after('buyer_id')->constrained('users')->onDelete('cascade');
            }
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropColumn([
                'cancelled_at',
                'cancellation_reason',
                'qr_code_path',
                'seat_number',
                'user_id'
            ]);
        });
    }
};

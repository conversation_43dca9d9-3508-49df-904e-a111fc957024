@extends('layouts.admin')

@section('title', 'Payment Methods Management')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css">
<style>
/* Payment Methods Admin Styles */
.payment-methods-container {
    background: #F8FAFC;
    min-height: 100vh;
    padding: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border-left: 4px solid var(--stat-color, #0066CC);
}

.stat-card.total { --stat-color: #0066CC; }
.stat-card.active { --stat-color: #10B981; }
.stat-card.manual { --stat-color: #F59E0B; }
.stat-card.gateway { --stat-color: #8B5CF6; }
.stat-card.configured { --stat-color: #06B6D4; }

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: #1A1A1A;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 14px;
    color: #666666;
    font-weight: 500;
}

.filters-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
}

.filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
}

.filter-input {
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-input:focus {
    border-color: #0066CC;
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.payment-methods-table {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.table-header {
    background: #F8FAFC;
    padding: 20px 24px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: between;
    align-items: center;
}

.table-title {
    font-size: 18px;
    font-weight: 700;
    color: #1A1A1A;
    margin: 0;
}

.payment-method-row {
    padding: 20px 24px;
    border-bottom: 1px solid #F3F4F6;
    display: grid;
    grid-template-columns: auto 1fr auto auto auto auto;
    gap: 16px;
    align-items: center;
    transition: all 0.3s ease;
    cursor: grab;
}

.payment-method-row:hover {
    background: #F8FAFC;
}

.payment-method-row:last-child {
    border-bottom: none;
}

.payment-method-row.sortable-ghost {
    opacity: 0.5;
}

.payment-method-row.sortable-chosen {
    background: #E6F3FF;
}

.drag-handle {
    color: #9CA3AF;
    cursor: grab;
    font-size: 16px;
}

.drag-handle:active {
    cursor: grabbing;
}

.method-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.method-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #F3F4F6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #6B7280;
}

.method-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.method-details h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1A1A1A;
    margin: 0 0 4px 0;
}

.method-details p {
    font-size: 14px;
    color: #6B7280;
    margin: 0;
}

.method-type {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.method-type.manual {
    background: #FEF3C7;
    color: #92400E;
}

.method-type.tripay {
    background: #DBEAFE;
    color: #1E40AF;
}

.method-type.midtrans {
    background: #F3E8FF;
    color: #7C3AED;
}

.method-type.xendit {
    background: #D1FAE5;
    color: #065F46;
}

.method-category {
    font-size: 14px;
    color: #6B7280;
    font-weight: 500;
}

.method-fee {
    font-size: 14px;
    color: #1A1A1A;
    font-weight: 600;
}

.method-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.method-status.active {
    background: #D1FAE5;
    color: #065F46;
}

.method-status.inactive {
    background: #FEE2E2;
    color: #991B1B;
}

.method-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn.edit {
    background: #DBEAFE;
    color: #1E40AF;
}

.action-btn.edit:hover {
    background: #BFDBFE;
}

.action-btn.test {
    background: #F3E8FF;
    color: #7C3AED;
}

.action-btn.test:hover {
    background: #E9D5FF;
}

.action-btn.delete {
    background: #FEE2E2;
    color: #DC2626;
}

.action-btn.delete:hover {
    background: #FECACA;
}

.btn-primary {
    background: #0066CC;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: #0052A3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6B7280;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #4B5563;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6B7280;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: #F3F4F6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: #9CA3AF;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-methods-container {
        padding: 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .filter-form {
        grid-template-columns: 1fr;
    }

    .payment-method-row {
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: left;
    }

    .method-actions {
        justify-content: flex-start;
    }
}
</style>
@endpush

@section('content')
<div class="payment-methods-container">
    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
        <div>
            <h1 style="font-size: 28px; font-weight: 700; color: #1A1A1A; margin: 0 0 8px 0;">Payment Methods</h1>
            <p style="color: #6B7280; margin: 0;">Manage payment methods and gateway configurations</p>
        </div>
        <a href="{{ route('admin.payment-methods.create') }}" class="btn-primary">
            <i class="fas fa-plus"></i>
            Add Payment Method
        </a>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card total">
            <div class="stat-value">{{ $stats['total_methods'] }}</div>
            <div class="stat-label">Total Methods</div>
        </div>
        <div class="stat-card active">
            <div class="stat-value">{{ $stats['active_methods'] }}</div>
            <div class="stat-label">Active Methods</div>
        </div>
        <div class="stat-card manual">
            <div class="stat-value">{{ $stats['manual_methods'] }}</div>
            <div class="stat-label">Manual Methods</div>
        </div>
        <div class="stat-card gateway">
            <div class="stat-value">{{ $stats['gateway_methods'] }}</div>
            <div class="stat-label">Gateway Methods</div>
        </div>
        <div class="stat-card configured">
            <div class="stat-value">{{ $stats['configured_gateways'] }}</div>
            <div class="stat-label">Configured Gateways</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filters-card">
        <form method="GET" action="{{ route('admin.payment-methods.index') }}" class="filter-form">
            <div class="filter-group">
                <label class="filter-label">Search</label>
                <input type="text" name="search" class="filter-input" value="{{ request('search') }}" placeholder="Search methods...">
            </div>

            <div class="filter-group">
                <label class="filter-label">Type</label>
                <select name="type" class="filter-input">
                    <option value="">All Types</option>
                    <option value="manual" {{ request('type') === 'manual' ? 'selected' : '' }}>Manual</option>
                    <option value="tripay" {{ request('type') === 'tripay' ? 'selected' : '' }}>TriPay</option>
                    <option value="midtrans" {{ request('type') === 'midtrans' ? 'selected' : '' }}>Midtrans</option>
                    <option value="xendit" {{ request('type') === 'xendit' ? 'selected' : '' }}>Xendit</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Category</label>
                <select name="category" class="filter-input">
                    <option value="">All Categories</option>
                    <option value="bank_transfer" {{ request('category') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                    <option value="e_wallet" {{ request('category') === 'e_wallet' ? 'selected' : '' }}>E-Wallet</option>
                    <option value="qris" {{ request('category') === 'qris' ? 'selected' : '' }}>QRIS</option>
                    <option value="virtual_account" {{ request('category') === 'virtual_account' ? 'selected' : '' }}>Virtual Account</option>
                    <option value="credit_card" {{ request('category') === 'credit_card' ? 'selected' : '' }}>Credit Card</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select name="status" class="filter-input">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <div style="display: flex; gap: 8px;">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-search"></i>
                    Filter
                </button>
                <a href="{{ route('admin.payment-methods.index') }}" class="btn-secondary">
                    <i class="fas fa-times"></i>
                    Reset
                </a>
            </div>
        </form>
    </div>

    <!-- Payment Methods Table -->
    <div class="payment-methods-table">
        <div class="table-header">
            <h3 class="table-title">Payment Methods</h3>
            <div style="display: flex; gap: 12px; align-items: center;">
                <span style="font-size: 14px; color: #6B7280;">Drag to reorder</span>
                <button type="button" class="btn-secondary" onclick="saveOrder()" id="saveOrderBtn" style="display: none;">
                    <i class="fas fa-save"></i>
                    Save Order
                </button>
            </div>
        </div>

        @if($paymentMethods->count() > 0)
            <div id="sortable-list">
                @foreach($paymentMethods as $method)
                <div class="payment-method-row" data-id="{{ $method->id }}">
                    <div class="drag-handle">
                        <i class="fas fa-grip-vertical"></i>
                    </div>

                    <div class="method-info">
                        <div class="method-logo">
                            @if($method->logo)
                                <img src="{{ asset($method->logo) }}" alt="{{ $method->name }}">
                            @else
                                <i class="{{ $method->icon ?? 'fas fa-credit-card' }}"></i>
                            @endif
                        </div>
                        <div class="method-details">
                            <h4>{{ $method->name }}</h4>
                            <p>{{ $method->description }}</p>
                        </div>
                    </div>

                    <div class="method-type {{ $method->type }}">
                        <i class="fas fa-{{ $method->is_manual ? 'hand-paper' : 'plug' }}"></i>
                        {{ $method->type_label }}
                    </div>

                    <div class="method-category">{{ $method->category_label }}</div>

                    <div class="method-fee">{{ $method->formatted_fee }}</div>

                    <div class="method-status {{ $method->is_active ? 'active' : 'inactive' }}"
                         onclick="toggleStatus({{ $method->id }})">
                        <i class="fas fa-{{ $method->is_active ? 'check-circle' : 'times-circle' }}"></i>
                        {{ $method->is_active ? 'Active' : 'Inactive' }}
                    </div>

                    <div class="method-actions">
                        <a href="{{ route('admin.payment-methods.edit', $method) }}" class="action-btn edit" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>

                        @if(!$method->is_manual)
                            <button type="button" class="action-btn test" onclick="testGateway({{ $method->id }})" title="Test Gateway">
                                <i class="fas fa-flask"></i>
                            </button>
                        @endif

                        <button type="button" class="action-btn delete" onclick="deleteMethod({{ $method->id }})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            @if($paymentMethods->hasPages())
                <div style="padding: 24px; border-top: 1px solid #E5E7EB;">
                    {{ $paymentMethods->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 style="font-size: 20px; font-weight: 700; color: #1A1A1A; margin: 0 0 8px 0;">No Payment Methods</h3>
                <p style="margin: 0 0 24px 0;">Get started by adding your first payment method</p>
                <a href="{{ route('admin.payment-methods.create') }}" class="btn-primary">
                    <i class="fas fa-plus"></i>
                    Add Payment Method
                </a>
            </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Payment Methods Management JavaScript
let sortable;
let hasChanges = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeSortable();
});

function initializeSortable() {
    const sortableList = document.getElementById('sortable-list');
    if (!sortableList) return;

    sortable = Sortable.create(sortableList, {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        onEnd: function(evt) {
            hasChanges = true;
            document.getElementById('saveOrderBtn').style.display = 'block';
        }
    });
}

function saveOrder() {
    if (!hasChanges) return;

    const items = [];
    const rows = document.querySelectorAll('.payment-method-row');

    rows.forEach((row, index) => {
        items.push({
            id: parseInt(row.dataset.id),
            sort_order: index
        });
    });

    const saveBtn = document.getElementById('saveOrderBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    saveBtn.disabled = true;

    fetch('{{ route("admin.payment-methods.update-sort-order") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ items: items })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            hasChanges = false;
            saveBtn.style.display = 'none';
        } else {
            showToast(data.message || 'Failed to save order', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to save order', 'error');
    })
    .finally(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

function toggleStatus(methodId) {
    fetch(`/admin/payment-methods/${methodId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            // Update UI
            const statusElement = document.querySelector(`[onclick="toggleStatus(${methodId})"]`);
            if (data.is_active) {
                statusElement.className = 'method-status active';
                statusElement.innerHTML = '<i class="fas fa-check-circle"></i> Active';
            } else {
                statusElement.className = 'method-status inactive';
                statusElement.innerHTML = '<i class="fas fa-times-circle"></i> Inactive';
            }
        } else {
            showToast(data.message || 'Failed to toggle status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to toggle status', 'error');
    });
}

function testGateway(methodId) {
    const testBtn = document.querySelector(`[onclick="testGateway(${methodId})"]`);
    const originalHtml = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    testBtn.disabled = true;

    fetch(`/admin/payment-methods/${methodId}/test-gateway`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        const type = data.success ? 'success' : 'error';
        showToast(data.message, type);
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to test gateway', 'error');
    })
    .finally(() => {
        testBtn.innerHTML = originalHtml;
        testBtn.disabled = false;
    });
}

function deleteMethod(methodId) {
    if (!confirm('Are you sure you want to delete this payment method? This action cannot be undone.')) {
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/payment-methods/${methodId}`;
    form.innerHTML = `
        <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
        <input type="hidden" name="_method" value="DELETE">
    `;

    document.body.appendChild(form);
    form.submit();
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
@endpush

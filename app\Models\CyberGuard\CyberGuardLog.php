<?php

namespace App\Models\CyberGuard;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class CyberGuardLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'severity',
        'ip_address',
        'user_agent',
        'request_method',
        'request_url',
        'request_data',
        'description',
        'additional_data',
        'action_taken',
        'user_id',
        'detected_at',
        'is_resolved',
        'resolution_notes',
    ];

    protected $casts = [
        'request_data' => 'array',
        'additional_data' => 'array',
        'detected_at' => 'datetime',
        'is_resolved' => 'boolean',
    ];

    // Security event types
    const TYPE_DDOS_ATTEMPT = 'ddos_attempt';
    const TYPE_SQL_INJECTION = 'sql_injection';
    const TYPE_XSS_ATTEMPT = 'xss_attempt';
    const TYPE_RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded';
    const TYPE_SUSPICIOUS_ACTIVITY = 'suspicious_activity';
    const TYPE_BRUTE_FORCE = 'brute_force';
    const TYPE_UNAUTHORIZED_ACCESS = 'unauthorized_access';
    const TYPE_FILE_UPLOAD_THREAT = 'file_upload_threat';
    const TYPE_CSRF_ATTACK = 'csrf_attack';
    const TYPE_BOT_DETECTED = 'bot_detected';

    // Severity level
    const SEVERITY_LOW = 'low';
    const SEVERITY_MEDIUM = 'medium';
    const SEVERITY_HIGH = 'high';
    const SEVERITY_CRITICAL = 'critical';

    // Actions taken
    const ACTION_LOGGED_ONLY = 'logged_only';
    const ACTION_BLOCKED = 'blocked';
    const ACTION_RATE_LIMITED = 'rate_limited';
    const ACTION_IP_BANNED = 'ip_banned';
    const ACTION_USER_SUSPENDED = 'user_suspended';

    /**
     * Get the user associated with this log
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for specific severity
     */
    public function scopeOfSeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for unresolved logs
     */
    public function scopeUnresolved($query)
    {
        return $query->where('is_resolved', false);
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('detected_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for specific IP
     */
    public function scopeFromIp($query, $ip)
    {
        return $query->where('ip_address', $ip);
    }

    /**
     * Log security event
     */
    public static function logEvent($type, $severity, $description, $additionalData = [])
    {
        $request = request();

        return static::create([
            'type' => $type,
            'severity' => $severity,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_data' => $request->except(['password', 'password_confirmation', '_token']),
            'description' => $description,
            'additional_data' => $additionalData,
            'user_id' => auth()->id(),
            'detected_at' => now(),
        ]);
    }

    /**
     * Log DDoS attempt
     */
    public static function logDdosAttempt($requestCount, $timeWindow)
    {
        return static::logEvent(
            static::TYPE_DDOS_ATTEMPT,
            static::SEVERITY_HIGH,
            "DDoS attempt detected: {$requestCount} requests in {$timeWindow} seconds",
            [
                'request_count' => $requestCount,
                'time_window' => $timeWindow,
                'threshold_exceeded' => true,
            ]
        );
    }

    /**
     * Log SQL injection attempt
     */
    public static function logSqlInjection($pattern, $input)
    {
        return static::logEvent(
            static::TYPE_SQL_INJECTION,
            static::SEVERITY_CRITICAL,
            "SQL injection attempt detected: pattern '{$pattern}' found in input",
            [
                'detected_pattern' => $pattern,
                'suspicious_input' => substr($input, 0, 500), // Limit length
                'input_length' => strlen($input),
            ]
        );
    }

    /**
     * Log XSS attempt
     */
    public static function logXssAttempt($pattern, $input)
    {
        return static::logEvent(
            static::TYPE_XSS_ATTEMPT,
            static::SEVERITY_HIGH,
            "XSS attempt detected: pattern '{$pattern}' found in input",
            [
                'detected_pattern' => $pattern,
                'suspicious_input' => substr($input, 0, 500),
                'input_length' => strlen($input),
            ]
        );
    }

    /**
     * Log rate limit exceeded
     */
    public static function logRateLimitExceeded($endpoint, $attempts, $limit)
    {
        return static::logEvent(
            static::TYPE_RATE_LIMIT_EXCEEDED,
            static::SEVERITY_MEDIUM,
            "Rate limit exceeded for endpoint '{$endpoint}': {$attempts}/{$limit} attempts",
            [
                'endpoint' => $endpoint,
                'attempts' => $attempts,
                'limit' => $limit,
                'exceeded_by' => $attempts - $limit,
            ]
        );
    }

    /**
     * Log brute force attempt
     */
    public static function logBruteForceAttempt($target, $attempts)
    {
        return static::logEvent(
            static::TYPE_BRUTE_FORCE,
            static::SEVERITY_HIGH,
            "Brute force attempt detected on '{$target}': {$attempts} failed attempts",
            [
                'target' => $target,
                'failed_attempts' => $attempts,
                'attack_type' => 'brute_force',
            ]
        );
    }

    /**
     * Mark as resolved
     */
    public function markResolved($notes = null)
    {
        $this->update([
            'is_resolved' => true,
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Get severity color
     */
    public function getSeverityColorAttribute()
    {
        return match($this->severity) {
            static::SEVERITY_LOW => '#10B981',
            static::SEVERITY_MEDIUM => '#F59E0B',
            static::SEVERITY_HIGH => '#EF4444',
            static::SEVERITY_CRITICAL => '#7C2D12',
            default => '#6B7280',
        };
    }

    /**
     * Get type icon
     */
    public function getTypeIconAttribute()
    {
        return match($this->type) {
            static::TYPE_DDOS_ATTEMPT => 'fas fa-shield-alt',
            static::TYPE_SQL_INJECTION => 'fas fa-database',
            static::TYPE_XSS_ATTEMPT => 'fas fa-code',
            static::TYPE_RATE_LIMIT_EXCEEDED => 'fas fa-tachometer-alt',
            static::TYPE_SUSPICIOUS_ACTIVITY => 'fas fa-exclamation-triangle',
            static::TYPE_BRUTE_FORCE => 'fas fa-hammer',
            static::TYPE_UNAUTHORIZED_ACCESS => 'fas fa-lock',
            static::TYPE_FILE_UPLOAD_THREAT => 'fas fa-file-upload',
            static::TYPE_CSRF_ATTACK => 'fas fa-user-secret',
            static::TYPE_BOT_DETECTED => 'fas fa-robot',
            default => 'fas fa-bug',
        };
    }

    /**
     * Get statistics
     */
    public static function getStatistics($hours = 24)
    {
        $baseQuery = static::where('detected_at', '>=', now()->subHours($hours));

        return [
            'total_events' => $baseQuery->count(),
            'by_severity' => static::where('detected_at', '>=', now()->subHours($hours))
                                  ->groupBy('severity')
                                  ->selectRaw('severity, count(*) as event_count')
                                  ->pluck('event_count', 'severity'),
            'by_type' => static::where('detected_at', '>=', now()->subHours($hours))
                              ->groupBy('type')
                              ->selectRaw('type, count(*) as event_count')
                              ->pluck('event_count', 'type'),
            'top_ips' => static::where('detected_at', '>=', now()->subHours($hours))
                              ->groupBy('ip_address')
                              ->selectRaw('ip_address, count(*) as event_count')
                              ->orderByDesc('event_count')
                              ->limit(10)
                              ->pluck('event_count', 'ip_address'),
            'unresolved_count' => static::where('detected_at', '>=', now()->subHours($hours))
                                       ->where('is_resolved', false)
                                       ->count(),
            'critical_count' => static::where('detected_at', '>=', now()->subHours($hours))
                                     ->where('severity', static::SEVERITY_CRITICAL)
                                     ->count(),
            'recent_attacks' => static::where('detected_at', '>=', now()->subHours($hours))
                                     ->orderByDesc('detected_at')
                                     ->limit(5)
                                     ->get(),
        ];
    }

    /**
     * Get threat level based on recent activity
     */
    public static function getThreatLevel($hours = 1)
    {
        $recentEvents = static::where('detected_at', '>=', now()->subHours($hours))->count();
        $criticalEvents = static::where('detected_at', '>=', now()->subHours($hours))
                               ->where('severity', static::SEVERITY_CRITICAL)
                               ->count();

        if ($criticalEvents > 5) {
            return 'critical';
        } elseif ($criticalEvents > 0 || $recentEvents > 50) {
            return 'high';
        } elseif ($recentEvents > 20) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Clean old logs
     */
    public static function cleanOldLogs($days = 30)
    {
        return static::where('detected_at', '<', now()->subDays($days))->delete();
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if notifications table exists and has correct structure
        if (Schema::hasTable('notifications')) {
            // Check if this is <PERSON><PERSON>'s built-in notifications table
            $columns = Schema::getColumnListing('notifications');

            // If it's <PERSON><PERSON>'s built-in table (has 'notifiable_id' instead of 'user_id')
            if (in_array('notifiable_id', $columns) && !in_array('user_id', $columns)) {
                // Drop the built-in table and recreate our custom one
                Schema::dropIfExists('notifications');
                $this->createCustomNotificationsTable();
            }
            // If it doesn't have user_id column, add it
            elseif (!in_array('user_id', $columns)) {
                Schema::table('notifications', function (Blueprint $table) {
                    $table->foreignId('user_id')->constrained()->onDelete('cascade')->after('id');
                });
            }
            // If it has user_id but missing other columns, add them
            else {
                $this->addMissingColumns();
            }
        } else {
            // Create the table if it doesn't exist
            $this->createCustomNotificationsTable();
        }
    }

    /**
     * Create our custom notifications table
     */
    private function createCustomNotificationsTable(): void
    {
        if (!Schema::hasTable('notifications')) {
            Schema::create('notifications', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('title');
                $table->text('message');
                $table->enum('type', ['system', 'user', 'event', 'payment', 'order', 'ticket'])->default('system');
                $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal');
                $table->json('data')->nullable();
                $table->string('action_url')->nullable();
                $table->timestamp('read_at')->nullable();
                $table->timestamps();

            // Indexes for better performance
                $table->index(['user_id', 'read_at']);
                $table->index(['type', 'created_at']);
                $table->index(['priority', 'created_at']);
            });
        }
    }

    /**
     * Add missing columns to existing table
     */
    private function addMissingColumns(): void
    {
        $columns = Schema::getColumnListing('notifications');

        Schema::table('notifications', function (Blueprint $table) use ($columns) {
            if (!in_array('title', $columns)) {
                $table->string('title')->after('user_id');
            }
            if (!in_array('message', $columns)) {
                $table->text('message')->after('title');
            }
            if (!in_array('type', $columns)) {
                $table->enum('type', ['system', 'user', 'event', 'payment', 'order', 'ticket'])->default('system')->after('message');
            }
            if (!in_array('priority', $columns)) {
                $table->enum('priority', ['low', 'normal', 'high', 'urgent'])->default('normal')->after('type');
            }
            if (!in_array('data', $columns)) {
                $table->json('data')->nullable()->after('priority');
            }
            if (!in_array('action_url', $columns)) {
                $table->string('action_url')->nullable()->after('data');
            }
            if (!in_array('read_at', $columns)) {
                $table->timestamp('read_at')->nullable()->after('action_url');
            }
        });

        // Add indexes if they don't exist
        try {
            DB::statement('CREATE INDEX IF NOT EXISTS notifications_user_id_read_at_index ON notifications (user_id, read_at)');
            DB::statement('CREATE INDEX IF NOT EXISTS notifications_type_created_at_index ON notifications (type, created_at)');
            DB::statement('CREATE INDEX IF NOT EXISTS notifications_priority_created_at_index ON notifications (priority, created_at)');
        } catch (Exception $e) {
            // Indexes might already exist, ignore error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't drop the table as it might contain important data
        // Just log that this migration was rolled back
        \Log::info('Notifications table fix migration rolled back');
    }
};

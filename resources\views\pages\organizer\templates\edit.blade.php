@extends('layouts.organizer')

@section('title', 'Edit Template - ' . $template->name)

@push('styles')
<!-- GrapesJS CSS -->
<link rel="stylesheet" href="https://unpkg.com/grapesjs@0.21.7/dist/css/grapes.min.css">
<link rel="stylesheet" href="https://unpkg.com/grapesjs-preset-webpage@1.0.2/dist/grapesjs-preset-webpage.min.css">

<style>
.editor-container {
    height: 80vh;
    border: 1px solid #E5E7EB;
    border-radius: 12px;
    overflow: hidden;
}

.editor-toolbar {
    background: #F8FAFC;
    border-bottom: 1px solid #E5E7EB;
    padding: 12px 16px;
    display: flex;
    justify-content: between;
    align-items: center;
    gap: 12px;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-btn {
    padding: 8px 12px;
    border: 1px solid #D1D5DB;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
}

.toolbar-btn:hover {
    background: #F3F4F6;
    border-color: #9CA3AF;
}

.toolbar-btn.active {
    background: #3B82F6;
    color: white;
    border-color: #3B82F6;
}

.save-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.save-status.saving {
    background: #FEF3C7;
    color: #92400E;
}

.save-status.saved {
    background: #D1FAE5;
    color: #065F46;
}

.save-status.error {
    background: #FEE2E2;
    color: #DC2626;
}

.template-info {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    padding: 20px;
    margin-bottom: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    font-weight: 500;
    color: #6B7280;
    font-size: 14px;
}

.info-value {
    color: #1F2937;
    font-size: 14px;
}

.badge-requirement-notice {
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border: 1px solid #F59E0B;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.badge-requirement-notice.error {
    background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
    border-color: #EF4444;
}

/* GrapesJS Customizations */
.gjs-editor {
    height: 100%;
}

.gjs-cv-canvas {
    background: #F8FAFC;
}

.gjs-blocks-cs {
    background: white;
}

.gjs-block {
    border-radius: 6px;
    border: 1px solid #E5E7EB;
    margin-bottom: 8px;
}

.gjs-block:hover {
    border-color: #3B82F6;
}

.gjs-sm-sector .gjs-sm-title {
    background: #F8FAFC;
    border-bottom: 1px solid #E5E7EB;
}

/* Custom blocks styling */
.ticket-element {
    border: 2px dashed #D1D5DB;
    padding: 12px;
    margin: 4px;
    border-radius: 6px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(59, 130, 246, 0.05);
    color: #3B82F6;
    font-weight: 500;
    cursor: move;
}

.ticket-element:hover {
    border-color: #3B82F6;
    background: rgba(59, 130, 246, 0.1);
}

.qr-code-placeholder {
    width: 100px;
    height: 100px;
    background: #F3F4F6;
    border: 2px dashed #9CA3AF;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6B7280;
    font-size: 12px;
    text-align: center;
}

@media (max-width: 768px) {
    .editor-container {
        height: 70vh;
    }
    
    .toolbar-group {
        flex-wrap: wrap;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Template</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $template->name }}</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('organizer.templates.preview', $template) }}" target="_blank" class="btn btn-secondary">
                <i data-lucide="eye" class="w-4 h-4"></i>
                Preview
            </a>
            <a href="{{ route('organizer.templates.show', $template) }}" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4"></i>
                Back
            </a>
        </div>
    </div>

    <!-- Template Info -->
    <div class="template-info">
        <div class="info-grid">
            <div class="info-item">
                <i data-lucide="layout-template" class="w-4 h-4 text-gray-400"></i>
                <span class="info-label">Type:</span>
                <span class="info-value">{{ ucfirst($template->template_type) }}</span>
            </div>
            <div class="info-item">
                <i data-lucide="calendar" class="w-4 h-4 text-gray-400"></i>
                <span class="info-label">Created:</span>
                <span class="info-value">{{ $template->created_at->format('M d, Y') }}</span>
            </div>
            <div class="info-item">
                <i data-lucide="activity" class="w-4 h-4 text-gray-400"></i>
                <span class="info-label">Usage:</span>
                <span class="info-value">{{ $template->usage_count ?? 0 }} times</span>
            </div>
            <div class="info-item">
                <i data-lucide="toggle-left" class="w-4 h-4 text-gray-400"></i>
                <span class="info-label">Status:</span>
                <span class="info-value {{ $template->is_active ? 'text-green-600' : 'text-red-600' }}">
                    {{ $template->is_active ? 'Active' : 'Inactive' }}
                </span>
            </div>
        </div>
    </div>

    @if($template->template_type === 'custom' && auth()->user()->badge_level_id < 4)
        <!-- Badge Requirement Notice -->
        <div class="badge-requirement-notice error">
            <div class="flex items-center gap-2">
                <i data-lucide="alert-circle" class="w-5 h-5"></i>
                <div>
                    <h4 class="font-semibold">Custom Template Access Restricted</h4>
                    <p class="text-sm">
                        Custom template editing requires Platinum badge level or higher. 
                        <a href="{{ route('organizer.dashboard') }}" class="underline">Upgrade your badge</a> to unlock the drag-and-drop editor.
                    </p>
                </div>
            </div>
        </div>
    @else
        <!-- Editor Container -->
        <div class="editor-container">
            <!-- Toolbar -->
            <div class="editor-toolbar">
                <div class="toolbar-group">
                    <button type="button" class="toolbar-btn" id="undoBtn">
                        <i data-lucide="undo" class="w-4 h-4"></i>
                        Undo
                    </button>
                    <button type="button" class="toolbar-btn" id="redoBtn">
                        <i data-lucide="redo" class="w-4 h-4"></i>
                        Redo
                    </button>
                    <div class="border-l border-gray-300 h-6 mx-2"></div>
                    <button type="button" class="toolbar-btn" id="previewBtn">
                        <i data-lucide="eye" class="w-4 h-4"></i>
                        Preview
                    </button>
                    <button type="button" class="toolbar-btn" id="codeBtn">
                        <i data-lucide="code" class="w-4 h-4"></i>
                        Code
                    </button>
                </div>
                
                <div class="toolbar-group">
                    <span class="save-status" id="saveStatus">Ready</span>
                    <button type="button" class="toolbar-btn" id="saveBtn">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        Save
                    </button>
                </div>
            </div>
            
            <!-- GrapesJS Editor -->
            <div id="gjs" style="height: calc(100% - 60px);"></div>
        </div>
    @endif

    <!-- Basic Template Form (for non-custom templates) -->
    @if($template->template_type !== 'custom' || auth()->user()->badge_level_id < 4)
        <form method="POST" action="{{ route('organizer.templates.update', $template) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Template Settings</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Template Name *
                        </label>
                        <input type="text" id="name" name="name" value="{{ old('name', $template->name) }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="preview_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Preview Image
                        </label>
                        <input type="file" id="preview_image" name="preview_image" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        @if($template->preview_image)
                            <p class="text-sm text-gray-500 mt-1">Current: {{ basename($template->preview_image) }}</p>
                        @endif
                        @error('preview_image')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mt-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Describe your template">{{ old('description', $template->description) }}</textarea>
                    @error('description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="mt-4">
                    <label class="flex items-center gap-2">
                        <input type="checkbox" name="is_active" value="1" {{ $template->is_active ? 'checked' : '' }}
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="text-sm font-medium text-gray-700">Active Template</span>
                    </label>
                </div>
                
                <div class="flex justify-end gap-4 mt-6">
                    <a href="{{ route('organizer.templates.show', $template) }}" class="btn btn-secondary">
                        Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i data-lucide="save" class="w-4 h-4"></i>
                        Update Template
                    </button>
                </div>
            </div>
        </form>
    @endif
</div>

@if($template->template_type === 'custom' && auth()->user()->badge_level_id >= 4)
@push('scripts')
<!-- GrapesJS Scripts -->
<script src="https://unpkg.com/grapesjs@0.21.7/dist/grapes.min.js"></script>
<script src="https://unpkg.com/grapesjs-preset-webpage@1.0.2/dist/grapesjs-preset-webpage.min.js"></script>

<script>
let editor;
let saveTimeout;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize GrapesJS Editor
    editor = grapesjs.init({
        container: '#gjs',
        height: '100%',
        width: 'auto',
        storageManager: false,
        plugins: ['gjs-preset-webpage'],
        pluginsOpts: {
            'gjs-preset-webpage': {
                modalImportTitle: 'Import Template',
                modalImportLabel: '<div style="margin-bottom: 10px; font-size: 13px;">Paste here your HTML/CSS and click Import</div>',
                modalImportContent: function(editor) {
                    return editor.getHtml() + '<style>' + editor.getCss() + '</style>';
                }
            }
        },
        blockManager: {
            appendTo: '.gjs-blocks-cs',
            blocks: [
                {
                    id: 'ticket-title',
                    label: 'Event Title',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element ticket-title" data-gjs-type="text">Event Title</div>',
                    attributes: { class: 'fa fa-heading' }
                },
                {
                    id: 'attendee-name',
                    label: 'Attendee Name',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element attendee-name" data-gjs-type="text">Attendee Name</div>',
                    attributes: { class: 'fa fa-user' }
                },
                {
                    id: 'event-date',
                    label: 'Event Date',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element event-date" data-gjs-type="text">Event Date & Time</div>',
                    attributes: { class: 'fa fa-calendar' }
                },
                {
                    id: 'venue-name',
                    label: 'Venue',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element venue-name" data-gjs-type="text">Venue Name</div>',
                    attributes: { class: 'fa fa-map-marker' }
                },
                {
                    id: 'ticket-price',
                    label: 'Price',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element ticket-price" data-gjs-type="text">Ticket Price</div>',
                    attributes: { class: 'fa fa-money' }
                },
                {
                    id: 'qr-code',
                    label: 'QR Code',
                    category: 'Ticket Elements',
                    content: '<div class="qr-code-placeholder">QR Code<br>Placeholder</div>',
                    attributes: { class: 'fa fa-qrcode' }
                },
                {
                    id: 'ticket-number',
                    label: 'Ticket Number',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element ticket-number" data-gjs-type="text">Ticket Number</div>',
                    attributes: { class: 'fa fa-hashtag' }
                },
                {
                    id: 'seat-number',
                    label: 'Seat Number',
                    category: 'Ticket Elements',
                    content: '<div class="ticket-element seat-number" data-gjs-type="text">Seat Number</div>',
                    attributes: { class: 'fa fa-chair' }
                }
            ]
        },
        canvas: {
            styles: [
                'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
            ]
        }
    });

    // Load existing template data
    @if($template->template_data)
        const templateData = @json($template->template_data);
        if (templateData.html) {
            editor.setComponents(templateData.html);
        }
        if (templateData.css) {
            editor.setStyle(templateData.css);
        }
    @endif

    // Auto-save functionality
    editor.on('component:update', function() {
        clearTimeout(saveTimeout);
        updateSaveStatus('saving');
        saveTimeout = setTimeout(autoSave, 2000);
    });

    // Toolbar event listeners
    document.getElementById('undoBtn').addEventListener('click', function() {
        editor.UndoManager.undo();
    });

    document.getElementById('redoBtn').addEventListener('click', function() {
        editor.UndoManager.redo();
    });

    document.getElementById('previewBtn').addEventListener('click', function() {
        const button = this;
        button.classList.toggle('active');
        
        if (button.classList.contains('active')) {
            editor.runCommand('preview');
        } else {
            editor.stopCommand('preview');
        }
    });

    document.getElementById('codeBtn').addEventListener('click', function() {
        const button = this;
        button.classList.toggle('active');
        
        if (button.classList.contains('active')) {
            editor.runCommand('core:open-code');
        } else {
            editor.runCommand('core:close-code');
        }
    });

    document.getElementById('saveBtn').addEventListener('click', function() {
        saveTemplate();
    });
});

function autoSave() {
    saveTemplate(true);
}

function saveTemplate(isAutoSave = false) {
    if (!isAutoSave) {
        updateSaveStatus('saving');
    }

    const html = editor.getHtml();
    const css = editor.getCss();
    
    const designData = {
        html: html,
        css: css,
        components: editor.getComponents(),
        styles: editor.getStyles()
    };

    fetch('{{ route("organizer.templates.save-design", $template) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            design_data: designData,
            preview_html: html,
            preview_css: css
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateSaveStatus('saved');
            if (!isAutoSave) {
                showNotification('Template saved successfully!', 'success');
            }
        } else {
            updateSaveStatus('error');
            showNotification('Failed to save template', 'error');
        }
    })
    .catch(error => {
        console.error('Save error:', error);
        updateSaveStatus('error');
        showNotification('Failed to save template', 'error');
    });
}

function updateSaveStatus(status) {
    const statusElement = document.getElementById('saveStatus');
    statusElement.className = 'save-status ' + status;
    
    switch(status) {
        case 'saving':
            statusElement.textContent = 'Saving...';
            break;
        case 'saved':
            statusElement.textContent = 'Saved';
            setTimeout(() => {
                statusElement.className = 'save-status';
                statusElement.textContent = 'Ready';
            }, 2000);
            break;
        case 'error':
            statusElement.textContent = 'Error';
            setTimeout(() => {
                statusElement.className = 'save-status';
                statusElement.textContent = 'Ready';
            }, 3000);
            break;
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 's':
                e.preventDefault();
                saveTemplate();
                break;
            case 'z':
                e.preventDefault();
                if (e.shiftKey) {
                    editor.UndoManager.redo();
                } else {
                    editor.UndoManager.undo();
                }
                break;
        }
    }
});
</script>
@endpush
@endif
@endsection

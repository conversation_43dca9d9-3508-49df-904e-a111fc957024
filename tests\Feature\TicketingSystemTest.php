<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\Category;
use App\Models\Ticket;
use App\Models\Order;
use App\Services\TicketValidationService;
use App\Services\BoardingPassService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class TicketingSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $organizer;
    protected $staff;
    protected $admin;
    protected $customer;
    protected $category;
    protected $event;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->organizer = User::factory()->create([
            'role' => 'penjual',
            'name' => 'Test Organizer',
            'email' => '<EMAIL>'
        ]);
        
        $this->staff = User::factory()->create([
            'role' => 'staff',
            'name' => 'Test Staff',
            'email' => '<EMAIL>'
        ]);
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'name' => 'Test Admin',
            'email' => '<EMAIL>'
        ]);
        
        $this->customer = User::factory()->create([
            'role' => 'user',
            'name' => 'Test Customer',
            'email' => '<EMAIL>'
        ]);
        
        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category'
        ]);
        
        // Create test event
        $this->event = Event::factory()->create([
            'title' => 'Test Event',
            'organizer_id' => $this->organizer->id,
            'category_id' => $this->category->id,
            'boarding_pass_template' => 'unix',
            'auto_generate_tickets' => true,
            'email_tickets_to_buyers' => true,
            'status' => 'published'
        ]);
        
        // Assign staff to organizer
        DB::table('staff_organizer_assignments')->insert([
            'staff_id' => $this->staff->id,
            'organizer_id' => $this->organizer->id,
            'is_active' => true,
            'assigned_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /** @test */
    public function organizer_can_create_event_with_template_selection()
    {
        $this->actingAs($this->organizer);
        
        $eventData = [
            'title' => 'New Test Event',
            'description' => 'Test event description',
            'category_id' => $this->category->id,
            'venue_name' => 'Test Venue',
            'venue_address' => 'Test Address',
            'city' => 'Test City',
            'province' => 'Test Province',
            'start_date' => now()->addDays(7)->format('Y-m-d\TH:i'),
            'end_date' => now()->addDays(7)->addHours(3)->format('Y-m-d\TH:i'),
            'price' => 100000,
            'total_capacity' => 100,
            'boarding_pass_template' => 'modern',
            'auto_generate_tickets' => true,
            'email_tickets_to_buyers' => true
        ];
        
        $response = $this->post(route('organizer.events.store'), $eventData);
        
        $response->assertRedirect();
        
        $this->assertDatabaseHas('events', [
            'title' => 'New Test Event',
            'organizer_id' => $this->organizer->id,
            'boarding_pass_template' => 'modern',
            'auto_generate_tickets' => true,
            'email_tickets_to_buyers' => true
        ]);
    }

    /** @test */
    public function boarding_pass_service_generates_unique_ids()
    {
        $boardingPassService = new BoardingPassService();
        
        // Test different templates
        $templates = ['unix', 'modern', 'vip', 'custom'];
        $generatedIds = [];
        
        foreach ($templates as $template) {
            $id = $boardingPassService->generateBoardingPassId($template);
            
            // Check format
            $this->assertMatchesRegularExpression('/^BP-[A-Z]{3}-\d{6}-[A-Z0-9]{6}$/', $id);
            
            // Check uniqueness
            $this->assertNotContains($id, $generatedIds);
            $generatedIds[] = $id;
            
            // Check template prefix
            $extractedTemplate = $boardingPassService->extractTemplateFromBoardingPassId($id);
            $this->assertEquals($template, $extractedTemplate);
        }
    }

    /** @test */
    public function staff_can_scan_assigned_organizer_tickets()
    {
        // Create ticket for the event
        $ticket = Ticket::factory()->create([
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'status' => 'active',
            'boarding_pass_id' => 'BP-UNX-241215-TEST01'
        ]);
        
        $validationService = new TicketValidationService();
        
        // Test staff scan
        $result = $validationService->staffScanTicket($ticket->qr_code, $this->staff);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('ready_to_use', $result['status']);
        $this->assertEquals('green', $result['color']);
        $this->assertEquals('check', $result['icon']);
    }

    /** @test */
    public function staff_cannot_scan_unassigned_organizer_tickets()
    {
        // Create another organizer
        $otherOrganizer = User::factory()->create([
            'role' => 'penjual',
            'name' => 'Other Organizer'
        ]);
        
        // Create event for other organizer
        $otherEvent = Event::factory()->create([
            'organizer_id' => $otherOrganizer->id,
            'category_id' => $this->category->id,
            'status' => 'published'
        ]);
        
        // Create ticket for other organizer's event
        $ticket = Ticket::factory()->create([
            'event_id' => $otherEvent->id,
            'buyer_id' => $this->customer->id,
            'status' => 'active',
            'boarding_pass_id' => 'BP-UNX-241215-TEST02'
        ]);
        
        $validationService = new TicketValidationService();
        
        // Test staff scan (should fail)
        $result = $validationService->staffScanTicket($ticket->qr_code, $this->staff);
        
        $this->assertFalse($result['success']);
        $this->assertEquals('not_assigned', $result['status']);
        $this->assertStringContains('tidak bisa digunakan karena bukan event Anda', $result['message']);
    }

    /** @test */
    public function admin_can_scan_all_tickets()
    {
        // Create ticket
        $ticket = Ticket::factory()->create([
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'status' => 'active',
            'boarding_pass_id' => 'BP-UNX-241215-TEST03'
        ]);
        
        $validationService = new TicketValidationService();
        
        // Test admin scan
        $result = $validationService->staffScanTicket($ticket->qr_code, $this->admin);
        
        $this->assertTrue($result['success']);
        $this->assertEquals('ready_to_use', $result['status']);
    }

    /** @test */
    public function staff_can_use_valid_ticket()
    {
        // Create ticket
        $ticket = Ticket::factory()->create([
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'status' => 'active',
            'boarding_pass_id' => 'BP-UNX-241215-TEST04'
        ]);
        
        $validationService = new TicketValidationService();
        
        // Use ticket
        $result = $validationService->staffUseTicket($ticket->id, $this->staff);
        
        $this->assertTrue($result['success']);
        $this->assertStringContains('berhasil digunakan', $result['message']);
        
        // Check ticket is marked as used
        $ticket->refresh();
        $this->assertEquals('used', $ticket->status);
        $this->assertNotNull($ticket->used_at);
        $this->assertEquals($this->staff->id, $ticket->validated_by);
    }

    /** @test */
    public function authenticity_check_works_correctly()
    {
        // Create authentic ticket
        $ticket = Ticket::factory()->create([
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'status' => 'active',
            'boarding_pass_id' => 'BP-UNX-241215-TEST05'
        ]);
        
        $validationService = new TicketValidationService();
        
        // Check authenticity
        $result = $validationService->checkAuthenticity($ticket->boarding_pass_id);
        
        $this->assertTrue($result['success']);
        $this->assertTrue($result['is_authentic']);
        $this->assertEquals('authentic', $result['status']);
        $this->assertStringContains('AUTHENTIC', $result['message']);
        
        // Check with fake ID
        $fakeResult = $validationService->checkAuthenticity('BP-UNX-241215-FAKE01');
        
        $this->assertFalse($fakeResult['success']);
        $this->assertFalse($fakeResult['is_authentic']);
        $this->assertEquals('not_found', $fakeResult['status']);
    }

    /** @test */
    public function staff_assignment_system_works()
    {
        $validationService = new TicketValidationService();
        
        // Get assigned organizers
        $assignedOrganizers = $validationService->getStaffAssignedOrganizers($this->staff);
        
        $this->assertCount(1, $assignedOrganizers);
        $this->assertEquals($this->organizer->id, $assignedOrganizers[0]['id']);
        
        // Get validatable events
        $validatableEvents = $validationService->getStaffValidatableEvents($this->staff);
        
        $this->assertCount(1, $validatableEvents);
        $this->assertEquals($this->event->id, $validatableEvents[0]['id']);
    }
}

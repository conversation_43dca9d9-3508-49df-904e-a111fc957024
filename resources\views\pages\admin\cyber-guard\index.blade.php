@extends('layouts.admin')

@section('title', 'CyberGuard Security Dashboard')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
/* CyberGuard Dashboard Styles */
.cyber-guard-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 24px;
}

.cyber-guard-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cyber-guard-title {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0 0 8px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.cyber-guard-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 0;
}

.cyber-guard-brand {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.cyber-guard-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.threat-level-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.threat-level-low {
    background: rgba(16, 185, 129, 0.2);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.threat-level-medium {
    background: rgba(245, 158, 11, 0.2);
    color: #F59E0B;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.threat-level-high {
    background: rgba(239, 68, 68, 0.2);
    color: #EF4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.threat-level-critical {
    background: rgba(124, 45, 18, 0.2);
    color: #7C2D12;
    border: 1px solid rgba(124, 45, 18, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    margin-bottom: 16px;
}

.stat-icon.events { background: linear-gradient(45deg, #667eea, #764ba2); }
.stat-icon.threats { background: linear-gradient(45deg, #f093fb, #f5576c); }
.stat-icon.blocked { background: linear-gradient(45deg, #4facfe, #00f2fe); }
.stat-icon.protected { background: linear-gradient(45deg, #43e97b, #38f9d7); }

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: white;
    margin-bottom: 4px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
}

.security-overview {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.security-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.security-card-title {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.security-item:last-child {
    border-bottom: none;
}

.security-item-name {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: capitalize;
}

.security-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.security-status.protected {
    background: rgba(16, 185, 129, 0.2);
    color: #10B981;
}

.security-status.partial {
    background: rgba(245, 158, 11, 0.2);
    color: #F59E0B;
}

.security-status.vulnerable {
    background: rgba(239, 68, 68, 0.2);
    color: #EF4444;
}

.recent-logs {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.log-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.log-item:last-child {
    margin-bottom: 0;
}

.log-severity {
    width: 8px;
    height: 40px;
    border-radius: 4px;
}

.log-severity.low { background: #10B981; }
.log-severity.medium { background: #F59E0B; }
.log-severity.high { background: #EF4444; }
.log-severity.critical { background: #7C2D12; }

.log-content {
    flex: 1;
}

.log-type {
    font-weight: 600;
    color: white;
    font-size: 14px;
    margin-bottom: 4px;
}

.log-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    line-height: 1.4;
}

.log-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    white-space: nowrap;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.btn-cyber {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-cyber:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-cyber.secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cyber.danger {
    background: linear-gradient(45deg, #f093fb, #f5576c);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .cyber-guard-container {
        padding: 16px;
    }

    .security-overview {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>
@endpush

@section('content')
<div class="cyber-guard-container">
    <!-- Header -->
    <div class="cyber-guard-header">
        <div class="cyber-guard-brand">
            <div class="cyber-guard-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div>
                <h1 class="cyber-guard-title">CyberGuard Security Center</h1>
                <p class="cyber-guard-subtitle">by kangpcode - Advanced Security Protection System</p>
            </div>
        </div>

        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div class="threat-level-indicator threat-level-{{ $threatLevel }}">
                <i class="fas fa-{{ $threatLevel === 'low' ? 'shield-check' : ($threatLevel === 'medium' ? 'shield-alt' : ($threatLevel === 'high' ? 'exclamation-triangle' : 'skull-crossbones')) }}"></i>
                Threat Level: {{ ucfirst($threatLevel) }}
            </div>

            <div style="display: flex; gap: 12px;">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">
                    Last Updated: {{ now()->format('H:i:s') }}
                </span>
                <button onclick="refreshDashboard()" class="btn-cyber secondary" style="padding: 8px 16px; font-size: 12px;">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon events">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-value">{{ $stats['total_events'] ?? 0 }}</div>
            <div class="stat-label">Security Events (24h)</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon threats">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-value">{{ $stats['critical_count'] ?? 0 }}</div>
            <div class="stat-label">Critical Threats</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon blocked">
                <i class="fas fa-ban"></i>
            </div>
            <div class="stat-value">{{ $stats['unresolved_count'] ?? 0 }}</div>
            <div class="stat-label">Unresolved Issues</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon protected">
                <i class="fas fa-shield-check"></i>
            </div>
            <div class="stat-value">{{ $settingsOverview['active_settings'] ?? 0 }}</div>
            <div class="stat-label">Active Protections</div>
        </div>
    </div>

    <!-- Security Overview -->
    <div class="security-overview">
        <!-- Security Status -->
        <div class="security-card">
            <h3 class="security-card-title">
                <i class="fas fa-shield-alt"></i>
                Security Modules
            </h3>

            @foreach($securityStatus as $category => $status)
                <div class="security-item">
                    <span class="security-item-name">{{ str_replace('_', ' ', $category) }}</span>
                    <span class="security-status {{ $status['status'] }}">
                        <i class="fas fa-{{ $status['status'] === 'protected' ? 'check' : ($status['status'] === 'partial' ? 'exclamation' : 'times') }}"></i>
                        {{ ucfirst($status['status']) }} ({{ $status['percentage'] }}%)
                    </span>
                </div>
            @endforeach
        </div>

        <!-- Recent Logs -->
        <div class="security-card">
            <h3 class="security-card-title">
                <i class="fas fa-history"></i>
                Recent Security Events
            </h3>

            @if($recentLogs->count() > 0)
                @foreach($recentLogs->take(5) as $log)
                    <div class="log-item">
                        <div class="log-severity {{ $log->severity }}"></div>
                        <div class="log-content">
                            <div class="log-type">{{ str_replace('_', ' ', ucfirst($log->type)) }}</div>
                            <div class="log-description">{{ Str::limit($log->description, 60) }}</div>
                        </div>
                        <div class="log-time">{{ $log->detected_at->diffForHumans() }}</div>
                    </div>
                @endforeach
            @else
                <div style="text-align: center; padding: 40px 20px; color: rgba(255, 255, 255, 0.6);">
                    <i class="fas fa-shield-check" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                    <p>No security events detected</p>
                    <p style="font-size: 14px;">Your system is secure</p>
                </div>
            @endif
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ route('admin.cyber-guard.settings') }}" class="btn-cyber">
            <i class="fas fa-cog"></i>
            Security Settings
        </a>

        <a href="{{ route('admin.cyber-guard.logs') }}" class="btn-cyber">
            <i class="fas fa-list"></i>
            View All Logs
        </a>

        <button onclick="runSecurityScan()" class="btn-cyber">
            <i class="fas fa-search"></i>
            Run Security Scan
        </button>

        <a href="{{ route('admin.cyber-guard.blocked-ips') }}" class="btn-cyber secondary">
            <i class="fas fa-ban"></i>
            Blocked IPs
        </a>

        <a href="{{ route('admin.cyber-guard.firewall-rules') }}" class="btn-cyber secondary">
            <i class="fas fa-fire"></i>
            Firewall Rules
        </a>

        <button onclick="exportSecurityReport()" class="btn-cyber danger">
            <i class="fas fa-download"></i>
            Export Report
        </button>
    </div>
</div>
@endsection

@push('scripts')
<script>
// CyberGuard Dashboard JavaScript
function refreshDashboard() {
    location.reload();
}

function runSecurityScan() {
    const button = event.target;
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
    button.disabled = true;

    fetch('{{ route("admin.cyber-guard.security-scan") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showCyberToast('Security scan completed successfully!', 'success');

            // Show scan results
            const results = data.results;
            let message = `Security Score: ${results.security_score}%\n`;
            message += `Vulnerabilities Found: ${results.vulnerabilities_found}\n`;

            if (results.recommendations.length > 0) {
                message += '\nRecommendations:\n';
                results.recommendations.forEach(rec => {
                    message += `• ${rec}\n`;
                });
            }

            alert(message);
        } else {
            showCyberToast('Security scan failed: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showCyberToast('Failed to run security scan', 'error');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function showCyberToast(message, type = 'info') {
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'linear-gradient(45deg, #43e97b, #38f9d7)' :
                   type === 'error' ? 'linear-gradient(45deg, #f093fb, #f5576c)' :
                   'linear-gradient(45deg, #667eea, #764ba2)';

    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 16px 24px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        max-width: 400px;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Slide in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

// Auto-refresh dashboard every 30 seconds
setInterval(() => {
    // You can implement partial refresh here instead of full page reload
    console.log('Auto-refreshing security data...');
}, 30000);
</script>
@endpush

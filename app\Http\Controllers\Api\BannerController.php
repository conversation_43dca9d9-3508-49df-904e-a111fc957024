<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Banner;
use Illuminate\Http\JsonResponse;

class BannerController extends Controller
{
    /**
     * Get active banners for display
     */
    public function index(): JsonResponse
    {
        $banners = Banner::displayable()
            ->select([
                'id',
                'title',
                'description',
                'subtitle',
                'image_path',
                'mobile_image_path',
                'link_url',
                'button_text',
                'button_color',
                'text_position',
                'text_color',
                'show_overlay',
                'overlay_color',
                'overlay_opacity',
                'animation_settings'
            ])
            ->get()
            ->map(function ($banner) {
                return [
                    'id' => $banner->id,
                    'title' => $banner->title,
                    'description' => $banner->description,
                    'subtitle' => $banner->subtitle,
                    'image_url' => $banner->image_url,
                    'mobile_image_url' => $banner->mobile_image_url,
                    'link_url' => $banner->link_url,
                    'button_text' => $banner->button_text,
                    'button_color_class' => $banner->button_color_class,
                    'text_alignment_class' => $banner->text_alignment_class,
                    'text_color_class' => $banner->text_color_class,
                    'overlay_style' => $banner->overlay_style,
                    'animation_settings' => $banner->animation_settings ?? [
                        'entrance' => 'fadeIn',
                        'duration' => 1000,
                        'delay' => 0
                    ]
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $banners
        ]);
    }

    /**
     * Get single banner by ID
     */
    public function show(Banner $banner): JsonResponse
    {
        if (!$banner->isDisplayable()) {
            return response()->json([
                'success' => false,
                'message' => 'Banner not available'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $banner->id,
                'title' => $banner->title,
                'description' => $banner->description,
                'subtitle' => $banner->subtitle,
                'image_url' => $banner->image_url,
                'mobile_image_url' => $banner->mobile_image_url,
                'link_url' => $banner->link_url,
                'button_text' => $banner->button_text,
                'button_color_class' => $banner->button_color_class,
                'text_alignment_class' => $banner->text_alignment_class,
                'text_color_class' => $banner->text_color_class,
                'overlay_style' => $banner->overlay_style,
                'animation_settings' => $banner->animation_settings ?? [
                    'entrance' => 'fadeIn',
                    'duration' => 1000,
                    'delay' => 0
                ]
            ]
        ]);
    }
}

@php
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#1F2937';
    $secondaryColor = $config['secondary_color'] ?? '#9CA3AF';
    $backgroundColor = $config['background_color'] ?? '#ffffff';
    $textColor = $config['text_color'] ?? '#1F2937';
@endphp

<div class="ticket-container minimal-design" style="
    background: {{ $backgroundColor }};
    color: {{ $textColor }};
    font-family: 'Inter', sans-serif;
    width: 750px;
    height: 280px;
    position: relative;
    border: 1px solid {{ $secondaryColor }};
    border-radius: 4px;
    overflow: hidden;
">
    <!-- Header -->
    <div style="
        padding: 24px 24px 16px 24px;
        border-bottom: 1px solid {{ $secondaryColor }};
    ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h1 style="
                    font-size: 24px;
                    font-weight: 600;
                    margin: 0 0 4px 0;
                    color: {{ $textColor }};
                ">{{ $ticket['event']['title'] ?? 'Event Title' }}</h1>
                <p style="
                    font-size: 14px;
                    margin: 0;
                    color: {{ $secondaryColor }};
                    font-weight: 500;
                ">Electronic Ticket</p>
            </div>
            
            <div style="text-align: right;">
                <p style="
                    font-size: 12px;
                    margin: 0 0 4px 0;
                    color: {{ $secondaryColor }};
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                ">Ticket ID</p>
                <p style="
                    font-size: 16px;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                ">{{ $ticket['ticket_number'] ?? 'TIX-000000' }}</p>
            </div>
        </div>
    </div>
    
    <!-- Content -->
    <div style="
        padding: 24px;
        display: flex;
        gap: 32px;
        height: calc(100% - 120px);
    ">
        <!-- Event Information -->
        <div style="flex: 1;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 24px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Date & Time</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 500;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('M j, Y') }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: {{ $secondaryColor }};
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('g:i A') }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Location</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 500;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['event']['venue_name'] ?? 'TBA' }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: {{ $secondaryColor }};
                    ">{{ $ticket['event']['city'] ?? 'Location TBA' }}</p>
                </div>
            </div>
            
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
            ">
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Attendee</h3>
                    <p style="
                        font-size: 16px;
                        font-weight: 500;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['attendee_name'] ?? $ticket['buyer']['name'] ?? 'Guest' }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: {{ $secondaryColor }};
                    ">{{ $ticket['attendee_email'] ?? $ticket['buyer']['email'] ?? 'N/A' }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 12px;
                        font-weight: 600;
                        color: {{ $secondaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    ">Price</h3>
                    <p style="
                        font-size: 20px;
                        font-weight: 600;
                        margin: 0;
                        color: {{ $textColor }};
                    ">Rp {{ number_format($ticket['price'] ?? 0, 0, ',', '.') }}</p>
                </div>
            </div>
        </div>
        
        <!-- QR Code Section -->
        <div style="
            width: 160px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 1px solid {{ $secondaryColor }};
            padding-left: 32px;
        ">
            <div style="
                background: white;
                padding: 12px;
                border: 1px solid {{ $secondaryColor }};
                border-radius: 4px;
                margin-bottom: 16px;
            ">
                {!! QrCode::size(120)->generate($ticket['qr_code'] ?? $ticket['ticket_number'] ?? 'INVALID') !!}
            </div>
            
            <p style="
                font-size: 12px;
                text-align: center;
                color: {{ $secondaryColor }};
                margin: 0;
                line-height: 1.4;
                font-weight: 500;
            ">Scan for entry verification</p>
        </div>
    </div>
    
    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12px 24px;
        border-top: 1px solid {{ $secondaryColor }};
        background: #F9FAFB;
    ">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <p style="
                font-size: 11px;
                color: {{ $secondaryColor }};
                margin: 0;
                font-weight: 500;
            ">Valid for single entry • Keep this ticket safe</p>
            
            <p style="
                font-size: 11px;
                color: {{ $secondaryColor }};
                margin: 0;
                font-weight: 500;
            ">Powered by Tixara</p>
        </div>
    </div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

.ticket-container {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
}

@media print {
    .ticket-container {
        width: 750px !important;
        height: 280px !important;
        margin: 0 !important;
        border: 1px solid {{ $secondaryColor }} !important;
    }
}
</style>

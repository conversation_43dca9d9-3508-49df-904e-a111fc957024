@extends('layouts.app')

@section('title', 'E-Ticket Templates Test')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">E-Ticket Templates Test</h1>
            <p class="text-gray-600 dark:text-gray-400">Test dan preview semua template E-Tiket yang tersedia</p>
        </div>

        <!-- Template Selection -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Pilih Template</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                @php
                    $templates = \App\Models\TicketTemplate::active()->orderBy('sort_order')->get();
                @endphp
                
                @foreach($templates as $template)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors template-card"
                         data-template="{{ $template->slug }}"
                         onclick="selectTemplate('{{ $template->slug }}', '{{ $template->name }}')">
                        <div class="flex items-center mb-3">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white text-xl"
                                 style="background: {{ $template->config['primary_color'] ?? '#3B82F6' }};">
                                <i class="fas fa-ticket-alt"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $template->name }}</h3>
                                @if($template->is_default)
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Default</span>
                                @endif
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $template->description }}</p>
                        
                        <div class="mt-3 flex items-center justify-between">
                            <div class="flex space-x-2">
                                <div class="w-4 h-4 rounded-full" style="background: {{ $template->config['primary_color'] ?? '#3B82F6' }};"></div>
                                <div class="w-4 h-4 rounded-full" style="background: {{ $template->config['secondary_color'] ?? '#E5E7EB' }};"></div>
                                <div class="w-4 h-4 rounded-full" style="background: {{ $template->config['background_color'] ?? '#ffffff' }};"></div>
                            </div>
                            <span class="text-xs text-gray-500">{{ $template->events()->count() }} events</span>
                        </div>
                    </div>
                @endforeach
            </div>
            
            <!-- Sample Data Form -->
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Sample Data</h3>
                
                <form id="sampleDataForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Title</label>
                        <input type="text" id="eventTitle" value="Tech Conference 2024" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Venue Name</label>
                        <input type="text" id="venueName" value="Jakarta Convention Center" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">City</label>
                        <input type="text" id="city" value="Jakarta" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Attendee Name</label>
                        <input type="text" id="attendeeName" value="John Doe" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label>
                        <input type="email" id="attendeeEmail" value="<EMAIL>" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price</label>
                        <input type="number" id="price" value="250000" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Date</label>
                        <input type="datetime-local" id="eventDate" value="{{ now()->addDays(30)->format('Y-m-d\TH:i') }}" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ticket Number</label>
                        <input type="text" id="ticketNumber" value="TIX-PREVIEW-001" 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>
                    
                    <div class="flex items-end">
                        <button type="button" onclick="generatePreview()" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                            Generate Preview
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Preview Section -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Template Preview</h2>
                <div class="flex space-x-2">
                    <button onclick="downloadPDF()" 
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-download mr-2"></i>Download PDF
                    </button>
                    <button onclick="printTicket()" 
                            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                </div>
            </div>
            
            <div id="templatePreview" class="border border-gray-200 dark:border-gray-700 rounded-lg p-8 bg-gray-50 dark:bg-gray-900 overflow-x-auto">
                <div class="text-center text-gray-500 dark:text-gray-400 py-16">
                    <i class="fas fa-ticket-alt text-6xl mb-4"></i>
                    <p class="text-lg">Pilih template dan klik "Generate Preview" untuk melihat hasil</p>
                </div>
            </div>
        </div>

        <!-- Template Statistics -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Template Statistics</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $templates->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Available Templates</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400">{{ $templates->where('is_active', true)->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Active Templates</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ \App\Models\Event::whereNotNull('ticket_template_id')->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Events Using Templates</div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let selectedTemplate = 'modern-boarding-pass';
let selectedTemplateName = 'Modern Boarding Pass';

function selectTemplate(slug, name) {
    selectedTemplate = slug;
    selectedTemplateName = name;
    
    // Update UI
    document.querySelectorAll('.template-card').forEach(card => {
        card.classList.remove('ring-2', 'ring-blue-500', 'bg-blue-50');
    });
    
    document.querySelector(`[data-template="${slug}"]`).classList.add('ring-2', 'ring-blue-500', 'bg-blue-50');
    
    // Auto generate preview
    generatePreview();
}

async function generatePreview() {
    const previewContainer = document.getElementById('templatePreview');
    previewContainer.innerHTML = '<div class="text-center py-16"><i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i><p>Generating preview...</p></div>';
    
    const sampleData = {
        event_title: document.getElementById('eventTitle').value,
        venue_name: document.getElementById('venueName').value,
        city: document.getElementById('city').value,
        attendee_name: document.getElementById('attendeeName').value,
        attendee_email: document.getElementById('attendeeEmail').value,
        price: parseInt(document.getElementById('price').value),
        event_date: document.getElementById('eventDate').value,
        ticket_number: document.getElementById('ticketNumber').value
    };
    
    try {
        const response = await fetch('/test/ticket-templates/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                template: selectedTemplate,
                sample_data: sampleData
            })
        });
        
        if (response.ok) {
            const html = await response.text();
            previewContainer.innerHTML = html;
        } else {
            throw new Error('Failed to generate preview');
        }
    } catch (error) {
        console.error('Preview error:', error);
        previewContainer.innerHTML = `
            <div class="text-center text-red-500 py-16">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <p>Failed to generate preview: ${error.message}</p>
            </div>
        `;
    }
}

async function downloadPDF() {
    const sampleData = {
        event_title: document.getElementById('eventTitle').value,
        venue_name: document.getElementById('venueName').value,
        city: document.getElementById('city').value,
        attendee_name: document.getElementById('attendeeName').value,
        attendee_email: document.getElementById('attendeeEmail').value,
        price: parseInt(document.getElementById('price').value),
        event_date: document.getElementById('eventDate').value,
        ticket_number: document.getElementById('ticketNumber').value
    };
    
    try {
        const response = await fetch('/test/ticket-templates/pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                template: selectedTemplate,
                sample_data: sampleData
            })
        });
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${selectedTemplate}-preview.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            throw new Error('Failed to generate PDF');
        }
    } catch (error) {
        console.error('PDF error:', error);
        alert('Failed to generate PDF: ' + error.message);
    }
}

function printTicket() {
    const printContent = document.getElementById('templatePreview').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Print Ticket - ${selectedTemplateName}</title>
                <style>
                    body { margin: 0; padding: 20px; }
                    @media print {
                        body { margin: 0; padding: 0; }
                        .ticket-container { margin: 0 !important; }
                    }
                </style>
            </head>
            <body>
                ${printContent}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Auto-select first template on load
document.addEventListener('DOMContentLoaded', function() {
    const firstTemplate = document.querySelector('.template-card');
    if (firstTemplate) {
        const slug = firstTemplate.dataset.template;
        const name = firstTemplate.querySelector('h3').textContent;
        selectTemplate(slug, name);
    }
});
</script>
@endpush
@endsection

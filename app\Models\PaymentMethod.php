<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'code',
        'category',
        'description',
        'icon',
        'logo',
        'fee_percentage',
        'fee_fixed',
        'min_amount',
        'max_amount',
        'config',
        'manual_config',
        'is_active',
        'is_manual',
        'sort_order',
        'instructions',
        'gateway_balance',
        'balance_last_updated',
    ];

    protected $casts = [
        'config' => 'array',
        'manual_config' => 'array',
        'is_active' => 'boolean',
        'is_manual' => 'boolean',
        'fee_percentage' => 'decimal:2',
        'fee_fixed' => 'decimal:2',
        'min_amount' => 'decimal:2',
        'max_amount' => 'decimal:2',
        'gateway_balance' => 'decimal:2',
        'balance_last_updated' => 'datetime',
    ];

    // Payment method types
    const TYPE_MANUAL = 'manual';
    const TYPE_TRIPAY = 'tripay';
    const TYPE_MIDTRANS = 'midtrans';
    const TYPE_XENDIT = 'xendit';

    // Payment categories
    const CATEGORY_BANK_TRANSFER = 'bank_transfer';
    const CATEGORY_E_WALLET = 'e_wallet';
    const CATEGORY_QRIS = 'qris';
    const CATEGORY_VIRTUAL_ACCOUNT = 'virtual_account';
    const CATEGORY_CREDIT_CARD = 'credit_card';

    /**
     * Scope for active payment methods
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for manual payment methods
     */
    public function scopeManual($query)
    {
        return $query->where('is_manual', true);
    }

    /**
     * Scope for gateway payment methods
     */
    public function scopeGateway($query)
    {
        return $query->where('is_manual', false);
    }

    /**
     * Scope by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get ordered payment methods
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Calculate fee for given amount
     */
    public function calculateFee($amount)
    {
        $percentageFee = ($amount * $this->fee_percentage) / 100;
        $totalFee = $percentageFee + $this->fee_fixed;

        return [
            'percentage_fee' => $percentageFee,
            'fixed_fee' => $this->fee_fixed,
            'total_fee' => $totalFee,
            'amount_with_fee' => $amount + $totalFee,
        ];
    }

    /**
     * Check if amount is within limits
     */
    public function isAmountValid($amount)
    {
        if ($amount < $this->min_amount) {
            return false;
        }

        if ($this->max_amount && $amount > $this->max_amount) {
            return false;
        }

        return true;
    }

    /**
     * Get formatted fee display
     */
    public function getFormattedFeeAttribute()
    {
        $parts = [];

        if ($this->fee_percentage > 0) {
            $parts[] = $this->fee_percentage . '%';
        }

        if ($this->fee_fixed > 0) {
            $parts[] = 'Rp ' . number_format($this->fee_fixed, 0, ',', '.');
        }

        if (empty($parts)) {
            return 'Gratis';
        }

        return implode(' + ', $parts);
    }

    /**
     * Get formatted amount limits
     */
    public function getFormattedLimitsAttribute()
    {
        $min = 'Rp ' . number_format($this->min_amount, 0, ',', '.');

        if ($this->max_amount) {
            $max = 'Rp ' . number_format($this->max_amount, 0, ',', '.');
            return $min . ' - ' . $max;
        }

        return 'Min. ' . $min;
    }

    /**
     * Get category label
     */
    public function getCategoryLabelAttribute()
    {
        return match($this->category) {
            self::CATEGORY_BANK_TRANSFER => 'Transfer Bank',
            self::CATEGORY_E_WALLET => 'E-Wallet',
            self::CATEGORY_QRIS => 'QRIS',
            self::CATEGORY_VIRTUAL_ACCOUNT => 'Virtual Account',
            self::CATEGORY_CREDIT_CARD => 'Kartu Kredit',
            default => ucfirst(str_replace('_', ' ', $this->category)),
        };
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute()
    {
        return match($this->type) {
            self::TYPE_MANUAL => 'Manual',
            self::TYPE_TRIPAY => 'TriPay',
            self::TYPE_MIDTRANS => 'Midtrans',
            self::TYPE_XENDIT => 'Xendit',
            default => ucfirst($this->type),
        };
    }

    /**
     * Check if gateway is configured
     */
    public function isConfigured()
    {
        if ($this->is_manual) {
            return !empty($this->manual_config);
        }

        return !empty($this->config) && $this->hasRequiredConfig();
    }

    /**
     * Check if required config is present for gateway
     */
    private function hasRequiredConfig()
    {
        $config = $this->config ?? [];

        switch ($this->type) {
            case self::TYPE_TRIPAY:
                return isset($config['api_key']) && isset($config['private_key']) && isset($config['merchant_code']);

            case self::TYPE_MIDTRANS:
                return isset($config['server_key']) && isset($config['client_key']);

            case self::TYPE_XENDIT:
                return isset($config['secret_key']);

            default:
                return true;
        }
    }

    /**
     * Get manual payment account info
     */
    public function getAccountInfo()
    {
        if (!$this->is_manual || empty($this->manual_config)) {
            return null;
        }

        return $this->manual_config;
    }

    /**
     * Get payment instructions
     */
    public function getInstructions()
    {
        return $this->instructions;
    }

    /**
     * Get icon HTML
     */
    public function getIconHtml()
    {
        if ($this->logo) {
            return '<img src="' . asset($this->logo) . '" alt="' . $this->name . '" class="payment-logo">';
        }

        if ($this->icon) {
            return '<i class="' . $this->icon . '"></i>';
        }

        return '<i class="fas fa-credit-card"></i>';
    }

    /**
     * Update gateway balance
     */
    public function updateGatewayBalance($balance)
    {
        $this->update([
            'gateway_balance' => $balance,
            'balance_last_updated' => now(),
        ]);
    }

    /**
     * Get formatted gateway balance
     */
    public function getFormattedGatewayBalanceAttribute()
    {
        if ($this->is_manual) {
            return 'N/A';
        }

        return 'Rp ' . number_format($this->gateway_balance, 0, ',', '.');
    }

    /**
     * Check if balance is low
     */
    public function isBalanceLow($threshold = 100000)
    {
        if ($this->is_manual) {
            return false;
        }

        return $this->gateway_balance < $threshold;
    }

    /**
     * Get balance status
     */
    public function getBalanceStatusAttribute()
    {
        if ($this->is_manual) {
            return 'manual';
        }

        if ($this->gateway_balance >= 1000000) {
            return 'high';
        } elseif ($this->gateway_balance >= 100000) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get balance status color
     */
    public function getBalanceStatusColorAttribute()
    {
        return match($this->balance_status) {
            'high' => '#10B981',
            'medium' => '#F59E0B',
            'low' => '#EF4444',
            default => '#6B7280',
        };
    }

    /**
     * Sync all gateway balances
     */
    public static function syncAllGatewayBalances()
    {
        $gateways = static::gateway()->active()->get();
        $results = [];

        foreach ($gateways as $gateway) {
            try {
                $balance = $gateway->fetchGatewayBalance();
                $gateway->updateGatewayBalance($balance);
                $results[$gateway->code] = ['success' => true, 'balance' => $balance];
            } catch (\Exception $e) {
                $results[$gateway->code] = ['success' => false, 'error' => $e->getMessage()];
            }
        }

        return $results;
    }

    /**
     * Fetch balance from gateway API
     */
    public function fetchGatewayBalance()
    {
        if ($this->is_manual || !$this->isConfigured()) {
            return 0;
        }

        switch ($this->type) {
            case self::TYPE_TRIPAY:
                return $this->fetchTripayBalance();
            case self::TYPE_MIDTRANS:
                return $this->fetchMidtransBalance();
            case self::TYPE_XENDIT:
                return $this->fetchXenditBalance();
            default:
                return 0;
        }
    }

    /**
     * Fetch TriPay balance
     */
    private function fetchTripayBalance()
    {
        // Implement TriPay balance API call
        // This is a placeholder - implement actual API call
        return rand(50000, 5000000); // Mock data
    }

    /**
     * Fetch Midtrans balance
     */
    private function fetchMidtransBalance()
    {
        // Implement Midtrans balance API call
        // This is a placeholder - implement actual API call
        return rand(50000, 5000000); // Mock data
    }

    /**
     * Fetch Xendit balance
     */
    private function fetchXenditBalance()
    {
        // Implement Xendit balance API call
        // This is a placeholder - implement actual API call
        return rand(50000, 5000000); // Mock data
    }

    /**
     * Get gateway balance statistics
     */
    public static function getBalanceStatistics()
    {
        $gateways = static::gateway()->active()->get();

        return [
            'total_balance' => $gateways->sum('gateway_balance'),
            'by_gateway' => $gateways->mapWithKeys(function($gateway) {
                return [$gateway->name => $gateway->gateway_balance];
            }),
            'low_balance_count' => $gateways->filter(function($gateway) {
                return $gateway->isBalanceLow();
            })->count(),
            'last_updated' => $gateways->max('balance_last_updated'),
        ];
    }
}

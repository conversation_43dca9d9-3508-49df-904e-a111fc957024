<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\User;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display organizer's notifications
     */
    public function index(Request $request)
    {
        $query = Notification::where('user_id', Auth::id());

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by read status
        if ($request->filled('status')) {
            if ($request->status === 'unread') {
                $query->whereNull('read_at');
            } elseif ($request->status === 'read') {
                $query->whereNotNull('read_at');
            }
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $notifications = $query->latest()->paginate(20);

        // Statistics
        $stats = [
            'total_notifications' => Notification::where('user_id', Auth::id())->count(),
            'unread_notifications' => Notification::where('user_id', Auth::id())->whereNull('read_at')->count(),
            'today_notifications' => Notification::where('user_id', Auth::id())->whereDate('created_at', today())->count(),
            'this_week_notifications' => Notification::where('user_id', Auth::id())->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
        ];

        return view('pages.organizer.notifications.index', compact('notifications', 'stats'));
    }

    /**
     * Show create notification form
     */
    public function create()
    {
        // Get organizer's event attendees for targeting
        $events = Event::where('organizer_id', Auth::id())
                      ->with(['orders.user'])
                      ->get();

        $attendees = collect();
        foreach ($events as $event) {
            foreach ($event->orders as $order) {
                if ($order->user && !$attendees->contains('id', $order->user->id)) {
                    $attendees->push($order->user);
                }
            }
        }

        return view('pages.organizer.notifications.create', compact('events', 'attendees'));
    }

    /**
     * Store new notification
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|in:info,success,warning,error,event_update,promotion',
            'target_type' => 'required|in:all_attendees,event_attendees,specific_users',
            'event_id' => 'required_if:target_type,event_attendees|exists:events,id',
            'user_ids' => 'required_if:target_type,specific_users|array',
            'user_ids.*' => 'exists:users,id',
            'send_email' => 'boolean',
            'send_push' => 'boolean',
        ]);

        // Validate event ownership
        if ($request->event_id) {
            $event = Event::where('id', $request->event_id)
                         ->where('organizer_id', Auth::id())
                         ->first();
            if (!$event) {
                return back()->withErrors(['event_id' => 'Invalid event selected.']);
            }
        }

        // Determine target users
        $targetUsers = collect();

        switch ($request->target_type) {
            case 'all_attendees':
                // Get all users who have attended organizer's events
                $events = Event::where('organizer_id', Auth::id())->get();
                foreach ($events as $event) {
                    foreach ($event->orders as $order) {
                        if ($order->user && !$targetUsers->contains('id', $order->user->id)) {
                            $targetUsers->push($order->user);
                        }
                    }
                }
                break;

            case 'event_attendees':
                // Get users who attended specific event
                $event = Event::find($request->event_id);
                foreach ($event->orders as $order) {
                    if ($order->user && !$targetUsers->contains('id', $order->user->id)) {
                        $targetUsers->push($order->user);
                    }
                }
                break;

            case 'specific_users':
                // Get specific users
                $targetUsers = User::whereIn('id', $request->user_ids)->get();
                break;
        }

        // Create notifications for each target user
        $notificationCount = 0;
        foreach ($targetUsers as $user) {
            $notification = Notification::create([
                'user_id' => $user->id,
                'title' => $request->title,
                'message' => $request->message,
                'type' => $request->type,
                'data' => [
                    'sender_id' => Auth::id(),
                    'sender_name' => Auth::user()->name,
                    'event_id' => $request->event_id,
                    'send_email' => $request->boolean('send_email'),
                    'send_push' => $request->boolean('send_push'),
                ]
            ]);

            // Send email if requested
            if ($request->boolean('send_email') && $user->email_notifications) {
                // TODO: Implement email sending
                // Mail::to($user)->send(new NotificationMail($notification));
            }

            // Send push notification if requested
            if ($request->boolean('send_push') && $user->push_notifications) {
                // TODO: Implement push notification
                // PushNotificationService::send($user, $notification);
            }

            $notificationCount++;
        }

        return redirect()->route('organizer.notifications.index')
                        ->with('success', "Notification sent to {$notificationCount} users successfully!");
    }

    /**
     * Show notification details
     */
    public function show(Notification $notification)
    {
        // Ensure notification belongs to organizer
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this notification.');
        }

        // Mark as read
        if (!$notification->read_at) {
            $notification->update(['read_at' => now()]);
        }

        return view('pages.organizer.notifications.show', compact('notification'));
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Notification $notification)
    {
        // Ensure notification belongs to organizer
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this notification.');
        }

        $notification->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read.'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead()
    {
        $count = Notification::where('user_id', Auth::id())
                            ->whereNull('read_at')
                            ->update(['read_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => "Marked {$count} notifications as read.",
            'count' => $count
        ]);
    }

    /**
     * Delete notification
     */
    public function destroy(Notification $notification)
    {
        // Ensure notification belongs to organizer
        if ($notification->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this notification.');
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully.'
        ]);
    }

    /**
     * Bulk delete notifications
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'exists:notifications,id'
        ]);

        $count = Notification::whereIn('id', $request->notification_ids)
                            ->where('user_id', Auth::id())
                            ->delete();

        return response()->json([
            'success' => true,
            'message' => "Deleted {$count} notifications successfully.",
            'count' => $count
        ]);
    }

    /**
     * Get unread notification count
     */
    public function getUnreadCount()
    {
        $count = Notification::where('user_id', Auth::id())
                            ->whereNull('read_at')
                            ->count();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Get recent notifications for dropdown
     */
    public function getRecent()
    {
        $notifications = Notification::where('user_id', Auth::id())
                                   ->latest()
                                   ->take(10)
                                   ->get()
                                   ->map(function($notification) {
                                       return [
                                           'id' => $notification->id,
                                           'title' => $notification->title,
                                           'message' => Str::limit($notification->message, 100),
                                           'type' => $notification->type,
                                           'read_at' => $notification->read_at,
                                           'created_at' => $notification->created_at->diffForHumans(),
                                           'url' => route('organizer.notifications.show', $notification)
                                       ];
                                   });

        return response()->json([
            'success' => true,
            'notifications' => $notifications,
            'unread_count' => Notification::where('user_id', Auth::id())->whereNull('read_at')->count()
        ]);
    }

    /**
     * Send test notification
     */
    public function sendTest(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|in:info,success,warning,error'
        ]);

        $notification = Notification::create([
            'user_id' => Auth::id(),
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'data' => [
                'is_test' => true,
                'sender_id' => Auth::id(),
                'sender_name' => Auth::user()->name,
            ]
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent successfully!',
            'notification' => $notification
        ]);
    }
}

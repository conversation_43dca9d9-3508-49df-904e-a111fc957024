@extends('layouts.main')

@section('title', 'Payment - ' . $order->order_number)

@push('styles')
<style>
/* CSS Variables for Theme Support */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --background-primary: #ffffff;
    --background-secondary: #f8fafc;
    --background-tertiary: #f1f5f9;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.8);
    --glass-border: rgba(0, 0, 0, 0.1);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #60a5fa;
    --primary-dark: #3b82f6;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    --background-primary: #1f2937;
    --background-secondary: #111827;
    --background-tertiary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --glass-bg: rgba(31, 41, 55, 0.8);
    --glass-border: rgba(255, 255, 255, 0.1);
}

/* Base Styles */
.payment-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    color: var(--text-primary);
    transition: all 0.3s ease;
    position: relative;
}

.payment-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* Header Styles */
.payment-header {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    position: relative;
    z-index: 1;
}

.payment-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--primary-color));
    border-radius: 1.5rem 1.5rem 0 0;
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Timer Styles */
.payment-timer {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 2px solid #f59e0b;
    border-radius: 1rem;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.payment-timer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(245, 158, 11, 0.2), transparent);
    animation: timerPulse 2s infinite;
}

@keyframes timerPulse {
    0% { left: -100%; }
    100% { left: 100%; }
}

.countdown-display {
    font-size: 1.5rem;
    font-weight: 700;
    color: #d97706;
    font-family: 'Courier New', monospace;
}

/* Payment Cards */
.payment-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.payment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px var(--shadow-color);
}

/* Payment Method Cards */
.payment-method-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1rem;
}

.payment-method-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.5s;
}

.payment-method-card:hover::before {
    left: 100%;
}

.payment-method-card.selected {
    border-color: var(--primary-color);
    background: rgba(59, 130, 246, 0.05);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.payment-method-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

/* Gateway Badge */
.gateway-badge {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.gateway-badge.xendit {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.gateway-badge.midtrans {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.gateway-badge.tripay {
    background: rgba(147, 51, 234, 0.1);
    color: #9333ea;
    border: 1px solid rgba(147, 51, 234, 0.2);
}

.gateway-badge.manual {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

/* Input Styles */
.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    font-size: 1rem;
    background: var(--background-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    width: 100%;
    font-size: 1.125rem;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Summary Card */
.summary-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow: 0 20px 40px var(--shadow-color);
    position: sticky;
    top: 2rem;
    z-index: 1;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
    font-weight: 700;
    font-size: 1.125rem;
    color: var(--primary-color);
}

/* Security Badge */
.security-badge {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    margin-top: 1.5rem;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .payment-container {
        padding: 1rem;
    }

    .payment-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .summary-card {
        position: static;
        margin-top: 2rem;
    }

    .payment-method-card {
        padding: 1rem;
    }

    .countdown-display {
        font-size: 1.25rem;
    }
}

/* Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--background-tertiary);
    border-radius: 0.25rem;
    overflow: hidden;
    margin-bottom: 2rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 0.25rem;
    transition: width 0.3s ease;
}
</style>
@endpush

@section('content')
<div class="payment-container" data-theme="light" id="paymentContainer">
    <div class="container mx-auto px-4 py-8">
        <!-- Progress Bar -->
        <div class="progress-bar">
            <div class="progress-fill" style="width: 75%"></div>
        </div>

        <!-- Header -->
        <div class="payment-header fade-in">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Complete Payment</h1>
                    <p class="text-lg text-gray-600">Order #{{ $order->order_number }}</p>
                </div>
                <div class="flex gap-3">
                    <button id="themeToggle" class="btn-primary">
                        <i data-lucide="sun" class="w-4 h-4 mr-2"></i>
                        Theme
                    </button>
                </div>
            </div>
        </div>

        <!-- Payment Timer -->
        <div class="payment-timer fade-in" style="animation-delay: 0.1s;">
            <div class="flex items-center justify-center gap-2">
                <i data-lucide="clock" class="w-5 h-5 text-orange-600"></i>
                <span class="text-orange-700 font-semibold">Complete payment within:</span>
                <div class="countdown-display" id="countdown">23:59:45</div>
            </div>
        </div>

        <form method="POST" action="{{ route('orders.process-payment', $order->id ?? 1) }}"
              x-data="modernPaymentForm()"
              @submit="handleSubmit"
              novalidate>
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Payment Form -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Order Summary -->
                    <div class="payment-card fade-in" style="animation-delay: 0.2s;">
                        <h2 class="text-xl font-bold mb-4">Order Details</h2>
                        <div class="flex items-start gap-4">
                            <img src="{{ $order->event->poster_url ?? 'https://via.placeholder.com/80x80' }}"
                                 alt="{{ $order->event->title }}"
                                 class="w-20 h-20 object-cover rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-semibold text-lg mb-2">{{ $order->event->title }}</h3>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i data-lucide="calendar" class="w-4 h-4 mr-2"></i>
                                        {{ $order->event->start_date->format('d M Y, H:i') }} WIB
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="map-pin" class="w-4 h-4 mr-2"></i>
                                        {{ $order->event->venue_name }}, {{ $order->event->city }}
                                    </div>
                                    <div class="flex items-center">
                                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                                        {{ $order->customer_name }} ({{ $order->quantity }} tickets)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="payment-card fade-in" style="animation-delay: 0.3s;">
                        <h2 class="text-xl font-bold mb-4">Payment Method</h2>

                        <!-- Loading Payment Methods -->
                        <div x-show="loadingPaymentMethods" class="text-center py-8">
                            <div class="loading"></div>
                            <p class="text-sm text-gray-600 mt-2">Loading payment methods...</p>
                        </div>

                        <!-- Payment Methods List -->
                        <div x-show="!loadingPaymentMethods" class="space-y-3">
                            <template x-for="method in availablePaymentMethods" :key="method.code">
                                <label class="payment-method-card"
                                       :class="selectedPaymentMethod === method.code ? 'selected' : ''">
                                    <input type="radio" name="payment_method"
                                           :value="method.code"
                                           x-model="selectedPaymentMethod"
                                           :disabled="!method.is_active"
                                           class="sr-only">

                                    <div class="gateway-badge"
                                         :class="method.gateway_name.toLowerCase()"
                                         x-text="method.gateway_name"></div>

                                    <div class="flex items-center">
                                        <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4"
                                             :class="method.icon_bg_class">
                                            <i :class="method.icon_class" class="text-xl"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold" x-text="method.name"></h4>
                                            <p class="text-sm text-gray-600" x-text="method.description"></p>
                                            <div x-show="method.fee > 0" class="text-xs text-orange-600 mt-1">
                                                Fee: <span x-text="formatCurrency(method.fee)"></span>
                                            </div>
                                        </div>
                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full flex items-center justify-center">
                                            <div class="w-2 h-2 bg-blue-600 rounded-full"
                                                 x-show="selectedPaymentMethod === method.code"></div>
                                        </div>
                                    </div>
                                </label>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Payment Summary -->
                <div class="lg:col-span-1">
                    <div class="summary-card fade-in" style="animation-delay: 0.4s;">
                        <h3 class="text-xl font-bold mb-4">Payment Summary</h3>

                        <div class="summary-item">
                            <span>Subtotal</span>
                            <span>{{ 'Rp ' . number_format($order->subtotal ?? 0, 0, ',', '.') }}</span>
                        </div>

                        <div class="summary-item">
                            <span>Admin Fee</span>
                            <span>{{ 'Rp ' . number_format($order->admin_fee ?? 0, 0, ',', '.') }}</span>
                        </div>

                        <div class="summary-item" x-show="paymentFee > 0">
                            <span>Payment Fee</span>
                            <span x-text="formatCurrency(paymentFee)"></span>
                        </div>

                        <div class="summary-item">
                            <span>Total</span>
                            <span>{{ 'Rp ' . number_format($order->total_amount ?? 0, 0, ',', '.') }}</span>
                        </div>

                        <button type="submit"
                                class="btn-primary mt-6"
                                :disabled="!selectedPaymentMethod || processing">
                            <span x-show="!processing">
                                <i data-lucide="credit-card" class="w-4 h-4 mr-2"></i>
                                Pay Now
                            </span>
                            <span x-show="processing">
                                <i data-lucide="loader" class="w-4 h-4 mr-2 animate-spin"></i>
                                Processing...
                            </span>
                        </button>

                        <div class="security-badge">
                            <div class="flex items-center justify-center text-sm text-green-700">
                                <i data-lucide="shield-check" class="w-4 h-4 mr-2"></i>
                                Secured with SSL encryption
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
<script>
function modernPaymentForm() {
    return {
        // Basic properties
        selectedPaymentMethod: '',
        processing: false,

        // Payment methods
        availablePaymentMethods: [],
        loadingPaymentMethods: true,
        paymentFee: 0,

        // Timer
        timeLeft: 24 * 60 * 60, // 24 hours in seconds
        timerInterval: null,

        // Theme
        currentTheme: 'light',

        // Initialize
        init() {
            this.loadPaymentMethods();
            this.setupThemeToggle();
            this.startCountdown();
            this.setupFormValidation();
        },

        // Load payment methods from API
        async loadPaymentMethods() {
            try {
                const response = await fetch('/api/payment-methods/available', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.availablePaymentMethods = data.payment_methods || [];

                    // Set default payment method
                    if (this.availablePaymentMethods.length > 0) {
                        this.selectedPaymentMethod = this.availablePaymentMethods[0].code;
                        this.calculatePaymentFee();
                    }
                } else {
                    console.error('Failed to load payment methods');
                    this.availablePaymentMethods = this.getDefaultPaymentMethods();
                }
            } catch (error) {
                console.error('Error loading payment methods:', error);
                this.availablePaymentMethods = this.getDefaultPaymentMethods();
            } finally {
                this.loadingPaymentMethods = false;
            }
        },

        // Get default payment methods as fallback
        getDefaultPaymentMethods() {
            return [
                {
                    code: 'bank_transfer',
                    name: 'Bank Transfer',
                    description: 'Transfer to bank account',
                    icon_class: 'fas fa-university',
                    icon_bg_class: 'bg-blue-100 text-blue-600',
                    gateway_name: 'Manual',
                    fee: 0,
                    is_active: true
                },
                {
                    code: 'gopay',
                    name: 'GoPay',
                    description: 'Pay with GoPay e-wallet',
                    icon_class: 'fas fa-mobile-alt',
                    icon_bg_class: 'bg-green-100 text-green-600',
                    gateway_name: 'Xendit',
                    fee: 2500,
                    is_active: true
                },
                {
                    code: 'ovo',
                    name: 'OVO',
                    description: 'Pay with OVO e-wallet',
                    icon_class: 'fas fa-wallet',
                    icon_bg_class: 'bg-purple-100 text-purple-600',
                    gateway_name: 'Xendit',
                    fee: 2500,
                    is_active: true
                }
            ];
        },

        // Calculate payment fee when method changes
        async calculatePaymentFee() {
            if (!this.selectedPaymentMethod) {
                this.paymentFee = 0;
                return;
            }

            try {
                const response = await fetch('/api/payment-methods/calculate-fee', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        payment_method_code: this.selectedPaymentMethod,
                        amount: {{ $order->total_amount ?? 0 }}
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    this.paymentFee = data.fee || 0;
                } else {
                    // Fallback to method fee
                    const method = this.availablePaymentMethods.find(m => m.code === this.selectedPaymentMethod);
                    this.paymentFee = method ? method.fee : 0;
                }
            } catch (error) {
                console.error('Error calculating fee:', error);
                this.paymentFee = 0;
            }
        },

        // Countdown timer
        startCountdown() {
            this.updateCountdownDisplay();

            this.timerInterval = setInterval(() => {
                this.timeLeft--;
                this.updateCountdownDisplay();

                if (this.timeLeft <= 0) {
                    clearInterval(this.timerInterval);
                    this.handleTimeout();
                }
            }, 1000);
        },

        updateCountdownDisplay() {
            const hours = Math.floor(this.timeLeft / 3600);
            const minutes = Math.floor((this.timeLeft % 3600) / 60);
            const seconds = this.timeLeft % 60;

            const display = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                countdownElement.textContent = display;

                // Change color when time is running out
                if (this.timeLeft < 300) { // Less than 5 minutes
                    countdownElement.style.color = '#dc2626';
                } else if (this.timeLeft < 900) { // Less than 15 minutes
                    countdownElement.style.color = '#f59e0b';
                }
            }
        },

        handleTimeout() {
            this.showNotification('Payment time expired. Please create a new order.', 'error');
            setTimeout(() => {
                window.location.href = '/';
            }, 3000);
        },

        // Theme toggle
        setupThemeToggle() {
            document.getElementById('themeToggle').addEventListener('click', () => {
                this.toggleTheme();
            });
        },

        toggleTheme() {
            this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
            document.getElementById('paymentContainer').setAttribute('data-theme', this.currentTheme);

            const icon = document.querySelector('#themeToggle i');
            icon.setAttribute('data-lucide', this.currentTheme === 'light' ? 'sun' : 'moon');

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            this.showNotification(`Switched to ${this.currentTheme} theme`, 'success');
        },

        // Form validation
        setupFormValidation() {
            // Real-time validation for payment method selection
            this.$watch('selectedPaymentMethod', () => {
                this.calculatePaymentFee();
            });
        },

        // Form submission
        async handleSubmit(event) {
            event.preventDefault();

            if (this.processing) return;

            if (!this.selectedPaymentMethod) {
                this.showNotification('Please select a payment method', 'error');
                return;
            }

            this.processing = true;
            this.updateProgressBar(90);

            try {
                // Submit form
                const form = event.target;
                const formData = new FormData(form);

                const response = await fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    this.updateProgressBar(100);
                    this.showNotification('Payment initiated successfully! Redirecting...', 'success');

                    // Clear timer
                    if (this.timerInterval) {
                        clearInterval(this.timerInterval);
                    }

                    // Redirect after success
                    const result = await response.json();
                    if (result.redirect_url) {
                        window.location.href = result.redirect_url;
                    } else {
                        setTimeout(() => {
                            window.location.href = '/orders/' + '{{ $order->id }}' + '/status';
                        }, 2000);
                    }
                } else {
                    const errorData = await response.json();
                    this.showNotification(errorData.message || 'Payment failed', 'error');
                }
            } catch (error) {
                console.error('Payment error:', error);
                this.showNotification('Network error. Please try again.', 'error');
            } finally {
                this.processing = false;
            }
        },

        // Utility functions
        formatCurrency(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },

        updateProgressBar(percentage) {
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = percentage + '%';
            }
        },

        showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;

            notification.innerHTML = `
                <div class="flex items-center">
                    <i data-lucide="${this.getNotificationIcon(type)}" class="w-5 h-5 mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Hide and remove notification
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 3000);

            // Re-initialize Lucide icons
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        },

        getNotificationIcon(type) {
            switch (type) {
                case 'success': return 'check-circle';
                case 'error': return 'x-circle';
                case 'warning': return 'alert-triangle';
                default: return 'info';
            }
        },

        // Watch for payment method changes
        $watch: {
            selectedPaymentMethod() {
                this.calculatePaymentFee();
            }
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // ESC to go back
        if (e.key === 'Escape') {
            if (confirm('Are you sure you want to leave this page? Your payment session will be lost.')) {
                window.history.back();
            }
        }

        // Enter to submit form (if payment method selected)
        if (e.key === 'Enter' && e.ctrlKey) {
            const form = document.querySelector('form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
    });

    // Prevent accidental page refresh
    window.addEventListener('beforeunload', function(e) {
        const form = document.querySelector('form');
        if (form && !form.submitted) {
            e.preventDefault();
            e.returnValue = 'Are you sure you want to leave? Your payment session will be lost.';
        }
    });
});

// Mark form as submitted when form is actually submitted
document.addEventListener('submit', function(e) {
    e.target.submitted = true;
});
</script>
@endpush

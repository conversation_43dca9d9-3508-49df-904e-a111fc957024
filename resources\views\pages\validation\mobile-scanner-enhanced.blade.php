@extends('layouts.app')

@section('title', 'Mobile E-Tiket Scanner Enhanced')

@push('styles')
<style>
/* CSS Variables for Theme Support */
:root {
    --primary-color: #10b981;
    --primary-dark: #059669;
    --secondary-color: #3b82f6;
    --background-primary: #111827;
    --background-secondary: #1f2937;
    --background-tertiary: #374151;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #4b5563;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Light Theme */
[data-theme="light"] {
    --primary-color: #059669;
    --primary-dark: #047857;
    --secondary-color: #2563eb;
    --background-primary: #ffffff;
    --background-secondary: #f9fafb;
    --background-tertiary: #f3f4f6;
    --text-primary: #111827;
    --text-secondary: #374151;
    --text-muted: #6b7280;
    --border-color: #d1d5db;
    --success-color: #059669;
    --error-color: #dc2626;
    --warning-color: #d97706;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Base Styles */
.scanner-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 100%);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

/* Header Styles */
.scanner-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px var(--shadow-color);
}

.header-content {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.header-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.header-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    margin: 0;
}

.header-subtitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.status-indicator {
    width: 0.5rem;
    height: 0.5rem;
    background: #34d399;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    color: white;
}

/* Scanner Section */
.scanner-section {
    padding: 1.5rem;
}

.scanner-card {
    background: var(--background-secondary);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 10px 30px var(--shadow-color);
    border: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.scanner-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px var(--shadow-color);
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    height: 300px;
    background: #000;
    border-radius: 1rem;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

#scannerVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 1rem;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: white;
    border-radius: 1rem;
    transition: all 0.3s ease;
}

.scanning-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 3px solid var(--primary-color);
    border-radius: 1rem;
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
    display: none;
}

.scanning-frame.active {
    display: block;
    animation: scanPulse 2s infinite;
}

@keyframes scanPulse {
    0% {
        border-color: var(--primary-color);
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
    }
    50% {
        border-color: #34d399;
        box-shadow: 0 0 30px rgba(52, 211, 153, 0.8);
    }
    100% {
        border-color: var(--primary-color);
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
    }
}

/* Control Buttons */
.control-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.control-btn {
    padding: 0.875rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.control-btn:hover:before {
    left: 100%;
}

.btn-start {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-start:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.btn-stop {
    background: linear-gradient(135deg, var(--error-color), #dc2626);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.btn-stop:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.btn-camera {
    background: linear-gradient(135deg, var(--secondary-color), #2563eb);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-camera:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.btn-torch {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-torch:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Status Display */
.status-display {
    background: var(--background-tertiary);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    font-weight: 500;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    background: var(--background-secondary);
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 10px var(--shadow-color);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow-color);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .scanner-section {
        padding: 1rem;
    }

    .scanner-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .video-container {
        height: 250px;
    }

    .scanning-frame {
        width: 150px;
        height: 150px;
    }

    .control-buttons {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .header-title {
        font-size: 1.125rem;
    }
}

/* PWA Support */
@media (display-mode: standalone) {
    .scanner-header {
        padding-top: env(safe-area-inset-top);
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .scanning-frame.active {
        animation: none;
    }

    .control-btn:hover:not(:disabled) {
        transform: none;
    }

    .stat-card:hover {
        transform: none;
    }
}
</style>
@endpush

@section('content')
<div class="scanner-container" data-theme="dark" id="scannerContainer">
    <!-- Enhanced Header -->
    <div class="scanner-header">
        <div class="header-content">
            <div class="header-left">
                <div class="header-icon">
                    <i data-lucide="scan-line" class="w-6 h-6 text-white"></i>
                </div>
                <div>
                    <h1 class="header-title">E-Tiket Scanner</h1>
                    <div class="header-subtitle">
                        <div class="status-indicator"></div>
                        <span class="text-sm text-green-100">Staff Validation System</span>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button id="themeToggle" class="header-btn" title="Toggle Theme">
                    <i data-lucide="sun" class="w-5 h-5"></i>
                </button>
                <a href="{{ route('staff.dashboard') }}" class="header-btn" title="Back to Dashboard">
                    <i data-lucide="arrow-left" class="w-5 h-5"></i>
                </a>
            </div>
        </div>
    </div>

    <!-- Scanner Section -->
    <div class="scanner-section">
        <!-- Main Scanner Card -->
        <div class="scanner-card">
            <!-- Video Container -->
            <div class="video-container">
                <video id="scannerVideo" autoplay muted playsinline></video>
                <div id="scannerOverlay" class="scanner-overlay">
                    <div class="text-center">
                        <i data-lucide="camera" class="w-16 h-16 mb-4 mx-auto"></i>
                        <p class="text-lg font-medium mb-2">Tap Start to begin scanning</p>
                        <p class="text-sm opacity-80">Position QR code within the frame</p>
                    </div>
                </div>
                <div id="scanningFrame" class="scanning-frame"></div>
            </div>

            <!-- Control Buttons -->
            <div class="control-buttons">
                <button id="startScanner" class="control-btn btn-start">
                    <i data-lucide="play" class="w-4 h-4"></i>
                    Start Scanner
                </button>
                <button id="stopScanner" class="control-btn btn-stop" disabled>
                    <i data-lucide="square" class="w-4 h-4"></i>
                    Stop Scanner
                </button>
                <button id="switchCamera" class="control-btn btn-camera">
                    <i data-lucide="camera" class="w-4 h-4"></i>
                    Switch Camera
                </button>
                <button id="toggleTorch" class="control-btn btn-torch" disabled>
                    <i data-lucide="flashlight" class="w-4 h-4"></i>
                    Torch
                </button>
            </div>

            <!-- Status Display -->
            <div id="scannerStatus" class="status-display">
                Scanner tidak aktif
            </div>
        </div>

        <!-- Statistics -->
        <div class="scanner-card">
            <h3 class="text-lg font-semibold mb-4">
                <i data-lucide="bar-chart-3" class="w-5 h-5 inline mr-2"></i>
                Session Statistics
            </h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div id="validCount" class="stat-number">0</div>
                    <div class="stat-label">Valid Scans</div>
                </div>
                <div class="stat-card">
                    <div id="invalidCount" class="stat-number">0</div>
                    <div class="stat-label">Invalid Scans</div>
                </div>
                <div class="stat-card">
                    <div id="sessionTime" class="stat-number">00:00</div>
                    <div class="stat-label">Session Time</div>
                </div>
                <div class="stat-card">
                    <div id="accuracyRate" class="stat-number">100%</div>
                    <div class="stat-label">Accuracy</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audio feedback -->
<audio id="successSound" preload="auto">
    <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
</audio>
<audio id="errorSound" preload="auto">
    <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT" type="audio/wav">
</audio>
@endsection

@push('scripts')
<script src="https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js"></script>
<script>
class EnhancedMobileScanner {
    constructor() {
        this.html5QrCode = null;
        this.isScanning = false;
        this.currentCameraIndex = 0;
        this.availableCameras = [];
        this.currentFacingMode = 'environment';
        this.torchEnabled = false;
        this.stats = { valid: 0, invalid: 0, total: 0, startTime: null };
        this.sessionTimer = null;
        this.currentTheme = 'dark';

        this.initializeElements();
        this.bindEvents();
        this.initializeCamera();
        this.startSessionTimer();
    }

    initializeElements() {
        this.startButton = document.getElementById('startScanner');
        this.stopButton = document.getElementById('stopScanner');
        this.switchCameraButton = document.getElementById('switchCamera');
        this.torchButton = document.getElementById('toggleTorch');
        this.themeToggle = document.getElementById('themeToggle');
        this.statusElement = document.getElementById('scannerStatus');
        this.overlayElement = document.getElementById('scannerOverlay');
        this.frameElement = document.getElementById('scanningFrame');
        this.validCountElement = document.getElementById('validCount');
        this.invalidCountElement = document.getElementById('invalidCount');
        this.sessionTimeElement = document.getElementById('sessionTime');
        this.accuracyRateElement = document.getElementById('accuracyRate');
        this.container = document.getElementById('scannerContainer');
    }

    bindEvents() {
        this.startButton.addEventListener('click', () => this.startScanning());
        this.stopButton.addEventListener('click', () => this.stopScanning());
        this.switchCameraButton.addEventListener('click', () => this.switchCamera());
        this.torchButton.addEventListener('click', () => this.toggleTorch());
        this.themeToggle.addEventListener('click', () => this.toggleTheme());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F1') {
                e.preventDefault();
                this.startScanning();
            } else if (e.key === 'F2') {
                e.preventDefault();
                this.stopScanning();
            } else if (e.key === 'F3') {
                e.preventDefault();
                this.switchCamera();
            } else if (e.key === 'F4') {
                e.preventDefault();
                this.toggleTorch();
            }
        });
    }

    async initializeCamera() {
        try {
            this.updateStatus('Initializing camera...');

            // Get available cameras
            const devices = await Html5Qrcode.getCameras();
            this.availableCameras = devices;

            if (devices && devices.length > 0) {
                this.updateStatus(`Found ${devices.length} camera(s)`);
                this.switchCameraButton.disabled = devices.length <= 1;
            } else {
                this.updateStatus('No cameras found');
                this.startButton.disabled = true;
            }
        } catch (error) {
            console.error('Camera initialization error:', error);
            this.updateStatus('Camera access denied');
            this.startButton.disabled = true;
        }
    }

    async startScanning() {
        if (this.isScanning) return;

        try {
            this.updateStatus('Starting scanner...');

            this.html5QrCode = new Html5Qrcode('scannerVideo');

            const cameraId = this.availableCameras.length > 0 ?
                this.availableCameras[this.currentCameraIndex].id :
                { facingMode: this.currentFacingMode };

            const config = {
                fps: 10,
                qrbox: { width: 200, height: 200 },
                aspectRatio: 1.0,
                experimentalFeatures: {
                    useBarCodeDetectorIfSupported: true
                }
            };

            await this.html5QrCode.start(
                cameraId,
                config,
                (decodedText) => this.onScanSuccess(decodedText),
                () => {} // Ignore scan errors
            );

            this.isScanning = true;
            this.updateStatus('Scanner active - Ready to scan');
            this.updateButtons();
            this.checkTorchSupport();
            this.showScanningFrame();

        } catch (error) {
            console.error('Scanner start error:', error);
            this.updateStatus('Failed to start scanner');
            this.showError('Failed to start camera. Please check permissions.');
        }
    }

    async stopScanning() {
        if (!this.isScanning) return;

        try {
            this.updateStatus('Stopping scanner...');

            if (this.html5QrCode) {
                await this.html5QrCode.stop();
                this.html5QrCode = null;
            }

            this.isScanning = false;
            this.torchEnabled = false;
            this.updateStatus('Scanner stopped');
            this.updateButtons();
            this.hideScanningFrame();

        } catch (error) {
            console.error('Scanner stop error:', error);
            this.updateStatus('Error stopping scanner');
        }
    }

    async switchCamera() {
        if (!this.availableCameras.length || this.availableCameras.length <= 1) return;

        const wasScanning = this.isScanning;

        if (wasScanning) {
            await this.stopScanning();
        }

        this.currentCameraIndex = (this.currentCameraIndex + 1) % this.availableCameras.length;
        this.updateStatus(`Switched to camera ${this.currentCameraIndex + 1}`);

        if (wasScanning) {
            setTimeout(() => this.startScanning(), 500);
        }
    }

    async toggleTorch() {
        if (!this.isScanning || !this.html5QrCode) return;

        try {
            const track = this.html5QrCode.getRunningTrackCameraCapabilities();
            if (track.torch) {
                this.torchEnabled = !this.torchEnabled;
                await track.applyConstraints({
                    advanced: [{ torch: this.torchEnabled }]
                });

                this.updateStatus(`Torch ${this.torchEnabled ? 'enabled' : 'disabled'}`);
                this.torchButton.innerHTML = `
                    <i data-lucide="flashlight${this.torchEnabled ? '-off' : ''}" class="w-4 h-4"></i>
                    ${this.torchEnabled ? 'Torch Off' : 'Torch On'}
                `;

                // Re-initialize Lucide icons
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }
        } catch (error) {
            console.error('Torch toggle error:', error);
            this.updateStatus('Torch not supported');
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.container.setAttribute('data-theme', this.currentTheme);

        const icon = this.themeToggle.querySelector('i');
        icon.setAttribute('data-lucide', this.currentTheme === 'dark' ? 'sun' : 'moon');

        // Re-initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        this.updateStatus(`Switched to ${this.currentTheme} theme`);
    }

    checkTorchSupport() {
        if (this.html5QrCode) {
            try {
                const track = this.html5QrCode.getRunningTrackCameraCapabilities();
                this.torchButton.disabled = !track.torch;
            } catch (error) {
                this.torchButton.disabled = true;
            }
        }
    }

    onScanSuccess(decodedText) {
        console.log('QR Code detected:', decodedText);
        this.validateTicket(decodedText);
    }

    async validateTicket(ticketCode) {
        try {
            this.updateStatus('Validating ticket...');

            const response = await fetch('{{ route("tickets.validate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    identifier: ticketCode,
                    type: 'qr_code'
                })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showValidationSuccess(result);
                this.updateStats(true);
                this.playSound('success');
                this.vibrate([100, 50, 100]);
                this.updateStatus('✅ Valid ticket');
            } else {
                this.showValidationError(result.message || 'Invalid ticket');
                this.updateStats(false);
                this.playSound('error');
                this.vibrate([200]);
                this.updateStatus('❌ Invalid ticket');
            }

        } catch (error) {
            console.error('Validation error:', error);
            this.showValidationError('Network error');
            this.updateStats(false);
            this.playSound('error');
            this.updateStatus('🌐 Network error');
        }
    }

    showValidationSuccess(result) {
        // Create success notification
        this.showNotification('✅ Valid Ticket', 'success');

        // Flash green border
        this.frameElement.style.borderColor = '#10b981';
        setTimeout(() => {
            this.frameElement.style.borderColor = 'var(--primary-color)';
        }, 1000);
    }

    showValidationError(message) {
        // Create error notification
        this.showNotification('❌ ' + message, 'error');

        // Flash red border
        this.frameElement.style.borderColor = '#ef4444';
        setTimeout(() => {
            this.frameElement.style.borderColor = 'var(--primary-color)';
        }, 1000);
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-20 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translate(-50%, 0)';
        }, 100);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translate(-50%, -100px)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 3000);
    }

    updateStats(isValid) {
        if (isValid) {
            this.stats.valid++;
        } else {
            this.stats.invalid++;
        }
        this.stats.total++;

        this.validCountElement.textContent = this.stats.valid;
        this.invalidCountElement.textContent = this.stats.invalid;

        // Update accuracy rate
        const accuracy = this.stats.total > 0 ? Math.round((this.stats.valid / this.stats.total) * 100) : 100;
        this.accuracyRateElement.textContent = accuracy + '%';
    }

    startSessionTimer() {
        this.stats.startTime = Date.now();
        this.sessionTimer = setInterval(() => {
            const elapsed = Date.now() - this.stats.startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            this.sessionTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    updateButtons() {
        this.startButton.disabled = this.isScanning;
        this.stopButton.disabled = !this.isScanning;
        this.switchCameraButton.disabled = this.isScanning || this.availableCameras.length <= 1;
    }

    showScanningFrame() {
        this.overlayElement.style.display = 'none';
        this.frameElement.classList.add('active');
    }

    hideScanningFrame() {
        this.overlayElement.style.display = 'flex';
        this.frameElement.classList.remove('active');
    }

    updateStatus(message) {
        this.statusElement.textContent = message;
    }

    playSound(type) {
        try {
            const audio = document.getElementById(type + 'Sound');
            if (audio) {
                audio.currentTime = 0;
                audio.play().catch(() => {
                    // Ignore audio play errors
                });
            }
        } catch (error) {
            // Ignore audio errors
        }
    }

    vibrate(pattern) {
        if ('vibrate' in navigator) {
            navigator.vibrate(pattern);
        }
    }

    showError(message) {
        this.showNotification(message, 'error');
    }
}

// Initialize scanner when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    const scanner = new EnhancedMobileScanner();

    // Show keyboard shortcuts info
    console.log('Keyboard Shortcuts:');
    console.log('F1 - Start Scanner');
    console.log('F2 - Stop Scanner');
    console.log('F3 - Switch Camera');
    console.log('F4 - Toggle Torch');
});
</script>
@endpush

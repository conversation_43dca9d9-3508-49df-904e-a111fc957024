@extends('layouts.app')

@section('title', 'Staff E-Ticket Scanner')

@push('styles')
<style>
    .scanner-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .scan-animation {
        animation: scanPulse 2s infinite;
    }

    @keyframes scanPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
    }

    .success-animation {
        animation: successBounce 0.6s ease-out;
    }

    @keyframes successBounce {
        0% { transform: scale(0.3); opacity: 0; }
        50% { transform: scale(1.05); }
        70% { transform: scale(0.9); }
        100% { transform: scale(1); opacity: 1; }
    }

    .error-shake {
        animation: errorShake 0.5s ease-in-out;
    }

    @keyframes errorShake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    .glass-effect {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .neon-border {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
        border: 2px solid #22c55e;
    }

    .scan-line {
        position: relative;
        overflow: hidden;
    }

    .scan-line::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.4), transparent);
        animation: scanLine 2s infinite;
    }

    @keyframes scanLine {
        0% { left: -100%; }
        100% { left: 100%; }
    }
</style>
@endpush

@section('content')
<div class="min-h-screen scanner-container">
    <!-- Animated Background -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white opacity-10 rounded-full"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white opacity-5 rounded-full"></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-8">
        <!-- Header with Stats -->
        <div class="text-center mb-8">
            <div class="glass-effect rounded-2xl p-6 mb-6">
                <h1 class="text-4xl font-bold text-white mb-3">
                    <i class="fas fa-qrcode mr-3 text-green-400 scan-animation"></i>
                    Staff E-Ticket Scanner
                </h1>
                <p class="text-gray-200 text-lg mb-4">Scan tiket untuk validasi - Scan dulu, pakai kemudian</p>

                <!-- Staff Info Card -->
                <div class="inline-flex items-center bg-white/20 rounded-full px-6 py-3 backdrop-blur-sm">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user-shield text-white"></i>
                    </div>
                    <div class="text-left">
                        <div class="text-white font-semibold">{{ auth()->user()->name }}</div>
                        <div class="text-green-200 text-sm">Staff Validator</div>
                    </div>
                    <div class="ml-4 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                </div>
            </div>

            <!-- Real-time Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div class="glass-effect rounded-xl p-4">
                    <div class="text-3xl font-bold text-green-400" id="todayScans">0</div>
                    <div class="text-white text-sm">Scans Hari Ini</div>
                </div>
                <div class="glass-effect rounded-xl p-4">
                    <div class="text-3xl font-bold text-blue-400" id="validTickets">0</div>
                    <div class="text-white text-sm">Tiket Valid</div>
                </div>
                <div class="glass-effect rounded-xl p-4">
                    <div class="text-3xl font-bold text-yellow-400" id="usedTickets">0</div>
                    <div class="text-white text-sm">Tiket Digunakan</div>
                </div>
            </div>
        </div>

        <!-- Enhanced Scanner Form -->
        <div class="max-w-3xl mx-auto mb-8">
            <div class="glass-effect rounded-2xl p-8 backdrop-blur-lg">
                <form id="scanForm" method="POST" action="{{ route('tickets.staff-scan') }}">
                    @csrf

                    <!-- Scanner Input Section -->
                    <div class="mb-8">
                        <div class="text-center mb-6">
                            <h3 class="text-2xl font-bold text-white mb-2">
                                <i class="fas fa-scan-qr mr-2 text-green-400"></i>
                                Scan E-Ticket
                            </h3>
                            <p class="text-gray-200">Gunakan kamera atau ketik manual</p>
                        </div>

                        <div class="relative mb-4">
                            <div class="scan-line">
                                <input type="text"
                                       id="identifier"
                                       name="identifier"
                                       class="w-full px-6 py-4 bg-white/10 border-2 border-white/20 rounded-xl text-white placeholder-gray-300 focus:ring-4 focus:ring-green-500/50 focus:border-green-400 transition-all duration-300 text-lg font-mono backdrop-blur-sm"
                                       placeholder="TIK-YYYYMMDD-XXXXX atau BP-YYYYMMDD-XXXXX"
                                       autocomplete="off"
                                       required>
                            </div>

                            <!-- Quick Scan Button -->
                            <button type="button"
                                    id="scanButton"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-2 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl">
                                <i class="fas fa-camera mr-2"></i>
                                <span class="hidden sm:inline">Scan</span>
                            </button>
                        </div>

                        <!-- Format Helper -->
                        <div class="flex flex-wrap gap-2 justify-center mb-6">
                            <span class="bg-white/10 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                                <i class="fas fa-ticket-alt mr-1 text-blue-400"></i>
                                Ticket Number
                            </span>
                            <span class="bg-white/10 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                                <i class="fas fa-id-card mr-1 text-purple-400"></i>
                                Boarding Pass ID
                            </span>
                            <span class="bg-white/10 text-white px-3 py-1 rounded-full text-sm backdrop-blur-sm">
                                <i class="fas fa-qrcode mr-1 text-green-400"></i>
                                QR Code
                            </span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button type="button"
                                id="quickScanButton"
                                class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                            <i class="fas fa-camera-retro mr-3 text-xl"></i>
                            <div class="text-left">
                                <div class="font-bold">Quick Scan</div>
                                <div class="text-sm opacity-90">Buka kamera</div>
                            </div>
                        </button>

                        <button type="submit"
                                id="scanTicketButton"
                                class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105">
                            <span id="scanSpinner" class="hidden">
                                <i class="fas fa-spinner fa-spin mr-3 text-xl"></i>
                            </span>
                            <span id="scanButtonContent">
                                <i class="fas fa-search mr-3 text-xl"></i>
                                <div class="text-left">
                                    <div class="font-bold">Validate Ticket</div>
                                    <div class="text-sm opacity-90">Cek validitas</div>
                                </div>
                            </span>
                        </button>
                    </div>
                </form>

                <!-- Voice Commands Info -->
                <div class="mt-6 text-center">
                    <div class="inline-flex items-center bg-white/10 rounded-full px-4 py-2 backdrop-blur-sm">
                        <i class="fas fa-microphone mr-2 text-yellow-400"></i>
                        <span class="text-white text-sm">Voice: "Scan ticket" atau "Clear input"</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scan Result -->
        <div id="scanResult" class="max-w-2xl mx-auto hidden">
            <!-- Result will be populated by JavaScript -->
        </div>

        <!-- Enhanced QR Scanner Modal -->
        <div id="scannerModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="glass-effect rounded-2xl p-6 w-full max-w-lg border border-white/20">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center mb-6">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-qrcode text-white"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white">QR Scanner</h3>
                                <p class="text-gray-300 text-sm">Arahkan kamera ke QR code</p>
                            </div>
                        </div>
                        <button id="closeScannerModal" class="text-gray-300 hover:text-white transition-colors">
                            <i class="fas fa-times text-2xl"></i>
                        </button>
                    </div>

                    <!-- Scanner Area -->
                    <div class="relative mb-6">
                        <div id="qr-reader" class="w-full rounded-xl overflow-hidden neon-border"></div>

                        <!-- Scanner Overlay -->
                        <div class="absolute inset-0 pointer-events-none">
                            <div class="absolute top-4 left-4 w-6 h-6 border-l-4 border-t-4 border-green-400"></div>
                            <div class="absolute top-4 right-4 w-6 h-6 border-r-4 border-t-4 border-green-400"></div>
                            <div class="absolute bottom-4 left-4 w-6 h-6 border-l-4 border-b-4 border-green-400"></div>
                            <div class="absolute bottom-4 right-4 w-6 h-6 border-r-4 border-b-4 border-green-400"></div>
                        </div>
                    </div>

                    <!-- Scanner Controls -->
                    <div class="grid grid-cols-2 gap-4">
                        <button id="switchCamera" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-sync-alt mr-2"></i>
                            Switch Camera
                        </button>
                        <button id="toggleFlash" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-3 rounded-xl transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-flashlight mr-2"></i>
                            Flash
                        </button>
                    </div>

                    <!-- Scanner Status -->
                    <div class="mt-4 text-center">
                        <div id="scannerStatus" class="text-gray-300 text-sm">
                            <i class="fas fa-search mr-1"></i>
                            Mencari QR code...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Recent Scans -->
        <div class="max-w-6xl mx-auto mt-8">
            <div class="glass-effect rounded-2xl p-6 backdrop-blur-lg">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-2xl font-bold text-white">
                        <i class="fas fa-history mr-3 text-blue-400"></i>
                        Recent Activity
                    </h3>
                    <div class="flex items-center space-x-4">
                        <button id="refreshScans" class="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-lg transition-all duration-300">
                            <i class="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                        <div class="text-white text-sm">
                            <i class="fas fa-clock mr-1"></i>
                            Auto-refresh: <span id="autoRefreshCountdown">30</span>s
                        </div>
                    </div>
                </div>

                <div id="recentScans" class="space-y-4">
                    <!-- Recent scans will be populated here -->
                    <div class="text-center text-gray-300 py-8">
                        <i class="fas fa-search text-4xl mb-4 opacity-50"></i>
                        <p>Belum ada aktivitas scan hari ini</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <div class="fixed bottom-6 right-6 z-40">
            <button id="floatingScanner" class="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110 scan-animation">
                <i class="fas fa-qrcode text-2xl"></i>
            </button>
        </div>

        <!-- Sound Effects -->
        <audio id="successSound" preload="auto">
            <source src="/sounds/success.mp3" type="audio/mpeg">
        </audio>
        <audio id="errorSound" preload="auto">
            <source src="/sounds/error.mp3" type="audio/mpeg">
        </audio>
        <audio id="scanSound" preload="auto">
            <source src="/sounds/scan.mp3" type="audio/mpeg">
        </audio>
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/html5-qrcode" type="text/javascript"></script>
<script>
let html5QrCode;
let currentCamera = 0;
let cameras = [];
let isScanning = false;
let autoRefreshInterval;
let autoRefreshCountdown = 30;
let scanCount = 0;
let validCount = 0;
let usedCount = 0;

// Initialize QR Scanner
async function initQRScanner() {
    try {
        cameras = await Html5Qrcode.getCameras();
        if (cameras && cameras.length) {
            html5QrCode = new Html5Qrcode("qr-reader");
            console.log(`Found ${cameras.length} cameras`);
        } else {
            console.warn('No cameras found');
            showNotification('Kamera tidak ditemukan', 'warning');
        }
    } catch (err) {
        console.error('Error initializing QR scanner:', err);
        showNotification('Error initializing camera', 'error');
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;

    const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        warning: 'bg-yellow-500 text-black',
        info: 'bg-blue-500 text-white'
    };

    notification.className += ` ${colors[type] || colors.info}`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Play sound effect
function playSound(type) {
    try {
        const audio = document.getElementById(type + 'Sound');
        if (audio) {
            audio.currentTime = 0;
            audio.play().catch(e => console.log('Audio play failed:', e));
        }
    } catch (e) {
        console.log('Sound effect failed:', e);
    }
}

// Vibrate device (mobile)
function vibrate(pattern = [100]) {
    if (navigator.vibrate) {
        navigator.vibrate(pattern);
    }
}

// Start QR Scanner
async function startScanner() {
    if (!html5QrCode || cameras.length === 0) {
        alert('No cameras found or QR scanner not initialized');
        return;
    }

    try {
        await html5QrCode.start(
            cameras[currentCamera].id,
            {
                fps: 10,
                qrbox: { width: 250, height: 250 }
            },
            (decodedText, decodedResult) => {
                handleScanResult(decodedText);
            },
            (errorMessage) => {
                // Handle scan errors silently
            }
        );
    } catch (err) {
        console.error('Error starting scanner:', err);
    }
}

// Stop QR Scanner
async function stopScanner() {
    if (html5QrCode && html5QrCode.isScanning) {
        try {
            await html5QrCode.stop();
        } catch (err) {
            console.error('Error stopping scanner:', err);
        }
    }
}

// Handle scan result
function handleScanResult(decodedText) {
    playSound('scan');
    vibrate([100, 50, 100]);

    document.getElementById('identifier').value = decodedText;
    updateScannerStatus('QR Code detected!', 'success');

    closeScanner();
    showNotification('QR Code berhasil di-scan', 'success');

    // Auto-submit form after short delay
    setTimeout(() => {
        document.getElementById('scanForm').dispatchEvent(new Event('submit'));
    }, 500);
}

// Update scanner status
function updateScannerStatus(message, type = 'info') {
    const statusElement = document.getElementById('scannerStatus');
    if (statusElement) {
        const icons = {
            info: 'fas fa-search',
            success: 'fas fa-check',
            error: 'fas fa-times',
            warning: 'fas fa-exclamation-triangle'
        };

        statusElement.innerHTML = `
            <i class="${icons[type] || icons.info} mr-1"></i>
            ${message}
        `;

        statusElement.className = `text-sm ${
            type === 'success' ? 'text-green-400' :
            type === 'error' ? 'text-red-400' :
            type === 'warning' ? 'text-yellow-400' :
            'text-gray-300'
        }`;
    }
}

// Open scanner modal
function openScanner() {
    document.getElementById('scannerModal').classList.remove('hidden');
    startScanner();
}

// Close scanner modal
function closeScanner() {
    document.getElementById('scannerModal').classList.add('hidden');
    stopScanner();
}

// Switch camera
function switchCamera() {
    if (cameras.length > 1) {
        currentCamera = (currentCamera + 1) % cameras.length;
        stopScanner().then(() => {
            startScanner();
        });
    }
}

// Handle form submission
document.getElementById('scanForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = document.getElementById('scanTicketButton');
    const spinner = document.getElementById('scanSpinner');

    // Show loading state
    button.disabled = true;
    spinner.classList.remove('hidden');

    try {
        const response = await fetch('{{ route("tickets.staff-scan") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const result = await response.json();
        displayScanResult(result);

    } catch (error) {
        console.error('Error:', error);
        displayScanResult({
            success: false,
            message: 'Terjadi kesalahan sistem',
            status: 'error',
            icon: 'exclamation-triangle',
            color: 'red'
        });
    } finally {
        // Hide loading state
        button.disabled = false;
        spinner.classList.add('hidden');
    }
});

// Display enhanced scan result
function displayScanResult(result) {
    const resultDiv = document.getElementById('scanResult');

    // Play appropriate sound and vibration
    if (result.success) {
        playSound('success');
        vibrate([200, 100, 200]);
        showNotification(result.message, 'success');
    } else {
        playSound('error');
        vibrate([500]);
        showNotification(result.message, 'error');
    }

    // Update stats
    scanCount++;
    if (result.success && result.data && result.data.can_use) {
        validCount++;
    }
    updateStats();

    let iconClass = 'fas fa-' + (result.icon || 'question-circle');
    let animationClass = result.success ? 'success-animation' : 'error-shake';

    let html = `
        <div class="glass-effect rounded-2xl backdrop-blur-lg border border-white/20 p-8 ${animationClass}">
            <div class="text-center mb-6">
                <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center ${
                    result.success ? 'bg-green-500' :
                    result.color === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'
                }">
                    <i class="${iconClass} text-3xl text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-white mb-2">
                    ${result.message}
                </h3>
            </div>
    `;

    if (result.success && result.data && result.data.can_use) {
        // Show ticket details and use button
        html += `
            <div class="bg-white/10 rounded-xl p-6 mb-6 backdrop-blur-sm">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Ticket Number</p>
                            <p class="font-bold text-white text-lg font-mono">${result.data.ticket_number}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Boarding Pass ID</p>
                            <p class="font-bold text-blue-300 text-lg font-mono">${result.data.boarding_pass_id}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Template</p>
                            <p class="font-semibold text-purple-300">${result.data.template_used?.toUpperCase() || 'UNIX'}</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Event</p>
                            <p class="font-semibold text-white">${result.data.event_title}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Attendee</p>
                            <p class="font-semibold text-white">${result.data.attendee_name}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-300 mb-1">Venue</p>
                            <p class="font-semibold text-white">${result.data.venue_name}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button onclick="useTicket(${result.data.ticket_id})"
                        class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <i class="fas fa-check mr-3 text-xl"></i>
                    <span class="text-lg">PAKAI TIKET</span>
                </button>
                <p class="text-gray-300 text-sm mt-3">
                    <i class="fas fa-info-circle mr-1"></i>
                    Klik tombol di atas untuk menandai tiket sebagai digunakan
                </p>
            </div>
        `;
    } else if (result.data) {
        // Show error details
        html += `
            <div class="bg-white/10 rounded-xl p-6 backdrop-blur-sm">
                <div class="text-center text-gray-300">
        `;
        if (result.data.ticket_number) {
            html += `<p class="mb-2"><span class="font-semibold">Ticket:</span> ${result.data.ticket_number}</p>`;
        }
        if (result.data.boarding_pass_id) {
            html += `<p class="mb-2"><span class="font-semibold">Boarding Pass:</span> ${result.data.boarding_pass_id}</p>`;
        }
        if (result.data.event_title) {
            html += `<p class="mb-2"><span class="font-semibold">Event:</span> ${result.data.event_title}</p>`;
        }
        if (result.data.scanned_at) {
            html += `<p class="mb-2"><span class="font-semibold">Scanned:</span> ${result.data.scanned_at}</p>`;
        }
        if (result.data.used_at) {
            html += `<p class="mb-2"><span class="font-semibold">Used:</span> ${result.data.used_at}</p>`;
        }
        html += `
                </div>
            </div>
        `;
    }

    html += `
            <div class="text-center mt-6">
                <button onclick="clearResult()"
                        class="bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg transition-all duration-300">
                    <i class="fas fa-times mr-2"></i>Tutup
                </button>
            </div>
        </div>
    `;

    resultDiv.innerHTML = html;
    resultDiv.classList.remove('hidden');
    resultDiv.scrollIntoView({ behavior: 'smooth' });
}

// Clear scan result
function clearResult() {
    const resultDiv = document.getElementById('scanResult');
    resultDiv.classList.add('hidden');
    document.getElementById('identifier').value = '';
    document.getElementById('identifier').focus();
}

// Update stats display
function updateStats() {
    document.getElementById('todayScans').textContent = scanCount;
    document.getElementById('validTickets').textContent = validCount;
    document.getElementById('usedTickets').textContent = usedCount;
}

// Use ticket function
async function useTicket(ticketId) {
    if (!confirm('Apakah Anda yakin ingin menggunakan tiket ini?')) {
        return;
    }

    try {
        const response = await fetch('{{ route("tickets.staff-use") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ ticket_id: ticketId })
        });

        const result = await response.json();

        if (result.success) {
            alert('Tiket berhasil digunakan!');
            // Refresh the page or update UI
            location.reload();
        } else {
            alert('Error: ' + result.message);
        }

    } catch (error) {
        console.error('Error using ticket:', error);
        alert('Terjadi kesalahan saat menggunakan tiket');
    }
}

// Auto-refresh functionality
function startAutoRefresh() {
    autoRefreshInterval = setInterval(() => {
        autoRefreshCountdown--;
        document.getElementById('autoRefreshCountdown').textContent = autoRefreshCountdown;

        if (autoRefreshCountdown <= 0) {
            loadRecentScans();
            autoRefreshCountdown = 30;
        }
    }, 1000);
}

// Load recent scans
async function loadRecentScans() {
    try {
        const response = await fetch('/validation/recent');
        const scans = await response.json();

        const container = document.getElementById('recentScans');
        if (scans.length === 0) {
            container.innerHTML = `
                <div class="text-center text-gray-300 py-8">
                    <i class="fas fa-search text-4xl mb-4 opacity-50"></i>
                    <p>Belum ada aktivitas scan hari ini</p>
                </div>
            `;
            return;
        }

        container.innerHTML = scans.map(scan => `
            <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                            scan.status === 'used' ? 'bg-green-500' :
                            scan.status === 'active' ? 'bg-blue-500' : 'bg-red-500'
                        }">
                            <i class="fas fa-${
                                scan.status === 'used' ? 'check' :
                                scan.status === 'active' ? 'ticket-alt' : 'times'
                            } text-white"></i>
                        </div>
                        <div>
                            <p class="text-white font-semibold">${scan.ticket_number}</p>
                            <p class="text-gray-300 text-sm">${scan.event_title}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-white text-sm">${scan.attendee_name}</p>
                        <p class="text-gray-300 text-xs">${scan.validated_at}</p>
                    </div>
                </div>
            </div>
        `).join('');

    } catch (error) {
        console.error('Error loading recent scans:', error);
    }
}

// Voice commands (experimental)
function initVoiceCommands() {
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'id-ID';

        recognition.onresult = function(event) {
            const command = event.results[0][0].transcript.toLowerCase();

            if (command.includes('scan') || command.includes('kamera')) {
                openScanner();
                showNotification('Voice command: Opening scanner', 'info');
            } else if (command.includes('clear') || command.includes('hapus')) {
                clearResult();
                document.getElementById('identifier').value = '';
                showNotification('Voice command: Input cleared', 'info');
            }
        };

        // Add voice activation button
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === ' ') {
                recognition.start();
                showNotification('Listening for voice command...', 'info');
            }
        });
    }
}

// Keyboard shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S = Open scanner
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            openScanner();
        }

        // Escape = Close modal/clear result
        if (e.key === 'Escape') {
            closeScanner();
            clearResult();
        }

        // Enter = Submit form (when input is focused)
        if (e.key === 'Enter' && document.activeElement.id === 'identifier') {
            e.preventDefault();
            document.getElementById('scanForm').dispatchEvent(new Event('submit'));
        }
    });
}

// Event listeners
document.getElementById('scanButton').addEventListener('click', openScanner);
document.getElementById('quickScanButton').addEventListener('click', openScanner);
document.getElementById('floatingScanner').addEventListener('click', openScanner);
document.getElementById('closeScannerModal').addEventListener('click', closeScanner);
document.getElementById('switchCamera').addEventListener('click', switchCamera);
document.getElementById('refreshScans').addEventListener('click', loadRecentScans);

// Toggle flash (if supported)
document.getElementById('toggleFlash').addEventListener('click', function() {
    // Flash functionality would need camera API support
    showNotification('Flash toggle not yet implemented', 'warning');
});

// Auto-focus input
document.getElementById('identifier').addEventListener('input', function() {
    // Auto-submit if input looks like a complete ticket ID
    const value = this.value.trim();
    if (value.length >= 15 && (value.startsWith('TIK-') || value.startsWith('BP') || value.startsWith('QR-'))) {
        setTimeout(() => {
            document.getElementById('scanForm').dispatchEvent(new Event('submit'));
        }, 500);
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initQRScanner();
    initVoiceCommands();
    initKeyboardShortcuts();
    loadRecentScans();
    startAutoRefresh();

    // Focus input
    document.getElementById('identifier').focus();

    // Show welcome notification
    setTimeout(() => {
        showNotification('Staff Scanner siap digunakan!', 'success');
    }, 1000);
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
    stopScanner();
});
</script>
@endpush
@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table already exists
        if (!Schema::hasTable('payment_methods')) {
            Schema::create('payment_methods', function (Blueprint $table) {
                $table->id();
                $table->string('name'); // e.g., "Transfer Bank BCA"
                $table->string('type'); // manual, tripay, midtrans, xendit
                $table->string('code')->unique(); // e.g., "bank_bca", "gopay", "ovo"
                $table->string('category'); // bank_transfer, e_wallet, qris, virtual_account
                $table->text('description')->nullable();
                $table->string('icon')->nullable(); // icon class or image path
                $table->string('logo')->nullable(); // logo image path
                $table->decimal('fee_percentage', 5, 2)->default(0); // fee percentage
                $table->decimal('fee_fixed', 10, 2)->default(0); // fixed fee amount
                $table->decimal('min_amount', 15, 2)->default(0); // minimum transaction amount
                $table->decimal('max_amount', 15, 2)->nullable(); // maximum transaction amount
                $table->json('config')->nullable(); // gateway specific config (API keys, etc)
                $table->json('manual_config')->nullable(); // manual payment config (account numbers, etc)
                $table->boolean('is_active')->default(true);
                $table->boolean('is_manual')->default(false); // true for manual payments
                $table->integer('sort_order')->default(0);
                $table->text('instructions')->nullable(); // payment instructions for manual methods
                $table->decimal('gateway_balance', 15, 2)->default(0); // gateway balance tracking
                $table->timestamp('balance_last_updated')->nullable(); // last balance update
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */

    public function down(): void
    {
        Schema::dropIfExists('payment_methods');
    }
};

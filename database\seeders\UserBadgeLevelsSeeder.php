<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\UserBadgeLevel;
use Carbon\Carbon;

class UserBadgeLevelsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing badge levels
        UserBadgeLevel::truncate();

        $now = Carbon::now();

        // 1. Bronze Badge (Default)
        UserBadgeLevel::create([
            'name' => 'Bronze',
            'slug' => 'bronze',
            'description' => 'Bronze Badge - Basic access with 3 months duration. Perfect for new users starting their journey.',
            'color' => '#CD7F32',
            'icon' => 'fas fa-medal',
            'badge_image' => null,
            'min_spent_amount' => 0,
            'min_uangtix_balance' => 0,
            'min_transactions' => 0,
            'min_events_attended' => 0,
            'discount_percentage' => 0,
            'cashback_percentage' => 0,
            'benefits' => json_encode([
                'Basic event access',
                'Standard ticket templates',
                'Email support',
                'Basic profile features'
            ]),
            'requirements' => json_encode([
                'New user registration',
                'Email verification'
            ]),
            'is_active' => true,
            'is_default' => true,
            'sort_order' => 1,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // 2. Silver Badge
        UserBadgeLevel::create([
            'name' => 'Silver',
            'slug' => 'silver',
            'description' => 'Silver Badge - Enhanced features with 6 months duration. Unlock additional benefits and priority support.',
            'color' => '#C0C0C0',
            'icon' => 'fas fa-award',
            'badge_image' => null,
            'min_spent_amount' => 500000, // 500k
            'min_uangtix_balance' => 100000, // 100k
            'min_transactions' => 5,
            'min_events_attended' => 3,
            'discount_percentage' => 5,
            'cashback_percentage' => 2,
            'benefits' => json_encode([
                'All Bronze benefits',
                'Priority customer support',
                '5% discount on tickets',
                '2% cashback on purchases',
                'Enhanced profile features',
                'Event wishlist',
                'Early bird notifications'
            ]),
            'requirements' => json_encode([
                'Minimum 5 transactions',
                'Minimum 3 events attended',
                'Minimum Rp 500,000 total spent',
                'Minimum Rp 100,000 UangTix balance'
            ]),
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 2,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // 3. Gold Badge
        UserBadgeLevel::create([
            'name' => 'Gold',
            'slug' => 'gold',
            'description' => 'Gold Badge - Premium features with 1 year duration. Access exclusive events and advanced features.',
            'color' => '#FFD700',
            'icon' => 'fas fa-crown',
            'badge_image' => null,
            'min_spent_amount' => 2000000, // 2M
            'min_uangtix_balance' => 500000, // 500k
            'min_transactions' => 15,
            'min_events_attended' => 10,
            'discount_percentage' => 10,
            'cashback_percentage' => 5,
            'benefits' => json_encode([
                'All Silver benefits',
                'VIP customer support',
                '10% discount on tickets',
                '5% cashback on purchases',
                'Exclusive event access',
                'Premium ticket templates',
                'Advanced analytics',
                'Priority booking',
                'Free event promotion'
            ]),
            'requirements' => json_encode([
                'Minimum 15 transactions',
                'Minimum 10 events attended',
                'Minimum Rp 2,000,000 total spent',
                'Minimum Rp 500,000 UangTix balance'
            ]),
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 3,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // 4. Platinum Badge
        UserBadgeLevel::create([
            'name' => 'Platinum',
            'slug' => 'platinum',
            'description' => 'Platinum Badge - Full access + Custom Templates with 2 years duration. Ultimate experience with all premium features.',
            'color' => '#E5E4E2',
            'icon' => 'fas fa-gem',
            'badge_image' => null,
            'min_spent_amount' => ********, // 10M
            'min_uangtix_balance' => 2000000, // 2M
            'min_transactions' => 50,
            'min_events_attended' => 25,
            'discount_percentage' => 15,
            'cashback_percentage' => 10,
            'benefits' => json_encode([
                'All Gold benefits',
                'Dedicated account manager',
                '15% discount on tickets',
                '10% cashback on purchases',
                'Custom ticket template editor',
                'Unlimited event creation',
                'Advanced branding options',
                'API access',
                'White-label solutions',
                'Priority feature requests',
                'Exclusive networking events'
            ]),
            'requirements' => json_encode([
                'Minimum 50 transactions',
                'Minimum 25 events attended',
                'Minimum Rp 10,000,000 total spent',
                'Minimum Rp 2,000,000 UangTix balance',
                'Verified business account'
            ]),
            'is_active' => true,
            'is_default' => false,
            'sort_order' => 4,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('✅ User Badge Levels seeded successfully!');
        $this->command->info('');
        $this->command->info('🏆 BADGE LEVELS CREATED:');
        $this->command->info('1. Bronze (Default) - Basic access, 3 months duration');
        $this->command->info('2. Silver - Enhanced features, 6 months duration');
        $this->command->info('3. Gold - Premium features, 1 year duration');
        $this->command->info('4. Platinum - Full access + Custom Templates, 2 years duration');
        $this->command->info('');
        $this->command->info('💎 PLATINUM FEATURES:');
        $this->command->info('- Custom ticket template editor');
        $this->command->info('- Advanced branding options');
        $this->command->info('- API access');
        $this->command->info('- White-label solutions');
    }
}

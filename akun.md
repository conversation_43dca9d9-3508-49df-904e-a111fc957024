# 📋 **DOKUMENTASI SISTEM AKUN & HAK AKSES**
## Platform Manajemen Tiket Tixara

---

## 🎯 **OVERVIEW SISTEM AKUN**

Platform Tixara menggunakan sistem role-based access control (RBAC) dengan badge level system untuk memberikan akses bertingkat kepada pengguna berdasarkan aktivitas dan pencapaian mereka.

---

## 👥 **JENIS AKUN & HAK AKSES**

### 🔴 **1. ADMIN (Super User)**
**Role:** `admin`  
**Badge Level:** Unlimited  
**Akses Penuh:** ✅ Semua fitur sistem

#### **Hak Akses Admin:**
- ✅ **CRUD semua data** (users, events, orders, tickets, templates)
- ✅ **Manajemen Badge Level** - Buat, edit, hapus badge level
- ✅ **Template Global** - Buat template yang bisa dipakai semua organizer
- ✅ **User Management** - Assign/remove badge, upgrade/downgrade user
- ✅ **System Settings** - Konfigurasi platform, payment gateway, dll
- ✅ **Analytics & Reports** - Lihat semua data analytics platform
- ✅ **Payment Management** - Kelola semua transaksi dan pembayaran
- ✅ **Staff Assignment** - Assign staff ke organizer tertentu
- ✅ **CyberGuard Management** - Kelola keamanan sistem
- ✅ **UangTix Management** - Kelola sistem digital wallet

#### **Dashboard Admin:**
- Overview semua aktivitas platform
- Statistik pengguna, event, dan transaksi
- Manajemen konten dan moderasi
- System health monitoring

---

### 🟡 **2. ORGANIZER (Penjual/Event Creator)**
**Role:** `penjual`  
**Badge Level:** Bronze, Silver, Gold, Platinum, Diamond  
**Akses:** Terbatas pada data milik sendiri

#### **Hak Akses Organizer:**

##### **📅 Event Management:**
- ✅ **CRUD Event Sendiri** - Buat, edit, hapus event milik sendiri
- ❌ **Tidak bisa akses event organizer lain**
- ✅ **Publish/Unpublish** event sendiri
- ✅ **Event Analytics** - Statistik event sendiri
- ✅ **QR Code Download** - Download QR code untuk event sendiri

##### **🎫 Template Management:**
- ✅ **CRUD Template Sendiri** - Kelola template tiket milik sendiri
- ❌ **Tidak bisa edit template organizer lain**
- ✅ **Akses Template Global** - Gunakan template yang dibuat admin
- ✅ **Duplicate Template** - Copy template global atau milik sendiri
- 🔒 **Custom Template** - **Hanya untuk Platinum badge ke atas**

##### **💰 Financial Management:**
- ✅ **Order Management** - Kelola pesanan untuk event sendiri
- ✅ **Payment Tracking** - Lihat pembayaran event sendiri
- ✅ **Revenue Analytics** - Statistik pendapatan sendiri
- ✅ **UangTix Balance** - Kelola saldo digital wallet

##### **👥 Customer Management:**
- ✅ **Ticket Validation** - Validasi tiket untuk event sendiri
- ✅ **Attendee List** - Lihat daftar peserta event sendiri
- ✅ **Customer Communication** - Komunikasi dengan pembeli tiket

#### **Badge Level Requirements:**

| Badge Level | Min Spent | Min UangTix | Min Transactions | Min Events | Template Access |
|-------------|-----------|-------------|------------------|------------|-----------------|
| **Bronze** | Rp 0 | Rp 0 | 0 | 0 | Classic, Unix, Minimal |
| **Silver** | Rp 500K | Rp 100K | 5 | 2 | + Pro Templates |
| **Gold** | Rp 2M | Rp 500K | 15 | 5 | + Advanced Features |
| **Platinum** | Rp 5M | Rp 1M | 30 | 10 | + **Custom Templates** |
| **Diamond** | Rp 10M | Rp 2M | 50 | 20 | + Premium Support |

---

### 🟢 **3. STAFF (Moderator)**
**Role:** `staff`  
**Badge Level:** Assigned by Admin  
**Akses:** Terbatas pada organizer yang di-assign

#### **Hak Akses Staff:**
- ✅ **Assigned Organizer Management** - Kelola organizer yang di-assign
- ✅ **Ticket Validation** - Validasi tiket untuk organizer yang di-assign
- ✅ **Event Moderation** - Moderasi event dari organizer yang di-assign
- ✅ **Customer Support** - Bantu customer untuk organizer yang di-assign
- ❌ **Tidak bisa akses data organizer lain**
- ❌ **Tidak bisa akses system settings**

#### **Assignment System:**
- Staff di-assign ke organizer tertentu oleh admin
- Satu staff bisa di-assign ke multiple organizer
- Staff hanya bisa lihat data organizer yang di-assign

---

### 🔵 **4. USER/CUSTOMER (Pembeli)**
**Role:** `customer`  
**Badge Level:** Bronze (default)  
**Akses:** Terbatas pada pembelian dan tiket sendiri

#### **Hak Akses Customer:**
- ✅ **Browse Events** - Lihat dan cari event yang tersedia
- ✅ **Purchase Tickets** - Beli tiket event
- ✅ **My Tickets** - Lihat tiket yang sudah dibeli
- ✅ **Order History** - Riwayat pembelian
- ✅ **UangTix Wallet** - Kelola saldo digital wallet
- ✅ **Profile Management** - Kelola profil sendiri
- ❌ **Tidak bisa akses data user lain**
- ❌ **Tidak bisa buat event**

---

## 🎨 **SISTEM TEMPLATE TIKET**

### **Template Types & Access:**

#### **1. Classic Template**
- **Access:** Semua badge level
- **Features:** Desain tiket tradisional
- **Customization:** Warna dasar, font, layout sederhana

#### **2. Unix Terminal Template**
- **Access:** Semua badge level
- **Features:** Desain terminal/command line
- **Customization:** Color scheme, font monospace

#### **3. Minimal Template**
- **Access:** Semua badge level
- **Features:** Desain minimalis dan clean
- **Customization:** Typography, spacing, colors

#### **4. Professional Template**
- **Access:** Silver badge ke atas
- **Features:** Desain business/corporate
- **Customization:** Branding elements, professional layout

#### **5. Custom Template** 🔒
- **Access:** **Platinum badge ke atas**
- **Features:** Drag-and-drop editor seperti Canva
- **Customization:** Full customization dengan GrapesJS editor

### **Template Management Rules:**

#### **Organizer Template Access:**
- ✅ **Own Templates:** Full CRUD access
- ✅ **Global Templates:** Read + Duplicate access
- ❌ **Other Organizer Templates:** No access
- 🔒 **Custom Editor:** Platinum badge required

#### **Admin Template Access:**
- ✅ **All Templates:** Full CRUD access
- ✅ **Global Templates:** Create templates for all organizers
- ✅ **Template Moderation:** Review and approve custom templates

---

## 🔐 **SECURITY & PERMISSIONS**

### **Data Isolation:**
- **Organizer:** Hanya bisa akses data milik sendiri
- **Staff:** Hanya bisa akses data organizer yang di-assign
- **Customer:** Hanya bisa akses data pembelian sendiri
- **Admin:** Akses semua data

### **Badge Level Security:**
- Custom template access di-check di controller level
- Frontend UI menyembunyikan fitur yang tidak accessible
- Database constraint memastikan data integrity

### **Template Security:**
- Template ownership di-check setiap request
- Global template read-only untuk organizer
- Custom config validation untuk prevent XSS

---

## 📊 **UPGRADE PATH & MONETIZATION**

### **Badge Upgrade Requirements:**
1. **Automatic Upgrade:** Berdasarkan spending dan activity
2. **Manual Upgrade:** Admin bisa assign badge manual
3. **Subscription Model:** Premium badge dengan recurring payment

### **Revenue Streams:**
- **Transaction Fee:** Persentase dari setiap penjualan tiket
- **Premium Badge:** Subscription untuk akses fitur advanced
- **Custom Template:** One-time fee untuk unlock custom editor
- **Premium Support:** Dedicated support untuk Diamond badge

---

## 🚀 **IMPLEMENTATION NOTES**

### **Database Structure:**
```sql
users:
- id, name, email, role, badge_level_id
- badge_assigned_at, badge_expires_at, badge_auto_renew

user_badge_level:
- id, name, min_spent_amount, min_uangtix_balance
- benefits, requirements, is_active

ticket_templates:
- id, organizer_id (nullable for global), name, template_type
- config, custom_config, is_active, badge_level_minimum

staff_organizer_assignments:
- staff_id, organizer_id, is_active, assigned_at
```

### **Middleware & Guards:**
- `CheckBadgeLevel` - Verify badge requirements
- `CheckTemplateOwnership` - Verify template access
- `CheckOrganizerAccess` - Verify organizer data access
- `CheckStaffAssignment` - Verify staff assignment

---

## 📞 **SUPPORT & CONTACT**

### **Developer Information:**
- **Company:** CV Bintang Gumilang Group (Bintangcode Sub Holding)
- **Developer:** Dhafa Nazula I
- **Instagram:** @seehai.dhafa
- **Email:** <EMAIL>

### **Copyright Attribution:**
```
© 2024 CV Bintang Gumilang Group (Bintangcode Sub Holding)
Developed by Dhafa Nazula I (@seehai.dhafa)
Platform Tixara - Event Ticketing Management System
```

---

**Last Updated:** {{ date('Y-m-d H:i:s') }}  
**Version:** 1.0.0  
**Status:** Production Ready ✅

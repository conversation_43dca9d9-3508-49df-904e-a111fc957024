@extends('layouts.app')

@section('title', 'Detail Tiket - ' . $ticket->ticket_number)

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-ticket-alt mr-3 text-blue-600"></i>
                Detail E-Tiket
            </h1>
            <p class="text-gray-600 dark:text-gray-400">{{ $ticket->ticket_number }}</p>
        </div>

        <!-- Ticket Status Alert -->
        @if($ticket->status === 'used')
            <div class="max-w-4xl mx-auto mb-6">
                <div class="bg-orange-100 border border-orange-400 text-orange-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="font-medium">Tiket sudah digunakan pada {{ $ticket->used_at?->format('d M Y H:i') }}</span>
                    </div>
                </div>
            </div>
        @elseif($ticket->status === 'active')
            <div class="max-w-4xl mx-auto mb-6">
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="font-medium">Tiket aktif dan siap digunakan</span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Ticket Actions -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-download mr-2"></i>Aksi Tiket
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Download PDF -->
                    <a href="{{ route('tickets.download', $ticket) }}" 
                       class="flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                       onclick="trackDownload()">
                        <i class="fas fa-download mr-2"></i>
                        Download PDF
                    </a>
                    
                    <!-- Print Ticket -->
                    <a href="{{ route('tickets.print', $ticket) }}" 
                       target="_blank"
                       class="flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200"
                       onclick="trackPrint()">
                        <i class="fas fa-print mr-2"></i>
                        Print Tiket
                    </a>
                    
                    <!-- View QR Code -->
                    <button onclick="showQRCode()" 
                            class="flex items-center justify-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200">
                        <i class="fas fa-qrcode mr-2"></i>
                        Lihat QR Code
                    </button>
                </div>
                
                <!-- Usage Statistics -->
                <div class="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $ticket->download_count ?? 0 }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Download</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $ticket->print_count ?? 0 }}</div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Print</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            @if($ticket->is_authentic)
                                <i class="fas fa-shield-check"></i>
                            @else
                                <i class="fas fa-shield-times"></i>
                            @endif
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Authentic</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold {{ $ticket->status === 'used' ? 'text-orange-600' : 'text-green-600' }}">
                            @if($ticket->status === 'used')
                                <i class="fas fa-check-circle"></i>
                            @else
                                <i class="fas fa-clock"></i>
                            @endif
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-400">Status</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ticket Details -->
        <div class="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Event Information -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-calendar-alt mr-2"></i>Informasi Event
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Nama Event</label>
                        <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ $ticket->event->title }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Tanggal & Waktu</label>
                        <p class="text-gray-900 dark:text-white">
                            {{ \Carbon\Carbon::parse($ticket->event->start_date)->format('d M Y, H:i') }} WIB
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Venue</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->event->venue_name }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $ticket->event->venue_address }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Kota</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->event->city }}, {{ $ticket->event->province }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Organizer</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->event->organizer->name }}</p>
                    </div>
                </div>
            </div>

            <!-- Ticket Information -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-ticket-alt mr-2"></i>Informasi Tiket
                </h2>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Nomor Tiket</label>
                        <p class="text-lg font-mono font-semibold text-gray-900 dark:text-white">{{ $ticket->ticket_number }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Nama Peserta</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->attendee_name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Email Peserta</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->attendee_email }}</p>
                    </div>
                    
                    @if($ticket->attendee_phone)
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Telepon</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->attendee_phone }}</p>
                    </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Harga</label>
                        <p class="text-xl font-bold text-green-600 dark:text-green-400">
                            Rp {{ number_format($ticket->price, 0, ',', '.') }}
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Status</label>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            @if($ticket->status === 'active') bg-green-100 text-green-800
                            @elseif($ticket->status === 'used') bg-orange-100 text-orange-800
                            @elseif($ticket->status === 'cancelled') bg-red-100 text-red-800
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ ucfirst($ticket->status) }}
                        </span>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Dibeli Pada</label>
                        <p class="text-gray-900 dark:text-white">{{ $ticket->created_at->format('d M Y, H:i') }} WIB</p>
                    </div>
                    
                    @if($ticket->used_at)
                    <div>
                        <label class="block text-sm font-medium text-gray-600 dark:text-gray-400">Digunakan Pada</label>
                        <p class="text-orange-600 dark:text-orange-400 font-medium">{{ $ticket->used_at->format('d M Y, H:i') }} WIB</p>
                        @if($ticket->validator)
                            <p class="text-sm text-gray-600 dark:text-gray-400">Divalidasi oleh: {{ $ticket->validator->name }}</p>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- QR Code Modal -->
        <div id="qrModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-xl p-8 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">QR Code Tiket</h3>
                    <button onclick="closeQRCode()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                
                <div class="text-center">
                    <div class="bg-white p-4 rounded-lg inline-block mb-4">
                        {!! QrCode::size(200)->generate($ticket->qr_code) !!}
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Tunjukkan QR Code ini kepada petugas saat masuk event
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-500 font-mono">
                        {{ $ticket->qr_code }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function showQRCode() {
    document.getElementById('qrModal').classList.remove('hidden');
}

function closeQRCode() {
    document.getElementById('qrModal').classList.add('hidden');
}

function trackDownload() {
    // Track download analytics
    fetch('/api/track-download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            ticket_id: {{ $ticket->id }},
            action: 'download'
        })
    }).catch(error => console.log('Tracking error:', error));
}

function trackPrint() {
    // Track print analytics
    fetch('/api/track-print', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            ticket_id: {{ $ticket->id }},
            action: 'print'
        })
    }).catch(error => console.log('Tracking error:', error));
}

// Close modal when clicking outside
document.getElementById('qrModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQRCode();
    }
});
</script>
@endpush
@endsection

<?php

echo "🔧 Comprehensive Migration Syntax Fix...\n";

$migrationPath = 'database/migrations/';
$migrationFiles = glob($migrationPath . '*.php');

$fixedCount = 0;
$successCount = 0;

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    $originalContent = $content;
    
    echo "Processing {$filename}...\n";
    
    // Fix 1: Missing closing brace and parenthesis for Schema::create
    // Pattern: Look for lines ending with table operations but missing });
    $content = preg_replace(
        '/(\s+\$table->(?:index|timestamps|foreign)\([^)]*\)(?:->[\w()]+)*;\s*)\s*}\s*}\s*$/m',
        '$1' . "\n            });\n        }\n    }",
        $content
    );
    
    // Fix 2: Missing closing brace for Schema::create function specifically
    $content = preg_replace(
        '/(\s+\$table->(?:index|timestamps)\([^)]*\);\s*)\s*}\s*}\s*(\n\s*\/\*\*)/m',
        '$1' . "\n            });\n        }\n    }\n$2",
        $content
    );
    
    // Fix 3: Fix malformed comment blocks
    $content = preg_replace(
        '/\/\*\*\s*}\s*\*\s*Reverse\s+the\s+migrations\.\s*\*\//',
        "/**\n     * Reverse the migrations.\n     */",
        $content
    );
    
    // Fix 4: Fix missing closing for if (!Schema::hasTable())
    $content = preg_replace(
        '/(if\s*\(\s*!\s*Schema::hasTable\([^)]+\)\s*\)\s*{[^}]*Schema::create[^}]*}\);\s*)(}\s*\/\*\*)/',
        '$1' . "\n        }\n    }\n\n    /**",
        $content
    );
    
    // Fix 5: Fix double closing braces
    $content = preg_replace('/}\s*}\s*\/\*\*/', "}\n\n    /**", $content);
    
    // Fix 6: Fix indentation issues
    $lines = explode("\n", $content);
    $newLines = [];
    $inSchemaCreate = false;
    
    foreach ($lines as $line) {
        if (preg_match('/Schema::create\([^,]+,\s*function\s*\([^)]*\)\s*{/', $line)) {
            $inSchemaCreate = true;
            $newLines[] = $line;
            continue;
        }
        
        if ($inSchemaCreate && preg_match('/^\s*\$table->/', $line)) {
            // Ensure proper indentation for table operations
            $line = preg_replace('/^\s*/', '                ', $line);
        }
        
        if ($inSchemaCreate && preg_match('/^\s*}\);/', $line)) {
            $inSchemaCreate = false;
        }
        
        $newLines[] = $line;
    }
    
    $content = implode("\n", $newLines);
    
    // Fix 7: Ensure proper structure for Schema::create blocks
    $content = preg_replace(
        '/(Schema::create\([^,]+,\s*function\s*\([^)]*\)\s*{[^}]*)\s*}\s*}\s*}/s',
        '$1' . "\n            });\n        }\n    }",
        $content
    );
    
    // Write the fixed content
    if ($content !== $originalContent) {
        file_put_contents($file, $content);
        $fixedCount++;
        echo "  ✅ Fixed {$filename}\n";
    }
    
    // Validate syntax
    $output = [];
    $returnCode = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        $successCount++;
        echo "  ✅ Syntax OK: {$filename}\n";
    } else {
        echo "  ❌ Syntax Error: {$filename} - " . implode(' ', $output) . "\n";
    }
}

echo "\n📊 Final Summary:\n";
echo "  Files processed: " . count($migrationFiles) . "\n";
echo "  Files fixed: {$fixedCount}\n";
echo "  Files with valid syntax: {$successCount}\n";
echo "  Files with errors: " . (count($migrationFiles) - $successCount) . "\n";

if ($successCount === count($migrationFiles)) {
    echo "\n🎉 All migration files are now syntax-error free!\n";
    echo "You can now run: php artisan migrate\n";
} else {
    echo "\n⚠️  Some files still have syntax errors. Manual review may be needed.\n";
}

echo "\n✅ Comprehensive migration syntax fix completed!\n";

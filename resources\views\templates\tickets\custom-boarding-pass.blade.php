<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1f2937;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 850px;
            width: 100%;
            background: white;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e5e7eb;
            position: relative;
        }

        .brand-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 24px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .brand-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .brand-info {
            position: relative;
            z-index: 2;
        }

        .brand-title {
            font-size: 24px;
            font-weight: 800;
            letter-spacing: 1px;
            margin-bottom: 4px;
        }

        .brand-subtitle {
            font-size: 12px;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .custom-logo {
            position: relative;
            z-index: 2;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 800;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .boarding-body {
            padding: 32px;
        }

        .event-header {
            text-align: center;
            margin-bottom: 32px;
            padding: 24px;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-radius: 16px;
            border: 1px solid #bbf7d0;
            position: relative;
        }

        .event-title {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-organizer {
            font-size: 14px;
            color: #059669;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 1.5fr 1fr;
            gap: 32px;
            align-items: start;
        }

        .details-section {
            background: #fafafa;
            border-radius: 16px;
            padding: 28px;
            border: 1px solid #e5e7eb;
        }

        .section-title {
            font-size: 16px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 2px solid #10b981;
            padding-bottom: 8px;
        }

        .details-grid {
            display: grid;
            gap: 20px;
        }

        .detail-pair {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            background: white;
            padding: 16px;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .detail-item:hover {
            border-color: #10b981;
            box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.1);
        }

        .detail-label {
            font-size: 11px;
            font-weight: 600;
            color: #10b981;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .detail-value {
            font-size: 15px;
            font-weight: 600;
            color: #1f2937;
            line-height: 1.3;
        }

        .detail-value.primary {
            font-size: 17px;
            color: #10b981;
        }

        .qr-section {
            background: white;
            border-radius: 16px;
            padding: 28px;
            text-align: center;
            border: 1px solid #e5e7eb;
            position: relative;
        }

        .qr-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #059669, #047857);
            border-radius: 16px 16px 0 0;
        }

        .qr-header {
            margin-bottom: 20px;
        }

        .qr-title {
            font-size: 14px;
            font-weight: 700;
            color: #10b981;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 4px;
        }

        .qr-subtitle {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #1f2937, #374151);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border: 3px solid #10b981;
        }

        .boarding-id {
            background: #f0fdf4;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            font-weight: 700;
            color: #10b981;
            font-family: 'JetBrains Mono', monospace;
            margin-top: 12px;
        }

        .custom-footer {
            background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
            padding: 20px 32px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .footer-section {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-label {
            font-size: 10px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .footer-value {
            font-size: 12px;
            font-weight: 600;
            color: #1f2937;
        }

        .tixara-custom {
            font-size: 16px;
            font-weight: 800;
            color: #10b981;
            letter-spacing: 1px;
        }

        .platinum-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: absolute;
            top: 16px;
            right: 16px;
        }

        .customization-note {
            background: #fffbeb;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            text-align: center;
        }

        .customization-note p {
            font-size: 11px;
            color: #92400e;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-pair {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .brand-header {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }
            
            .custom-footer {
                flex-direction: column;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 2px solid #000;
            }
            
            .platinum-badge {
                background: #000 !important;
                color: white !important;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="platinum-badge">
            ⭐ PLATINUM
        </div>
        
        <div class="brand-header">
            <div class="brand-info">
                <div class="brand-title">CUSTOM BOARDING PASS</div>
                <div class="brand-subtitle">Platinum Exclusive Design</div>
            </div>
            <div class="custom-logo">
                {{ strtoupper(substr($ticket->event->organizer->name ?? 'ORG', 0, 3)) }}
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-header">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-organizer">Organized by {{ $ticket->event->organizer->name ?? 'Event Organizer' }}</div>
            </div>

            <div class="boarding-content">
                <div class="details-section">
                    <div class="section-title">Event Information</div>
                    <div class="details-grid">
                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Event Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('l, d F Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Event Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Venue Location</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Attendee Name</div>
                            <div class="detail-value primary">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Ticket Number</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Seat/Section</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                            </div>
                        </div>

                        <div class="detail-pair">
                            <div class="detail-item">
                                <div class="detail-label">Order Reference</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Ticket Price</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-section">
                    <div class="qr-header">
                        <div class="qr-title">Validation QR</div>
                        <div class="qr-subtitle">Custom Security Code</div>
                    </div>
                    
                    <div class="qr-code">
                        CUSTOM<br>
                        QR CODE<br>
                        VALIDATION
                    </div>
                    
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-CST-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>

                    <div class="customization-note">
                        <p>This template can be customized with your brand colors, logo, and layout. Available exclusively for Platinum badge users.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="custom-footer">
            <div class="footer-section">
                <div class="footer-label">Issue Date</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y H:i') ?? now()->format('d M Y H:i') }}</div>
            </div>
            <div class="footer-section">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-section">
                <div class="footer-label">Category</div>
                <div class="footer-value">{{ $ticket->event->category->name ?? 'Event' }}</div>
            </div>
            <div class="tixara-custom">TiXara Custom</div>
        </div>
    </div>
</body>
</html>

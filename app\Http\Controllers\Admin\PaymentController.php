<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * Display payments management
     */
    public function index(Request $request)
    {
        $query = Order::with(['user', 'event', 'payment'])
            ->whereNotNull('payment_method');

        // Filter by payment status
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('order_number', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  })
                  ->orWhereHas('event', function($eventQuery) use ($search) {
                      $eventQuery->where('title', 'like', "%{$search}%");
                  });
            });
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        if (in_array($sortBy, ['created_at', 'total_amount', 'payment_status', 'status'])) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $payments = $query->paginate(20)->withQueryString();

        // Statistics
        $stats = [
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'pending_payments' => Order::where('payment_status', 'pending')->count(),
            'completed_payments' => Order::where('payment_status', 'paid')->count(),
            'failed_payments' => Order::where('payment_status', 'failed')->count(),
            'today_revenue' => Order::where('payment_status', 'paid')
                                   ->whereDate('created_at', today())
                                   ->sum('total_amount'),
            'today_payments' => Order::whereDate('created_at', today())->count(),
        ];

        return view('pages.admin.payments.index', compact('payments', 'stats'));
    }

    /**
     * Show payment details
     */
    public function show(Order $order)
    {
        $order->load(['user', 'event', 'tickets', 'payment']);
        return view('pages.admin.payments.show', compact('order'));
    }

    /**
     * Approve payment
     */
    public function approve(Order $order)
    {
        $order->update([
            'payment_status' => 'paid',
            'status' => 'completed'
        ]);

        // Create notification
        \App\Models\Notification::create([
            'user_id' => $order->user_id,
            'title' => 'Payment Approved',
            'message' => "Your payment for order #{$order->order_number} has been approved.",
            'type' => 'payment',
            'data' => [
                'order_id' => $order->id,
                'amount' => $order->total_amount
            ]
        ]);

        return redirect()->back()->with('success', 'Payment approved successfully!');
    }

    /**
     * Reject payment
     */
    public function reject(Order $order)
    {
        $order->update([
            'payment_status' => 'failed',
            'status' => 'cancelled'
        ]);

        // Create notification
        \App\Models\Notification::create([
            'user_id' => $order->user_id,
            'title' => 'Payment Rejected',
            'message' => "Your payment for order #{$order->order_number} has been rejected. Please contact support for more information.",
            'type' => 'payment',
            'data' => [
                'order_id' => $order->id,
                'amount' => $order->total_amount
            ]
        ]);

        return redirect()->back()->with('success', 'Payment rejected successfully!');
    }

    /**
     * Bulk approve payments
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'order_ids' => 'required|array',
            'order_ids.*' => 'exists:orders,id'
        ]);

        $approved = 0;
        foreach ($request->order_ids as $orderId) {
            $order = Order::find($orderId);
            if ($order && $order->payment_status === 'pending') {
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'completed'
                ]);

                // Create notification
                \App\Models\Notification::create([
                    'user_id' => $order->user_id,
                    'title' => 'Payment Approved',
                    'message' => "Your payment for order #{$order->order_number} has been approved.",
                    'type' => 'payment',
                    'data' => [
                        'order_id' => $order->id,
                        'amount' => $order->total_amount
                    ]
                ]);

                $approved++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$approved} payments approved successfully!"
        ]);
    }

    /**
     * Export payments
     */
    public function export(Request $request)
    {
        $query = Order::with(['user', 'event'])
            ->whereNotNull('payment_method');

        // Apply same filters as index
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Order Number',
            'Customer Name',
            'Customer Email',
            'Event Title',
            'Amount',
            'Payment Method',
            'Payment Status',
            'Order Status',
            'Created Date',
            'Updated Date'
        ];

        foreach ($payments as $payment) {
            $csvData[] = [
                $payment->order_number,
                $payment->user->name,
                $payment->user->email,
                $payment->event->title,
                'Rp ' . number_format($payment->total_amount, 0, ',', '.'),
                ucfirst(str_replace('_', ' ', $payment->payment_method)),
                ucfirst($payment->payment_status),
                ucfirst($payment->status),
                $payment->created_at->format('Y-m-d H:i:s'),
                $payment->updated_at->format('Y-m-d H:i:s')
            ];
        }

        $filename = 'payments_' . now()->format('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Check for payment updates (for real-time updates)
     */
    public function checkUpdates(Request $request)
    {
        $lastCheck = $request->get('last_check', now()->subMinutes(5));
        
        $hasUpdates = Order::where('updated_at', '>', $lastCheck)
                          ->whereNotNull('payment_method')
                          ->exists();

        return response()->json([
            'hasUpdates' => $hasUpdates,
            'timestamp' => now()->toISOString()
        ]);
    }
}

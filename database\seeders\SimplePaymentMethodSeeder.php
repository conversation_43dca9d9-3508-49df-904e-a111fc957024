<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentMethod;

class SimplePaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing payment methods
        PaymentMethod::truncate();

        // Create basic payment methods
        $paymentMethods = [
            [
                'name' => 'Bank Transfer',
                'code' => 'bank_transfer',
                'type' => 'manual',
                'category' => 'bank_transfer',
                'description' => 'Transfer to bank account',
                'icon' => 'fas fa-university',
                'fee_percentage' => 0,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => true,
                'is_active' => true,
                'sort_order' => 1,
                'instructions' => 'Transfer to the provided bank account and upload proof of payment'
            ],
            [
                'name' => 'GoPay',
                'code' => 'gopay',
                'type' => 'xendit',
                'category' => 'e_wallet',
                'description' => 'Pay with GoPay e-wallet',
                'icon' => 'fas fa-mobile-alt',
                'fee_percentage' => 0,
                'fee_fixed' => 2500,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 2,
                'instructions' => 'Scan QR code with GoPay app'
            ],
            [
                'name' => 'OVO',
                'code' => 'ovo',
                'type' => 'xendit',
                'category' => 'e_wallet',
                'description' => 'Pay with OVO e-wallet',
                'icon' => 'fas fa-wallet',
                'fee_percentage' => 0,
                'fee_fixed' => 2500,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 3,
                'instructions' => 'Enter your OVO phone number'
            ],
            [
                'name' => 'DANA',
                'code' => 'dana',
                'type' => 'xendit',
                'category' => 'e_wallet',
                'description' => 'Pay with DANA e-wallet',
                'icon' => 'fas fa-mobile-alt',
                'fee_percentage' => 0,
                'fee_fixed' => 2500,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 4,
                'instructions' => 'Scan QR code with DANA app'
            ],
            [
                'name' => 'BCA Virtual Account',
                'code' => 'bca_va',
                'type' => 'xendit',
                'category' => 'virtual_account',
                'description' => 'Pay via BCA Virtual Account',
                'icon' => 'fas fa-university',
                'fee_percentage' => 0,
                'fee_fixed' => 4000,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 5,
                'instructions' => 'Transfer to the provided BCA Virtual Account number'
            ],
            [
                'name' => 'BNI Virtual Account',
                'code' => 'bni_va',
                'type' => 'xendit',
                'category' => 'virtual_account',
                'description' => 'Pay via BNI Virtual Account',
                'icon' => 'fas fa-university',
                'fee_percentage' => 0,
                'fee_fixed' => 4000,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 6,
                'instructions' => 'Transfer to the provided BNI Virtual Account number'
            ],
            [
                'name' => 'Credit Card',
                'code' => 'credit_card',
                'type' => 'midtrans',
                'category' => 'credit_card',
                'description' => 'Pay with Visa, MasterCard, JCB',
                'icon' => 'fas fa-credit-card',
                'fee_percentage' => 2.9,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => *********,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 7,
                'instructions' => 'Enter your credit card details'
            ],
            [
                'name' => 'QRIS',
                'code' => 'qris',
                'type' => 'xendit',
                'category' => 'qris',
                'description' => 'Scan QR code with any e-wallet',
                'icon' => 'fas fa-qrcode',
                'fee_percentage' => 0.7,
                'fee_fixed' => 0,
                'min_amount' => 1500,
                'max_amount' => 10000000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 8,
                'instructions' => 'Scan QR code with your preferred e-wallet app'
            ],
            [
                'name' => 'Alfamart',
                'code' => 'alfamart',
                'type' => 'tripay',
                'category' => 'retail',
                'description' => 'Pay at Alfamart stores',
                'icon' => 'fas fa-store',
                'fee_percentage' => 0,
                'fee_fixed' => 2500,
                'min_amount' => 10000,
                'max_amount' => 2500000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 9,
                'instructions' => 'Show payment code at Alfamart cashier'
            ],
            [
                'name' => 'Indomaret',
                'code' => 'indomaret',
                'type' => 'tripay',
                'category' => 'retail',
                'description' => 'Pay at Indomaret stores',
                'icon' => 'fas fa-store',
                'fee_percentage' => 0,
                'fee_fixed' => 2500,
                'min_amount' => 10000,
                'max_amount' => 2500000,
                'is_manual' => false,
                'is_active' => true,
                'sort_order' => 10,
                'instructions' => 'Show payment code at Indomaret cashier'
            ]
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::create($method);
        }

        $this->command->info('Payment methods seeded successfully!');
    }
}

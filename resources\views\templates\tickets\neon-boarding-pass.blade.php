<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Orbitron', monospace;
            background: #0f0f23;
            color: #e5e7eb;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: #1a1a2e;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 
                0 0 50px rgba(139, 92, 246, 0.3),
                0 0 100px rgba(139, 92, 246, 0.1),
                inset 0 0 50px rgba(139, 92, 246, 0.05);
            border: 2px solid #8b5cf6;
            position: relative;
        }

        .neon-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #8b5cf6, #a78bfa, #c084fc, #e879f9, #8b5cf6);
            border-radius: 20px;
            z-index: -1;
            animation: neon-pulse 3s ease-in-out infinite;
        }

        @keyframes neon-pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .boarding-header {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #8b5cf6;
            padding: 32px;
            text-align: center;
            position: relative;
            border-bottom: 2px solid #8b5cf6;
        }

        .header-title {
            font-size: 36px;
            font-weight: 900;
            margin-bottom: 8px;
            letter-spacing: 4px;
            text-shadow: 
                0 0 10px #8b5cf6,
                0 0 20px #8b5cf6,
                0 0 30px #8b5cf6;
            animation: neon-flicker 2s ease-in-out infinite alternate;
        }

        @keyframes neon-flicker {
            0%, 100% { 
                text-shadow: 
                    0 0 10px #8b5cf6,
                    0 0 20px #8b5cf6,
                    0 0 30px #8b5cf6;
            }
            50% { 
                text-shadow: 
                    0 0 5px #8b5cf6,
                    0 0 10px #8b5cf6,
                    0 0 15px #8b5cf6;
            }
        }

        .header-subtitle {
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 3px;
            text-transform: uppercase;
            color: #a78bfa;
            text-shadow: 0 0 10px #a78bfa;
        }

        .boarding-body {
            padding: 40px;
            background: linear-gradient(135deg, #1a1a2e 0%, #0f0f23 100%);
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 40px;
            padding: 32px;
            background: rgba(139, 92, 246, 0.1);
            border: 2px solid #8b5cf6;
            border-radius: 16px;
            position: relative;
            box-shadow: 
                inset 0 0 20px rgba(139, 92, 246, 0.2),
                0 0 20px rgba(139, 92, 246, 0.3);
        }

        .event-title {
            font-size: 28px;
            font-weight: 700;
            color: #e5e7eb;
            margin-bottom: 16px;
            line-height: 1.2;
            text-shadow: 0 0 10px #8b5cf6;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            color: #0f0f23;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 2px;
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .details-panel {
            background: rgba(139, 92, 246, 0.05);
            border: 2px solid #8b5cf6;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 
                inset 0 0 20px rgba(139, 92, 246, 0.1),
                0 0 20px rgba(139, 92, 246, 0.2);
        }

        .panel-title {
            font-size: 20px;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 24px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #8b5cf6;
        }

        .details-grid {
            display: grid;
            gap: 20px;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #a78bfa;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .detail-item:hover {
            background: rgba(139, 92, 246, 0.2);
            box-shadow: 0 0 15px rgba(139, 92, 246, 0.4);
            transform: translateY(-2px);
        }

        .detail-label {
            font-size: 10px;
            font-weight: 700;
            color: #a78bfa;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
            text-shadow: 0 0 5px #a78bfa;
        }

        .detail-value {
            font-size: 14px;
            font-weight: 700;
            color: #e5e7eb;
            line-height: 1.3;
        }

        .detail-value.highlight {
            font-size: 16px;
            color: #8b5cf6;
            text-shadow: 0 0 10px #8b5cf6;
        }

        .qr-panel {
            background: rgba(139, 92, 246, 0.05);
            border: 2px solid #8b5cf6;
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            box-shadow: 
                inset 0 0 20px rgba(139, 92, 246, 0.1),
                0 0 20px rgba(139, 92, 246, 0.2);
        }

        .qr-title {
            font-size: 16px;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 0 10px #8b5cf6;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e);
            border: 3px solid #8b5cf6;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #8b5cf6;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            box-shadow: 
                0 0 30px rgba(139, 92, 246, 0.5),
                inset 0 0 20px rgba(139, 92, 246, 0.1);
            position: relative;
            overflow: hidden;
        }

        .qr-code::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.3), transparent);
            animation: qr-scan 2s linear infinite;
        }

        @keyframes qr-scan {
            0% { transform: translateX(-100%) translateY(-100%) rotate(0deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(360deg); }
        }

        .boarding-id {
            background: rgba(139, 92, 246, 0.1);
            border: 2px solid #8b5cf6;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 700;
            color: #8b5cf6;
            font-family: 'Orbitron', monospace;
            margin-top: 16px;
            text-shadow: 0 0 10px #8b5cf6;
            box-shadow: 0 0 15px rgba(139, 92, 246, 0.3);
        }

        .neon-footer {
            background: linear-gradient(135deg, #16213e, #1a1a2e);
            padding: 24px 40px;
            border-top: 2px solid #8b5cf6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-label {
            font-size: 10px;
            color: #a78bfa;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 700;
            text-shadow: 0 0 5px #a78bfa;
        }

        .footer-value {
            font-size: 12px;
            font-weight: 700;
            color: #e5e7eb;
        }

        .tixara-neon {
            font-size: 20px;
            font-weight: 900;
            color: #8b5cf6;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-shadow: 
                0 0 10px #8b5cf6,
                0 0 20px #8b5cf6;
            animation: neon-glow-text 2s ease-in-out infinite alternate;
        }

        @keyframes neon-glow-text {
            0% { 
                text-shadow: 
                    0 0 10px #8b5cf6,
                    0 0 20px #8b5cf6;
            }
            100% { 
                text-shadow: 
                    0 0 15px #8b5cf6,
                    0 0 25px #8b5cf6,
                    0 0 35px #8b5cf6;
            }
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .neon-footer {
                flex-direction: column;
                text-align: center;
                padding: 24px;
            }
        }

        @media print {
            body {
                background: white;
                color: black;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 2px solid #000;
            }
            
            .neon-glow,
            .qr-code::before {
                display: none;
            }
            
            * {
                text-shadow: none !important;
                animation: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="neon-glow"></div>
        
        <div class="boarding-header">
            <div class="header-title">NEON</div>
            <div class="header-subtitle">Cyberpunk Boarding Pass</div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Neon Event' }}</div>
            </div>

            <div class="boarding-content">
                <div class="details-panel">
                    <div class="panel-title">Event Data</div>
                    <div class="details-grid">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('d.M.Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }}</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Location</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">User</div>
                            <div class="detail-value highlight">{{ strtoupper($ticket->attendee_name) }}</div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Ticket ID</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Zone</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'GENERAL' }}</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Order</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Credits</div>
                                <div class="detail-value">{{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-title">Scan Code</div>
                    <div class="qr-code">
                        NEON<br>
                        CYBER<br>
                        SCAN
                    </div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-NEO-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="neon-footer">
            <div class="footer-info">
                <div class="footer-label">Generated</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d.m.Y H:i') ?? now()->format('d.m.Y H:i') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ strtoupper($ticket->status ?? 'ACTIVE') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Type</div>
                <div class="footer-value">{{ strtoupper($ticket->event->category->name ?? 'NEON') }}</div>
            </div>
            <div class="tixara-neon">TiXara Neon</div>
        </div>
    </div>
</body>
</html>

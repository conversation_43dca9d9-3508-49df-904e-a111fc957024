@extends('layouts.organizer')

@section('title', 'Create Ticket - Organizer Dashboard')

@section('content')
<div class="min-h-screen bg-dynamic-secondary py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-dynamic-primary">Create New Ticket</h1>
                    <p class="mt-2 text-dynamic-secondary">Add a new ticket for your events</p>
                </div>
                <a href="{{ route('organizer.events.index') }}" 
                   class="btn-theme-secondary">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Tickets
                </a>
            </div>
        </div>

        <!-- Create Form -->
        <div class="card-theme" data-aos="fade-up">
            <div class="p-6">
                <form action="{{ route('organizer.events.store') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <!-- Event Selection -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="event_id" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Event <span class="text-red-500">*</span>
                            </label>
                            <select name="event_id" id="event_id" required
                                    class="input-theme w-full">
                                <option value="">Select Your Event</option>
                                @if(isset($events))
                                    @foreach($events as $event)
                                        <option value="{{ $event->id }}" {{ old('event_id') == $event->id ? 'selected' : '' }}>
                                            {{ $event->title }} - {{ $event->date ? $event->date->format('M d, Y') : 'No date' }}
                                        </option>
                                    @endforeach
                                @endif
                            </select>
                            @error('event_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="ticket_type" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Ticket Type <span class="text-red-500">*</span>
                            </label>
                            <select name="ticket_type" id="ticket_type" required
                                    class="input-theme w-full">
                                <option value="">Select Type</option>
                                <option value="regular" {{ old('ticket_type') == 'regular' ? 'selected' : '' }}>Regular</option>
                                <option value="vip" {{ old('ticket_type') == 'vip' ? 'selected' : '' }}>VIP</option>
                                <option value="vvip" {{ old('ticket_type') == 'vvip' ? 'selected' : '' }}>VVIP</option>
                                <option value="early_bird" {{ old('ticket_type') == 'early_bird' ? 'selected' : '' }}>Early Bird</option>
                                <option value="student" {{ old('ticket_type') == 'student' ? 'selected' : '' }}>Student</option>
                            </select>
                            @error('ticket_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Ticket Details -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Ticket Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" 
                                   value="{{ old('name') }}" required
                                   placeholder="e.g., General Admission, VIP Access"
                                   class="input-theme w-full">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="price" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-3 top-3 text-dynamic-secondary">Rp</span>
                                <input type="number" name="price" id="price" 
                                       value="{{ old('price') }}" required min="0" step="1000"
                                       class="input-theme w-full pl-10">
                            </div>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="quantity" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Available Quantity <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="quantity" id="quantity" 
                                   value="{{ old('quantity') }}" required min="1"
                                   class="input-theme w-full">
                            @error('quantity')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Sale Period -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="sale_start" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Sale Start Date <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local" name="sale_start" id="sale_start" 
                                   value="{{ old('sale_start') }}" required
                                   class="input-theme w-full">
                            @error('sale_start')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="sale_end" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Sale End Date <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local" name="sale_end" id="sale_end" 
                                   value="{{ old('sale_end') }}" required
                                   class="input-theme w-full">
                            @error('sale_end')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Additional Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="max_per_order" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Max Tickets Per Order
                            </label>
                            <input type="number" name="max_per_order" id="max_per_order" 
                                   value="{{ old('max_per_order', 5) }}" min="1" max="20"
                                   class="input-theme w-full">
                            @error('max_per_order')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="status" class="block text-sm font-medium text-dynamic-primary mb-2">
                                Status <span class="text-red-500">*</span>
                            </label>
                            <select name="status" id="status" required
                                    class="input-theme w-full">
                                <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                <option value="sold_out" {{ old('status') == 'sold_out' ? 'selected' : '' }}>Sold Out</option>
                            </select>
                            @error('status')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-dynamic-primary mb-2">
                            Description
                        </label>
                        <textarea name="description" id="description" rows="4" 
                                  placeholder="Describe what's included with this ticket..."
                                  class="input-theme w-full">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Benefits/Features -->
                    <div>
                        <label for="features" class="block text-sm font-medium text-dynamic-primary mb-2">
                            Ticket Features/Benefits
                        </label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="checkbox" name="features[]" value="priority_entry" id="priority_entry"
                                       class="rounded border-gray-300 text-primary focus:ring-primary">
                                <label for="priority_entry" class="ml-2 text-sm text-dynamic-primary">Priority Entry</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="features[]" value="meet_greet" id="meet_greet"
                                       class="rounded border-gray-300 text-primary focus:ring-primary">
                                <label for="meet_greet" class="ml-2 text-sm text-dynamic-primary">Meet & Greet</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="features[]" value="merchandise" id="merchandise"
                                       class="rounded border-gray-300 text-primary focus:ring-primary">
                                <label for="merchandise" class="ml-2 text-sm text-dynamic-primary">Free Merchandise</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="features[]" value="parking" id="parking"
                                       class="rounded border-gray-300 text-primary focus:ring-primary">
                                <label for="parking" class="ml-2 text-sm text-dynamic-primary">Free Parking</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="features[]" value="food_beverage" id="food_beverage"
                                       class="rounded border-gray-300 text-primary focus:ring-primary">
                                <label for="food_beverage" class="ml-2 text-sm text-dynamic-primary">Food & Beverage</label>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex items-center justify-end space-x-4 pt-6 border-t border-dynamic">
                        <a href="{{ route('organizer.events.index') }}" 
                           class="px-6 py-3 border border-dynamic text-dynamic-primary rounded-lg hover:bg-dynamic-secondary transition-colors duration-200">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="btn-theme-primary">
                            <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                            Create Ticket
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-set sale dates based on event
    const eventSelect = document.getElementById('event_id');
    const saleStartInput = document.getElementById('sale_start');
    const saleEndInput = document.getElementById('sale_end');
    
    eventSelect.addEventListener('change', function() {
        if (this.value) {
            // Set sale start to now
            const now = new Date();
            saleStartInput.value = now.toISOString().slice(0, 16);
            
            // Set sale end to 1 hour before event (if event date is available)
            // This would need event date from the selected option
        }
    });
    
    // Validate sale dates
    saleStartInput.addEventListener('change', validateDates);
    saleEndInput.addEventListener('change', validateDates);
    
    function validateDates() {
        const startDate = new Date(saleStartInput.value);
        const endDate = new Date(saleEndInput.value);
        
        if (startDate && endDate && startDate >= endDate) {
            alert('Sale end date must be after sale start date');
            saleEndInput.value = '';
        }
    }
    
    // Format price input
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
    });
});
</script>
@endpush
@endsection

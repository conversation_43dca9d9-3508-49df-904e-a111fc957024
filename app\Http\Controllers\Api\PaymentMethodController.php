<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use App\Models\PaymentGateway;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentMethodController extends Controller
{
    /**
     * Get available payment methods
     */
    public function getAvailablePaymentMethods(): JsonResponse
    {
        try {
            // Get active payment methods
            $paymentMethods = PaymentMethod::where('is_active', true)
                ->orderBy('sort_order')
                ->get()
                ->map(function ($method) {
                    return [
                        'code' => $method->code,
                        'name' => $method->name,
                        'description' => $method->description,
                        'icon_class' => $method->icon ?? 'fas fa-credit-card',
                        'icon_bg_class' => $this->getIconBgClass($method->code),
                        'gateway_name' => ucfirst($method->type),
                        'fee' => $method->fee_percentage > 0 ? $method->fee_percentage : $method->fee_fixed,
                        'fee_type' => $method->fee_percentage > 0 ? 'percentage' : 'fixed',
                        'is_active' => $method->is_active,
                        'min_amount' => $method->min_amount,
                        'max_amount' => $method->max_amount,
                        'instructions' => $method->instructions
                    ];
                });

            return response()->json([
                'success' => true,
                'payment_methods' => $paymentMethods,
                'total_count' => $paymentMethods->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load payment methods',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test payment gateway connection
     */
    public function testGatewayConnection(Request $request): JsonResponse
    {
        $request->validate([
            'gateway_id' => 'required|exists:payment_gateways,id'
        ]);

        try {
            $gateway = PaymentGateway::findOrFail($request->gateway_id);

            $testResult = $this->performGatewayTest($gateway);

            return response()->json([
                'success' => $testResult['success'],
                'message' => $testResult['message'],
                'gateway_name' => $gateway->name,
                'test_details' => $testResult['details'] ?? null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment method details
     */
    public function getPaymentMethodDetails(string $code): JsonResponse
    {
        try {
            $paymentMethod = PaymentMethod::with(['gateway'])
                ->where('code', $code)
                ->where('is_active', true)
                ->first();

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found or inactive'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'payment_method' => [
                    'code' => $paymentMethod->code,
                    'name' => $paymentMethod->name,
                    'description' => $paymentMethod->description,
                    'instructions' => $paymentMethod->instructions,
                    'fee' => $paymentMethod->fee,
                    'fee_type' => $paymentMethod->fee_type,
                    'min_amount' => $paymentMethod->min_amount,
                    'max_amount' => $paymentMethod->max_amount,
                    'processing_time' => $paymentMethod->processing_time,
                    'gateway' => $paymentMethod->gateway ? [
                        'name' => $paymentMethod->gateway->name,
                        'description' => $paymentMethod->gateway->description,
                        'is_active' => $paymentMethod->gateway->is_active
                    ] : null
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment method details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate payment fee
     */
    public function calculateFee(Request $request): JsonResponse
    {
        $request->validate([
            'payment_method_code' => 'required|string',
            'amount' => 'required|numeric|min:0'
        ]);

        try {
            $paymentMethod = PaymentMethod::where('code', $request->payment_method_code)
                ->where('is_active', true)
                ->first();

            if (!$paymentMethod) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment method not found'
                ], 404);
            }

            $amount = $request->amount;
            $fee = $this->calculatePaymentFee($paymentMethod, $amount);

            return response()->json([
                'success' => true,
                'amount' => $amount,
                'fee' => $fee,
                'total' => $amount + $fee,
                'fee_type' => $paymentMethod->fee_type,
                'fee_description' => $this->getFeeDescription($paymentMethod, $fee)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate fee',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get icon background class for payment method
     */
    private function getIconBgClass(string $code): string
    {
        $bgClasses = [
            'bank_bca' => 'bg-blue-100 text-blue-600',
            'bank_mandiri' => 'bg-yellow-100 text-yellow-600',
            'bank_bni' => 'bg-orange-100 text-orange-600',
            'gopay' => 'bg-green-100 text-green-600',
            'ovo' => 'bg-purple-100 text-purple-600',
            'dana' => 'bg-blue-100 text-blue-600',
            'qris' => 'bg-indigo-100 text-indigo-600',
            'va_bca' => 'bg-blue-100 text-blue-600',
            'va_mandiri' => 'bg-yellow-100 text-yellow-600',
            'credit_card' => 'bg-purple-100 text-purple-600',
        ];

        return $bgClasses[$code] ?? 'bg-gray-100 text-gray-600';
    }

    /**
     * Get gateway badge class based on gateway type
     */
    private function getGatewayBadgeClass(?PaymentGateway $gateway): string
    {
        if (!$gateway) {
            return 'bg-gray-100 text-gray-800';
        }

        return match($gateway->code) {
            'xendit' => 'bg-blue-100 text-blue-800',
            'midtrans' => 'bg-green-100 text-green-800',
            'tripay' => 'bg-purple-100 text-purple-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Perform gateway connection test
     */
    private function performGatewayTest(PaymentGateway $gateway): array
    {
        try {
            switch ($gateway->code) {
                case 'xendit':
                    return $this->testXenditConnection($gateway);
                case 'midtrans':
                    return $this->testMidtransConnection($gateway);
                case 'tripay':
                    return $this->testTripayConnection($gateway);
                default:
                    return [
                        'success' => true,
                        'message' => 'Manual payment method - no connection test needed'
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Xendit connection
     */
    private function testXenditConnection(PaymentGateway $gateway): array
    {
        $config = $gateway->config;
        $apiKey = $config['secret_key'] ?? null;

        if (!$apiKey) {
            return [
                'success' => false,
                'message' => 'Xendit API key not configured'
            ];
        }

        try {
            $response = \Http::withBasicAuth($apiKey, '')
                ->timeout(10)
                ->get('https://api.xendit.co/balance');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Xendit connection successful',
                    'details' => [
                        'balance' => $response->json('balance'),
                        'status' => 'Connected'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Xendit connection failed: ' . $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Xendit connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Midtrans connection
     */
    private function testMidtransConnection(PaymentGateway $gateway): array
    {
        $config = $gateway->config;
        $serverKey = $config['server_key'] ?? null;

        if (!$serverKey) {
            return [
                'success' => false,
                'message' => 'Midtrans server key not configured'
            ];
        }

        try {
            $baseUrl = $config['is_production'] ? 'https://api.midtrans.com' : 'https://api.sandbox.midtrans.com';

            $response = \Http::withBasicAuth($serverKey, '')
                ->timeout(10)
                ->get($baseUrl . '/v2/ping');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Midtrans connection successful',
                    'details' => [
                        'environment' => $config['is_production'] ? 'Production' : 'Sandbox',
                        'status' => 'Connected'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Midtrans connection failed: ' . $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Midtrans connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Tripay connection
     */
    private function testTripayConnection(PaymentGateway $gateway): array
    {
        $config = $gateway->config;
        $apiKey = $config['api_key'] ?? null;

        if (!$apiKey) {
            return [
                'success' => false,
                'message' => 'Tripay API key not configured'
            ];
        }

        try {
            $baseUrl = $config['is_production'] ? 'https://tripay.co.id/api' : 'https://tripay.co.id/api-sandbox';

            $response = \Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey
            ])->timeout(10)->get($baseUrl . '/merchant/payment-channel');

            if ($response->successful()) {
                return [
                    'success' => true,
                    'message' => 'Tripay connection successful',
                    'details' => [
                        'environment' => $config['is_production'] ? 'Production' : 'Sandbox',
                        'channels_count' => count($response->json('data', [])),
                        'status' => 'Connected'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Tripay connection failed: ' . $response->body()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Tripay connection error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Calculate payment fee based on method configuration
     */
    private function calculatePaymentFee(PaymentMethod $method, float $amount): float
    {
        if ($method->fee_percentage > 0) {
            return round($amount * ($method->fee_percentage / 100));
        } else {
            return $method->fee_fixed;
        }
    }

    /**
     * Get fee description
     */
    private function getFeeDescription(PaymentMethod $method, float $fee): string
    {
        if ($method->fee_type === 'percentage') {
            return "Fee {$method->fee}% dari total pembayaran";
        } else {
            return "Fee tetap Rp " . number_format($fee, 0, ',', '.');
        }
    }
}

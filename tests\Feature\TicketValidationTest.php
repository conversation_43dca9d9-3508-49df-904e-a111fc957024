<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class TicketValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $organizer;
    protected $customer;
    protected $event;
    protected $order;
    protected $ticket;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->organizer = User::factory()->create(['role' => 'penjual']);
        $this->customer = User::factory()->create(['role' => 'pembeli']);

        // Create test event
        $this->event = Event::factory()->create([
            'organizer_id' => $this->organizer->id,
            'status' => 'published'
        ]);

        // Create test order
        $this->order = Order::factory()->create([
            'user_id' => $this->customer->id,
            'event_id' => $this->event->id,
            'payment_status' => 'paid',
            'status' => 'confirmed'
        ]);

        // Create test ticket
        $this->ticket = Ticket::factory()->create([
            'order_id' => $this->order->id,
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'user_id' => $this->customer->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function validation_page_is_accessible_to_authenticated_users()
    {
        $response = $this->actingAs($this->admin)
                         ->get('/validation');

        $response->assertStatus(200);
        $response->assertViewIs('pages.validation.index');
    }

    /** @test */
    public function validation_page_redirects_unauthenticated_users()
    {
        $response = $this->get('/validation');

        $response->assertRedirect('/login');
    }

    /** @test */
    public function authenticity_check_page_is_publicly_accessible()
    {
        $response = $this->get('/authenticity-check');

        $response->assertStatus(200);
        $response->assertViewIs('pages.authenticity.index');
    }

    /** @test */
    public function can_validate_ticket_with_qr_code()
    {
        $response = $this->actingAs($this->admin)
                         ->post('/validation/validate', [
                             'identifier' => $this->ticket->qr_code,
                             'type' => 'qr_code'
                         ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function can_validate_ticket_with_ticket_number()
    {
        $response = $this->actingAs($this->admin)
                         ->post('/validation/validate', [
                             'identifier' => $this->ticket->ticket_number,
                             'type' => 'ticket_number'
                         ]);

        $response->assertRedirect();
        $response->assertSessionHas('success');
    }

    /** @test */
    public function validation_requires_identifier()
    {
        $response = $this->actingAs($this->admin)
                         ->post('/validation/validate', [
                             'type' => 'qr_code'
                         ]);

        $response->assertSessionHasErrors(['identifier']);
    }

    /** @test */
    public function validation_requires_valid_type()
    {
        $response = $this->actingAs($this->admin)
                         ->post('/validation/validate', [
                             'identifier' => $this->ticket->qr_code,
                             'type' => 'invalid_type'
                         ]);

        $response->assertSessionHasErrors(['type']);
    }

    /** @test */
    public function can_check_ticket_authenticity()
    {
        $response = $this->post('/authenticity-check/verify', [
            'identifier' => $this->ticket->qr_code
        ]);

        $response->assertStatus(200);
        $response->assertViewIs('tickets.authenticity.result');
    }

    /** @test */
    public function authenticity_check_requires_identifier()
    {
        $response = $this->post('/authenticity-check/verify', []);

        $response->assertSessionHasErrors(['identifier']);
    }

    /** @test */
    public function can_get_validation_statistics()
    {
        $response = $this->actingAs($this->admin)
                         ->get('/validation/stats');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_tickets',
            'active_tickets',
            'used_tickets',
            'authentic_tickets',
            'fake_attempts',
            'today_validations',
            'this_week_validations'
        ]);
    }

    /** @test */
    public function can_get_recent_validations()
    {
        // Mark ticket as used
        $this->ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => $this->admin->id
        ]);

        $response = $this->actingAs($this->admin)
                         ->get('/validation/recent');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'ticket_number',
                'event_title',
                'attendee_name',
                'validated_at',
                'validator',
                'status'
            ]
        ]);
    }

    /** @test */
    public function can_bulk_validate_tickets()
    {
        // Create additional tickets
        $ticket2 = Ticket::factory()->create([
            'order_id' => $this->order->id,
            'event_id' => $this->event->id,
            'buyer_id' => $this->customer->id,
            'user_id' => $this->customer->id,
            'status' => 'active'
        ]);

        $response = $this->actingAs($this->admin)
                         ->post('/validation/bulk-validate', [
                             'ticket_numbers' => [
                                 $this->ticket->ticket_number,
                                 $ticket2->ticket_number
                             ]
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'results'
        ]);
    }

    /** @test */
    public function bulk_validation_requires_ticket_numbers_array()
    {
        $response = $this->actingAs($this->admin)
                         ->post('/validation/bulk-validate', [
                             'ticket_numbers' => 'not_an_array'
                         ]);

        $response->assertSessionHasErrors(['ticket_numbers']);
    }

    /** @test */
    public function can_verify_ticket_by_qr_code_publicly()
    {
        $response = $this->get("/tickets/verify/{$this->ticket->qr_code}");

        $response->assertStatus(200);
        $response->assertViewIs('tickets.verify');
        $response->assertViewHas('qrCode', $this->ticket->qr_code);
    }

    /** @test */
    public function validation_api_returns_json_when_requested()
    {
        $response = $this->actingAs($this->admin)
                         ->postJson('/validation/validate', [
                             'identifier' => $this->ticket->qr_code,
                             'type' => 'qr_code'
                         ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message'
        ]);
    }

    /** @test */
    public function authenticity_check_api_returns_json_when_requested()
    {
        $response = $this->postJson('/authenticity-check/verify', [
            'identifier' => $this->ticket->qr_code
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message'
        ]);
    }
}

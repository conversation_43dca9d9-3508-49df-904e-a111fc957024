@extends('layouts.admin')

@section('title', 'Payment Method Details')

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
/* Payment Method Show Styles */
.show-container {
    background: #F8FAFC;
    min-height: 100vh;
    padding: 24px;
}

.details-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 24px;
}

.card-header {
    background: #F8FAFC;
    padding: 24px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 24px;
    font-weight: 700;
    color: #1A1A1A;
    margin: 0;
}

.card-body {
    padding: 32px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-size: 14px;
    font-weight: 600;
    color: #6B7280;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 16px;
    color: #1A1A1A;
    font-weight: 500;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.status-badge.active {
    background: #D1FAE5;
    color: #065F46;
}

.status-badge.inactive {
    background: #FEE2E2;
    color: #991B1B;
}

.type-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.type-badge.manual {
    background: #FEF3C7;
    color: #92400E;
}

.type-badge.tripay {
    background: #DBEAFE;
    color: #1E40AF;
}

.type-badge.midtrans {
    background: #F3E8FF;
    color: #7C3AED;
}

.type-badge.xendit {
    background: #D1FAE5;
    color: #065F46;
}

.logo-display {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F9FAFB;
}

.logo-display img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.logo-display i {
    font-size: 24px;
    color: #6B7280;
}

.config-section {
    background: #F8FAFC;
    border-radius: 8px;
    padding: 20px;
    margin-top: 16px;
}

.config-title {
    font-size: 16px;
    font-weight: 600;
    color: #1A1A1A;
    margin: 0 0 16px 0;
}

.config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.config-item {
    display: flex;
    flex-direction: column;
}

.config-label {
    font-size: 12px;
    font-weight: 600;
    color: #6B7280;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.config-value {
    font-size: 14px;
    color: #1A1A1A;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #E5E7EB;
}

.btn-primary {
    background: #0066CC;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    background: #0052A3;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6B7280;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-secondary:hover {
    background: #4B5563;
    color: white;
    text-decoration: none;
}

.btn-success {
    background: #10B981;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-success:hover {
    background: #059669;
}

.btn-warning {
    background: #F59E0B;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-warning:hover {
    background: #D97706;
}

.actions-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.section-divider {
    height: 1px;
    background: #E5E7EB;
    margin: 32px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .show-container {
        padding: 16px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .config-grid {
        grid-template-columns: 1fr;
    }

    .card-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .actions-group {
        width: 100%;
        justify-content: flex-start;
    }
}
</style>
@endpush

@section('content')
<div class="show-container">
    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
        <div>
            <h1 style="font-size: 28px; font-weight: 700; color: #1A1A1A; margin: 0 0 8px 0;">Payment Method Details</h1>
            <p style="color: #6B7280; margin: 0;">View and manage payment method configuration</p>
        </div>
        <a href="{{ route('admin.payment-methods.index') }}" class="btn-secondary">
            <i class="fas fa-arrow-left"></i>
            Back to List
        </a>
    </div>

    <!-- Payment Method Details -->
    <div class="details-card">
        <div class="card-header">
            <div style="display: flex; align-items: center; gap: 16px;">
                <div class="logo-display">
                    @if($paymentMethod->logo)
                        <img src="{{ asset($paymentMethod->logo) }}" alt="{{ $paymentMethod->name }}">
                    @else
                        <i class="{{ $paymentMethod->icon ?? 'fas fa-credit-card' }}"></i>
                    @endif
                </div>
                <div>
                    <h2 class="card-title">{{ $paymentMethod->name }}</h2>
                    <p style="color: #6B7280; margin: 4px 0 0 0;">{{ $paymentMethod->description }}</p>
                </div>
            </div>

            <div class="actions-group">
                <div class="status-badge {{ $paymentMethod->is_active ? 'active' : 'inactive' }}">
                    <i class="fas fa-{{ $paymentMethod->is_active ? 'check-circle' : 'times-circle' }}"></i>
                    {{ $paymentMethod->is_active ? 'Active' : 'Inactive' }}
                </div>

                @if(!$paymentMethod->is_manual && !$paymentMethod->isConfigured())
                    <button type="button" class="btn-warning" onclick="testGateway({{ $paymentMethod->id }})">
                        <i class="fas fa-exclamation-triangle"></i>
                        Not Configured
                    </button>
                @elseif(!$paymentMethod->is_manual)
                    <button type="button" class="btn-success" onclick="testGateway({{ $paymentMethod->id }})">
                        <i class="fas fa-flask"></i>
                        Test Gateway
                    </button>
                @endif

                <a href="{{ route('admin.payment-methods.edit', $paymentMethod) }}" class="btn-primary">
                    <i class="fas fa-edit"></i>
                    Edit
                </a>
            </div>
        </div>

        <div class="card-body">
            <!-- Basic Information -->
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Code</div>
                    <div class="info-value">{{ $paymentMethod->code }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">Type</div>
                    <div class="info-value">
                        <span class="type-badge {{ $paymentMethod->type }}">
                            <i class="fas fa-{{ $paymentMethod->is_manual ? 'hand-paper' : 'plug' }}"></i>
                            {{ $paymentMethod->type_label }}
                        </span>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-label">Category</div>
                    <div class="info-value">{{ $paymentMethod->category_label }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">Sort Order</div>
                    <div class="info-value">{{ $paymentMethod->sort_order }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">Fee</div>
                    <div class="info-value">{{ $paymentMethod->formatted_fee }}</div>
                </div>

                <div class="info-item">
                    <div class="info-label">Amount Limits</div>
                    <div class="info-value">{{ $paymentMethod->formatted_limits }}</div>
                </div>
            </div>

            <div class="section-divider"></div>

            <!-- Configuration Details -->
            @if($paymentMethod->is_manual && $paymentMethod->manual_config)
                <div class="config-section">
                    <h3 class="config-title">Manual Payment Configuration</h3>

                    <div class="config-grid">
                        @if(isset($paymentMethod->manual_config['bank_name']))
                            <div class="config-item">
                                <div class="config-label">Bank Name</div>
                                <div class="config-value">{{ $paymentMethod->manual_config['bank_name'] }}</div>
                            </div>
                        @endif

                        @if(isset($paymentMethod->manual_config['account_number']))
                            <div class="config-item">
                                <div class="config-label">Account Number</div>
                                <div class="config-value">{{ $paymentMethod->manual_config['account_number'] }}</div>
                            </div>
                        @endif

                        @if(isset($paymentMethod->manual_config['account_name']))
                            <div class="config-item">
                                <div class="config-label">Account Name</div>
                                <div class="config-value">{{ $paymentMethod->manual_config['account_name'] }}</div>
                            </div>
                        @endif

                        @if(isset($paymentMethod->manual_config['bank_code']))
                            <div class="config-item">
                                <div class="config-label">Bank Code</div>
                                <div class="config-value">{{ $paymentMethod->manual_config['bank_code'] }}</div>
                            </div>
                        @endif
                    </div>

                    @if($paymentMethod->instructions)
                        <div style="margin-top: 20px;">
                            <div class="config-label">Payment Instructions</div>
                            <div style="background: white; padding: 16px; border-radius: 8px; border: 1px solid #E5E7EB; margin-top: 8px;">
                                <div style="white-space: pre-wrap; font-size: 14px; line-height: 1.6;">{{ $paymentMethod->instructions }}</div>
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            @if(!$paymentMethod->is_manual && $paymentMethod->config)
                <div class="config-section">
                    <h3 class="config-title">Gateway Configuration</h3>

                    <div class="config-grid">
                        @if($paymentMethod->type === 'tripay')
                            @if(isset($paymentMethod->config['api_key']))
                                <div class="config-item">
                                    <div class="config-label">API Key</div>
                                    <div class="config-value">{{ str_repeat('*', max(0, strlen($paymentMethod->config['api_key']) - 8)) . substr($paymentMethod->config['api_key'], -8) }}</div>
                                </div>
                            @endif

                            @if(isset($paymentMethod->config['private_key']))
                                <div class="config-item">
                                    <div class="config-label">Private Key</div>
                                    <div class="config-value">{{ str_repeat('*', max(0, strlen($paymentMethod->config['private_key']) - 8)) . substr($paymentMethod->config['private_key'], -8) }}</div>
                                </div>
                            @endif

                            @if(isset($paymentMethod->config['merchant_code']))
                                <div class="config-item">
                                    <div class="config-label">Merchant Code</div>
                                    <div class="config-value">{{ $paymentMethod->config['merchant_code'] }}</div>
                                </div>
                            @endif
                        @endif

                        @if($paymentMethod->type === 'midtrans')
                            @if(isset($paymentMethod->config['server_key']))
                                <div class="config-item">
                                    <div class="config-label">Server Key</div>
                                    <div class="config-value">{{ str_repeat('*', max(0, strlen($paymentMethod->config['server_key']) - 8)) . substr($paymentMethod->config['server_key'], -8) }}</div>
                                </div>
                            @endif

                            @if(isset($paymentMethod->config['client_key']))
                                <div class="config-item">
                                    <div class="config-label">Client Key</div>
                                    <div class="config-value">{{ str_repeat('*', max(0, strlen($paymentMethod->config['client_key']) - 8)) . substr($paymentMethod->config['client_key'], -8) }}</div>
                                </div>
                            @endif
                        @endif

                        @if($paymentMethod->type === 'xendit')
                            @if(isset($paymentMethod->config['secret_key']))
                                <div class="config-item">
                                    <div class="config-label">Secret Key</div>
                                    <div class="config-value">{{ str_repeat('*', max(0, strlen($paymentMethod->config['secret_key']) - 8)) . substr($paymentMethod->config['secret_key'], -8) }}</div>
                                </div>
                            @endif
                        @endif
                    </div>
                </div>
            @endif

            <!-- Fee Calculation Details -->
            <div class="section-divider"></div>

            <div class="config-section">
                <h3 class="config-title">Fee Calculation</h3>

                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">Percentage Fee</div>
                        <div class="config-value">{{ $paymentMethod->fee_percentage }}%</div>
                    </div>

                    <div class="config-item">
                        <div class="config-label">Fixed Fee</div>
                        <div class="config-value">Rp {{ number_format($paymentMethod->fee_fixed, 0, ',', '.') }}</div>
                    </div>

                    <div class="config-item">
                        <div class="config-label">Minimum Amount</div>
                        <div class="config-value">Rp {{ number_format($paymentMethod->min_amount, 0, ',', '.') }}</div>
                    </div>

                    @if($paymentMethod->max_amount)
                        <div class="config-item">
                            <div class="config-label">Maximum Amount</div>
                            <div class="config-value">Rp {{ number_format($paymentMethod->max_amount, 0, ',', '.') }}</div>
                        </div>
                    @endif
                </div>

                <!-- Fee Examples -->
                <div style="margin-top: 20px;">
                    <div class="config-label">Fee Examples</div>
                    <div style="background: white; padding: 16px; border-radius: 8px; border: 1px solid #E5E7EB; margin-top: 8px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            @php
                                $examples = [50000, 100000, 500000, 1000000];
                            @endphp
                            @foreach($examples as $amount)
                                @if($paymentMethod->isAmountValid($amount))
                                    @php
                                        $feeCalc = $paymentMethod->calculateFee($amount);
                                    @endphp
                                    <div style="text-align: center; padding: 12px; background: #F8FAFC; border-radius: 6px;">
                                        <div style="font-weight: 600; color: #1A1A1A;">Rp {{ number_format($amount, 0, ',', '.') }}</div>
                                        <div style="font-size: 12px; color: #6B7280; margin: 4px 0;">Fee: Rp {{ number_format($feeCalc['total_fee'], 0, ',', '.') }}</div>
                                        <div style="font-size: 12px; color: #059669; font-weight: 600;">Total: Rp {{ number_format($feeCalc['amount_with_fee'], 0, ',', '.') }}</div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function testGateway(methodId) {
    const testBtn = event.target;
    const originalHtml = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;

    fetch(`/admin/payment-methods/${methodId}/test-gateway`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        const type = data.success ? 'success' : 'error';
        showToast(data.message, type);

        if (data.success) {
            testBtn.className = 'btn-success';
            testBtn.innerHTML = '<i class="fas fa-check-circle"></i> Connected';
        } else {
            testBtn.className = 'btn-warning';
            testBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Failed';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to test gateway', 'error');
        testBtn.innerHTML = originalHtml;
    })
    .finally(() => {
        testBtn.disabled = false;
        setTimeout(() => {
            testBtn.innerHTML = originalHtml;
            testBtn.className = testBtn.className.replace('btn-warning', 'btn-success').replace('btn-success', 'btn-success');
        }, 3000);
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        font-weight: 600;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
@endpush
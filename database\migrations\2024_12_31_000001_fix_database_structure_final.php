<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // Disable foreign key checks temporarily
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            // 1. Ensure events table exists with correct structure
            if (!Schema::hasTable('events')) {
                Schema::create('events', function (Blueprint $table) {
                    $table->id();
                    $table->string('title');
                    $table->string('slug')->unique();
                    $table->text('description');
                    $table->text('short_description')->nullable();
                    $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
                    $table->foreignId('organizer_id')->constrained('users')->onDelete('cascade');

                    // Event Details
                    $table->string('venue_name');
                    $table->text('venue_address');
                    $table->string('city');
                    $table->string('province')->default('DKI Jakarta');
                    $table->decimal('latitude', 10, 8)->nullable();
                    $table->decimal('longitude', 11, 8)->nullable();

                    // Date & Time
                    $table->datetime('start_date');
                    $table->datetime('end_date');
                    $table->string('timezone')->default('Asia/Jakarta');

                    // Pricing
                    $table->decimal('price', 12, 2)->default(0);
                    $table->decimal('original_price', 12, 2)->nullable();

                    // Capacity
                    $table->integer('total_capacity');
                    $table->integer('available_capacity');
                    $table->integer('min_purchase')->default(1);
                    $table->integer('max_purchase')->default(10);

                    // Media
                    $table->string('poster')->nullable();
                    $table->json('gallery')->nullable();

                    // Status & Features
                    $table->enum('status', ['draft', 'published', 'cancelled', 'completed'])->default('draft');
                    $table->boolean('is_featured')->default(false);
                    $table->boolean('is_free')->default(false);
                    $table->boolean('requires_approval')->default(false);

                    // SEO
                    $table->string('meta_title')->nullable();
                    $table->text('meta_description')->nullable();
                    $table->json('tags')->nullable();

                    // Sale Period
                    $table->datetime('sale_start_date')->nullable();
                    $table->datetime('sale_end_date')->nullable();

                    $table->timestamps();

                    // Indexes
                    $table->index(['status', 'start_date']);
                    $table->index(['category_id', 'status']);
                    $table->index(['organizer_id', 'status']);
                    $table->index(['city', 'status']);
                    $table->index('is_featured');
                    $table->index('slug');
                });
                echo "✓ Created 'events' table\n";
            } else {
                echo "✓ Events table already exists\n";
            }

            // 2. Ensure tickets table exists with correct structure (for purchased tickets)
            if (!Schema::hasTable('tickets')) {
                Schema::create('tickets', function (Blueprint $table) {
                $table->id();
                $table->string('ticket_number')->unique(); // TIK-YYYYMMDD-XXXXX
                $table->string('qr_code')->unique(); // QR Code string
                $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
                $table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');

                    // Ticket Details
                $table->string('attendee_name');
                $table->string('attendee_email');
                $table->string('attendee_phone')->nullable();

                    // Pricing
                $table->decimal('price', 12, 2);
                $table->decimal('admin_fee', 12, 2)->default(0);
                $table->decimal('total_paid', 12, 2);

                    // Status & Validation
                $table->enum('status', ['active', 'used', 'cancelled', 'refunded'])->default('active');
                $table->datetime('used_at')->nullable();
                $table->foreignId('validated_by')->nullable()->constrained('users');
                $table->text('validation_notes')->nullable();

                    // Download & Access
                $table->string('download_token')->nullable();
                $table->integer('download_count')->default(0);
                $table->datetime('last_downloaded_at')->nullable();

                $table->timestamps();

                    // Indexes
                $table->index(['event_id', 'status']);
                $table->index(['buyer_id', 'status']);
                $table->index(['order_id']);
                $table->index('qr_code');
                $table->index('ticket_number');
                });
                echo "✓ Created 'tickets' table\n";
            } else {
                echo "✓ Tickets table already exists\n";
            }

            // 3. Create ticket_validations table if it doesn't exist
            if (!Schema::hasTable('ticket_validations')) {
                Schema::create('ticket_validations', function (Blueprint $table) {
                $table->id();
                $table->foreignId('ticket_id')->constrained('tickets')->onDelete('cascade');
                $table->foreignId('validated_by')->constrained('users')->onDelete('cascade');
                $table->enum('status', ['valid', 'invalid'])->default('valid');
                $table->datetime('validated_at');
                $table->text('notes')->nullable();
                $table->timestamps();

                $table->index(['ticket_id', 'status']);
                $table->index(['validated_by', 'validated_at']);
                });
                echo "✓ Created 'ticket_validations' table\n";
            } else {
                echo "✓ Ticket validations table already exists\n";
            }

            // 4. Fix any incorrect table structure
            $this->fixTableStructure();

            // 5. Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');

            echo "✅ Database structure fixed successfully!\n";

        } catch (Exception $e) {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            echo "❌ Error fixing database structure: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * Fix table structure issues
     */
    private function fixTableStructure(): void
    {
        try {
            // Check if tickets table has wrong structure (like events)
            if (Schema::hasTable('tickets')) {
                $columns = Schema::getColumnListing('tickets');

                // If tickets table has 'title' column, it means it has wrong structure
                if (in_array('title', $columns)) {
                    echo "⚠️  Tickets table has wrong structure, fixing...\n";

                    // Rename the wrong tickets table to events if events doesn't exist
                    if (!Schema::hasTable('events')) {
                        DB::statement('RENAME TABLE tickets TO events');
                        echo "✓ Renamed incorrect 'tickets' table to 'events'\n";

                        // Now create the correct tickets table
                        Schema::create('tickets', function (Blueprint $table) {
                $table->id();
                $table->string('ticket_number')->unique();
                $table->string('qr_code')->unique();
                $table->foreignId('event_id')->constrained('events')->onDelete('cascade');
                $table->foreignId('buyer_id')->constrained('users')->onDelete('cascade');
                $table->foreignId('order_id')->constrained('orders')->onDelete('cascade');
                $table->string('attendee_name');
                $table->string('attendee_email');
                $table->string('attendee_phone')->nullable();
                $table->decimal('price', 12, 2);
                $table->decimal('admin_fee', 12, 2)->default(0);
                $table->decimal('total_paid', 12, 2);
                $table->enum('status', ['active', 'used', 'cancelled', 'refunded'])->default('active');
                $table->datetime('used_at')->nullable();
                $table->foreignId('validated_by')->nullable()->constrained('users');
                $table->text('validation_notes')->nullable();
                $table->string('download_token')->nullable();
                $table->integer('download_count')->default(0);
                $table->datetime('last_downloaded_at')->nullable();
                $table->timestamps();

                $table->index(['event_id', 'status']);
                $table->index(['buyer_id', 'status']);
                $table->index(['order_id']);
                $table->index('qr_code');
                $table->index('ticket_number');
                        });
                        echo "✓ Created correct 'tickets' table\n";
                    }
                }
            }

        } catch (Exception $e) {
            echo "⚠️  Error fixing table structure: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration fixes critical structure issues
        // Reversing could cause data loss, so we don't implement down()
        echo "⚠️  This migration cannot be reversed as it fixes critical database structure issues.\n";
    }
};

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGateway;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentGatewayController extends Controller
{
    /**
     * Display payment gateway management page
     */
    public function index()
    {
        $gateways = PaymentGateway::with(['paymentMethods'])
            ->orderBy('sort_order')
            ->get();

        $paymentMethods = PaymentMethod::with(['gateway'])
            ->orderBy('sort_order')
            ->get();

        return view('admin.payment-gateways.index', compact('gateways', 'paymentMethods'));
    }

    /**
     * Show gateway configuration form
     */
    public function show(PaymentGateway $gateway)
    {
        $gateway->load(['paymentMethods']);
        return view('admin.payment-gateways.show', compact('gateway'));
    }

    /**
     * Update gateway configuration
     */
    public function update(Request $request, PaymentGateway $gateway)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'is_production' => 'boolean',
            'config' => 'array'
        ]);

        $gateway->update([
            'name' => $request->name,
            'description' => $request->description,
            'is_active' => $request->boolean('is_active'),
            'is_production' => $request->boolean('is_production'),
            'config' => $request->config ?? []
        ]);

        return redirect()->route('admin.payment-gateways.index')
            ->with('success', 'Payment gateway updated successfully');
    }

    /**
     * Test gateway connection
     */
    public function testConnection(PaymentGateway $gateway): JsonResponse
    {
        try {
            $result = $gateway->testConnection();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'] ?? null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle gateway status
     */
    public function toggleStatus(PaymentGateway $gateway): JsonResponse
    {
        try {
            $gateway->update([
                'is_active' => !$gateway->is_active
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Gateway status updated successfully',
                'is_active' => $gateway->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update gateway status'
            ], 500);
        }
    }

    /**
     * Update payment method status
     */
    public function updateMethodStatus(Request $request, PaymentMethod $method): JsonResponse
    {
        try {
            $method->update([
                'is_active' => $request->boolean('is_active')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment method status updated successfully',
                'is_active' => $method->is_active
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment method status'
            ], 500);
        }
    }

    /**
     * Update payment method configuration
     */
    public function updateMethod(Request $request, PaymentMethod $method)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fee' => 'required|numeric|min:0',
            'fee_type' => 'required|in:fixed,percentage',
            'min_amount' => 'nullable|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'processing_time' => 'nullable|string',
            'instructions' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $method->update([
            'name' => $request->name,
            'description' => $request->description,
            'fee' => $request->fee,
            'fee_type' => $request->fee_type,
            'min_amount' => $request->min_amount,
            'max_amount' => $request->max_amount,
            'processing_time' => $request->processing_time,
            'instructions' => $request->instructions,
            'is_active' => $request->boolean('is_active')
        ]);

        return redirect()->route('admin.payment-gateways.index')
            ->with('success', 'Payment method updated successfully');
    }

    /**
     * Get gateway statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_gateways' => PaymentGateway::count(),
                'active_gateways' => PaymentGateway::where('is_active', true)->count(),
                'production_gateways' => PaymentGateway::where('is_production', true)->count(),
                'total_methods' => PaymentMethod::count(),
                'active_methods' => PaymentMethod::where('is_active', true)->count(),
                'methods_by_gateway' => PaymentGateway::withCount(['paymentMethods' => function ($query) {
                    $query->where('is_active', true);
                }])->get()->map(function ($gateway) {
                    return [
                        'name' => $gateway->name,
                        'active_methods' => $gateway->payment_methods_count,
                        'is_active' => $gateway->is_active
                    ];
                })
            ];

            return response()->json([
                'success' => true,
                'statistics' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics'
            ], 500);
        }
    }

    /**
     * Bulk update gateway status
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'gateway_ids' => 'required|array',
            'gateway_ids.*' => 'exists:payment_gateways,id',
            'is_active' => 'required|boolean'
        ]);

        try {
            PaymentGateway::whereIn('id', $request->gateway_ids)
                ->update(['is_active' => $request->is_active]);

            return response()->json([
                'success' => true,
                'message' => 'Gateway status updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update gateway status'
            ], 500);
        }
    }

    /**
     * Get available payment channels for gateway
     */
    public function getAvailableChannels(PaymentGateway $gateway): JsonResponse
    {
        try {
            $channels = $gateway->getAvailableChannels();

            return response()->json([
                'success' => true,
                'channels' => $channels
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get available channels'
            ], 500);
        }
    }

    /**
     * Export gateway configuration
     */
    public function exportConfig(PaymentGateway $gateway)
    {
        $config = [
            'gateway' => [
                'code' => $gateway->code,
                'name' => $gateway->name,
                'description' => $gateway->description,
                'provider' => $gateway->provider,
                'is_active' => $gateway->is_active,
                'is_production' => $gateway->is_production
            ],
            'payment_methods' => $gateway->paymentMethods->map(function ($method) {
                return [
                    'code' => $method->code,
                    'name' => $method->name,
                    'description' => $method->description,
                    'fee' => $method->fee,
                    'fee_type' => $method->fee_type,
                    'is_active' => $method->is_active
                ];
            }),
            'exported_at' => now()->toISOString()
        ];

        $filename = "gateway-config-{$gateway->code}-" . now()->format('Y-m-d-H-i-s') . '.json';

        return response()->json($config)
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }
}

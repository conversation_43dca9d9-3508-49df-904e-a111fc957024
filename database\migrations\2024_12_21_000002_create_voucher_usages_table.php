<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('voucher_usages')) {
            Schema::create('voucher_usages', function (Blueprint $table) {
                $table->id();
                $table->foreignId('voucher_id')->constrained()->onDelete('cascade');
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('order_id')->constrained()->onDelete('cascade');
                $table->foreignId('event_id')->constrained('events')->onDelete('cascade');

            // Usage Details
                $table->decimal('original_amount', 12, 2); // original order amount
                $table->decimal('discount_amount', 12, 2); // actual discount applied
                $table->decimal('final_amount', 12, 2); // final amount after discount
                $table->integer('tickets_quantity'); // number of tickets in order

            // Metadata
                $table->json('voucher_snapshot')->nullable(); // snapshot of voucher at time of use
                $table->string('applied_via')->default('manual'); // manual, auto, campaign
                $table->timestamp('used_at');

                $table->timestamps();

            // Indexes
                $table->index(['voucher_id', 'user_id']);
                $table->index(['user_id', 'used_at']);
                $table->index(['event_id', 'used_at']);
                $table->unique(['voucher_id', 'order_id']); // prevent duplicate usage on same order
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voucher_usages');
    }
};

@extends('layouts.app')

@section('title', 'QR Scanner Test')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">QR Scanner Test</h1>
            <p class="text-gray-600 dark:text-gray-400">Test QR code generation and scanning functionality</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- QR Code Generator -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Generate Test QR Codes</h2>
                
                <!-- Sample Tickets -->
                <div class="space-y-4 mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sample Tickets</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Valid Ticket -->
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-green-600 dark:text-green-400 mb-2">Valid Ticket</h4>
                            <div id="validQR" class="text-center mb-3"></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <p><strong>Ticket:</strong> TIX-VALID-001</p>
                                <p><strong>Event:</strong> Test Event</p>
                                <p><strong>Status:</strong> Active</p>
                            </div>
                            <button onclick="generateValidQR()" class="w-full mt-3 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Generate Valid QR
                            </button>
                        </div>

                        <!-- Invalid Ticket -->
                        <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                            <h4 class="font-medium text-red-600 dark:text-red-400 mb-2">Invalid Ticket</h4>
                            <div id="invalidQR" class="text-center mb-3"></div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <p><strong>Ticket:</strong> TIX-INVALID-001</p>
                                <p><strong>Event:</strong> Expired Event</p>
                                <p><strong>Status:</strong> Used/Expired</p>
                            </div>
                            <button onclick="generateInvalidQR()" class="w-full mt-3 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors">
                                Generate Invalid QR
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Custom QR Generator -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Custom QR Generator</h3>
                    <form id="customQRForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ticket Number</label>
                            <input type="text" id="customTicketNumber" placeholder="TIX-CUSTOM-001" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Name</label>
                            <input type="text" id="customEventName" placeholder="Custom Test Event" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                            <select id="customStatus" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <option value="active">Active</option>
                                <option value="used">Used</option>
                                <option value="cancelled">Cancelled</option>
                                <option value="expired">Expired</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                            Generate Custom QR
                        </button>
                    </form>
                    <div id="customQR" class="text-center mt-4"></div>
                </div>
            </div>

            <!-- Scanner Test -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Scanner Test</h2>
                
                <!-- Test Results -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Test Results</h3>
                    <div id="testResults" class="space-y-3">
                        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
                            <i class="fas fa-clipboard-list text-3xl mb-2"></i>
                            <p>No tests run yet</p>
                        </div>
                    </div>
                </div>

                <!-- Manual Test Input -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Manual Test</h3>
                    <form id="manualTestForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Test QR Code</label>
                            <input type="text" id="testQRInput" placeholder="Enter QR code or ticket number" 
                                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        </div>
                        <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors">
                            Test Validation
                        </button>
                    </form>
                </div>

                <!-- Quick Test Buttons -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Tests</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <button onclick="testValidTicket()" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
                            Test Valid
                        </button>
                        <button onclick="testInvalidTicket()" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
                            Test Invalid
                        </button>
                        <button onclick="testUsedTicket()" class="bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
                            Test Used
                        </button>
                        <button onclick="testExpiredTicket()" class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors text-sm">
                            Test Expired
                        </button>
                    </div>
                </div>

                <!-- Performance Test -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Test</h3>
                    <button onclick="runPerformanceTest()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg transition-colors">
                        Run Performance Test (100 validations)
                    </button>
                    <div id="performanceResults" class="mt-4 text-sm text-gray-600 dark:text-gray-400"></div>
                </div>
            </div>
        </div>

        <!-- Test Statistics -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">Test Statistics</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400" id="successCount">0</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Successful Tests</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-600 dark:text-red-400" id="failureCount">0</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Failed Tests</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="totalTests">0</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total Tests</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-purple-600 dark:text-purple-400" id="avgResponseTime">0ms</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Avg Response Time</div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script>
class QRScannerTester {
    constructor() {
        this.successCount = 0;
        this.failureCount = 0;
        this.totalTests = 0;
        this.responseTimes = [];
        
        this.bindEvents();
    }
    
    bindEvents() {
        document.getElementById('customQRForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.generateCustomQR();
        });
        
        document.getElementById('manualTestForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.testManualInput();
        });
    }
    
    async generateQR(data, containerId) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';
        
        try {
            const canvas = document.createElement('canvas');
            await QRCode.toCanvas(canvas, JSON.stringify(data), {
                width: 150,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            });
            container.appendChild(canvas);
        } catch (error) {
            console.error('Error generating QR code:', error);
            container.innerHTML = '<p class="text-red-500">Error generating QR</p>';
        }
    }
    
    generateValidQR() {
        const data = {
            ticket_number: 'TIX-VALID-001',
            event_id: 1,
            event_title: 'Test Event',
            buyer_name: 'Test User',
            price: 100000,
            status: 'active',
            qr_code: 'TIX-VALID-001'
        };
        this.generateQR(data, 'validQR');
    }
    
    generateInvalidQR() {
        const data = {
            ticket_number: 'TIX-INVALID-001',
            event_id: 999,
            event_title: 'Expired Event',
            buyer_name: 'Test User',
            price: 50000,
            status: 'used',
            qr_code: 'TIX-INVALID-001'
        };
        this.generateQR(data, 'invalidQR');
    }
    
    generateCustomQR() {
        const ticketNumber = document.getElementById('customTicketNumber').value;
        const eventName = document.getElementById('customEventName').value;
        const status = document.getElementById('customStatus').value;
        
        if (!ticketNumber) {
            alert('Please enter a ticket number');
            return;
        }
        
        const data = {
            ticket_number: ticketNumber,
            event_id: Math.floor(Math.random() * 100),
            event_title: eventName || 'Custom Test Event',
            buyer_name: 'Test User',
            price: Math.floor(Math.random() * 500000),
            status: status,
            qr_code: ticketNumber
        };
        
        this.generateQR(data, 'customQR');
    }
    
    async testValidation(qrCode, expectedResult = null) {
        const startTime = Date.now();
        
        try {
            const response = await fetch('{{ route("staff.validate-ticket") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({ ticket_code: qrCode })
            });
            
            const result = await response.json();
            const responseTime = Date.now() - startTime;
            this.responseTimes.push(responseTime);
            
            this.totalTests++;
            
            if (response.ok && result.success) {
                this.successCount++;
                this.addTestResult(qrCode, 'success', result.message || 'Valid ticket', responseTime);
            } else {
                this.failureCount++;
                this.addTestResult(qrCode, 'error', result.message || 'Invalid ticket', responseTime);
            }
            
            this.updateStatistics();
            
            return result;
            
        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.responseTimes.push(responseTime);
            this.totalTests++;
            this.failureCount++;
            
            this.addTestResult(qrCode, 'error', 'Network error: ' + error.message, responseTime);
            this.updateStatistics();
            
            throw error;
        }
    }
    
    addTestResult(qrCode, type, message, responseTime) {
        const container = document.getElementById('testResults');
        
        // Remove "no tests" message
        if (container.querySelector('.text-gray-500')) {
            container.innerHTML = '';
        }
        
        const resultDiv = document.createElement('div');
        resultDiv.className = `p-3 rounded-lg border ${type === 'success' ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'}`;
        
        resultDiv.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas ${type === 'success' ? 'fa-check-circle text-green-600' : 'fa-times-circle text-red-600'}"></i>
                    <span class="font-medium">${qrCode}</span>
                </div>
                <span class="text-sm text-gray-500">${responseTime}ms</span>
            </div>
            <p class="text-sm mt-1 ${type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}">${message}</p>
        `;
        
        container.insertBefore(resultDiv, container.firstChild);
        
        // Keep only last 10 results
        while (container.children.length > 10) {
            container.removeChild(container.lastChild);
        }
    }
    
    updateStatistics() {
        document.getElementById('successCount').textContent = this.successCount;
        document.getElementById('failureCount').textContent = this.failureCount;
        document.getElementById('totalTests').textContent = this.totalTests;
        
        if (this.responseTimes.length > 0) {
            const avgTime = Math.round(this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length);
            document.getElementById('avgResponseTime').textContent = avgTime + 'ms';
        }
    }
    
    async testManualInput() {
        const input = document.getElementById('testQRInput').value.trim();
        if (!input) {
            alert('Please enter a QR code or ticket number');
            return;
        }
        
        await this.testValidation(input);
        document.getElementById('testQRInput').value = '';
    }
    
    async testValidTicket() {
        await this.testValidation('TIX-VALID-001');
    }
    
    async testInvalidTicket() {
        await this.testValidation('TIX-INVALID-001');
    }
    
    async testUsedTicket() {
        await this.testValidation('TIX-USED-001');
    }
    
    async testExpiredTicket() {
        await this.testValidation('TIX-EXPIRED-001');
    }
    
    async runPerformanceTest() {
        const button = event.target;
        const originalText = button.textContent;
        button.disabled = true;
        button.textContent = 'Running...';
        
        const resultsDiv = document.getElementById('performanceResults');
        resultsDiv.innerHTML = 'Running performance test...';
        
        const startTime = Date.now();
        const testCodes = [];
        
        // Generate test codes
        for (let i = 0; i < 100; i++) {
            testCodes.push(`PERF-TEST-${String(i).padStart(3, '0')}`);
        }
        
        let completed = 0;
        const promises = testCodes.map(async (code) => {
            try {
                await this.testValidation(code);
                completed++;
                resultsDiv.innerHTML = `Progress: ${completed}/100 tests completed`;
            } catch (error) {
                completed++;
                resultsDiv.innerHTML = `Progress: ${completed}/100 tests completed (${error.message})`;
            }
        });
        
        await Promise.all(promises);
        
        const totalTime = Date.now() - startTime;
        const avgTime = totalTime / 100;
        
        resultsDiv.innerHTML = `
            <div class="text-sm">
                <p><strong>Performance Test Results:</strong></p>
                <p>Total Time: ${totalTime}ms</p>
                <p>Average Time per Request: ${avgTime.toFixed(2)}ms</p>
                <p>Requests per Second: ${(1000 / avgTime).toFixed(2)}</p>
            </div>
        `;
        
        button.disabled = false;
        button.textContent = originalText;
    }
}

// Global functions
window.generateValidQR = function() {
    tester.generateValidQR();
};

window.generateInvalidQR = function() {
    tester.generateInvalidQR();
};

window.testValidTicket = function() {
    tester.testValidTicket();
};

window.testInvalidTicket = function() {
    tester.testInvalidTicket();
};

window.testUsedTicket = function() {
    tester.testUsedTicket();
};

window.testExpiredTicket = function() {
    tester.testExpiredTicket();
};

window.runPerformanceTest = function() {
    tester.runPerformanceTest();
};

// Initialize tester
let tester;
document.addEventListener('DOMContentLoaded', function() {
    tester = new QRScannerTester();
    
    // Generate initial QR codes
    tester.generateValidQR();
    tester.generateInvalidQR();
});
</script>
@endpush
@endsection

@extends('layouts.organizer')

@section('title', 'My Events - Organizer Dashboard')

@push('styles')
<style>
.event-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.event-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-left-color: #3B82F6;
}

.event-card.published {
    border-left-color: #10B981;
}

.event-card.draft {
    border-left-color: #F59E0B;
}

.event-card.cancelled {
    border-left-color: #EF4444;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stats-card-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.stats-card-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.stats-card-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.template-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
    <!-- Header -->
    <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-lg border-b border-gray-200/50 dark:border-gray-700/50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div class="slide-in-up">
                    <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        <i class="fas fa-calendar-alt mr-3"></i>
                        My Events Dashboard
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">Manage your events and track performance with advanced analytics</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3 slide-in-up">
                    <button onclick="refreshData()" class="flex items-center justify-center px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                        <i class="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                    <a href="{{ route('organizer.events.create') }}"
                       class="flex items-center justify-center px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-plus mr-2"></i>
                        Create New Event
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" data-aos="fade-up">
            <!-- Total Events -->
            <div class="stats-card rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Total Events</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $stats['total'] ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-chart-line text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">All time</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-calendar-alt text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Published Events -->
            <div class="stats-card-success rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Published</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $stats['published'] ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-eye text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">Live events</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-globe text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Draft Events -->
            <div class="stats-card-warning rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Draft</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $stats['draft'] ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-edit text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">In progress</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-file-alt text-2xl text-white"></i>
                    </div>
                </div>
            </div>

            <!-- Completed Events -->
            <div class="stats-card-secondary rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Completed</p>
                        <p class="text-3xl font-bold text-white mt-2">{{ $stats['completed'] ?? 0 }}</p>
                        <div class="flex items-center mt-2">
                            <i class="fas fa-check-circle text-white/60 mr-1"></i>
                            <span class="text-white/80 text-sm">Finished</span>
                        </div>
                    </div>
                    <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-trophy text-2xl text-white"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="filter-card rounded-xl p-6 shadow-lg mb-8" data-aos="fade-up" data-aos-delay="100">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                <div class="flex flex-col sm:flex-row gap-4 flex-1">
                    <!-- Search -->
                    <div class="relative flex-1">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               id="searchInput"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search events by title or venue..."
                               class="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    </div>

                    <!-- Status Filter -->
                    <div class="relative">
                        <select id="statusFilter"
                                name="status"
                                class="block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="">All Status</option>
                            <option value="published" {{ request('status') == 'published' ? 'selected' : '' }}>Published</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex gap-3">
                    <button onclick="exportEvents()" class="flex items-center px-4 py-2 bg-green-100 hover:bg-green-200 text-green-800 rounded-lg transition-colors duration-200">
                        <i class="fas fa-download mr-2"></i>
                        Export
                    </button>
                    <button onclick="clearFilters()" class="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Events Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="200">
            @forelse($tickets as $event)
            <div class="event-card {{ strtolower($event->status) }} bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-xl transition-all duration-300">
                <!-- Event Image -->
                <div class="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
                    @if($event->poster)
                        <img src="{{ Storage::url($event->poster) }}"
                             alt="{{ $event->title }}"
                             class="w-full h-full object-cover">
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <i class="fas fa-image text-white/50 text-4xl"></i>
                        </div>
                    @endif

                    <!-- Status Badge -->
                    <div class="absolute top-4 left-4">
                        <span class="px-3 py-1 text-xs font-semibold rounded-full
                            @if($event->status === 'published') bg-green-100 text-green-800
                            @elseif($event->status === 'draft') bg-yellow-100 text-yellow-800
                            @elseif($event->status === 'cancelled') bg-red-100 text-red-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($event->status) }}
                        </span>
                    </div>

                    <!-- Template Badge -->
                    @if($event->ticket_template)
                    <div class="absolute top-4 right-4">
                        <span class="template-badge">
                            {{ ucfirst(str_replace('-', ' ', $event->ticket_template)) }}
                        </span>
                    </div>
                    @endif

                    <!-- Quick Actions -->
                    <div class="absolute bottom-4 right-4 flex gap-2">
                        <a href="{{ route('organizer.events.show', $event) }}"
                           class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                            <i class="fas fa-eye text-sm"></i>
                        </a>
                        <a href="{{ route('organizer.events.edit', $event) }}"
                           class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors">
                            <i class="fas fa-edit text-sm"></i>
                        </a>
                    </div>
                </div>

                <!-- Event Content -->
                <div class="p-6">
                    <!-- Event Title -->
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {{ $event->title }}
                    </h3>

                    <!-- Event Details -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-calendar-alt mr-2 w-4"></i>
                            <span>{{ $event->start_date ? $event->start_date->format('d M Y, H:i') : 'No Date' }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-map-marker-alt mr-2 w-4"></i>
                            <span class="line-clamp-1">{{ $event->venue_name }}, {{ $event->city }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-tag mr-2 w-4"></i>
                            <span>{{ $event->category->name ?? 'No Category' }}</span>
                        </div>
                    </div>

                    <!-- Event Stats -->
                    <div class="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">{{ $event->tickets->count() }}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">Sold</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">{{ $event->available_capacity }}</div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">Available</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-gray-900 dark:text-white">
                                Rp {{ number_format($event->price, 0, ',', '.') }}
                            </div>
                            <div class="text-xs text-gray-600 dark:text-gray-400">Price</div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-2">
                        <a href="{{ route('organizer.events.show', $event) }}"
                           class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-chart-bar mr-1"></i>
                            Analytics
                        </a>
                        <a href="{{ route('organizer.events.edit', $event) }}"
                           class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-edit mr-1"></i>
                            Edit
                        </a>
                        <button onclick="manageTickets({{ $event->id }})"
                                class="flex-1 bg-purple-100 hover:bg-purple-200 text-purple-800 py-2 px-3 rounded-lg text-center text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-ticket-alt mr-1"></i>
                            Tickets
                        </button>
                    </div>
                </div>
            </div>
            @empty
            <!-- Empty State -->
            <div class="col-span-full">
                <div class="text-center py-16">
                    <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-calendar-plus text-3xl text-gray-400"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Events Found</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        @if(request('search') || request('status'))
                            No events match your current filters. Try adjusting your search criteria.
                        @else
                            You haven't created any events yet. Start by creating your first event!
                        @endif
                    </p>
                    <a href="{{ route('organizer.events.create') }}"
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                        <i class="fas fa-plus mr-2"></i>
                        Create Your First Event
                    </a>
                </div>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if($tickets->hasPages())
        <div class="mt-8 flex justify-center" data-aos="fade-up" data-aos-delay="300">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4">
                {{ $tickets->links() }}
            </div>
        </div>
        @endif
    </div>
</div>

@push('scripts')
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    AOS.init({
        duration: 600,
        easing: 'ease-out-cubic',
        once: true
    });

    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');

    let searchTimeout;

    function performSearch() {
        const searchValue = searchInput.value;
        const statusValue = statusFilter.value;

        const url = new URL(window.location.href);
        url.searchParams.set('search', searchValue);
        url.searchParams.set('status', statusValue);

        if (!searchValue) url.searchParams.delete('search');
        if (!statusValue) url.searchParams.delete('status');

        window.location.href = url.toString();
    }

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 500);
    });

    statusFilter.addEventListener('change', performSearch);
});

// Utility functions
function refreshData() {
    window.location.reload();
}

function clearFilters() {
    const url = new URL(window.location.href);
    url.searchParams.delete('search');
    url.searchParams.delete('status');
    window.location.href = url.toString();
}

function exportEvents() {
    const url = new URL('{{ route("organizer.events.export") }}', window.location.origin);

    // Add current filters to export
    const searchParams = new URLSearchParams(window.location.search);
    for (const [key, value] of searchParams) {
        url.searchParams.set(key, value);
    }

    window.open(url.toString(), '_blank');
}

function manageTickets(eventId) {
    window.location.href = `/organizer/events/${eventId}/tickets`;
}

// Template management functions
function changeTemplate(eventId) {
    // Open template selection modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl w-full mx-4">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                <i class="fas fa-palette mr-2"></i>
                Select Ticket Template
            </h3>

            <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="template-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-template="boarding-pass">
                    <div class="text-center">
                        <i class="fas fa-plane text-2xl text-blue-600 mb-2"></i>
                        <h4 class="font-semibold">Boarding Pass</h4>
                        <p class="text-sm text-gray-600">Modern airline style</p>
                    </div>
                </div>

                <div class="template-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-template="classic-ticket">
                    <div class="text-center">
                        <i class="fas fa-ticket-alt text-2xl text-green-600 mb-2"></i>
                        <h4 class="font-semibold">Classic Ticket</h4>
                        <p class="text-sm text-gray-600">Traditional event ticket</p>
                    </div>
                </div>

                <div class="template-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-template="modern-card">
                    <div class="text-center">
                        <i class="fas fa-id-card text-2xl text-purple-600 mb-2"></i>
                        <h4 class="font-semibold">Modern Card</h4>
                        <p class="text-sm text-gray-600">Sleek card design</p>
                    </div>
                </div>

                <div class="template-option border-2 border-gray-200 rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors" data-template="concert-festival">
                    <div class="text-center">
                        <i class="fas fa-music text-2xl text-red-600 mb-2"></i>
                        <h4 class="font-semibold">Concert Festival</h4>
                        <p class="text-sm text-gray-600">Music & festival theme</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-end gap-3">
                <button onclick="closeTemplateModal()" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                    Cancel
                </button>
                <button onclick="saveTemplate(${eventId})" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    Save Template
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add click handlers for template options
    modal.querySelectorAll('.template-option').forEach(option => {
        option.addEventListener('click', function() {
            modal.querySelectorAll('.template-option').forEach(opt => {
                opt.classList.remove('border-blue-500', 'bg-blue-50');
                opt.classList.add('border-gray-200');
            });
            this.classList.remove('border-gray-200');
            this.classList.add('border-blue-500', 'bg-blue-50');
        });
    });
}

function closeTemplateModal() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) modal.remove();
}

function saveTemplate(eventId) {
    const selectedTemplate = document.querySelector('.template-option.border-blue-500');
    if (!selectedTemplate) {
        alert('Please select a template');
        return;
    }

    const template = selectedTemplate.dataset.template;

    // Send AJAX request to save template
    fetch(`/organizer/events/${eventId}/template`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ template: template })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeTemplateModal();
            window.location.reload();
        } else {
            alert('Failed to save template');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}
</script>
@endpush
@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Security Settings Table
        if (!Schema::hasTable('cyber_guard_settings')) {
            Schema::create('cyber_guard_settings', function (Blueprint $table) {
                $table->id();
                $table->string('key')->unique();
                $table->text('value');
                $table->string('type')->default('string'); // string, boolean, integer, json
                $table->text('description')->nullable();
                $table->string('category')->default('general'); // ddos, sql_injection, rate_limiting, etc
                $table->boolean('is_active')->default(true);
                $table->timestamps();
        });

        // Security Logs Table
        Schema::create('cyber_guard_logs', function (Blueprint $table) {
                $table->id();
                $table->string('type'); // ddos_attempt, sql_injection, rate_limit_exceeded, suspicious_activity
                $table->string('severity'); // low, medium, high, critical
                $table->ipAddress('ip_address');
                $table->string('user_agent')->nullable();
                $table->string('request_method')->nullable();
                $table->text('request_url')->nullable();
                $table->json('request_data')->nullable();
                $table->text('description');
                $table->json('additional_data')->nullable();
                $table->string('action_taken')->nullable(); // blocked, rate_limited, logged_only
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
                $table->timestamp('detected_at');
                $table->boolean('is_resolved')->default(false);
                $table->text('resolution_notes')->nullable();
                $table->timestamps();
            
                $table->index(['type', 'severity']);
                $table->index(['ip_address', 'detected_at']);
                $table->index(['user_id', 'detected_at']);
        });

        // Blocked IPs Table
        Schema::create('cyber_guard_blocked_ips', function (Blueprint $table) {
                $table->id();
                $table->ipAddress('ip_address')->unique();
                $table->string('reason'); // ddos, suspicious_activity, manual_block
                $table->text('description')->nullable();
                $table->timestamp('blocked_at');
                $table->timestamp('expires_at')->nullable(); // null for permanent block
                $table->boolean('is_permanent')->default(false);
                $table->foreignId('blocked_by_user_id')->nullable()->constrained('users')->onDelete('set null');
                $table->integer('violation_count')->default(1);
                $table->json('violation_history')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            
                $table->index(['ip_address', 'is_active']);
                $table->index(['expires_at', 'is_active']);
        });

        // Rate Limiting Table
        Schema::create('cyber_guard_rate_limits', function (Blueprint $table) {
                $table->id();
                $table->string('identifier'); // IP address or user ID
                $table->string('type'); // ip, user, endpoint
                $table->string('endpoint')->nullable(); // specific endpoint being rate limited
                $table->integer('attempts')->default(1);
                $table->timestamp('window_start');
                $table->timestamp('last_attempt');
                $table->boolean('is_blocked')->default(false);
                $table->timestamp('blocked_until')->nullable();
                $table->timestamps();
            
                $table->index(['identifier', 'type', 'endpoint']);
                $table->index(['window_start', 'is_blocked']);
        });

        // Firewall Rules Table
        Schema::create('cyber_guard_firewall_rules', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->text('description')->nullable();
                $table->string('type'); // ip_whitelist, ip_blacklist, country_block, user_agent_block
                $table->text('pattern'); // IP range, country code, user agent pattern, etc
                $table->string('action'); // allow, block, rate_limit
                $table->json('conditions')->nullable(); // additional conditions
                $table->boolean('is_active')->default(true);
                $table->integer('priority')->default(0); // higher number = higher priority
                $table->integer('hit_count')->default(0);
                $table->timestamp('last_hit')->nullable();
                $table->timestamps();
            
                $table->index(['type', 'is_active', 'priority']);
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('cyber_guard_firewall_rules');
        Schema::dropIfExists('cyber_guard_rate_limits');
        Schema::dropIfExists('cyber_guard_blocked_ips');
        Schema::dropIfExists('cyber_guard_logs');
        Schema::dropIfExists('cyber_guard_settings');
    }
};

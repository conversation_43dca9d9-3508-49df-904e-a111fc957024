<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing categories
        DB::table('categories')->truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Insert categories
        DB::table('categories')->insert([
            [
                'name' => 'Konser & Musik',
                'slug' => 'konser-musik',
                'description' => 'Konser musik, festival musik, dan pertunjukan musik lainnya',
                'icon' => 'fas fa-music',
                'color' => '#FF6B6B',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Seminar & Workshop',
                'slug' => 'seminar-workshop',
                'description' => 'Seminar, workshop, pelatihan, dan acara edukasi',
                'icon' => 'fas fa-chalkboard-teacher',
                'color' => '#4ECDC4',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Olahraga',
                'slug' => 'olahraga',
                'description' => 'Pertandingan olahraga, turnamen, dan event olahraga',
                'icon' => 'fas fa-running',
                'color' => '#45B7D1',
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Teater & Seni',
                'slug' => 'teater-seni',
                'description' => 'Pertunjukan teater, seni, dan budaya',
                'icon' => 'fas fa-theater-masks',
                'color' => '#F7DC6F',
                'is_active' => true,
                'sort_order' => 4,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Teknologi',
                'slug' => 'teknologi',
                'description' => 'Conference teknologi, startup event, dan tech meetup',
                'icon' => 'fas fa-laptop-code',
                'color' => '#BB8FCE',
                'is_active' => true,
                'sort_order' => 5,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Kuliner',
                'slug' => 'kuliner',
                'description' => 'Food festival, cooking class, dan acara kuliner',
                'icon' => 'fas fa-utensils',
                'color' => '#F8C471',
                'is_active' => true,
                'sort_order' => 6,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Fashion & Beauty',
                'slug' => 'fashion-beauty',
                'description' => 'Fashion show, beauty workshop, dan lifestyle event',
                'icon' => 'fas fa-tshirt',
                'color' => '#F1948A',
                'is_active' => true,
                'sort_order' => 7,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Bisnis & Networking',
                'slug' => 'bisnis-networking',
                'description' => 'Business conference, networking event, dan entrepreneur meetup',
                'icon' => 'fas fa-handshake',
                'color' => '#85C1E9',
                'is_active' => true,
                'sort_order' => 8,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Anak & Keluarga',
                'slug' => 'anak-keluarga',
                'description' => 'Acara anak-anak, family gathering, dan event keluarga',
                'icon' => 'fas fa-child',
                'color' => '#A9DFBF',
                'is_active' => true,
                'sort_order' => 9,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Agama & Spiritual',
                'slug' => 'agama-spiritual',
                'description' => 'Kajian agama, retreat spiritual, dan acara keagamaan',
                'icon' => 'fas fa-pray',
                'color' => '#D7BDE2',
                'is_active' => true,
                'sort_order' => 10,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Otomotif',
                'slug' => 'otomotif',
                'description' => 'Pameran otomotif, car show, dan motor show',
                'icon' => 'fas fa-car',
                'color' => '#AED6F1',
                'is_active' => true,
                'sort_order' => 11,
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'Lainnya',
                'slug' => 'lainnya',
                'description' => 'Kategori untuk event yang tidak masuk kategori lain',
                'icon' => 'fas fa-ellipsis-h',
                'color' => '#BDC3C7',
                'is_active' => true,
                'sort_order' => 12,
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ]);

        $this->command->info('✅ Categories seeded successfully!');
        $this->command->info('📂 12 categories created with icons and colors');
    }
}

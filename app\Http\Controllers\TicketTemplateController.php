<?php

namespace App\Http\Controllers;

use App\Models\TicketTemplate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class TicketTemplateController extends Controller
{
    /**
     * Display a listing of templates for the authenticated organizer
     */
    public function index()
    {
        $user = Auth::user();
        
        // Check if user is organizer
        if ($user->role !== 'penjual') {
            abort(403, 'Only organizers can access ticket templates');
        }

        $templates = TicketTemplate::forOrganizer($user->id)
                                  ->active()
                                  ->orderBy('is_default', 'desc')
                                  ->orderBy('created_at', 'desc')
                                  ->get();

        $availableTypes = TicketTemplate::getAvailableTemplateTypes($user);
        $canUseCustom = $user->badge_level_id >= 4;

        return view('organizer.templates.index', compact('templates', 'availableTypes', 'canUseCustom'));
    }

    /**
     * Show the form for creating a new template
     */
    public function create()
    {
        $user = Auth::user();
        
        if ($user->role !== 'penjual') {
            abort(403, 'Only organizers can create ticket templates');
        }

        $availableTypes = TicketTemplate::getAvailableTemplateTypes($user);
        $canUseCustom = $user->badge_level_id >= 4;

        return view('organizer.templates.create', compact('availableTypes', 'canUseCustom'));
    }

    /**
     * Store a newly created template
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        if ($user->role !== 'penjual') {
            abort(403, 'Only organizers can create ticket templates');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'template_type' => 'required|in:classic,unix,minimal,pro,custom',
            'config' => 'nullable|array',
            'custom_config' => 'nullable|array',
        ]);

        // Check if user can use custom template
        if ($request->template_type === 'custom' && $user->badge_level_id < 4) {
            return back()->withErrors(['template_type' => 'Custom templates are only available for Platinum badge holders']);
        }

        // Validate custom config if provided
        if ($request->template_type === 'custom' && $request->custom_config) {
            $template = new TicketTemplate();
            $errors = $template->validateCustomConfig($request->custom_config);
            if (!empty($errors)) {
                return back()->withErrors(['custom_config' => $errors]);
            }
        }

        $template = TicketTemplate::create([
            'organizer_id' => $user->id,
            'name' => $request->name,
            'slug' => Str::slug($request->name) . '-' . $user->id . '-' . time(),
            'template_type' => $request->template_type,
            'is_active' => true,
            'is_default' => false,
            'config' => array_merge(
                TicketTemplate::DEFAULT_CONFIGS[$request->template_type] ?? [],
                $request->config ?? []
            ),
            'custom_config' => $request->custom_config,
        ]);

        return redirect()->route('organizer.templates.index')
                        ->with('success', 'Template created successfully!');
    }

    /**
     * Display the specified template
     */
    public function show(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only view your own templates');
        }

        return view('organizer.templates.show', compact('template'));
    }

    /**
     * Show the form for editing the specified template
     */
    public function edit(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only edit your own templates');
        }

        // Check if user can edit custom template
        if ($template->template_type === 'custom' && $user->badge_level_id < 4) {
            abort(403, 'Custom templates are only available for Platinum badge holders');
        }

        $availableTypes = TicketTemplate::getAvailableTemplateTypes($user);
        $canUseCustom = $user->badge_level_id >= 4;

        return view('organizer.templates.edit', compact('template', 'availableTypes', 'canUseCustom'));
    }

    /**
     * Update the specified template
     */
    public function update(Request $request, TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only edit your own templates');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'config' => 'nullable|array',
            'custom_config' => 'nullable|array',
        ]);

        // Check if user can edit custom template
        if ($template->template_type === 'custom' && $user->badge_level_id < 4) {
            return back()->withErrors(['template_type' => 'Custom templates are only available for Platinum badge holders']);
        }

        // Validate custom config if provided
        if ($template->template_type === 'custom' && $request->custom_config) {
            $errors = $template->validateCustomConfig($request->custom_config);
            if (!empty($errors)) {
                return back()->withErrors(['custom_config' => $errors]);
            }
        }

        $template->update([
            'name' => $request->name,
            'config' => array_merge(
                $template->config ?? [],
                $request->config ?? []
            ),
            'custom_config' => $request->custom_config,
        ]);

        return redirect()->route('organizer.templates.index')
                        ->with('success', 'Template updated successfully!');
    }

    /**
     * Set template as default
     */
    public function setDefault(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only modify your own templates');
        }

        $template->setAsDefault();

        return back()->with('success', 'Template set as default successfully!');
    }

    /**
     * Preview template with sample data
     */
    public function preview(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only preview your own templates');
        }

        // Sample ticket data for preview
        $sampleTicket = [
            'ticket_number' => 'TIX-PREVIEW-001',
            'ticket_code' => 'PREVIEW001',
            'price' => 150000,
            'attendee_name' => 'John Doe',
            'attendee_email' => '<EMAIL>',
            'event' => [
                'title' => 'Sample Event Title',
                'start_date' => now()->addDays(30),
                'venue_name' => 'Sample Venue',
                'city' => 'Jakarta',
            ],
            'buyer' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
        ];

        $config = $template->getMergedConfig();

        return view($template->getViewPath(), [
            'ticket' => $sampleTicket,
            'config' => $config,
            'template' => $template,
        ]);
    }

    /**
     * Duplicate template
     */
    public function duplicate(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only duplicate your own templates');
        }

        // Check if user can duplicate custom template
        if ($template->template_type === 'custom' && $user->badge_level_id < 4) {
            return back()->withErrors(['error' => 'Custom templates are only available for Platinum badge holders']);
        }

        $newTemplate = $template->replicate();
        $newTemplate->name = $template->name . ' (Copy)';
        $newTemplate->slug = Str::slug($newTemplate->name) . '-' . $user->id . '-' . time();
        $newTemplate->is_default = false;
        $newTemplate->usage_count = 0;
        $newTemplate->last_used_at = null;
        $newTemplate->save();

        return redirect()->route('organizer.templates.index')
                        ->with('success', 'Template duplicated successfully!');
    }

    /**
     * Remove the specified template
     */
    public function destroy(TicketTemplate $template)
    {
        $user = Auth::user();
        
        // Check ownership
        if ($template->organizer_id !== $user->id) {
            abort(403, 'You can only delete your own templates');
        }

        // Prevent deletion of default template if it's the only one
        if ($template->is_default) {
            $otherTemplates = TicketTemplate::forOrganizer($user->id)
                                           ->where('id', '!=', $template->id)
                                           ->active()
                                           ->count();
            
            if ($otherTemplates === 0) {
                return back()->withErrors(['error' => 'Cannot delete the only template. Create another template first.']);
            }

            // Set another template as default
            $newDefault = TicketTemplate::forOrganizer($user->id)
                                       ->where('id', '!=', $template->id)
                                       ->active()
                                       ->first();
            if ($newDefault) {
                $newDefault->setAsDefault();
            }
        }

        $template->delete();

        return redirect()->route('organizer.templates.index')
                        ->with('success', 'Template deleted successfully!');
    }

    /**
     * Get template configuration options for AJAX
     */
    public function getConfigOptions(Request $request)
    {
        $templateType = $request->get('type');
        
        if (!array_key_exists($templateType, TicketTemplate::TEMPLATE_TYPES)) {
            return response()->json(['error' => 'Invalid template type'], 400);
        }

        $defaultConfig = TicketTemplate::DEFAULT_CONFIGS[$templateType] ?? [];
        
        $configOptions = [
            'colors' => [
                'primary_color' => $defaultConfig['primary_color'] ?? '#6366F1',
                'secondary_color' => $defaultConfig['secondary_color'] ?? '#F1F5F9',
                'background_color' => $defaultConfig['background_color'] ?? '#FFFFFF',
                'text_color' => $defaultConfig['text_color'] ?? '#0F172A',
                'accent_color' => $defaultConfig['accent_color'] ?? '#F59E0B',
            ],
        ];

        if ($templateType === 'custom') {
            $configOptions['layout_options'] = [
                'horizontal' => 'Horizontal Layout',
                'vertical' => 'Vertical Layout',
                'split' => 'Split Layout',
            ];
            
            $configOptions['header_styles'] = [
                'gradient' => 'Gradient Header',
                'solid' => 'Solid Color',
                'image' => 'Background Image',
            ];
            
            $configOptions['border_styles'] = [
                'rounded' => 'Rounded Corners',
                'sharp' => 'Sharp Corners',
                'dashed' => 'Dashed Border',
            ];
            
            $configOptions['font_families'] = [
                'Inter' => 'Inter',
                'Poppins' => 'Poppins',
                'Roboto' => 'Roboto',
                'Custom' => 'Custom Font',
            ];
            
            $configOptions['background_patterns'] = [
                'none' => 'No Pattern',
                'dots' => 'Dots Pattern',
                'lines' => 'Lines Pattern',
                'waves' => 'Waves Pattern',
            ];
        }

        return response()->json($configOptions);
    }
}

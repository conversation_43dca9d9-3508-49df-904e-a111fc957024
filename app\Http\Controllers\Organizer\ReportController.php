<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display reports dashboard
     */
    public function index(Request $request)
    {
        $dateRange = $request->get('range', '30'); // Default 30 days
        $startDate = Carbon::now()->subDays($dateRange);
        $endDate = Carbon::now();

        // Revenue Report
        $revenueData = $this->getRevenueReport($startDate, $endDate);
        
        // Sales Report
        $salesData = $this->getSalesReport($startDate, $endDate);
        
        // Event Performance Report
        $eventPerformance = $this->getEventPerformanceReport($startDate, $endDate);
        
        // Customer Report
        $customerData = $this->getCustomerReport($startDate, $endDate);

        return view('pages.organizer.reports.index', compact(
            'revenueData', 
            'salesData', 
            'eventPerformance', 
            'customerData',
            'dateRange'
        ));
    }

    /**
     * Revenue report
     */
    public function revenue(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());
        
        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        $revenueData = $this->getRevenueReport($startDate, $endDate);
        
        // Daily revenue breakdown
        $dailyRevenue = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Payment method breakdown
        $paymentMethods = Payment::whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('payment_method, SUM(amount) as total, COUNT(*) as count')
            ->groupBy('payment_method')
            ->get();

        return view('pages.organizer.reports.revenue', compact(
            'revenueData', 
            'dailyRevenue', 
            'paymentMethods',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Sales report
     */
    public function sales(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());
        
        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        $salesData = $this->getSalesReport($startDate, $endDate);
        
        // Top selling events
        $topEvents = Event::where('organizer_id', Auth::id())
            ->withCount(['orders as tickets_sold' => function($q) use ($startDate, $endDate) {
                $q->where('status', 'completed')
                  ->whereBetween('created_at', [$startDate, $endDate]);
            }])
            ->withSum(['orders as revenue' => function($q) use ($startDate, $endDate) {
                $q->where('status', 'completed')
                  ->whereBetween('created_at', [$startDate, $endDate]);
            }], 'total_amount')
            ->having('tickets_sold', '>', 0)
            ->orderByDesc('tickets_sold')
            ->take(10)
            ->get();

        // Sales by hour
        $hourlyStats = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as orders, SUM(total_amount) as revenue')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        return view('pages.organizer.reports.sales', compact(
            'salesData', 
            'topEvents', 
            'hourlyStats',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Event performance report
     */
    public function events(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());
        
        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        $events = Event::where('organizer_id', Auth::id())
            ->with(['category'])
            ->withCount([
                'orders as total_orders',
                'orders as completed_orders' => function($q) {
                    $q->where('status', 'completed');
                },
                'tickets as total_tickets',
                'tickets as used_tickets' => function($q) {
                    $q->where('status', 'used');
                }
            ])
            ->withSum(['orders as total_revenue' => function($q) {
                $q->where('status', 'completed');
            }], 'total_amount')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderByDesc('total_revenue')
            ->paginate(20);

        // Event status breakdown
        $statusBreakdown = Event::where('organizer_id', Auth::id())
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        return view('pages.organizer.reports.events', compact(
            'events', 
            'statusBreakdown',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Customer report
     */
    public function customers(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30));
        $endDate = $request->get('end_date', Carbon::now());
        
        if (is_string($startDate)) {
            $startDate = Carbon::parse($startDate);
        }
        if (is_string($endDate)) {
            $endDate = Carbon::parse($endDate);
        }

        $customerData = $this->getCustomerReport($startDate, $endDate);
        
        // Top customers
        $topCustomers = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('user')
            ->selectRaw('user_id, COUNT(*) as order_count, SUM(total_amount) as total_spent')
            ->groupBy('user_id')
            ->orderByDesc('total_spent')
            ->take(20)
            ->get();

        // Customer acquisition by month
        $customerAcquisition = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as month, COUNT(DISTINCT user_id) as new_customers')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('pages.organizer.reports.customers', compact(
            'customerData', 
            'topCustomers', 
            'customerAcquisition',
            'startDate',
            'endDate'
        ));
    }

    /**
     * Export report
     */
    public function export(Request $request)
    {
        $type = $request->get('type', 'revenue');
        $format = $request->get('format', 'csv');
        $startDate = Carbon::parse($request->get('start_date', Carbon::now()->subDays(30)));
        $endDate = Carbon::parse($request->get('end_date', Carbon::now()));

        switch ($type) {
            case 'revenue':
                return $this->exportRevenue($startDate, $endDate, $format);
            case 'sales':
                return $this->exportSales($startDate, $endDate, $format);
            case 'events':
                return $this->exportEvents($startDate, $endDate, $format);
            case 'customers':
                return $this->exportCustomers($startDate, $endDate, $format);
            default:
                return back()->with('error', 'Invalid report type.');
        }
    }

    /**
     * Get revenue report data
     */
    private function getRevenueReport($startDate, $endDate)
    {
        $totalRevenue = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->sum('total_amount');

        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $previousRevenue = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$previousPeriodStart, $startDate])
            ->sum('total_amount');

        $revenueGrowth = $previousRevenue > 0 ? 
            (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

        return [
            'total_revenue' => $totalRevenue,
            'previous_revenue' => $previousRevenue,
            'growth_percentage' => round($revenueGrowth, 2),
            'average_order_value' => $this->getAverageOrderValue($startDate, $endDate),
        ];
    }

    /**
     * Get sales report data
     */
    private function getSalesReport($startDate, $endDate)
    {
        $totalOrders = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $totalTickets = Ticket::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return [
            'total_orders' => $totalOrders,
            'total_tickets' => $totalTickets,
            'conversion_rate' => $this->getConversionRate($startDate, $endDate),
        ];
    }

    /**
     * Get event performance report data
     */
    private function getEventPerformanceReport($startDate, $endDate)
    {
        $totalEvents = Event::where('organizer_id', Auth::id())
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        $publishedEvents = Event::where('organizer_id', Auth::id())
            ->where('status', 'published')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return [
            'total_events' => $totalEvents,
            'published_events' => $publishedEvents,
            'average_attendance' => $this->getAverageAttendance($startDate, $endDate),
        ];
    }

    /**
     * Get customer report data
     */
    private function getCustomerReport($startDate, $endDate)
    {
        $totalCustomers = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('user_id')
            ->count();

        $newCustomers = Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereDoesntHave('user.orders', function($q) use ($startDate) {
                $q->where('created_at', '<', $startDate);
            })
            ->distinct('user_id')
            ->count();

        return [
            'total_customers' => $totalCustomers,
            'new_customers' => $newCustomers,
            'repeat_customers' => $totalCustomers - $newCustomers,
            'customer_retention_rate' => $totalCustomers > 0 ? 
                round((($totalCustomers - $newCustomers) / $totalCustomers) * 100, 2) : 0,
        ];
    }

    /**
     * Helper methods
     */
    private function getAverageOrderValue($startDate, $endDate)
    {
        return Order::whereHas('event', function($q) {
                $q->where('organizer_id', Auth::id());
            })
            ->where('status', 'completed')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->avg('total_amount') ?? 0;
    }

    private function getConversionRate($startDate, $endDate)
    {
        // This would need view tracking implementation
        return 0; // Placeholder
    }

    private function getAverageAttendance($startDate, $endDate)
    {
        return Ticket::whereHas('event', function($q) use ($startDate, $endDate) {
                $q->where('organizer_id', Auth::id())
                  ->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->where('status', 'used')
            ->count();
    }

    /**
     * Export methods
     */
    private function exportRevenue($startDate, $endDate, $format)
    {
        // Implementation for revenue export
        return response()->json(['message' => 'Revenue export not implemented yet']);
    }

    private function exportSales($startDate, $endDate, $format)
    {
        // Implementation for sales export
        return response()->json(['message' => 'Sales export not implemented yet']);
    }

    private function exportEvents($startDate, $endDate, $format)
    {
        // Implementation for events export
        return response()->json(['message' => 'Events export not implemented yet']);
    }

    private function exportCustomers($startDate, $endDate, $format)
    {
        // Implementation for customers export
        return response()->json(['message' => 'Customers export not implemented yet']);
    }
}

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Ticket Boarding Pass - {{ $event->title }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px 20px;
        }
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #2d3748;
        }
        .event-info {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }
        .event-title {
            font-size: 22px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .event-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .detail-item {
            display: flex;
            align-items: center;
        }
        .detail-icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            color: #667eea;
        }
        .detail-text {
            font-size: 14px;
            color: #4a5568;
        }
        .tickets-section {
            margin: 30px 0;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .ticket-card {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }
        .ticket-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .ticket-number {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .ticket-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .ticket-detail {
            font-size: 14px;
        }
        .ticket-detail strong {
            color: #2d3748;
        }
        .qr-section {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f7fafc;
            border-radius: 8px;
        }
        .qr-code {
            max-width: 150px;
            height: auto;
            margin: 10px auto;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
        }
        .download-section {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        .download-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px 5px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .download-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        .important-info {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .important-info h4 {
            color: #c53030;
            margin: 0 0 10px 0;
            font-size: 16px;
        }
        .important-info ul {
            margin: 0;
            padding-left: 20px;
            color: #742a2a;
        }
        .footer {
            background: #2d3748;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
            font-size: 14px;
        }
        .footer a {
            color: #90cdf4;
            text-decoration: none;
        }
        .social-links {
            margin: 15px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #90cdf4;
            font-size: 18px;
        }
        @media (max-width: 600px) {
            .event-details,
            .ticket-details {
                grid-template-columns: 1fr;
            }
            .download-button {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>🎫 E-Ticket Boarding Pass</h1>
            <p>Tiket Anda telah berhasil diterbitkan!</p>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="greeting">
                Halo <strong>{{ $user->name }}</strong>,
            </div>

            <p>Terima kasih telah melakukan pembelian tiket di <strong>TiXara</strong>! Pembayaran Anda telah berhasil diproses dan E-Ticket Boarding Pass Anda telah siap.</p>

            <!-- Event Information -->
            <div class="event-info">
                <div class="event-title">{{ $event->title }}</div>
                <div class="event-details">
                    <div class="detail-item">
                        <span class="detail-icon">📅</span>
                        <span class="detail-text">
                            <strong>Tanggal:</strong><br>
                            {{ \Carbon\Carbon::parse($event->start_date)->format('d M Y, H:i') }} WIB
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">📍</span>
                        <span class="detail-text">
                            <strong>Lokasi:</strong><br>
                            {{ $event->venue_name }}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">🏢</span>
                        <span class="detail-text">
                            <strong>Penyelenggara:</strong><br>
                            {{ $event->organizer->name }}
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-icon">💰</span>
                        <span class="detail-text">
                            <strong>Total Bayar:</strong><br>
                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Tickets Section -->
            <div class="tickets-section">
                <h3 class="section-title">Detail E-Ticket Anda ({{ $tickets->count() }} tiket)</h3>
                
                @foreach($tickets as $ticket)
                    <div class="ticket-card">
                        <div class="ticket-number">🎫 {{ $ticket->ticket_number }}</div>
                        <div class="ticket-details">
                            <div class="ticket-detail">
                                <strong>Nama Peserta:</strong><br>
                                {{ $ticket->attendee_name }}
                            </div>
                            <div class="ticket-detail">
                                <strong>Email:</strong><br>
                                {{ $ticket->attendee_email }}
                            </div>
                            <div class="ticket-detail">
                                <strong>Status:</strong><br>
                                <span style="color: #48bb78; font-weight: bold;">✅ Aktif</span>
                            </div>
                            <div class="ticket-detail">
                                <strong>Harga:</strong><br>
                                Rp {{ number_format($ticket->price, 0, ',', '.') }}
                            </div>
                        </div>
                        
                        @if($ticket->qr_code_path)
                            <div class="qr-section">
                                <p><strong>QR Code untuk Validasi:</strong></p>
                                <img src="{{ asset('storage/' . $ticket->qr_code_path) }}" alt="QR Code" class="qr-code">
                                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                                    Tunjukkan QR Code ini saat masuk event
                                </p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Download Section -->
            <div class="download-section">
                <h3 style="margin: 0 0 15px 0;">📥 Download E-Ticket Anda</h3>
                <p style="margin: 0 0 20px 0;">E-Ticket dalam format PDF telah dilampirkan dalam email ini. Anda juga dapat mengunduhnya melalui:</p>
                
                <a href="{{ route('tiket-saya') }}" class="download-button">
                    🎫 Lihat Tiket Saya
                </a>
                <a href="{{ route('orders.show', $order) }}" class="download-button">
                    📋 Detail Pesanan
                </a>
            </div>

            <!-- Important Information -->
            <div class="important-info">
                <h4>⚠️ Informasi Penting:</h4>
                <ul>
                    <li>Simpan E-Ticket ini dengan baik dan jangan bagikan kepada orang lain</li>
                    <li>Tunjukkan QR Code atau nomor tiket saat masuk event</li>
                    <li>Datang 30 menit sebelum acara dimulai untuk proses check-in</li>
                    <li>Bawa identitas diri yang sesuai dengan nama pada tiket</li>
                    <li>Tiket yang sudah digunakan tidak dapat digunakan kembali</li>
                    <li>Hubungi penyelenggara jika ada pertanyaan tentang event</li>
                </ul>
            </div>

            <p>Jika Anda memiliki pertanyaan atau memerlukan bantuan, jangan ragu untuk menghubungi tim support kami di <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

            <p style="margin-top: 30px;">
                Terima kasih telah memilih <strong>TiXara</strong>!<br>
                Selamat menikmati event Anda! 🎉
            </p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>TiXara - Platform Tiket Event Terpercaya</strong></p>
            <div class="social-links">
                <a href="#">📘 Facebook</a>
                <a href="#">📷 Instagram</a>
                <a href="#">🐦 Twitter</a>
                <a href="#">💼 LinkedIn</a>
            </div>
            <p>
                <a href="{{ route('home') }}">www.tixara.my.id</a> | 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p style="font-size: 12px; opacity: 0.8; margin-top: 15px;">
                Email ini dikirim secara otomatis. Mohon jangan membalas email ini.<br>
                © {{ date('Y') }} TiXara. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>

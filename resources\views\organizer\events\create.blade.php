@extends('layouts.organizer')

@section('title', 'Create Event - Organizer Dashboard')

@push('styles')
<style>
/* Modern Create Event Styles */
.create-form-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.form-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.form-section {
    border-bottom: 1px solid rgba(16, 185, 129, 0.1);
    padding: 2rem 0;
}

.form-section:last-child {
    border-bottom: none;
}

.input-group {
    position: relative;
}

.input-modern {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-modern:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    background: white;
}

.label-modern {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.btn-primary-modern {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-secondary-modern {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    cursor: pointer;
}

.btn-secondary-modern:hover {
    background: rgba(107, 114, 128, 0.2);
    border-color: #d1d5db;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    background: rgba(249, 250, 251, 0.5);
}

.upload-area:hover {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.05);
}

.upload-area.dragover {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: #10b981;
}

.progress-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.progress-step::after {
    content: '';
    position: absolute;
    top: 1rem;
    left: 50%;
    width: 100%;
    height: 2px;
    background: #e5e7eb;
    z-index: 1;
}

.progress-step:last-child::after {
    display: none;
}

.progress-step.active::after {
    background: #10b981;
}

.progress-circle {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: #e5e7eb;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.progress-step.active .progress-circle {
    background: #10b981;
    color: white;
}

.checkbox-modern {
    appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checkbox-modern:checked {
    background: #10b981;
    border-color: #10b981;
}

.checkbox-modern:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

/* Template Selection Styles */
.template-option input[type="radio"]:checked + .template-card .template-card-inner {
    border-color: #10b981;
    background-color: #f0fdf4;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.template-option input[type="radio"]:checked + .template-card .template-card-inner::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #10b981;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.template-card-inner {
    position: relative;
}

.template-option:hover .template-card-inner {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-option .template-card {
    transition: all 0.3s ease;
}
</style>
@endpush

@section('content')
<div class="create-form-container">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-up">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">Create New Event</h1>
                    <p class="text-gray-600 dark:text-gray-400">Create and manage your event details</p>
                </div>
                <a href="{{ route('organizer.events.index') }}"
                   class="btn-secondary-modern">
                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                    Back to Events
                </a>
            </div>
        </div>

        <!-- Progress Indicator -->
        <div class="progress-indicator mb-8" data-aos="fade-up" data-aos-delay="100">
            <div class="progress-step active">
                <div class="progress-circle">1</div>
                <span class="text-sm font-medium">Basic Info</span>
            </div>
            <div class="progress-step">
                <div class="progress-circle">2</div>
                <span class="text-sm font-medium">Details</span>
            </div>
            <div class="progress-step">
                <div class="progress-circle">3</div>
                <span class="text-sm font-medium">Media</span>
            </div>
            <div class="progress-step">
                <div class="progress-circle">4</div>
                <span class="text-sm font-medium">Settings</span>
            </div>
        </div>

        <!-- Create Form -->
        <div class="form-card rounded-2xl p-8" data-aos="fade-up" data-aos-delay="200">
            <form action="{{ route('organizer.events.store') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                @csrf

                <!-- Basic Information Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i data-lucide="info" class="w-6 h-6"></i>
                        Basic Information
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="lg:col-span-2">
                            <label for="title" class="label-modern">
                                Event Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="title" id="title"
                                   value="{{ old('title') }}" required
                                   placeholder="Enter your event title"
                                   class="input-modern">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="category_id" class="label-modern">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <select name="category_id" id="category_id" required class="input-modern">
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="price" class="label-modern">
                                Ticket Price <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">Rp</span>
                                <input type="number" name="price" id="price"
                                       value="{{ old('price') }}" required min="0" step="1000"
                                       placeholder="0"
                                       class="input-modern pl-12">
                            </div>
                            @error('price')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="description" class="label-modern">
                                Event Description <span class="text-red-500">*</span>
                            </label>
                            <textarea name="description" id="description" rows="4" required
                                      placeholder="Describe your event in detail..."
                                      class="input-modern">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- E-Ticket Template Selection Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i data-lucide="ticket" class="w-6 h-6"></i>
                        E-Ticket Boarding Pass Template
                    </h2>

                    <div class="mb-6">
                        <p class="text-gray-600 dark:text-gray-400 mb-4">
                            Pilih template boarding pass yang akan digunakan untuk generate e-tiket otomatis setelah pembayaran berhasil.
                        </p>

                        <!-- Template Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @php
                            $templates = [
                                ['id' => 'unix', 'name' => 'Unix Terminal', 'desc' => 'Dark terminal style with green accents', 'color' => 'bg-gray-900', 'accent' => 'text-green-400'],
                                ['id' => 'minimalist', 'name' => 'Minimalist', 'desc' => 'Clean and simple design', 'color' => 'bg-white', 'accent' => 'text-blue-500'],
                                ['id' => 'pro', 'name' => 'Professional', 'desc' => 'Premium business appearance', 'color' => 'bg-purple-600', 'accent' => 'text-white'],
                                ['id' => 'elegant', 'name' => 'Elegant', 'desc' => 'Sophisticated pink gradient', 'color' => 'bg-pink-500', 'accent' => 'text-white'],
                                ['id' => 'modern', 'name' => 'Modern', 'desc' => 'Contemporary cyan design', 'color' => 'bg-cyan-500', 'accent' => 'text-white'],
                                ['id' => 'classic', 'name' => 'Classic', 'desc' => 'Traditional amber style', 'color' => 'bg-amber-600', 'accent' => 'text-white'],
                                ['id' => 'neon', 'name' => 'Neon', 'desc' => 'Dark with purple neon effects', 'color' => 'bg-gray-900', 'accent' => 'text-purple-400'],
                                ['id' => 'retro', 'name' => 'Retro', 'desc' => 'Vintage orange gradient', 'color' => 'bg-orange-600', 'accent' => 'text-white'],
                                ['id' => 'corporate', 'name' => 'Corporate', 'desc' => 'Professional gray theme', 'color' => 'bg-gray-600', 'accent' => 'text-white'],
                                ['id' => 'festival', 'name' => 'Festival', 'desc' => 'Vibrant green for events', 'color' => 'bg-green-600', 'accent' => 'text-white'],
                                ['id' => 'vip', 'name' => 'VIP', 'desc' => 'Luxury gold premium design', 'color' => 'bg-yellow-600', 'accent' => 'text-white'],
                                ['id' => 'custom', 'name' => 'Custom', 'desc' => 'Customizable template (Platinum only)', 'color' => 'bg-gradient-to-r from-purple-500 to-pink-500', 'accent' => 'text-white', 'premium' => true]
                            ];
                            @endphp

                            @foreach($templates as $template)
                            <div class="template-option" data-template="{{ $template['id'] }}">
                                <input type="radio" name="boarding_pass_template" value="{{ $template['id'] }}"
                                       id="template_{{ $template['id'] }}"
                                       {{ old('boarding_pass_template', 'unix') == $template['id'] ? 'checked' : '' }}
                                       class="sr-only">
                                <label for="template_{{ $template['id'] }}" class="template-card cursor-pointer block">
                                    <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-green-400 transition-all duration-200 template-card-inner">
                                        <!-- Template Preview -->
                                        <div class="h-20 {{ $template['color'] }} rounded-md mb-3 flex items-center justify-center relative overflow-hidden">
                                            @if(isset($template['premium']) && $template['premium'])
                                                <div class="absolute top-1 right-1">
                                                    <span class="bg-yellow-400 text-yellow-900 text-xs px-2 py-1 rounded-full font-bold">
                                                        👑 PLATINUM
                                                    </span>
                                                </div>
                                            @endif
                                            <div class="{{ $template['accent'] }} text-center">
                                                <i data-lucide="ticket" class="w-8 h-8 mx-auto mb-1"></i>
                                                <div class="text-xs font-mono">{{ strtoupper($template['id']) }}</div>
                                            </div>
                                        </div>

                                        <!-- Template Info -->
                                        <div class="text-center">
                                            <h3 class="font-semibold text-gray-900 dark:text-white mb-1">{{ $template['name'] }}</h3>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">{{ $template['desc'] }}</p>
                                        </div>

                                        <!-- Preview Link -->
                                        <div class="mt-3">
                                            <a href="/templates/{{ $template['id'] }}-preview" target="_blank"
                                               class="text-xs text-green-600 hover:text-green-700 font-medium flex items-center justify-center">
                                                <i data-lucide="external-link" class="w-3 h-3 mr-1"></i>
                                                Preview
                                            </a>
                                        </div>
                                    </div>
                                </label>
                            </div>
                            @endforeach
                        </div>

                        @error('boarding_pass_template')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Template Options -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="auto_generate_tickets" id="auto_generate_tickets"
                                   value="1" {{ old('auto_generate_tickets', true) ? 'checked' : '' }}
                                   class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="auto_generate_tickets" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                Auto-generate E-Tickets after payment
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="email_tickets_to_buyers" id="email_tickets_to_buyers"
                                   value="1" {{ old('email_tickets_to_buyers', true) ? 'checked' : '' }}
                                   class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                            <label for="email_tickets_to_buyers" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                Email E-Tickets to buyers automatically
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Event Details Section -->
                <div class="form-section">
                    <h2 class="section-title">
                        <i data-lucide="calendar" class="w-6 h-6"></i>
                        Event Details
                    </h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="label-modern">
                                Start Date & Time <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local" name="start_date" id="start_date"
                                   value="{{ old('start_date') }}" required
                                   class="input-modern">
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="end_date" class="label-modern">
                                End Date & Time <span class="text-red-500">*</span>
                            </label>
                            <input type="datetime-local" name="end_date" id="end_date"
                                   value="{{ old('end_date') }}" required
                                   class="input-modern">
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="venue_name" class="label-modern">
                                Venue Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="venue_name" id="venue_name"
                                   value="{{ old('venue_name') }}" required
                                   placeholder="Enter venue name"
                                   class="input-modern">
                            @error('venue_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="total_capacity" class="label-modern">
                                Total Capacity <span class="text-red-500">*</span>
                            </label>
                            <input type="number" name="total_capacity" id="total_capacity"
                                   value="{{ old('total_capacity') }}" required min="1"
                                   placeholder="Number of attendees"
                                   class="input-modern">
                            @error('total_capacity')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="lg:col-span-2">
                            <label for="venue_address" class="label-modern">
                                Venue Address <span class="text-red-500">*</span>
                            </label>
                            <textarea name="venue_address" id="venue_address" rows="3" required
                                      placeholder="Enter complete venue address"
                                      class="input-modern">{{ old('venue_address') }}</textarea>
                            @error('venue_address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="city" class="label-modern">
                                City <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="city" id="city"
                                   value="{{ old('city') }}" required
                                   placeholder="Enter city"
                                   class="input-modern">
                            @error('city')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="province" class="label-modern">
                                Province <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="province" id="province"
                                   value="{{ old('province') }}" required
                                   placeholder="Enter province"
                                   class="input-modern">
                            @error('province')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6">
                    <a href="{{ route('organizer.events.index') }}"
                       class="btn-secondary-modern">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary-modern">
                        <i data-lucide="plus" class="w-4 h-4 mr-2"></i>
                        Create Event
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Date validation
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    startDateInput.addEventListener('change', validateDates);
    endDateInput.addEventListener('change', validateDates);

    function validateDates() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        const now = new Date();

        if (startDate < now) {
            alert('Start date cannot be in the past');
            startDateInput.value = '';
            return;
        }

        if (startDate && endDate && startDate >= endDate) {
            alert('End date must be after start date');
            endDateInput.value = '';
        }
    }

    // Price formatting
    const priceInput = document.getElementById('price');
    priceInput.addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9]/g, '');
    });

    // Template selection handling
    const templateOptions = document.querySelectorAll('.template-option');
    templateOptions.forEach(option => {
        option.addEventListener('click', function() {
            const template = this.dataset.template;
            const radio = this.querySelector('input[type="radio"]');

            // Check if custom template and user doesn't have platinum badge
            if (template === 'custom') {
                // You can add badge check here via AJAX if needed
                // For now, we'll show a warning
                const userBadge = '{{ auth()->user()->badge_level_id ?? 0 }}';
                if (userBadge < 4) { // Assuming platinum is level 4
                    alert('Custom templates are only available for Platinum badge users. Please upgrade your badge to access this feature.');
                    return;
                }
            }

            // Select the radio button
            radio.checked = true;

            // Update visual selection
            templateOptions.forEach(opt => {
                opt.querySelector('.template-card-inner').classList.remove('border-green-500', 'bg-green-50');
                opt.querySelector('.template-card-inner').classList.add('border-gray-200');
            });

            this.querySelector('.template-card-inner').classList.add('border-green-500', 'bg-green-50');
            this.querySelector('.template-card-inner').classList.remove('border-gray-200');
        });
    });

    // Initialize default selection
    const defaultTemplate = document.querySelector('input[name="boarding_pass_template"]:checked');
    if (defaultTemplate) {
        const defaultOption = defaultTemplate.closest('.template-option');
        defaultOption.querySelector('.template-card-inner').classList.add('border-green-500', 'bg-green-50');
        defaultOption.querySelector('.template-card-inner').classList.remove('border-gray-200');
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });

        // Check if template is selected
        const selectedTemplate = document.querySelector('input[name="boarding_pass_template"]:checked');
        if (!selectedTemplate) {
            isValid = false;
            alert('Please select an E-Ticket template');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields');
        }
    });
});
</script>
@endpush
@endsection

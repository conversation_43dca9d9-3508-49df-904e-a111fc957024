<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class PaymentMethodController extends Controller
{
    /**
     * Display payment methods management
     */
    public function index(Request $request)
    {
        $query = PaymentMethod::query();

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by status
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%");
            });
        }

        $paymentMethods = $query->ordered()->paginate(20);

        // Statistics
        $stats = [
            'total_methods' => PaymentMethod::count(),
            'active_methods' => PaymentMethod::where('is_active', true)->count(),
            'manual_methods' => PaymentMethod::where('is_manual', true)->count(),
            'gateway_methods' => PaymentMethod::where('is_manual', false)->count(),
            'configured_gateways' => PaymentMethod::gateway()->get()->filter->isConfigured()->count(),
        ];

        return view('pages.admin.payment-methods.index', compact('paymentMethods', 'stats'));
    }

    /**
     * Show form to create new payment method
     */
    public function create()
    {
        return view('pages.admin.payment-methods.create');
    }

    /**
     * Store new payment method
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:manual,tripay,midtrans,xendit',
            'code' => 'required|string|max:50|unique:payment_methods,code',
            'category' => 'required|in:bank_transfer,e_wallet,qris,virtual_account,credit_card',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'fee_percentage' => 'required|numeric|min:0|max:100',
            'fee_fixed' => 'required|numeric|min:0',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_manual' => 'boolean',
            'sort_order' => 'required|integer|min:0',
            'instructions' => 'nullable|string',
        ]);

        $data = $request->except(['logo', 'manual_config', 'config']);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('payment-logos', 'public');
            $data['logo'] = '/storage/' . $logoPath;
        }

        // Handle manual config
        if ($request->is_manual) {
            $data['manual_config'] = $this->processManualConfig($request);
        } else {
            $data['config'] = $this->processGatewayConfig($request);
        }

        PaymentMethod::create($data);

        return redirect()->route('admin.payment-methods.index')
            ->with('success', 'Payment method created successfully!');
    }

    /**
     * Show payment method details
     */
    public function show(PaymentMethod $paymentMethod)
    {
        return view('pages.admin.payment-methods.show', compact('paymentMethod'));
    }

    /**
     * Show form to edit payment method
     */
    public function edit(PaymentMethod $paymentMethod)
    {
        return view('pages.admin.payment-methods.edit', compact('paymentMethod'));
    }

    /**
     * Update payment method
     */
    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:manual,tripay,midtrans,xendit',
            'code' => 'required|string|max:50|unique:payment_methods,code,' . $paymentMethod->id,
            'category' => 'required|in:bank_transfer,e_wallet,qris,virtual_account,credit_card',
            'description' => 'nullable|string',
            'icon' => 'nullable|string|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'fee_percentage' => 'required|numeric|min:0|max:100',
            'fee_fixed' => 'required|numeric|min:0',
            'min_amount' => 'required|numeric|min:0',
            'max_amount' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'is_manual' => 'boolean',
            'sort_order' => 'required|integer|min:0',
            'instructions' => 'nullable|string',
        ]);

        $data = $request->except(['logo', 'manual_config', 'config']);

        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo
            if ($paymentMethod->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $paymentMethod->logo))) {
                Storage::disk('public')->delete(str_replace('/storage/', '', $paymentMethod->logo));
            }

            $logoPath = $request->file('logo')->store('payment-logos', 'public');
            $data['logo'] = '/storage/' . $logoPath;
        }

        // Handle manual config
        if ($request->is_manual) {
            $data['manual_config'] = $this->processManualConfig($request);
            $data['config'] = null;
        } else {
            $data['config'] = $this->processGatewayConfig($request);
            $data['manual_config'] = null;
        }

        $paymentMethod->update($data);

        return redirect()->route('admin.payment-methods.index')
            ->with('success', 'Payment method updated successfully!');
    }

    /**
     * Toggle payment method status
     */
    public function toggleStatus(PaymentMethod $paymentMethod)
    {
        $paymentMethod->update(['is_active' => !$paymentMethod->is_active]);

        $status = $paymentMethod->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Payment method {$status} successfully!",
            'is_active' => $paymentMethod->is_active
        ]);
    }

    /**
     * Delete payment method
     */
    public function destroy(PaymentMethod $paymentMethod)
    {
        // Delete logo if exists
        if ($paymentMethod->logo && Storage::disk('public')->exists(str_replace('/storage/', '', $paymentMethod->logo))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $paymentMethod->logo));
        }

        $paymentMethod->delete();

        return redirect()->route('admin.payment-methods.index')
            ->with('success', 'Payment method deleted successfully!');
    }

    /**
     * Test gateway connection
     */
    public function testGateway(PaymentMethod $paymentMethod)
    {
        if ($paymentMethod->is_manual) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot test manual payment methods'
            ]);
        }

        try {
            // Test gateway connection based on type
            $result = $this->performGatewayTest($paymentMethod);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gateway test failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process manual payment configuration
     */
    private function processManualConfig(Request $request)
    {
        $config = [];

        if ($request->filled('bank_name')) {
            $config['bank_name'] = $request->bank_name;
        }

        if ($request->filled('account_number')) {
            $config['account_number'] = $request->account_number;
        }

        if ($request->filled('account_name')) {
            $config['account_name'] = $request->account_name;
        }

        if ($request->filled('bank_code')) {
            $config['bank_code'] = $request->bank_code;
        }

        return $config;
    }

    /**
     * Process gateway configuration
     */
    private function processGatewayConfig(Request $request)
    {
        $config = [];

        // TriPay config
        if ($request->filled('tripay_api_key')) {
            $config['api_key'] = $request->tripay_api_key;
        }
        if ($request->filled('tripay_private_key')) {
            $config['private_key'] = $request->tripay_private_key;
        }
        if ($request->filled('tripay_merchant_code')) {
            $config['merchant_code'] = $request->tripay_merchant_code;
        }

        // Midtrans config
        if ($request->filled('midtrans_server_key')) {
            $config['server_key'] = $request->midtrans_server_key;
        }
        if ($request->filled('midtrans_client_key')) {
            $config['client_key'] = $request->midtrans_client_key;
        }

        // Xendit config
        if ($request->filled('xendit_secret_key')) {
            $config['secret_key'] = $request->xendit_secret_key;
        }

        return $config;
    }

    /**
     * Perform gateway test
     */
    private function performGatewayTest(PaymentMethod $paymentMethod)
    {
        // This would implement actual gateway testing
        // For now, just check if required config is present

        if (!$paymentMethod->isConfigured()) {
            return [
                'success' => false,
                'message' => 'Gateway not properly configured'
            ];
        }

        return [
            'success' => true,
            'message' => 'Gateway connection successful'
        ];
    }

    /**
     * Update sort order
     */
    public function updateSortOrder(Request $request)
    {
        $request->validate([
            'items' => 'required|array',
            'items.*.id' => 'required|exists:payment_methods,id',
            'items.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($request->items as $item) {
            PaymentMethod::where('id', $item['id'])
                ->update(['sort_order' => $item['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Sort order updated successfully!'
        ]);
    }
}

@extends('layouts.app')

@section('title', 'Ticket Validation - Staff')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-qrcode mr-3 text-blue-600"></i>
                Ticket Validation System
            </h1>
            <p class="text-gray-600 dark:text-gray-400">Scan QR Code atau masukkan nomor tiket untuk validasi</p>
        </div>

        <!-- Validation Form -->
        <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
            <form id="validationForm" method="POST" action="{{ route('tickets.validate') }}">
                @csrf

                <!-- Validation Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Metode Validasi</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="type" value="qr_code" checked
                                   class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                            <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                <i class="fas fa-qrcode mr-1"></i>QR Code
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="type" value="ticket_number"
                                   class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500">
                            <span class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">
                                <i class="fas fa-ticket-alt mr-1"></i>Nomor Tiket
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Input Field -->
                <div class="mb-6">
                    <label for="identifier" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <span id="inputLabel">QR Code / Nomor Tiket</span>
                    </label>
                    <div class="relative">
                        <input type="text" id="identifier" name="identifier" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-lg"
                               placeholder="Scan QR Code atau ketik nomor tiket...">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <button type="button" id="scanButton"
                                    class="text-blue-600 hover:text-blue-800 focus:outline-none">
                                <i class="fas fa-camera text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="validateButton"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>Validasi Tiket</span>
                    <div class="hidden ml-2" id="loadingSpinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </button>
            </form>
        </div>

        <!-- QR Scanner Modal -->
        <div id="scannerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-lg w-full mx-4">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
                        <i class="fas fa-qrcode mr-2 text-blue-600"></i>
                        Scan QR Code Tiket
                    </h3>
                    <button id="closeScannerButton" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="flex justify-center">
                    <x-qr-scanner
                        id="validationScanner"
                        :width="350"
                        :height="350"
                        :on-scan="'handleValidationScan'"
                        :show-toggle-camera="true"
                        :show-torch="true"
                        :auto-start="true"
                    />
                </div>

                <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="flex items-start space-x-3">
                        <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                        <div class="text-sm text-blue-800 dark:text-blue-200">
                            <p class="font-medium mb-1">Tips untuk scanning yang optimal:</p>
                            <ul class="list-disc list-inside space-y-1 text-xs">
                                <li>Pastikan QR code berada dalam frame</li>
                                <li>Jaga jarak 10-30 cm dari kamera</li>
                                <li>Pastikan pencahayaan cukup</li>
                                <li>Hindari refleksi atau bayangan</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Validation Result -->
        <div id="validationResult" class="max-w-2xl mx-auto hidden">
            <!-- Result will be populated by JavaScript -->
        </div>

        <!-- Recent Validations -->
        <div class="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-history mr-2"></i>Validasi Terbaru
            </h2>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th class="px-6 py-3">Nomor Tiket</th>
                            <th class="px-6 py-3">Event</th>
                            <th class="px-6 py-3">Peserta</th>
                            <th class="px-6 py-3">Waktu Validasi</th>
                            <th class="px-6 py-3">Validator</th>
                            <th class="px-6 py-3">Status</th>
                        </tr>
                    </thead>
                    <tbody id="recentValidationsTable">
                        <!-- Data will be loaded by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Statistics -->
        <div class="max-w-4xl mx-auto mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-green-600 dark:text-green-400" id="todayValidations">-</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Validasi Hari Ini</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-blue-600 dark:text-blue-400" id="weekValidations">-</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Validasi Minggu Ini</div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center">
                <div class="text-3xl font-bold text-purple-600 dark:text-purple-400" id="totalActive">-</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Tiket Aktif</div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Global function to handle QR scan results
function handleValidationScan(decodedText, decodedResult) {
    console.log('QR Code scanned:', decodedText);

    // Fill the input field with scanned data
    document.getElementById('identifier').value = decodedText;

    // Close the scanner modal
    document.getElementById('scannerModal').classList.add('hidden');

    // Auto-submit the form
    setTimeout(() => {
        document.getElementById('validationForm').dispatchEvent(new Event('submit'));
    }, 500);
}

document.addEventListener('DOMContentLoaded', function() {
    loadRecentValidations();
    loadStatistics();

    // Auto-refresh every 30 seconds
    setInterval(() => {
        loadRecentValidations();
        loadStatistics();
    }, 30000);
});

// Handle form submission
document.getElementById('validationForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = document.getElementById('validateButton');
    const spinner = document.getElementById('loadingSpinner');

    // Show loading state
    button.disabled = true;
    spinner.classList.remove('hidden');

    try {
        const response = await fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const result = await response.json();
        showValidationResult(result);

        // Clear form and refresh data
        document.getElementById('identifier').value = '';
        loadRecentValidations();
        loadStatistics();

    } catch (error) {
        console.error('Validation error:', error);
        showValidationResult({
            success: false,
            message: 'Terjadi kesalahan jaringan'
        });
    } finally {
        button.disabled = false;
        spinner.classList.add('hidden');
    }
});

// QR Scanner functionality
document.getElementById('scanButton').addEventListener('click', function() {
    document.getElementById('scannerModal').classList.remove('hidden');
});

document.getElementById('closeScannerButton').addEventListener('click', function() {
    document.getElementById('scannerModal').classList.add('hidden');
});

function showValidationResult(result) {
    const resultDiv = document.getElementById('validationResult');
    const isSuccess = result.success;

    let statusIcon, statusColor, statusText;

    if (isSuccess) {
        statusIcon = 'fas fa-check-circle';
        statusColor = 'green';
        statusText = 'VALID';
    } else {
        statusIcon = 'fas fa-times-circle';
        statusColor = 'red';
        statusText = 'INVALID';
    }

    const borderClass = isSuccess ? 'border-green-500' : 'border-red-500';
    const textClass = isSuccess ? 'text-green-600' : 'text-red-600';
    const iconClass = isSuccess ? 'text-green-600' : 'text-red-600';

    resultDiv.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border-l-4 ${borderClass}">
            <div class="flex items-center mb-4">
                <i class="${statusIcon} text-2xl ${iconClass} mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">${statusText}</h3>
                    <p class="${textClass} font-medium">${result.message}</p>
                </div>
            </div>

            ${result.ticket ? `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Nomor Tiket</p>
                        <p class="font-semibold text-gray-900 dark:text-white">${result.ticket.ticket_number}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Event</p>
                        <p class="font-semibold text-gray-900 dark:text-white">${result.event?.title || 'N/A'}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Peserta</p>
                        <p class="font-semibold text-gray-900 dark:text-white">${result.ticket.attendee_name}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Status</p>
                        <p class="font-semibold text-gray-900 dark:text-white">${result.ticket.status}</p>
                    </div>
                </div>
            ` : ''}
        </div>
    `;

    resultDiv.classList.remove('hidden');
    resultDiv.scrollIntoView({ behavior: 'smooth' });
}

async function loadRecentValidations() {
    try {
        const response = await fetch('/tickets/validation/recent');
        const validations = await response.json();

        const tbody = document.getElementById('recentValidationsTable');
        tbody.innerHTML = validations.map(validation => `
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">${validation.ticket_number}</td>
                <td class="px-6 py-4">${validation.event_title}</td>
                <td class="px-6 py-4">${validation.attendee_name}</td>
                <td class="px-6 py-4">${validation.validated_at}</td>
                <td class="px-6 py-4">${validation.validator}</td>
                <td class="px-6 py-4">
                    <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                        ${validation.status}
                    </span>
                </td>
            </tr>
        `).join('');

    } catch (error) {
        console.error('Error loading recent validations:', error);
    }
}

async function loadStatistics() {
    try {
        const response = await fetch('/tickets/validation/stats');
        const stats = await response.json();

        document.getElementById('todayValidations').textContent = stats.today_validations;
        document.getElementById('weekValidations').textContent = stats.this_week_validations;
        document.getElementById('totalActive').textContent = stats.active_tickets;

    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}
</script>
@endpush
@endsection

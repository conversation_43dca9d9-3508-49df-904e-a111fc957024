<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserBadgeLevel;

class FixUserBadgesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting badge fix process...');

        // First, create default badge level if they don't exist
        $this->createDefaultBadges();

        // Then assign badges to users
        $this->assignBadgesToUsers();

        $this->command->info('Badge fix process completed!');
    }

    private function createDefaultBadges()
    {
        $this->command->info('Creating default badge level...');

        // Bronze Badge (Default)
        $bronze = UserBadgeLevel::firstOrCreate(
            ['name' => 'Bronze'],
            [
                'description' => 'Default badge for all new users',
                'color' => '#CD7F32',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                ],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]
        );

        // Silver Badge
        $silver = UserBadgeLevel::firstOrCreate(
            ['name' => 'Silver'],
            [
                'description' => 'For users with moderate activity',
                'color' => '#C0C0C0',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 500000,
                'min_uangtix_balance' => 100000,
                'min_transactions' => 5,
                'min_events_attended' => 3,
                'discount_percentage' => 5,
                'cashback_percentage' => 2,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 2,
            ]
        );

        // Gold Badge
        $gold = UserBadgeLevel::firstOrCreate(
            ['name' => 'Gold'],
            [
                'description' => 'For highly active users',
                'color' => '#FFD700',
                'icon' => 'fas fa-crown',
                'min_spent_amount' => 2000000,
                'min_uangtix_balance' => 500000,
                'min_transactions' => 15,
                'min_events_attended' => 10,
                'discount_percentage' => 10,
                'cashback_percentage' => 5,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                    'priority_support' => true,
                    'exclusive_events' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 3,
            ]
        );

        // Platinum Badge
        $platinum = UserBadgeLevel::firstOrCreate(
            ['name' => 'Platinum'],
            [
                'description' => 'For VIP users',
                'color' => '#E5E4E2',
                'icon' => 'fas fa-gem',
                'min_spent_amount' => 5000000,
                'min_uangtix_balance' => 1000000,
                'min_transactions' => 30,
                'min_events_attended' => 25,
                'discount_percentage' => 15,
                'cashback_percentage' => 8,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                    'priority_booking' => true,
                    'priority_support' => true,
                    'exclusive_events' => true,
                    'personal_concierge' => true,
                    'vip_lounge' => true,
                ],
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 4,
            ]
        );

        $this->command->info('✓ Created/verified badge level: Bronze, Silver, Gold, Platinum');
    }

    private function assignBadgesToUsers()
    {
        $this->command->info('Assigning badges to users...');

        // Get Bronze badge (default)
        $bronzeBadge = UserBadgeLevel::where('name', 'Bronze')->first();
        
        if (!$bronzeBadge) {
            $this->command->error('Bronze badge not found!');
            return;
        }

        // Get all users without badges
        $usersWithoutBadges = User::whereNull('badge_level_id')->get();
        $this->command->info("Found {$usersWithoutBadges->count()} users without badges");

        $assignedCount = 0;
        foreach ($usersWithoutBadges as $user) {
            $user->update([
                'badge_level_id' => $bronzeBadge->id,
                'badge_assigned_at' => now(),
                'badge_expires_at' => null, // Permanent
                'badge_duration_days' => null,
                'badge_auto_renew' => false,
            ]);
            
            $assignedCount++;
            $this->command->line("✓ Assigned Bronze badge to: {$user->name} ({$user->email})");
        }

        // Fix users with invalid badge_level_id
        $usersWithInvalidBadges = User::whereNotNull('badge_level_id')
            ->whereDoesntHave('badgeLevel')
            ->get();

        if ($usersWithInvalidBadges->count() > 0) {
            $this->command->info("Found {$usersWithInvalidBadges->count()} users with invalid badge references");
            
            foreach ($usersWithInvalidBadges as $user) {
                $user->update([
                    'badge_level_id' => $bronzeBadge->id,
                    'badge_assigned_at' => now(),
                    'badge_expires_at' => null,
                    'badge_duration_days' => null,
                    'badge_auto_renew' => false,
                ]);
                
                $assignedCount++;
                $this->command->line("✓ Fixed invalid badge for: {$user->name} ({$user->email})");
            }
        }

        $this->command->info("✓ Total badges assigned/fixed: {$assignedCount}");

        // Show final statistics
        $totalUsers = User::count();
        $usersWithBadges = User::whereNotNull('badge_level_id')->count();
        $bronzeUsers = User::where('badge_level_id', $bronzeBadge->id)->count();

        $this->command->info("\nFinal Statistics:");
        $this->command->info("- Total Users: {$totalUsers}");
        $this->command->info("- Users with Badges: {$usersWithBadges}");
        $this->command->info("- Bronze Badge Users: {$bronzeUsers}");
    }
}

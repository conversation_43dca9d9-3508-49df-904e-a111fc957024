/*
 * UangTix - GoPay/OVO Style CSS
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: <PERSON><PERSON><PERSON> Nazu<PERSON> P
 * Instagram: @seehai.dhafa
 */

/* GoPay/OVO Style Digital Wallet */
.uangtix-container {
    --uangtix-primary-blue: #0066CC;
    --uangtix-primary-green: #00AA5B;
    --uangtix-secondary-blue: #E6F3FF;
    --uangtix-text-dark: #1A1A1A;
    --uangtix-text-gray: #666666;
    --uangtix-bg-light: #F8FAFC;
    --uangtix-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --uangtix-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --uangtix-border-radius: 16px;
}

/* Container */
.uangtix-container {
    background: var(--uangtix-bg-light);
    min-height: 100vh;
    padding-bottom: 100px; /* Space for floating footer */
    font-family: 'Poppins', 'DM Sans', sans-serif;
}

/* Header Section */
.uangtix-container .wallet-header {
    background: linear-gradient(135deg, var(--uangtix-primary-blue) 0%, var(--uangtix-primary-green) 100%);
    padding: 20px 16px 40px;
    position: relative;
    overflow: hidden;
}

.uangtix-container .wallet-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    filter: blur(60px);
}

.uangtix-container .wallet-header::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50%;
    filter: blur(40px);
}

/* Header Content */
.uangtix-container .header-content {
    position: relative;
    z-index: 2;
}

.uangtix-container .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.uangtix-container .greeting {
    color: white;
    font-size: 16px;
    font-weight: 500;
}

.uangtix-container .user-name {
    color: white;
    font-size: 20px;
    font-weight: 700;
    margin-top: 4px;
}

.uangtix-container .header-actions {
    display: flex;
    gap: 12px;
}

.uangtix-container .header-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 12px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    cursor: pointer;
}

.uangtix-container .header-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Balance Card */
.uangtix-container .balance-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border-radius: var(--uangtix-border-radius);
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.uangtix-container .balance-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.uangtix-container .balance-amount {
    color: white;
    font-size: 32px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 8px;
}

.uangtix-container .balance-currency {
    font-size: 18px;
    opacity: 0.8;
    font-weight: 500;
}

.uangtix-container .balance-idr {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
}

.uangtix-container .balance-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.uangtix-container .balance-btn {
    flex: 1;
    background: white;
    color: var(--uangtix-primary-blue);
    border: none;
    border-radius: 12px;
    padding: 12px 16px;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
}

.uangtix-container .balance-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--uangtix-shadow-medium);
}

.uangtix-container .balance-btn.secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Quick Actions */
.uangtix-container .quick-actions {
    padding: 24px 16px;
    background: white;
    margin: -20px 16px 0;
    border-radius: var(--uangtix-border-radius);
    box-shadow: var(--uangtix-shadow-light);
    position: relative;
    z-index: 3;
}

.uangtix-container .quick-actions-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin-bottom: 16px;
}

.uangtix-container .actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.uangtix-container .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 12px 8px;
    border-radius: 12px;
    cursor: pointer;
}

.uangtix-container .action-item:hover {
    background: var(--uangtix-secondary-blue);
    transform: translateY(-2px);
    text-decoration: none;
}

.uangtix-container .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    font-size: 20px;
    color: white;
}

.uangtix-container .action-icon.deposit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%);
}

.uangtix-container .action-icon.withdraw {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
}

.uangtix-container .action-icon.transfer {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
}

.uangtix-container .action-icon.history {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
}

.uangtix-container .action-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--uangtix-text-dark);
    text-align: center;
}

/* Statistics Cards */
.uangtix-container .stats-section {
    padding: 24px 16px;
}

.uangtix-container .stats-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin-bottom: 16px;
}

.uangtix-container .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.uangtix-container .stat-card {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 20px;
    box-shadow: var(--uangtix-shadow-light);
    transition: all 0.3s ease;
}

.uangtix-container .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--uangtix-shadow-medium);
}

.uangtix-container .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
    margin-bottom: 4px;
}

.uangtix-container .stat-label {
    font-size: 12px;
    color: var(--uangtix-text-gray);
    font-weight: 500;
}

/* Recent Transactions */
.uangtix-container .transactions-section {
    padding: 0 16px 24px;
}

.uangtix-container .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.uangtix-container .section-title {
    font-size: 18px;
    font-weight: 700;
    color: var(--uangtix-text-dark);
}

.uangtix-container .view-all-btn {
    color: var(--uangtix-primary-blue);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.uangtix-container .view-all-btn:hover {
    color: var(--uangtix-primary-green);
    text-decoration: underline;
}

.uangtix-container .transaction-item {
    background: white;
    border-radius: var(--uangtix-border-radius);
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: var(--uangtix-shadow-light);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.uangtix-container .transaction-item:hover {
    transform: translateY(-1px);
    box-shadow: var(--uangtix-shadow-medium);
}

.uangtix-container .transaction-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 16px;
}

.uangtix-container .transaction-icon.deposit {
    background: var(--uangtix-primary-green);
}

.uangtix-container .transaction-icon.withdrawal {
    background: #F59E0B;
}

.uangtix-container .transaction-icon.transfer_in {
    background: var(--uangtix-primary-blue);
}

.uangtix-container .transaction-icon.transfer_out {
    background: #EF4444;
}

.uangtix-container .transaction-details {
    flex: 1;
}

.uangtix-container .transaction-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--uangtix-text-dark);
    margin-bottom: 2px;
}

.uangtix-container .transaction-subtitle {
    font-size: 12px;
    color: var(--uangtix-text-gray);
}

.uangtix-container .transaction-amount {
    font-size: 14px;
    font-weight: 700;
    text-align: right;
}

.uangtix-container .transaction-amount.positive {
    color: var(--uangtix-primary-green);
}

.uangtix-container .transaction-amount.negative {
    color: #EF4444;
}

.uangtix-container .transaction-date {
    font-size: 11px;
    color: var(--uangtix-text-gray);
    text-align: right;
    margin-top: 2px;
}

/* Responsive Design */
@media (min-width: 768px) {
    .uangtix-container {
        max-width: 480px;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    }
    
    .uangtix-container .actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .uangtix-container .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1024px) {
    .uangtix-container {
        max-width: 1200px;
        padding: 0 24px;
    }
    
    .uangtix-container .wallet-header {
        border-radius: var(--uangtix-border-radius);
        margin: 24px 0;
    }
    
    .uangtix-container .quick-actions {
        margin: -20px 0 24px;
    }
    
    .uangtix-container .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
}

/* PWA Enhancements */
@media (display-mode: standalone) {
    .uangtix-container .wallet-header {
        padding-top: calc(env(safe-area-inset-top) + 20px);
    }
}

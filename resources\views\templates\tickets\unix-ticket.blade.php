@php
    // Configuration with fallbacks
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#00FF00';
    $secondaryColor = $config['secondary_color'] ?? '#333333';
    $backgroundColor = $config['background_color'] ?? '#000000';
    $textColor = $config['text_color'] ?? '#00FF00';
    
    // Helper functions
    function getTicketValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket[$key] ?? $default;
        } elseif (is_object($ticket)) {
            return $ticket->{$key} ?? $default;
        }
        return $default;
    }
    
    function getEventValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['event'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->event)) {
            return $ticket->event->{$key} ?? $default;
        }
        return $default;
    }
    
    function getBuyerValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['buyer'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->buyer)) {
            return $ticket->buyer->{$key} ?? $default;
        }
        return $default;
    }
@endphp

<div class="ticket-container unix-ticket" style="
    background: {{ $backgroundColor }};
    color: {{ $textColor }};
    font-family: 'Courier New', monospace;
    width: 700px;
    height: 350px;
    position: relative;
    border: 2px solid {{ $primaryColor }};
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 0 20px {{ $primaryColor }}40;
">
    <!-- Terminal Header -->
    <div style="
        background: {{ $secondaryColor }};
        color: {{ $primaryColor }};
        padding: 8px 16px;
        font-size: 12px;
        border-bottom: 1px solid {{ $primaryColor }};
        display: flex;
        justify-content: space-between;
        align-items: center;
    ">
        <span>root@tixara:~$ cat ticket.txt</span>
        <span>{{ now()->format('Y-m-d H:i:s') }}</span>
    </div>

    <!-- Terminal Content -->
    <div style="padding: 16px; font-size: 12px; line-height: 1.4;">
        <!-- ASCII Art Header -->
        <pre style="color: {{ $primaryColor }}; margin: 0 0 16px 0; font-size: 10px;">
╔══════════════════════════════════════════════════════════════════════════════╗
║                              ELECTRONIC TICKET                              ║
║                                 TIXARA.MY.ID                                ║
╚══════════════════════════════════════════════════════════════════════════════╝</pre>

        <div style="display: flex; gap: 24px;">
            <!-- Left Section -->
            <div style="flex: 1;">
                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">EVENT_NAME:</span>
                    <span style="color: white;">{{ getEventValue($ticket, 'title', 'Event Title') }}</span>
                </div>

                @php
                    $startDate = getEventValue($ticket, 'start_date', now());
                    $parsedDate = \Carbon\Carbon::parse($startDate);
                @endphp
                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">DATE_TIME:</span>
                    <span style="color: white;">{{ $parsedDate->format('Y-m-d H:i:s') }}</span>
                </div>

                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">VENUE:</span>
                    <span style="color: white;">{{ getEventValue($ticket, 'venue_name', 'TBA') }}</span>
                </div>

                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">LOCATION:</span>
                    <span style="color: white;">{{ getEventValue($ticket, 'city', 'Location TBA') }}</span>
                </div>

                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">ATTENDEE:</span>
                    <span style="color: white;">{{ getTicketValue($ticket, 'attendee_name', getBuyerValue($ticket, 'name', 'Guest')) }}</span>
                </div>

                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">EMAIL:</span>
                    <span style="color: white;">{{ getTicketValue($ticket, 'attendee_email', getBuyerValue($ticket, 'email', 'N/A')) }}</span>
                </div>

                @php
                    $price = getTicketValue($ticket, 'price', 0);
                @endphp
                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }};">PRICE:</span>
                    <span style="color: white;">IDR {{ number_format($price, 0, ',', '.') }}</span>
                </div>

                <div style="margin-bottom: 16px;">
                    <span style="color: {{ $primaryColor }};">TICKET_ID:</span>
                    <span style="color: white;">{{ getTicketValue($ticket, 'ticket_number', getTicketValue($ticket, 'ticket_code', 'TIX-000000')) }}</span>
                </div>

                <!-- Status Bar -->
                <div style="
                    background: {{ $secondaryColor }};
                    border: 1px solid {{ $primaryColor }};
                    padding: 8px;
                    margin-top: 16px;
                ">
                    <span style="color: {{ $primaryColor }};">STATUS:</span>
                    <span style="color: #00FF00;">VALID</span>
                    <span style="margin-left: 20px; color: {{ $primaryColor }};">CHECKSUM:</span>
                    <span style="color: white;">{{ substr(md5(getTicketValue($ticket, 'ticket_code', 'TIX-000000')), 0, 8) }}</span>
                </div>
            </div>

            <!-- Right Section - QR Code -->
            <div style="
                width: 180px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border-left: 1px solid {{ $primaryColor }};
                padding-left: 20px;
            ">
                <div style="margin-bottom: 12px;">
                    <span style="color: {{ $primaryColor }}; font-size: 10px;">QR_CODE:</span>
                </div>
                
                <div style="
                    background: white;
                    padding: 12px;
                    border: 1px solid {{ $primaryColor }};
                    margin-bottom: 12px;
                ">
                    @php
                        $qrData = getTicketValue($ticket, 'qr_code', 
                            getTicketValue($ticket, 'ticket_number', 
                                getTicketValue($ticket, 'ticket_code', 'INVALID')
                            )
                        );
                    @endphp
                    @if(class_exists('SimpleSoftwareIO\QrCode\Facades\QrCode'))
                        {!! QrCode::size(100)->generate($qrData) !!}
                    @else
                        <div style="
                            width: 100px;
                            height: 100px;
                            background: #f3f4f6;
                            border: 1px dashed #666;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 8px;
                            color: #666;
                            text-align: center;
                        ">
                            QR<br>{{ substr($qrData, 0, 8) }}
                        </div>
                    @endif
                </div>

                <div style="text-align: center; font-size: 8px;">
                    <div style="color: {{ $primaryColor }};">SCAN_TO_VERIFY</div>
                    <div style="color: #666; margin-top: 4px;">{{ $qrData }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Terminal Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: {{ $secondaryColor }};
        color: {{ $primaryColor }};
        padding: 4px 16px;
        font-size: 10px;
        border-top: 1px solid {{ $primaryColor }};
    ">
        <span>root@tixara:~$ echo "Ticket generated successfully" | tee /dev/stdout</span>
    </div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Courier+New:wght@400;700&display=swap');

.unix-ticket {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    box-sizing: border-box;
}

.unix-ticket * {
    box-sizing: border-box;
}

.unix-ticket pre {
    font-family: 'Courier New', monospace;
    white-space: pre;
    overflow: hidden;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .unix-ticket {
        width: 100% !important;
        max-width: 700px !important;
        height: auto !important;
        min-height: 350px !important;
    }
}

@media print {
    .unix-ticket {
        width: 700px !important;
        height: 350px !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }
}

/* Glowing effect */
.unix-ticket {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px {{ $primaryColor }}40;
    }
    to {
        box-shadow: 0 0 30px {{ $primaryColor }}60, 0 0 40px {{ $primaryColor }}40;
    }
}
</style>

<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\PasswordResetController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\Organizer\EventController as OrganizerEventController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Staff\DashboardController as StaffDashboardController;
use App\Http\Controllers\Organizer\DashboardController as OrganizerDashboardController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// SEO Routes
Route::get('/robots.txt', function () {
    $content = \App\Services\SeoService::getRobotsTxt();
    return response($content)->header('Content-Type', 'text/plain');
})->name('robots');

Route::get('/sitemap.xml', function () {
    $urls = \App\Services\SeoService::generateSitemapUrls();

    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    foreach ($urls as $url) {
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url['url']) . '</loc>' . "\n";
        $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
        $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";
    }

    $xml .= '</urlset>';

    return response($xml)->header('Content-Type', 'application/xml');
})->name('sitemap');

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/search', [HomeController::class, 'search'])->name('search');
Route::get('/category/{category}', [HomeController::class, 'ticketsByCategory'])->name('category');
Route::get('/nearby', [HomeController::class, 'nearbyTickets'])->name('nearby');

// Category Routes
Route::prefix('categories')->name('categories.')->group(function () {
    Route::get('/', [CategoryController::class, 'index'])->name('index');
    Route::get('/search', [CategoryController::class, 'search'])->name('search');
    Route::get('/recommendations', [CategoryController::class, 'recommendations'])->name('recommendations');
    Route::get('/{category}', [CategoryController::class, 'show'])->name('show');
    Route::get('/{category}/analytics', [CategoryController::class, 'analytics'])->name('analytics')->middleware('admin');
});

// PWA Routes
Route::get('/offline', function () {
    return view('offline');
})->name('offline');

// Development/Testing Routes
Route::get('/credentials', function () {
    return view('auth.credentials');
})->name('auth.credentials');

// Demo Purchase Form
Route::get('/demo/purchase-modern', function () {
    $event = (object) [
        'id' => 1,
        'title' => 'TechConf 2024 - Future of Technology',
        'price' => 250000,
        'poster_url' => 'https://via.placeholder.com/400x300/3b82f6/ffffff?text=TechConf+2024',
        'start_date' => \Carbon\Carbon::parse('2024-12-15 09:00:00'),
        'venue_name' => 'Jakarta Convention Center',
        'city' => 'Jakarta',
        'organizer' => (object) ['name' => 'TechCorp Indonesia']
    ];
    $maxQuantity = 10;
    return view('tickets.purchase-modern', compact('event', 'maxQuantity'));
})->name('demo.purchase-modern');

// Demo Payment Form
Route::get('/demo/payment-modern', function () {
    $order = (object) [
        'id' => 1,
        'order_number' => 'TXR-' . date('Ymd') . '-001',
        'subtotal' => 250000,
        'admin_fee' => 5000,
        'total_amount' => 255000,
        'quantity' => 1,
        'customer_name' => 'John Doe',
        'event' => (object) [
            'title' => 'TechConf 2024 - Future of Technology',
            'poster_url' => 'https://via.placeholder.com/400x300/3b82f6/ffffff?text=TechConf+2024',
            'start_date' => \Carbon\Carbon::parse('2024-12-15 09:00:00'),
            'venue_name' => 'Jakarta Convention Center',
            'city' => 'Jakarta'
        ]
    ];
    return view('orders.payment-modern', compact('order'));
})->name('demo.payment-modern');

// Demo route for payment processing
Route::post('/orders/{order}/process-payment', function ($order) {
    return response()->json([
        'success' => true,
        'message' => 'Payment processed successfully',
        'redirect_url' => '/demo/payment-success'
    ]);
})->name('orders.process-payment');

// Demo payment success page
Route::get('/demo/payment-success', function () {
    return view('demo.payment-success');
})->name('demo.payment-success');

// API Documentation
Route::get('/api/docs', function () {
    return view('api.documentation');
})->name('api.documentation');

// AJAX Routes
Route::get('/api/featured-tickets', [HomeController::class, 'featuredTickets'])->name('api.featured-tickets');
Route::get('/api/latest-tickets', [HomeController::class, 'latestTickets'])->name('api.latest-tickets');
Route::get('/api/popular-tickets', [HomeController::class, 'popularTickets'])->name('api.popular-tickets');
Route::get('/api/search-suggestions', [HomeController::class, 'searchSuggestions'])->name('api.search-suggestions');

// Banner API Routes
Route::get('/api/banners', [App\Http\Controllers\Api\BannerController::class, 'index'])->name('api.banners');
Route::get('/api/banners/{banner}', [App\Http\Controllers\Api\BannerController::class, 'show'])->name('api.banners.show');

// Ticket Routes (Public) - Event listing and browsing
Route::prefix('tickets')->name('tickets.')->group(function () {
    Route::get('/', [EventController::class, 'index'])->name('index');
    Route::get('/search', [EventController::class, 'search'])->name('search');
    Route::get('/featured', [EventController::class, 'featured'])->name('featured');
    Route::get('/upcoming', [EventController::class, 'upcoming'])->name('upcoming');
    Route::get('/nearby', [EventController::class, 'nearby'])->name('nearby');
    Route::get('/category/{category}', [EventController::class, 'byCategory'])->name('category');
    Route::get('/{event}', [EventController::class, 'show'])->name('show');

    // Wishlist routes (requires auth)
    Route::middleware('auth')->group(function () {
        Route::post('/{event}/wishlist', [EventController::class, 'addToWishlist'])->name('wishlist.add');
        Route::delete('/{event}/wishlist', [EventController::class, 'removeFromWishlist'])->name('wishlist.remove');

        // User ticket management routes
        Route::get('/my-tickets', [TicketController::class, 'myTickets'])->name('my-tickets');
        Route::get('/{event}/purchase', [TicketController::class, 'purchase'])->name('purchase');
        Route::get('/{event}/purchase-modern', function($event) {
            $event = \App\Models\Event::findOrFail($event);
            $maxQuantity = 10; // Default max quantity
            return view('tickets.purchase-modern', compact('event', 'maxQuantity'));
        })->name('purchase-modern');
        Route::post('/{event}', [TicketController::class, 'store'])->name('store');
        Route::get('/{ticket}/show', [TicketController::class, 'show'])->name('ticket.show');
        Route::get('/{ticket}/download', [TicketController::class, 'download'])->name('download');
        Route::get('/{ticket}/print', [TicketController::class, 'print'])->name('print');
        Route::get('/{ticket}/qr-code', [TicketValidationController::class, 'qrCode'])->name('qr-code');
        Route::post('/{ticket}/cancel', [TicketController::class, 'cancel'])->name('cancel');
        Route::post('/validate', [TicketController::class, 'validateTicket'])->name('validate');
    });
});

// Wishlist Management Routes
Route::middleware('auth')->prefix('wishlist')->name('wishlist.')->group(function () {
    Route::get('/', [App\Http\Controllers\WishlistController::class, 'index'])->name('index');
    Route::delete('/remove/{event}', [App\Http\Controllers\WishlistController::class, 'remove'])->name('remove');
    Route::delete('/clear', [App\Http\Controllers\WishlistController::class, 'clear'])->name('clear');
    Route::get('/count', [App\Http\Controllers\WishlistController::class, 'count'])->name('count');
});

// Redirect old routes for backward compatibility
// Remove the problematic self-redirecting routes that cause loops
Route::redirect('/tickets/tickets', '/tickets');
Route::redirect('/tickets/tickets/{path}', '/tickets/{path}')->where('path', '.*');

// Alias routes for backward compatibility
Route::get('/events/{event}', [EventController::class, 'show'])->name('events.show');

// Guide Routes
Route::prefix('guide')->name('guide.')->group(function () {
    Route::get('/event-creator', [App\Http\Controllers\GuideController::class, 'eventCreator'])->name('event-creator');
    Route::get('/organizer', [App\Http\Controllers\GuideController::class, 'organizer'])->name('organizer');
    Route::get('/buyer', [App\Http\Controllers\GuideController::class, 'buyer'])->name('buyer');
    Route::get('/faq', [App\Http\Controllers\GuideController::class, 'faq'])->name('faq');
    Route::get('/help-center', [App\Http\Controllers\GuideController::class, 'helpCenter'])->name('help-center');
    Route::get('/search', [App\Http\Controllers\GuideController::class, 'search'])->name('search');
});

// Reports routes
Route::get('/reports', [App\Http\Controllers\ReportController::class, 'index'])->name('reports.index');
Route::post('/reports', [App\Http\Controllers\ReportController::class, 'store'])->name('reports.store');
Route::middleware('auth')->group(function () {
    Route::get('/reports/my-reports', [App\Http\Controllers\ReportController::class, 'myReports'])->name('reports.my-reports');
    Route::get('/reports/{report}', [App\Http\Controllers\ReportController::class, 'show'])->name('reports.show');
});

// Feedback routes
Route::get('/feedback', [App\Http\Controllers\FeedbackController::class, 'index'])->name('feedback.index');
Route::post('/feedback', [App\Http\Controllers\FeedbackController::class, 'store'])->name('feedback.store');
Route::middleware('auth')->group(function () {
    Route::get('/feedback/my-feedback', [App\Http\Controllers\FeedbackController::class, 'myFeedback'])->name('feedback.my-feedback');
    Route::get('/feedback/{feedback}', [App\Http\Controllers\FeedbackController::class, 'show'])->name('feedback.show');
});

// ArtPosure Routes
Route::prefix('artposure')->name('artposure.')->group(function () {
    Route::get('/', [App\Http\Controllers\ArtPosureController::class, 'index'])->name('index');
    Route::get('/socmed', [App\Http\Controllers\ArtPosureController::class, 'socmed'])->name('socmed');
    Route::get('/website', [App\Http\Controllers\ArtPosureController::class, 'website'])->name('website');
    Route::get('/package', [App\Http\Controllers\ArtPosureController::class, 'package'])->name('package');
    Route::get('/addon', [App\Http\Controllers\ArtPosureController::class, 'addon'])->name('addon');
    Route::get('/case-studies', [App\Http\Controllers\ArtPosureController::class, 'caseStudies'])->name('case-studies');
    Route::post('/inquiry', [App\Http\Controllers\ArtPosureController::class, 'inquiry'])->name('inquiry');
});

// Contact Routes
Route::prefix('contact')->name('contact.')->group(function () {
    Route::get('/', [App\Http\Controllers\ContactController::class, 'index'])->name('index');
    Route::post('/send', [App\Http\Controllers\ContactController::class, 'send'])->name('send');
    Route::post('/feedback', [App\Http\Controllers\ContactController::class, 'feedback'])->name('feedback');
    Route::get('/info', [App\Http\Controllers\ContactController::class, 'getContactInfo'])->name('info');
    Route::get('/faq', [App\Http\Controllers\ContactController::class, 'getFaq'])->name('faq');
});

// Developer Routes
Route::prefix('developer')->name('developer.')->group(function () {
    Route::get('/', [App\Http\Controllers\DeveloperController::class, 'index'])->name('index');
    Route::get('/info', [App\Http\Controllers\DeveloperController::class, 'getDeveloperInfo'])->name('info');
    Route::get('/tech-stack', [App\Http\Controllers\DeveloperController::class, 'getTechStack'])->name('tech-stack');
    Route::get('/stats', [App\Http\Controllers\DeveloperController::class, 'getProjectStats'])->name('stats');
    Route::post('/message', [App\Http\Controllers\DeveloperController::class, 'sendMessage'])->name('message');
});

// Halaman Tiket Saya (tanpa redirect)
Route::middleware('auth')->get('/tiket-saya', [TicketController::class, 'tiketSaya'])->name('tiket-saya');

// Redirect routes for my-tickets (for backward compatibility and convenience)
Route::middleware('auth')->get('/my-tickets', function () {
    return redirect()->route('tiket-saya');
})->name('my-tickets');

// Debug route to check if routing works
Route::get('/debug-routes', function () {
    $routes = collect(Route::getRoutes())->map(function ($route) {
        return [
            'method' => implode('|', $route->methods()),
            'uri' => $route->uri(),
            'name' => $route->getName(),
            'action' => $route->getActionName(),
        ];
    })->filter(function ($route) {
        return str_contains($route['uri'], 'tickets') || str_contains($route['name'] ?? '', 'tickets');
    });

    return response()->json($routes);
})->name('debug.routes');

// Order Routes (Requires Auth)
Route::middleware('auth')->prefix('orders')->name('orders.')->group(function () {
    Route::get('/', [OrderController::class, 'index'])->name('index');
    Route::get('/{order}', [OrderController::class, 'show'])->name('show');
    Route::get('/{order}/payment', [OrderController::class, 'payment'])->name('payment');
    Route::post('/{order}/payment', [OrderController::class, 'processPayment'])->name('process-payment');
    Route::get('/{order}/payment-details', [OrderController::class, 'paymentDetails'])->name('payment-details');
    Route::get('/{order}/check-status', [OrderController::class, 'checkPaymentStatus'])->name('check-status');
    Route::get('/{order}/success', [OrderController::class, 'success'])->name('success');
    Route::post('/{order}/cancel', [OrderController::class, 'cancel'])->name('cancel');
});

// Webhook Routes (No Auth Required)
Route::prefix('webhooks')->name('webhooks.')->group(function () {
    Route::post('/xendit', [App\Http\Controllers\WebhookController::class, 'xendit'])->name('xendit');
    Route::post('/midtrans', [App\Http\Controllers\WebhookController::class, 'midtrans'])->name('midtrans');
    Route::post('/tripay', [App\Http\Controllers\WebhookController::class, 'tripay'])->name('tripay');
});

// Ticket Validation Routes (Updated paths)
// Public authenticity checking
Route::get('/authenticity-check', [App\Http\Controllers\TicketValidationController::class, 'authenticityCheck'])->name('tickets.authenticity-check');
Route::post('/authenticity-check/verify', [App\Http\Controllers\TicketValidationController::class, 'checkAuthenticity'])->name('tickets.check-authenticity');

// Staff validation (requires auth)
Route::middleware('auth')->group(function () {
    Route::get('/validation', function() {
        return view('pages.validation.staff-scanner');
    })->name('validation.index');
    Route::get('/validation/staff', function() {
        return view('pages.validation.staff-scanner');
    })->name('validation.staff');
    Route::post('/validation/validate', [App\Http\Controllers\TicketValidationController::class, 'validateTicket'])->name('tickets.validate');
    Route::get('/validation/stats', [App\Http\Controllers\TicketValidationController::class, 'stats'])->name('tickets.validation.stats');
    Route::get('/validation/recent', [App\Http\Controllers\TicketValidationController::class, 'recentValidations'])->name('tickets.validation.recent');
    Route::post('/validation/bulk-validate', [App\Http\Controllers\TicketValidationController::class, 'bulkValidate'])->name('tickets.bulk-validate');

    // New staff scan and use routes
    Route::post('/validation/staff-scan', [App\Http\Controllers\TicketValidationController::class, 'staffScan'])->name('tickets.staff-scan');
    Route::post('/validation/staff-use', [App\Http\Controllers\TicketValidationController::class, 'staffUse'])->name('tickets.staff-use');
});

// Ticket verification (public)
Route::get('/tickets/verify/{qrCode}', [App\Http\Controllers\TicketValidationController::class, 'verify'])->name('tickets.verify');

// Boarding Pass Template Preview Routes (public)
Route::get('/templates/unix-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-UNIX-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference'],
            'organizer' => (object) ['name' => 'TechCorp Indonesia']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.unix-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.unix-preview');

Route::get('/templates/minimalist-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-MIN-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference'],
            'organizer' => (object) ['name' => 'TechCorp Indonesia']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.minimalist-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.minimalist-preview');

Route::get('/templates/pro-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-PRO-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference'],
            'organizer' => (object) ['name' => 'TechCorp Indonesia']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.pro-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.pro-preview');

Route::get('/templates/custom-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-CUSTOM-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference'],
            'organizer' => (object) ['name' => 'TechCorp Indonesia']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.custom-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.custom-preview');

// Additional Template Previews
Route::get('/templates/elegant-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-ELG-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Elegant Gala Night 2024'),
            'location' => request('location', 'Grand Ballroom, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 19:00:00')),
            'category' => (object) ['name' => 'Gala Event'],
            'organizer' => (object) ['name' => 'Elegant Events']
        ],
        'status' => 'active',
        'seat_number' => 'VIP-001'
    ];

    return view('templates.tickets.elegant-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.elegant-preview');

Route::get('/templates/modern-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-MOD-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Modern Art Exhibition 2024'),
            'location' => request('location', 'Modern Gallery, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 10:00:00')),
            'category' => (object) ['name' => 'Art Exhibition'],
            'organizer' => (object) ['name' => 'Modern Arts']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.modern-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.modern-preview');

Route::get('/templates/classic-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-CLS-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Classical Concert 2024'),
            'location' => request('location', 'Concert Hall, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 20:00:00')),
            'category' => (object) ['name' => 'Classical Music'],
            'organizer' => (object) ['name' => 'Classical Arts']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.classic-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.classic-preview');

Route::get('/templates/neon-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-NEO-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Neon Night Party 2024'),
            'location' => request('location', 'Club Neon, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 22:00:00')),
            'category' => (object) ['name' => 'Night Party'],
            'organizer' => (object) ['name' => 'Neon Events']
        ],
        'status' => 'active',
        'seat_number' => 'VIP-001'
    ];

    return view('templates.tickets.neon-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.neon-preview');

Route::get('/templates/retro-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-RET-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Retro 80s Party 2024'),
            'location' => request('location', 'Retro Club, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 21:00:00')),
            'category' => (object) ['name' => 'Retro Party'],
            'organizer' => (object) ['name' => 'Retro Events']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.retro-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.retro-preview');

Route::get('/templates/corporate-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-CRP-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Corporate Summit 2024'),
            'location' => request('location', 'Business Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Business Conference'],
            'organizer' => (object) ['name' => 'Corporate Events']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.corporate-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.corporate-preview');

Route::get('/templates/festival-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-FES-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Summer Music Festival 2024'),
            'location' => request('location', 'Festival Grounds, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 16:00:00')),
            'category' => (object) ['name' => 'Music Festival'],
            'organizer' => (object) ['name' => 'Festival Productions']
        ],
        'status' => 'active',
        'seat_number' => 'GA-001'
    ];

    return view('templates.tickets.festival-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.festival-preview');

Route::get('/templates/vip-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'boarding_pass_id' => request('boardingPassId', 'BP-VIP-' . strtoupper(substr(md5(time()), 0, 8))),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-' . date('Ymd') . '-001'
        ],
        'event' => (object) [
            'title' => request('eventName', 'VIP Exclusive Event 2024'),
            'location' => request('location', 'VIP Lounge, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 18:00:00')),
            'category' => (object) ['name' => 'VIP Event'],
            'organizer' => (object) ['name' => 'VIP Events']
        ],
        'status' => 'active',
        'seat_number' => 'VIP-001'
    ];

    return view('templates.tickets.vip-boarding-pass', ['ticket' => $sampleTicket]);
})->name('templates.vip-preview');

// Legacy boarding pass preview (backward compatibility)
Route::get('/templates/boarding-pass-preview', function() {
    return redirect()->route('templates.unix-preview', request()->all());
})->name('templates.boarding-pass-preview');

Route::get('/boarding-pass-preview', function() {
    return view('tickets.boarding-pass-preview');
})->name('boarding-pass-preview');

// Additional template previews
Route::get('/templates/classic-ticket-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-20241215-XYZ789'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.classic-ticket', ['ticket' => $sampleTicket]);
})->name('templates.classic-ticket-preview');

Route::get('/templates/modern-card-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-20241215-XYZ789'
        ],
        'event' => (object) [
            'title' => request('eventName', 'TechConf 2024 - Future of Technology'),
            'location' => request('location', 'Jakarta Convention Center, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 09:00:00')),
            'category' => (object) ['name' => 'Technology Conference']
        ],
        'status' => 'active',
        'seat_number' => 'A001'
    ];

    return view('templates.tickets.modern-card', ['ticket' => $sampleTicket]);
})->name('templates.modern-card-preview');

Route::get('/templates/concert-festival-preview', function() {
    $sampleTicket = (object) [
        'id' => 1,
        'ticket_number' => request('ticketNumber', 'TIK-20241215-ABC123'),
        'attendee_name' => request('attendeeName', 'John Doe'),
        'qr_code' => 'SAMPLE-QR-CODE-FOR-PREVIEW',
        'order' => (object) [
            'customer_email' => '<EMAIL>',
            'customer_phone' => '+62 812-3456-7890',
            'created_at' => now(),
            'unit_price' => 250000,
            'order_number' => 'ORD-20241215-XYZ789'
        ],
        'event' => (object) [
            'title' => request('eventName', 'Summer Music Festival 2024'),
            'location' => request('location', 'Gelora Bung Karno Stadium, Jakarta'),
            'start_date' => \Carbon\Carbon::parse(request('eventDateTime', '2024-12-15 19:00:00')),
            'category' => (object) ['name' => 'Music Festival']
        ],
        'status' => 'active',
        'seat_number' => 'VIP-001'
    ];

    return view('templates.tickets.concert-festival', ['ticket' => $sampleTicket]);
})->name('templates.concert-festival-preview');

// Payment webhook routes (no auth required)
Route::post('/webhook/payment', [\App\Http\Controllers\PaymentWebhookController::class, 'handle'])->name('webhook.payment');

// Voucher Routes (Requires Auth)
Route::middleware('auth')->prefix('vouchers')->name('vouchers.')->group(function () {
    Route::post('/validate', [App\Http\Controllers\VoucherController::class, 'validateVoucher'])->name('validate');
    Route::get('/available', [App\Http\Controllers\VoucherController::class, 'available'])->name('available');
    Route::get('/{code}', [App\Http\Controllers\VoucherController::class, 'show'])->name('show');
    Route::get('/usage/history', [App\Http\Controllers\VoucherController::class, 'userUsage'])->name('user-usage');
});

// UangTix Routes (Accessible to all authenticated users)
Route::middleware('auth')->prefix('uangtix')->name('uangtix.')->group(function () {
    Route::get('/', [App\Http\Controllers\UangTixController::class, 'index'])->name('index');
    Route::get('/balance', [App\Http\Controllers\UangTixController::class, 'balance'])->name('balance');
    Route::get('/transactions', [App\Http\Controllers\UangTixController::class, 'transactions'])->name('transactions');
    Route::post('/deposit', [App\Http\Controllers\UangTixController::class, 'deposit'])->name('deposit');
    Route::post('/withdraw', [App\Http\Controllers\UangTixController::class, 'withdraw'])->name('withdraw');
    Route::post('/transfer', [App\Http\Controllers\UangTixController::class, 'transfer'])->name('transfer');
    Route::get('/exchange-rate', [App\Http\Controllers\UangTixController::class, 'exchangeRate'])->name('exchange-rate');
});

// Purchase History Route (alias for orders.index)
Route::middleware('auth')->get('/history-pembelian', [OrderController::class, 'index'])->name('history-pembelian');

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Register
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);

    // Organizer Register (alias for register with role=penjual)
    Route::get('/organizer/register', [AuthController::class, 'showOrganizerRegister'])->name('organizer.register');
    Route::post('/organizer/register', [AuthController::class, 'registerOrganizer'])->name('organizer.register.submit');

    // Password Reset
    Route::get('/forgot-password', [PasswordResetController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [PasswordResetController::class, 'forgotPassword'])->name('password.email');
    Route::get('/reset-password/{token}', [PasswordResetController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [PasswordResetController::class, 'resetPassword'])->name('password.update');
});

// Authenticated Routes
Route::middleware('auth')->group(function () {
    // Logout
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Email Verification
    Route::get('/verify-email', [AuthController::class, 'showVerifyEmail'])->name('auth.verify-email');
    Route::post('/verify-email', [AuthController::class, 'verifyEmail']);
    Route::post('/resend-otp', [AuthController::class, 'resendOtp'])->name('auth.resend-otp');

    // Change Password
    Route::get('/change-password', [PasswordResetController::class, 'showChangePassword'])->name('password.change');
    Route::post('/change-password', [PasswordResetController::class, 'changePassword']);

    // Dashboard Routes (Role-based)
    Route::middleware('admin')->group(function () {
        Route::get('/admin/dashboard', [AdminDashboardController::class, 'index'])->name('admin.dashboard');
        Route::get('/admin/dashboard/export', [AdminDashboardController::class, 'exportReport'])->name('admin.dashboard.export');
        Route::get('/admin/dashboard/report/{type}', [AdminDashboardController::class, 'generateReport'])->name('admin.dashboard.report');
        Route::get('/admin/dashboard/analytics', [AdminDashboardController::class, 'analytics'])->name('admin.dashboard.analytics');

        // Admin Report Management
        Route::prefix('admin/reports')->name('admin.reports.')->group(function () {
            Route::get('/export', [AdminDashboardController::class, 'exportReport'])->name('export');
            Route::get('/generate/{type}', [AdminDashboardController::class, 'generateReport'])->name('generate');
        });

        // Admin Feedback Management
        Route::prefix('admin/feedback')->name('admin.feedback.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\FeedbackController::class, 'index'])->name('index');
            Route::get('/recent', [\App\Http\Controllers\Admin\FeedbackController::class, 'recent'])->name('recent');
            Route::get('/pending-count', [\App\Http\Controllers\Admin\FeedbackController::class, 'pendingCount'])->name('pending-count');
            Route::patch('/{feedback}/status', [\App\Http\Controllers\Admin\FeedbackController::class, 'updateStatus'])->name('update-status');
            Route::post('/{feedback}/respond', [\App\Http\Controllers\Admin\FeedbackController::class, 'respond'])->name('respond');
            Route::delete('/{feedback}', [\App\Http\Controllers\Admin\FeedbackController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [\App\Http\Controllers\Admin\FeedbackController::class, 'bulkAction'])->name('bulk-action');
        });

        // Admin Event Management
        Route::prefix('admin/tickets')->name('admin.tickets.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\TicketController::class, 'index'])->name('index');

            Route::get('/create', [App\Http\Controllers\Admin\TicketController::class, 'create'])->name('create');

            Route::post('/', [App\Http\Controllers\Admin\TicketController::class, 'store'])->name('store');

            Route::get('/{event}/edit', [App\Http\Controllers\Admin\TicketController::class, 'edit'])->name('edit');

            Route::put('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'update'])->name('update');
            Route::delete('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'destroy'])->name('destroy');
            Route::get('/{event}', [App\Http\Controllers\Admin\TicketController::class, 'show'])->name('show');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\TicketController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/{event}/toggle-featured', [App\Http\Controllers\Admin\TicketController::class, 'toggleFeatured'])->name('toggle-featured');
        });

        // Admin User Management
        Route::prefix('admin/users')->name('admin.users.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('create');

            Route::post('/', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('store');
            Route::get('/{user}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('show');
            Route::get('/{user}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('edit');
            Route::put('/{user}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('update');
            Route::delete('/{user}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/{user}/toggle-status', [App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{user}/verify-email', [App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('verify-email');
            Route::get('/export', [App\Http\Controllers\Admin\UserController::class, 'export'])->name('export');
        });

        // Admin User Level Management
        Route::prefix('admin/user-level')->name('admin.user-level.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UserLevelController::class, 'index'])->name('index');
            Route::post('/{user}/update', [App\Http\Controllers\Admin\UserLevelController::class, 'updateLevel'])->name('update');
            Route::post('/bulk-update', [App\Http\Controllers\Admin\UserLevelController::class, 'bulkUpdateLevel'])->name('bulk-update');
            Route::post('/auto-upgrade', [App\Http\Controllers\Admin\UserLevelController::class, 'autoUpgrade'])->name('auto-upgrade');
            Route::get('/export', [App\Http\Controllers\Admin\UserLevelController::class, 'export'])->name('export');
            Route::get('/level-info/{level}', [App\Http\Controllers\Admin\UserLevelController::class, 'getLevelInfo'])->name('level-info');
        });

        // Admin Payment Management
        Route::prefix('admin/payments')->name('admin.payments.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\PaymentController::class, 'index'])->name('index');
            Route::get('/{order}', [App\Http\Controllers\Admin\PaymentController::class, 'show'])->name('show');
            Route::post('/{order}/approve', [App\Http\Controllers\Admin\PaymentController::class, 'approve'])->name('approve');
            Route::post('/{order}/reject', [App\Http\Controllers\Admin\PaymentController::class, 'reject'])->name('reject');
            Route::post('/bulk-approve', [App\Http\Controllers\Admin\PaymentController::class, 'bulkApprove'])->name('bulk-approve');
            Route::get('/export/csv', [App\Http\Controllers\Admin\PaymentController::class, 'export'])->name('export');
            Route::get('/check-updates', [App\Http\Controllers\Admin\PaymentController::class, 'checkUpdates'])->name('check-updates');
        });

        // Admin Notification Management
        Route::prefix('admin/notifications')->name('admin.notifications.')->group(function () {
            Route::get('/', function () {
                $notifications = \App\Models\Notification::with('user')
                    ->latest()
                    ->paginate(15);

                $stats = [
                    'total_notifications' => \App\Models\Notification::count(),
                    'unread_notifications' => \App\Models\Notification::whereNull('read_at')->count(),
                    'system_notifications' => \App\Models\Notification::where('type', 'system')->count(),
                    'user_notifications' => \App\Models\Notification::where('type', 'user')->count(),
                ];

                return view('pages.admin.notifications', compact('notifications', 'stats'));
            })->name('index');

            Route::get('/create', function () {
                $users = \App\Models\User::select('id', 'name', 'email')->get();
                return view('pages.admin.notifications.create', compact('users'));
            })->name('create');

            Route::post('/', function (\Illuminate\Http\Request $request) {
                $request->validate([
                    'title' => 'required|string|max:255',
                    'message' => 'required|string',
                    'type' => 'required|in:system,user,event,payment',
                    'recipients' => 'required|in:all,specific,role',
                    'user_ids' => 'required_if:recipients,specific|array',
                    'role' => 'required_if:recipients,role|in:admin,staff,penjual,pembeli',
                ]);

                $recipients = [];

                if ($request->recipients === 'all') {
                    $recipients = \App\Models\User::pluck('id')->toArray();
                } elseif ($request->recipients === 'specific') {
                    $recipients = $request->user_ids;
                } elseif ($request->recipients === 'role') {
                    $recipients = \App\Models\User::where('role', $request->role)->pluck('id')->toArray();
                }

                foreach ($recipients as $userId) {
                    \App\Models\Notification::create([
                        'user_id' => $userId,
                        'title' => $request->title,
                        'message' => $request->message,
                        'type' => $request->type,
                        'data' => json_encode(['sent_by' => auth()->id()]),
                    ]);
                }

                return redirect()->route('admin.notifications.index')->with('success', 'Notifications sent successfully!');
            })->name('store');
        });

        // Admin Organizer Management
        Route::prefix('admin/organizers')->name('admin.organizers.')->group(function () {
            Route::get('/', function () {
                $organizers = \App\Models\User::where('role', 'penjual')
                    ->withCount(['organizedEvents', 'orders'])
                    ->with(['organizedEvents' => function($query) {
                        $query->select('id', 'organizer_id', 'title', 'status', 'total_capacity')
                              ->withCount('tickets');
                    }])
                    ->latest()
                    ->paginate(10);

                $stats = [
                    'total_organizers' => \App\Models\User::where('role', 'penjual')->count(),
                    'active_organizers' => \App\Models\User::where('role', 'penjual')->where('is_active', true)->count(),
                    'pending_approval' => \App\Models\User::where('role', 'penjual')->whereNull('email_verified_at')->count(),
                    'total_tickets' => \App\Models\Event::count(),
                ];

                return view('pages.admin.organizers', compact('organizers', 'stats'));
            })->name('index');

            Route::get('/{organizer}', function (\App\Models\User $organizer) {
                $organizer->load(['organizedEvents.category', 'organizedEvents.tickets']);

                $analytics = [
                    'total_events' => $organizer->organizedEvents->count(),
                    'total_tickets' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->count();
                    }),
                    'total_revenue' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->sum('price');
                    }),
                    'total_tickets_sold' => $organizer->organizedEvents->sum(function($event) {
                        return $event->tickets->where('status', 'used')->count();
                    }),
                    'avg_event_rating' => 4.5, // Placeholder for rating system
                ];

                return view('pages.admin.organizers.show', compact('organizer', 'analytics'));
            })->name('show');

            Route::post('/{organizer}/approve', function (\App\Models\User $organizer) {
                $organizer->update([
                    'email_verified_at' => now(),
                    'is_active' => true
                ]);

                // Send approval notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Approved',
                    'message' => 'Your organizer account has been approved. You can now create and manage tickets.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer approved successfully!');
            })->name('approve');

            Route::post('/{organizer}/suspend', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => false]);

                // Send suspension notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Suspended',
                    'message' => 'Your organizer account has been suspended. Please contact support for more information.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer suspended successfully!');
            })->name('suspend');

            Route::post('/{organizer}/activate', function (\App\Models\User $organizer) {
                $organizer->update(['is_active' => true]);

                // Send activation notification
                \App\Models\Notification::create([
                    'user_id' => $organizer->id,
                    'title' => 'Account Activated',
                    'message' => 'Your organizer account has been activated. You can now create and manage tickets.',
                    'type' => 'system',
                ]);

                return redirect()->back()->with('success', 'Organizer activated successfully!');
            })->name('activate');
        });

        // Admin Category Management
        Route::prefix('admin/categories')->name('admin.categories.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('store');
            Route::get('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'show'])->name('show');
            Route::get('/{category}/edit', [App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('edit');
            Route::put('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('update');
            Route::delete('/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('destroy');
            Route::post('/{category}/toggle-status', [App\Http\Controllers\Admin\CategoryController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\CategoryController::class, 'bulkAction'])->name('bulk-action');
            Route::post('/update-sort-order', [App\Http\Controllers\Admin\CategoryController::class, 'updateSortOrder'])->name('update-sort-order');
            Route::get('/{category}/analytics', [App\Http\Controllers\Admin\CategoryController::class, 'analytics'])->name('analytics');
        });

        // Admin Order Management
        Route::prefix('admin/orders')->name('admin.orders.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\OrderController::class, 'index'])->name('index');
            Route::get('/{order}', [App\Http\Controllers\Admin\OrderController::class, 'show'])->name('show');
            Route::patch('/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('update-status');
            Route::patch('/{order}/payment-status', [App\Http\Controllers\Admin\OrderController::class, 'updatePaymentStatus'])->name('update-payment-status');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\OrderController::class, 'bulkAction'])->name('bulk-action');
            Route::get('/export/csv', [App\Http\Controllers\Admin\OrderController::class, 'export'])->name('export');
        });

        // Admin Voucher Management
        Route::prefix('admin/vouchers')->name('admin.vouchers.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\VoucherController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\VoucherController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\VoucherController::class, 'store'])->name('store');
            Route::get('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'show'])->name('show');
            Route::get('/{voucher}/edit', [App\Http\Controllers\Admin\VoucherController::class, 'edit'])->name('edit');
            Route::put('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'update'])->name('update');
            Route::delete('/{voucher}', [App\Http\Controllers\Admin\VoucherController::class, 'destroy'])->name('destroy');
            Route::post('/{voucher}/toggle-status', [App\Http\Controllers\Admin\VoucherController::class, 'toggleStatus'])->name('toggle-status');
            Route::get('/generate/code', [App\Http\Controllers\Admin\VoucherController::class, 'generateCode'])->name('generate-code');
        });

        // Admin Settings Management
        Route::prefix('admin/settings')->name('admin.settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('index');
            Route::post('/update-app', [App\Http\Controllers\Admin\SettingsController::class, 'updateAppSettings'])->name('update-app');
            Route::post('/update-mail', [App\Http\Controllers\Admin\SettingsController::class, 'updateMailSettings'])->name('update-mail');
            Route::post('/update-fees', [App\Http\Controllers\Admin\SettingsController::class, 'updateFeeSettings'])->name('update-fees');
            Route::post('/update-subscriptions', [App\Http\Controllers\Admin\SettingsController::class, 'updateSubscriptionPricing'])->name('update-subscriptions');
            Route::post('/update-balance', [App\Http\Controllers\Admin\SettingsController::class, 'updateBalanceSettings'])->name('update-balance');
            Route::post('/update-seo', [App\Http\Controllers\Admin\SettingsController::class, 'updateSeoSettings'])->name('update-seo');
            Route::post('/clear-cache', [App\Http\Controllers\Admin\SettingsController::class, 'clearCache'])->name('clear-cache');
            Route::post('/optimize', [App\Http\Controllers\Admin\SettingsController::class, 'optimize'])->name('optimize');
            Route::post('/clean-storage', [App\Http\Controllers\Admin\SettingsController::class, 'cleanStorage'])->name('clean-storage');
        });

        // Admin Balance Management
        Route::prefix('admin/balance')->name('admin.balance.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\BalanceController::class, 'index'])->name('index');
            Route::get('/withdrawals', [App\Http\Controllers\Admin\BalanceController::class, 'withdrawals'])->name('withdrawals');
            Route::post('/{transaction}/approve', [App\Http\Controllers\Admin\BalanceController::class, 'approveWithdrawal'])->name('approve-withdrawal');
            Route::post('/{transaction}/reject', [App\Http\Controllers\Admin\BalanceController::class, 'rejectWithdrawal'])->name('reject-withdrawal');
            Route::post('/bulk-approve', [App\Http\Controllers\Admin\BalanceController::class, 'bulkApproveWithdrawals'])->name('bulk-approve');
            Route::post('/{user}/adjust', [App\Http\Controllers\Admin\BalanceController::class, 'adjustBalance'])->name('adjust');
            Route::get('/export', [App\Http\Controllers\Admin\BalanceController::class, 'export'])->name('export');
        });

        // Admin Payment Methods Management
        Route::prefix('admin/payment-methods')->name('admin.payment-methods.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\PaymentMethodController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\PaymentMethodController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\PaymentMethodController::class, 'store'])->name('store');
            Route::get('/{paymentMethod}', [App\Http\Controllers\Admin\PaymentMethodController::class, 'show'])->name('show');
            Route::get('/{paymentMethod}/edit', [App\Http\Controllers\Admin\PaymentMethodController::class, 'edit'])->name('edit');
            Route::put('/{paymentMethod}', [App\Http\Controllers\Admin\PaymentMethodController::class, 'update'])->name('update');
            Route::delete('/{paymentMethod}', [App\Http\Controllers\Admin\PaymentMethodController::class, 'destroy'])->name('destroy');
            Route::post('/{paymentMethod}/toggle-status', [App\Http\Controllers\Admin\PaymentMethodController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/{paymentMethod}/test-gateway', [App\Http\Controllers\Admin\PaymentMethodController::class, 'testGateway'])->name('test-gateway');
            Route::post('/update-sort-order', [App\Http\Controllers\Admin\PaymentMethodController::class, 'updateSortOrder'])->name('update-sort-order');
        });

        // Admin Payment Gateways Management
        Route::prefix('admin/payment-gateways')->name('admin.payment-gateways.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'index'])->name('index');
            Route::get('/{gateway}', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'show'])->name('show');
            Route::put('/{gateway}', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'update'])->name('update');
            Route::post('/{gateway}/test', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'testConnection'])->name('test');
            Route::post('/{gateway}/toggle', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'toggleStatus'])->name('toggle');
            Route::put('/{method}/status', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'updateMethodStatus'])->name('method-status');
            Route::put('/{method}/update', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'updateMethod'])->name('method-update');
            Route::get('/statistics/data', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'getStatistics'])->name('statistics');
            Route::post('/bulk-status', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'bulkUpdateStatus'])->name('bulk-status');
            Route::get('/{gateway}/channels', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'getAvailableChannels'])->name('channels');
            Route::get('/{gateway}/export', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'exportConfig'])->name('export');
        });

        // Admin Badge Level Management
        Route::prefix('admin/badge-level')->name('admin.badge-level.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\BadgeLevelController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\BadgeLevelController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\BadgeLevelController::class, 'store'])->name('store');
            Route::get('/{badgelevel}', [App\Http\Controllers\Admin\BadgeLevelController::class, 'show'])->name('show');
            Route::get('/{badgelevel}/edit', [App\Http\Controllers\Admin\BadgeLevelController::class, 'edit'])->name('edit');
            Route::put('/{badgelevel}', [App\Http\Controllers\Admin\BadgeLevelController::class, 'update'])->name('update');
            Route::delete('/{badgelevel}', [App\Http\Controllers\Admin\BadgeLevelController::class, 'destroy'])->name('destroy');
            Route::post('/{badgelevel}/toggle-status', [App\Http\Controllers\Admin\BadgeLevelController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/auto-upgrade', [App\Http\Controllers\Admin\BadgeLevelController::class, 'autoUpgrade'])->name('auto-upgrade');
            Route::post('/update-sort-order', [App\Http\Controllers\Admin\BadgeLevelController::class, 'updateSortOrder'])->name('update-sort-order');
            Route::post('/assign-badge', [App\Http\Controllers\Admin\BadgeLevelController::class, 'assignBadge'])->name('assign-badge');
            Route::get('/users', [App\Http\Controllers\Admin\BadgeLevelController::class, 'getUsers'])->name('users');
            Route::post('/bulk-assign-badge', [App\Http\Controllers\Admin\BadgeLevelController::class, 'bulkAssignBadge'])->name('bulk-assign-badge');
            Route::post('/remove-badge', [App\Http\Controllers\Admin\BadgeLevelController::class, 'removeBadge'])->name('remove-badge');
            Route::post('/downgrade-badge', [App\Http\Controllers\Admin\BadgeLevelController::class, 'downgradeBadge'])->name('downgrade-badge');
            Route::post('/extend-badge', [App\Http\Controllers\Admin\BadgeLevelController::class, 'extendBadge'])->name('extend-badge');
            Route::get('/expired-badges', [App\Http\Controllers\Admin\BadgeLevelController::class, 'getExpiredBadges'])->name('expired-badges');
            Route::post('/auto-renew-badges', [App\Http\Controllers\Admin\BadgeLevelController::class, 'autoRenewBadges'])->name('auto-renew-badges');
        });

        // Admin CyberGuard Management
        Route::prefix('admin/cyber-guard')->name('admin.cyber-guard.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\CyberGuardController::class, 'index'])->name('index');

            // Settings
            Route::get('/settings', [App\Http\Controllers\Admin\CyberGuardController::class, 'settings'])->name('settings');
            Route::post('/settings', [App\Http\Controllers\Admin\CyberGuardController::class, 'updateSettings'])->name('update-settings');
            Route::post('/test-config', [App\Http\Controllers\Admin\CyberGuardController::class, 'testConfiguration'])->name('test-config');
            Route::post('/reset-defaults', [App\Http\Controllers\Admin\CyberGuardController::class, 'resetDefaults'])->name('reset-defaults');

            // Logs
            Route::get('/logs', [App\Http\Controllers\Admin\CyberGuardController::class, 'logs'])->name('logs');
            Route::get('/logs/{log}', [App\Http\Controllers\Admin\CyberGuardController::class, 'showLog'])->name('logs.show');
            Route::post('/logs/{log}/resolve', [App\Http\Controllers\Admin\CyberGuardController::class, 'resolveLog'])->name('logs.resolve');
            Route::delete('/logs/{log}', [App\Http\Controllers\Admin\CyberGuardController::class, 'deleteLog'])->name('logs.destroy');
            Route::post('/logs/clear', [App\Http\Controllers\Admin\CyberGuardController::class, 'clearLogs'])->name('logs.clear');
            Route::post('/logs/bulk-resolve', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkResolveLogs'])->name('logs.bulk-resolve');
            Route::post('/logs/bulk-delete', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkDeleteLogs'])->name('logs.bulk-delete');
            Route::post('/logs/bulk-block_ip', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkBlockIPs'])->name('logs.bulk-block-ip');
            Route::get('/logs/export', [App\Http\Controllers\Admin\CyberGuardController::class, 'exportLogs'])->name('logs.export');

            // Firewall Rules
            Route::get('/firewall-rules', [App\Http\Controllers\Admin\CyberGuardController::class, 'firewallRules'])->name('firewall-rules');
            Route::post('/firewall-rules', [App\Http\Controllers\Admin\CyberGuardController::class, 'storeFirewallRule'])->name('firewall-rules.store');
            Route::get('/firewall-rules/{rule}', [App\Http\Controllers\Admin\CyberGuardController::class, 'showFirewallRule'])->name('firewall-rules.show');
            Route::put('/firewall-rules/{rule}', [App\Http\Controllers\Admin\CyberGuardController::class, 'updateFirewallRule'])->name('firewall-rules.update');
            Route::delete('/firewall-rules/{rule}', [App\Http\Controllers\Admin\CyberGuardController::class, 'deleteFirewallRule'])->name('firewall-rules.destroy');
            Route::post('/firewall-rules/{rule}/toggle', [App\Http\Controllers\Admin\CyberGuardController::class, 'toggleFirewallRule'])->name('firewall-rules.toggle');
            Route::post('/firewall-rules/bulk-enable', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkEnableRules'])->name('firewall-rules.bulk-enable');
            Route::post('/firewall-rules/bulk-disable', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkDisableRules'])->name('firewall-rules.bulk-disable');
            Route::post('/firewall-rules/bulk-delete', [App\Http\Controllers\Admin\CyberGuardController::class, 'bulkDeleteRules'])->name('firewall-rules.bulk-delete');

            // Other routes
            Route::get('/blocked-ips', [App\Http\Controllers\Admin\CyberGuardController::class, 'blockedIps'])->name('blocked-ips');
            Route::post('/security-scan', [App\Http\Controllers\Admin\CyberGuardController::class, 'runSecurityScan'])->name('security-scan');
            Route::post('/export-report', [App\Http\Controllers\Admin\CyberGuardController::class, 'exportReport'])->name('export-report');
            Route::post('/initialize', [App\Http\Controllers\Admin\CyberGuardController::class, 'initialize'])->name('initialize');
        });

        // Admin UangTix Management (Admin Only)
        Route::prefix('admin/uangtix')->name('admin.uangtix.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\UangTixController::class, 'index'])->name('index');
            Route::get('/settings', [App\Http\Controllers\Admin\UangTixController::class, 'settings'])->name('settings');
            Route::post('/settings', [App\Http\Controllers\Admin\UangTixController::class, 'updateSettings'])->name('update-settings');
            Route::get('/requests', [App\Http\Controllers\Admin\UangTixController::class, 'requests'])->name('requests');
            Route::post('/requests/{uangTixRequest}/approve', [App\Http\Controllers\Admin\UangTixController::class, 'approveRequest'])->name('approve-request');
            Route::post('/requests/{uangTixRequest}/reject', [App\Http\Controllers\Admin\UangTixController::class, 'rejectRequest'])->name('reject-request');
            Route::post('/requests/bulk-approve', [App\Http\Controllers\Admin\UangTixController::class, 'bulkApproveRequests'])->name('bulk-approve-requests');
            Route::post('/{user}/adjust', [App\Http\Controllers\Admin\UangTixController::class, 'adjustBalance'])->name('adjust');
            Route::post('/{user}/toggle-status', [App\Http\Controllers\Admin\UangTixController::class, 'toggleBalanceStatus'])->name('toggle-status');
            Route::get('/transactions', [App\Http\Controllers\Admin\UangTixController::class, 'transactions'])->name('transactions');
            Route::get('/export', [App\Http\Controllers\Admin\UangTixController::class, 'export'])->name('export');
        });

        // Admin Analytics Management
        Route::prefix('admin/analytics')->name('admin.analytics.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('index');
            Route::get('/revenue', [App\Http\Controllers\Admin\AnalyticsController::class, 'revenue'])->name('revenue');
            Route::get('/users', [App\Http\Controllers\Admin\AnalyticsController::class, 'users'])->name('users');
            Route::get('/events', [App\Http\Controllers\Admin\AnalyticsController::class, 'events'])->name('events');
            Route::get('/geographic', [App\Http\Controllers\Admin\AnalyticsController::class, 'geographic'])->name('geographic');
            Route::get('/export', [App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('export');
        });

        // Admin Ads Management
        Route::prefix('admin/ads')->name('admin.ads.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AdController::class, 'index'])->name('index');
            Route::get('/subscriptions', [App\Http\Controllers\Admin\AdController::class, 'subscriptions'])->name('subscriptions');
            Route::get('/{ad}', [App\Http\Controllers\Admin\AdController::class, 'show'])->name('show');
            Route::post('/{ad}/approve', [App\Http\Controllers\Admin\AdController::class, 'approve'])->name('approve');
            Route::post('/{ad}/reject', [App\Http\Controllers\Admin\AdController::class, 'reject'])->name('reject');
            Route::post('/{ad}/pause', [App\Http\Controllers\Admin\AdController::class, 'pause'])->name('pause');
            Route::post('/{ad}/resume', [App\Http\Controllers\Admin\AdController::class, 'resume'])->name('resume');
            Route::post('/{ad}/toggle-active', [App\Http\Controllers\Admin\AdController::class, 'toggleActive'])->name('toggle-active');
            Route::delete('/{ad}', [App\Http\Controllers\Admin\AdController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\AdController::class, 'bulkAction'])->name('bulk-action');
            Route::get('/analytics/dashboard', [App\Http\Controllers\Admin\AdController::class, 'analytics'])->name('analytics');
            Route::get('/export/data', [App\Http\Controllers\Admin\AdController::class, 'export'])->name('export');
        });

        // Admin Help & Support Management
        Route::prefix('admin/help')->name('admin.help.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\HelpController::class, 'index'])->name('index');
            Route::get('/tickets', [App\Http\Controllers\Admin\HelpController::class, 'tickets'])->name('tickets');
            Route::get('/faq', [App\Http\Controllers\Admin\HelpController::class, 'faq'])->name('faq');
            Route::get('/documentation', [App\Http\Controllers\Admin\HelpController::class, 'documentation'])->name('documentation');
        });

        // Admin Profile Management
        Route::prefix('admin/profile')->name('admin.profile.')->group(function () {
            Route::get('/edit', [App\Http\Controllers\Admin\ProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [App\Http\Controllers\Admin\ProfileController::class, 'update'])->name('update');
            Route::put('/password', [App\Http\Controllers\Admin\ProfileController::class, 'updatePassword'])->name('password');
        });

        // Admin Staff Management - NEW
        Route::prefix('admin/staff-management')->name('admin.staff-management.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\StaffManagementController::class, 'index'])->name('index');
            Route::get('/stats', [App\Http\Controllers\Admin\StaffManagementController::class, 'getAssignmentStats'])->name('stats');
            Route::get('/report', [App\Http\Controllers\Admin\StaffManagementController::class, 'getAssignmentReport'])->name('report');
            Route::get('/{staff}/assignment-form', [App\Http\Controllers\Admin\StaffManagementController::class, 'showAssignmentForm'])->name('assignment-form');
            Route::post('/{staff}/assign-organizers', [App\Http\Controllers\Admin\StaffManagementController::class, 'assignOrganizers'])->name('assign-organizers');
            Route::delete('/{staff}/remove-organizer/{organizer}', [App\Http\Controllers\Admin\StaffManagementController::class, 'removeOrganizerAssignment'])->name('remove-organizer');
            Route::delete('/{staff}/clear-assignments', [App\Http\Controllers\Admin\StaffManagementController::class, 'clearAllAssignments'])->name('clear-assignments');
            Route::post('/bulk-assign', [App\Http\Controllers\Admin\StaffManagementController::class, 'bulkAssignOrganizers'])->name('bulk-assign');
        });

        // Admin Banner Management
        Route::prefix('admin/banners')->name('admin.banners.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\BannerController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\BannerController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\BannerController::class, 'store'])->name('store');
            Route::get('/{banner}', [App\Http\Controllers\Admin\BannerController::class, 'show'])->name('show');
            Route::get('/{banner}/edit', [App\Http\Controllers\Admin\BannerController::class, 'edit'])->name('edit');
            Route::put('/{banner}', [App\Http\Controllers\Admin\BannerController::class, 'update'])->name('update');
            Route::delete('/{banner}', [App\Http\Controllers\Admin\BannerController::class, 'destroy'])->name('destroy');
            Route::post('/{banner}/toggle-status', [App\Http\Controllers\Admin\BannerController::class, 'toggleStatus'])->name('toggle-status');
            Route::post('/update-order', [App\Http\Controllers\Admin\BannerController::class, 'updateOrder'])->name('update-order');
            Route::post('/preview', [App\Http\Controllers\Admin\BannerController::class, 'preview'])->name('preview');
        });

        // Admin Reports Management
        Route::prefix('admin/reports')->name('admin.reports.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ReportsController::class, 'index'])->name('index');
            Route::get('/export', [App\Http\Controllers\Admin\ReportsController::class, 'export'])->name('export');
        });

        // Admin Feedback Management
        Route::prefix('admin/feedback')->name('admin.feedback.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\FeedbackController::class, 'index'])->name('index');
            Route::get('/analytics', [App\Http\Controllers\Admin\FeedbackController::class, 'analytics'])->name('analytics');
            Route::get('/export', [App\Http\Controllers\Admin\FeedbackController::class, 'export'])->name('export');
            Route::post('/{feedback}/status', [App\Http\Controllers\Admin\FeedbackController::class, 'updateStatus'])->name('update-status');
            Route::post('/{feedback}/respond', [App\Http\Controllers\Admin\FeedbackController::class, 'respond'])->name('respond');
            Route::delete('/{feedback}', [App\Http\Controllers\Admin\FeedbackController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-action', [App\Http\Controllers\Admin\FeedbackController::class, 'bulkAction'])->name('bulk-action');
        });
    });

    Route::middleware('staff')->group(function () {
        Route::get('/staff/dashboard', [StaffDashboardController::class, 'index'])->name('staff.dashboard');
        Route::get('/staff/dashboard/stats', [StaffDashboardController::class, 'getStats'])->name('staff.dashboard.stats');
        Route::get('/staff/dashboard/today-events', [StaffDashboardController::class, 'getTodayEventsApi'])->name('staff.dashboard.today-events');
        Route::get('/staff/dashboard/validation-history', [StaffDashboardController::class, 'getValidationHistory'])->name('staff.dashboard.validation-history');
        Route::post('/staff/validate-ticket', [StaffDashboardController::class, 'validateTicket'])->name('staff.validate-ticket');

        // Enhanced staff dashboard features
        Route::get('/staff/dashboard/metrics', [StaffDashboardController::class, 'getRealTimeMetrics'])->name('staff.dashboard.metrics');
        Route::get('/staff/dashboard/leaderboard', [StaffDashboardController::class, 'getStaffLeaderboard'])->name('staff.dashboard.leaderboard');
        Route::get('/staff/dashboard/hourly-stats', [StaffDashboardController::class, 'getHourlyStats'])->name('staff.dashboard.hourly-stats');
        Route::get('/staff/dashboard/export-report', [StaffDashboardController::class, 'exportValidationReport'])->name('staff.dashboard.export-report');

        Route::get('/staff/scanner', function () {
            return view('pages.staff.qr-scanner');
        })->name('staff.scanner');

        // Staff additional pages
        Route::get('/staff/tickets', function () {
            return view('pages.staff.tickets');
        })->name('staff.tickets');

        Route::get('/staff/events', function () {
            return view('pages.staff.events');
        })->name('staff.events');

        Route::get('/staff/reports', function () {
            return view('pages.staff.reports');
        })->name('staff.reports');
    });

    Route::middleware('organizer')->group(function () {
        Route::get('/organizer/dashboard', [OrganizerDashboardController::class, 'index'])->name('organizer.dashboard');
        Route::get('/organizer/dashboard/export', [OrganizerDashboardController::class, 'export'])->name('organizer.dashboard.export');

        // Organizer Event Management
        Route::prefix('organizer/events')->name('organizer.events.')->group(function () {
            Route::get('/', [OrganizerEventController::class, 'index'])->name('index');
            Route::get('/create', [OrganizerEventController::class, 'create'])->name('create');
            Route::post('/', [OrganizerEventController::class, 'store'])->name('store');
            Route::get('/{event}', [OrganizerEventController::class, 'show'])->name('show');
            Route::get('/{event}/edit', [OrganizerEventController::class, 'edit'])->name('edit');
            Route::put('/{event}', [OrganizerEventController::class, 'update'])->name('update');
            Route::delete('/{event}', [OrganizerEventController::class, 'destroy'])->name('destroy');
            Route::post('/{event}/publish', [OrganizerEventController::class, 'publish'])->name('publish');
            Route::post('/{event}/unpublish', [OrganizerEventController::class, 'unpublish'])->name('unpublish');
            Route::post('/{event}/template', [OrganizerEventController::class, 'updateTemplate'])->name('update-template');
            Route::get('/{event}/templates', [OrganizerEventController::class, 'showTemplates'])->name('templates');
            Route::get('/templates/preview/{template}', [OrganizerEventController::class, 'previewTemplate'])->name('template-preview');

            // New boarding pass template routes
            Route::get('/templates/boarding-pass/unix', function() {
                $sampleTicket = [
                    'ticket_number' => 'TIX-UNIX-001',
                    'boarding_pass_id' => 'BP250127ABC123',
                    'event_title' => 'Unix Terminal Conference 2024',
                    'venue_name' => 'Jakarta Convention Center',
                    'event_date' => '31 Dec 2024',
                    'event_time' => '19:00 WIB',
                    'buyer_name' => 'John Doe',
                    'buyer_email' => '<EMAIL>',
                    'formatted_price' => 'Rp 150.000',
                    'qr_code' => 'SAMPLE-QR-CODE'
                ];
                return view('templates.tickets.unix', ['ticket' => $sampleTicket]);
            })->name('template-preview-unix');

            Route::get('/templates/boarding-pass/minimalist', function() {
                $sampleTicket = [
                    'ticket_number' => 'TIX-MIN-001',
                    'boarding_pass_id' => 'BP250127ABC123',
                    'event_title' => 'Minimalist Design Workshop',
                    'venue_name' => 'Creative Space Jakarta',
                    'event_date' => '31 Dec 2024',
                    'event_time' => '19:00 WIB',
                    'buyer_name' => 'Jane Smith',
                    'buyer_email' => '<EMAIL>',
                    'formatted_price' => 'Rp 200.000',
                    'qr_code' => 'SAMPLE-QR-CODE'
                ];
                return view('templates.tickets.minimalist', ['ticket' => $sampleTicket]);
            })->name('template-preview-minimalist');

            Route::get('/templates/boarding-pass/pro', function() {
                $sampleTicket = [
                    'ticket_number' => 'TIX-PRO-001',
                    'boarding_pass_id' => 'BP250127ABC123',
                    'event_title' => 'Professional Business Summit',
                    'venue_name' => 'Grand Ballroom Hotel Indonesia',
                    'event_date' => '31 Dec 2024',
                    'event_time' => '19:00 WIB',
                    'buyer_name' => 'Robert Johnson',
                    'buyer_email' => '<EMAIL>',
                    'formatted_price' => 'Rp 500.000',
                    'qr_code' => 'SAMPLE-QR-CODE'
                ];
                return view('templates.tickets.pro', ['ticket' => $sampleTicket]);
            })->name('template-preview-pro');

            Route::get('/templates/boarding-pass/custom', function() {
                $sampleTicket = [
                    'ticket_number' => 'TIX-CUSTOM-001',
                    'boarding_pass_id' => 'BP250127ABC123',
                    'event_title' => 'Custom Event Experience',
                    'venue_name' => 'Custom Venue Location',
                    'event_date' => '31 Dec 2024',
                    'event_time' => '19:00 WIB',
                    'buyer_name' => 'Alex Custom',
                    'buyer_email' => '<EMAIL>',
                    'formatted_price' => 'Rp 300.000',
                    'qr_code' => 'SAMPLE-QR-CODE'
                ];
                $config = [
                    'font_family' => 'Inter',
                    'background_color' => '#f8fafc',
                    'primary_color' => '#3b82f6',
                    'show_qr_code' => true,
                    'show_price' => true
                ];
                return view('templates.tickets.custom', ['ticket' => $sampleTicket, 'config' => $config]);
            })->name('template-preview-custom');
            Route::get('/{event}/qr-code', [OrganizerEventController::class, 'downloadQRCode'])->name('qr-code');
            Route::get('/export/csv', [OrganizerEventController::class, 'export'])->name('export');
        });

        // Organizer Template Management
        Route::prefix('organizer/templates')->name('organizer.templates.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\TemplateController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Organizer\TemplateController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Organizer\TemplateController::class, 'store'])->name('store');
            Route::get('/{template}', [App\Http\Controllers\Organizer\TemplateController::class, 'show'])->name('show');
            Route::get('/{template}/edit', [App\Http\Controllers\Organizer\TemplateController::class, 'edit'])->name('edit');
            Route::put('/{template}', [App\Http\Controllers\Organizer\TemplateController::class, 'update'])->name('update');
            Route::delete('/{template}', [App\Http\Controllers\Organizer\TemplateController::class, 'destroy'])->name('destroy');
            Route::post('/{template}/set-default', [App\Http\Controllers\Organizer\TemplateController::class, 'setDefault'])->name('set-default');
            Route::post('/{template}/duplicate', [App\Http\Controllers\Organizer\TemplateController::class, 'duplicate'])->name('duplicate');
            Route::get('/{template}/preview', [App\Http\Controllers\Organizer\TemplateController::class, 'preview'])->name('preview');
            Route::post('/{template}/save-design', [App\Http\Controllers\Organizer\TemplateController::class, 'saveDesign'])->name('save-design');
            Route::get('/{template}/editor-data', [App\Http\Controllers\Organizer\TemplateController::class, 'getEditorData'])->name('editor-data');
        });

        // Template Preview Routes (public access for iframe)
        Route::get('/templates/classic-preview', function() {
            return view('organizer.templates.classic-preview');
        })->name('templates.classic-preview');

        Route::get('/templates/unix-preview', function() {
            return view('organizer.templates.unix-preview');
        })->name('templates.unix-preview');

        Route::get('/templates/minimal-preview', function() {
            return view('organizer.templates.minimal-preview');
        })->name('templates.minimal-preview');

        Route::get('/templates/pro-preview', function() {
            return view('organizer.templates.pro-preview');
        })->name('templates.pro-preview');

        // Organizer Ticket Management (Legacy support)
        Route::prefix('organizer/events')->name('organizer.events.')->group(function () {
            Route::get('/', [OrganizerEventController::class, 'index'])->name('index');
            Route::get('/create', [OrganizerEventController::class, 'create'])->name('create');
            Route::post('/', [OrganizerEventController::class, 'store'])->name('store');
            Route::get('/{event}', [OrganizerEventController::class, 'show'])->name('show');
            Route::get('/{event}/edit', [OrganizerEventController::class, 'edit'])->name('edit');
            Route::put('/{event}', [OrganizerEventController::class, 'update'])->name('update');
            Route::delete('/{event}', [OrganizerEventController::class, 'destroy'])->name('destroy');
            Route::post('/{event}/publish', [OrganizerEventController::class, 'publish'])->name('publish');
            Route::post('/{event}/unpublish', [OrganizerEventController::class, 'unpublish'])->name('unpublish');
            Route::get('/{event}/qr-code', [OrganizerEventController::class, 'downloadQRCode'])->name('qr-code');
        });

        // Organizer Order Management
        Route::prefix('organizer/orders')->name('organizer.orders.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\OrderController::class, 'index'])->name('index');
            Route::get('/{order}', [App\Http\Controllers\Organizer\OrderController::class, 'show'])->name('show');
            Route::patch('/{order}/status', [App\Http\Controllers\Organizer\OrderController::class, 'updateStatus'])->name('update-status');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\OrderController::class, 'export'])->name('export');
        });

        // Organizer Analytics Management
        Route::prefix('organizer/analytics')->name('organizer.analytics.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\AnalyticsController::class, 'index'])->name('index');
            Route::get('/revenue', [App\Http\Controllers\Organizer\AnalyticsController::class, 'revenue'])->name('revenue');
            Route::get('/events', [App\Http\Controllers\Organizer\AnalyticsController::class, 'events'])->name('events');
            Route::get('/export', [App\Http\Controllers\Organizer\AnalyticsController::class, 'export'])->name('export');
        });

        // Organizer Payment Management
        Route::prefix('organizer/payments')->name('organizer.payments.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\PaymentController::class, 'index'])->name('index');
            Route::get('/{payment}', [App\Http\Controllers\Organizer\PaymentController::class, 'show'])->name('show');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\PaymentController::class, 'export'])->name('export');
        });

        // Organizer Profile Management
        Route::prefix('organizer/profile')->name('organizer.profile.')->group(function () {
            Route::get('/edit', [App\Http\Controllers\Organizer\ProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [App\Http\Controllers\Organizer\ProfileController::class, 'update'])->name('update');
            Route::put('/password', [App\Http\Controllers\Organizer\ProfileController::class, 'updatePassword'])->name('password');
        });

        // Organizer Settings Management
        Route::prefix('organizer/settings')->name('organizer.settings.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\SettingsController::class, 'index'])->name('index');
            Route::post('/update', [App\Http\Controllers\Organizer\SettingsController::class, 'update'])->name('update');
        });

        // Organizer Ads Management
        Route::prefix('organizer/ads')->name('organizer.ads.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\AdController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Organizer\AdController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Organizer\AdController::class, 'store'])->name('store');
            Route::get('/{ad}', [App\Http\Controllers\Organizer\AdController::class, 'show'])->name('show');
            Route::get('/{ad}/edit', [App\Http\Controllers\Organizer\AdController::class, 'edit'])->name('edit');
            Route::put('/{ad}', [App\Http\Controllers\Organizer\AdController::class, 'update'])->name('update');
            Route::delete('/{ad}', [App\Http\Controllers\Organizer\AdController::class, 'destroy'])->name('destroy');
            Route::post('/{ad}/pause', [App\Http\Controllers\Organizer\AdController::class, 'pause'])->name('pause');
            Route::post('/{ad}/resume', [App\Http\Controllers\Organizer\AdController::class, 'resume'])->name('resume');
            Route::prefix('subscriptions')->name('subscriptions.')->group(function () {
                Route::get('/', [App\Http\Controllers\Organizer\AdController::class, 'subscriptions'])->name('index');
                Route::get('/create', [App\Http\Controllers\Organizer\AdController::class, 'createSubscription'])->name('create');
                Route::post('/', [App\Http\Controllers\Organizer\AdController::class, 'subscribe'])->name('store');
                Route::get('/{subscription}', [App\Http\Controllers\Organizer\AdController::class, 'showSubscription'])->name('show');
                Route::post('/{subscription}/cancel', [App\Http\Controllers\Organizer\AdController::class, 'cancelSubscription'])->name('cancel');
            });
        });

        // Organizer Notifications Management
        Route::prefix('organizer/notifications')->name('organizer.notifications.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\NotificationController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Organizer\NotificationController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Organizer\NotificationController::class, 'store'])->name('store');
            Route::get('/{notification}', [App\Http\Controllers\Organizer\NotificationController::class, 'show'])->name('show');
            Route::post('/{notification}/mark-read', [App\Http\Controllers\Organizer\NotificationController::class, 'markAsRead'])->name('mark-read');
            Route::post('/mark-all-read', [App\Http\Controllers\Organizer\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
            Route::delete('/{notification}', [App\Http\Controllers\Organizer\NotificationController::class, 'destroy'])->name('destroy');
            Route::post('/bulk-delete', [App\Http\Controllers\Organizer\NotificationController::class, 'bulkDelete'])->name('bulk-delete');
            Route::get('/api/unread-count', [App\Http\Controllers\Organizer\NotificationController::class, 'getUnreadCount'])->name('unread-count');
            Route::get('/api/recent', [App\Http\Controllers\Organizer\NotificationController::class, 'getRecent'])->name('recent');
            Route::post('/test', [App\Http\Controllers\Organizer\NotificationController::class, 'sendTest'])->name('test');
        });

        // Organizer Vouchers Management
        Route::prefix('organizer/vouchers')->name('organizer.vouchers.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\VoucherController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Organizer\VoucherController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Organizer\VoucherController::class, 'store'])->name('store');
            Route::get('/{voucher}', [App\Http\Controllers\Organizer\VoucherController::class, 'show'])->name('show');
            Route::get('/{voucher}/edit', [App\Http\Controllers\Organizer\VoucherController::class, 'edit'])->name('edit');
            Route::put('/{voucher}', [App\Http\Controllers\Organizer\VoucherController::class, 'update'])->name('update');
            Route::delete('/{voucher}', [App\Http\Controllers\Organizer\VoucherController::class, 'destroy'])->name('destroy');
            Route::post('/{voucher}/toggle-status', [App\Http\Controllers\Organizer\VoucherController::class, 'toggleStatus'])->name('toggle-status');
            Route::get('/generate/code', [App\Http\Controllers\Organizer\VoucherController::class, 'generateCode'])->name('generate-code');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\VoucherController::class, 'export'])->name('export');
        });

        // Organizer Tickets Management
        Route::prefix('organizer/events')->name('organizer.events.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\TicketController::class, 'index'])->name('index');
            Route::get('/{ticket}', [App\Http\Controllers\Organizer\TicketController::class, 'show'])->name('show');
            Route::post('/{ticket}/validate', [App\Http\Controllers\Organizer\TicketController::class, 'validateTicket'])->name('validate');
            Route::post('/{ticket}/cancel', [App\Http\Controllers\Organizer\TicketController::class, 'cancel'])->name('cancel');
            Route::post('/bulk-validate', [App\Http\Controllers\Organizer\TicketController::class, 'bulkValidate'])->name('bulk-validate');
            Route::post('/scan', [App\Http\Controllers\Organizer\TicketController::class, 'scan'])->name('scan');
            Route::get('/export/csv', [App\Http\Controllers\Organizer\TicketController::class, 'export'])->name('export');
        });

        // Organizer Reports Management
        Route::prefix('organizer/reports')->name('organizer.reports.')->group(function () {
            Route::get('/', [App\Http\Controllers\Organizer\ReportController::class, 'index'])->name('index');
            Route::get('/revenue', [App\Http\Controllers\Organizer\ReportController::class, 'revenue'])->name('revenue');
            Route::get('/sales', [App\Http\Controllers\Organizer\ReportController::class, 'sales'])->name('sales');
            Route::get('/events', [App\Http\Controllers\Organizer\ReportController::class, 'events'])->name('events');
            Route::get('/customers', [App\Http\Controllers\Organizer\ReportController::class, 'customers'])->name('customers');
            Route::get('/export', [App\Http\Controllers\Organizer\ReportController::class, 'export'])->name('export');
        });
    });

    // User Dashboard (for pembeli and others)
    Route::get('/dashboard', [App\Http\Controllers\User\DashboardController::class, 'index'])->name('dashboard');

    // User Events Routes
    Route::prefix('events')->name('events.')->group(function () {
        Route::get('/', [EventController::class, 'index'])->name('index');
        Route::get('/{event}', [EventController::class, 'show'])->name('show');
    });

    // User Settings Routes
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [App\Http\Controllers\User\SettingsController::class, 'index'])->name('index');
        Route::post('/update', [App\Http\Controllers\User\SettingsController::class, 'update'])->name('update');
    });

    // User Profile Edit Routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('edit');
    });

    // Test Routes (Development only)
    if (app()->environment(['local', 'staging'])) {
        Route::get('/test/mobile-payment', function () {
            return view('test.mobile-payment');
        })->name('test.mobile-payment');
    }

    // Profile Routes
    Route::get('/profile', [App\Http\Controllers\ProfileController::class, 'show'])->name('profile');
    Route::post('/profile/update', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password');
    Route::post('/profile/photo', [App\Http\Controllers\ProfileController::class, 'updatePhoto'])->name('profile.photo');
    Route::post('/profile/notifications/email', [App\Http\Controllers\ProfileController::class, 'toggleEmailNotification'])->name('profile.notifications.email');
    Route::post('/profile/notifications/push', [App\Http\Controllers\ProfileController::class, 'togglePushNotification'])->name('profile.notifications.push');
    Route::post('/profile/notifications/sms', [App\Http\Controllers\ProfileController::class, 'toggleSmsNotification'])->name('profile.notifications.sms');
    Route::delete('/profile/delete', [App\Http\Controllers\ProfileController::class, 'deleteAccount'])->name('profile.delete');

    // Notification Routes
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
        Route::get('/latest', [App\Http\Controllers\NotificationController::class, 'latest'])->name('latest');
        Route::get('/unread-count', [App\Http\Controllers\NotificationController::class, 'unreadCount'])->name('unread-count');
        Route::post('/{notification}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::post('/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('destroy');
        Route::post('/test', [App\Http\Controllers\NotificationController::class, 'test'])->name('test');
    });

    // Alias route for backward compatibility
    Route::get('/notifikasi', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifikasi');
});

// Test routes (only in development)
if (app()->environment('local')) {
    Route::get('/test/organizer-preview', function () {
        return view('test.organizer-preview');
    })->name('test.organizer-preview');

    Route::get('/test/user-level', function () {
        return view('test.user-level');
    })->name('test.user-level');

    Route::get('/test/qr-scanner', function () {
        return view('test.qr-scanner-test');
    })->name('test.qr-scanner');

    Route::get('/debug/badge', function () {
        return view('debug.badge-debug');
    })->name('debug.badge');

    Route::get('/fix/badges', function () {
        // Create Bronze badge if not exists
        $bronze = \App\Models\UserBadgeLevel::firstOrCreate(
            ['name' => 'Bronze'],
            [
                'description' => 'Default badge for all new users',
                'color' => '#CD7F32',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'benefits' => ['basic_support' => true],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]
        );

        // Assign to all users without badges
        $users = \App\Models\User::whereNull('badge_level_id')->get();
        $count = 0;

        foreach ($users as $user) {
            $user->update([
                'badge_level_id' => $bronze->id,
                'badge_assigned_at' => now(),
            ]);
            $count++;
        }

        return "Fixed {$count} users. Bronze badge ID: {$bronze->id}";
    })->name('fix.badges');

    Route::get('/test/badge', function () {
        return view('test.badge-test');
    })->name('test.badge');

    Route::get('/test/ticket-templates', function () {
        return view('test.ticket-templates');
    })->name('test.ticket-templates');

    Route::post('/test/ticket-templates/preview', function (\Illuminate\Http\Request $request) {
        $template = \App\Models\TicketTemplate::where('slug', $request->template)->first();
        if (!$template) {
            return response('Template not found', 404);
        }

        $sampleData = $request->sample_data;
        $ticketData = [
            'ticket_number' => $sampleData['ticket_number'] ?? 'TIX-PREVIEW-001',
            'qr_code' => $sampleData['ticket_number'] ?? 'PREVIEW-QR-CODE',
            'price' => $sampleData['price'] ?? 250000,
            'attendee_name' => $sampleData['attendee_name'] ?? 'John Doe',
            'attendee_email' => $sampleData['attendee_email'] ?? '<EMAIL>',
            'attendee_phone' => '+62812345678',
            'event' => [
                'title' => $sampleData['event_title'] ?? 'Preview Event',
                'venue_name' => $sampleData['venue_name'] ?? 'Preview Venue',
                'city' => $sampleData['city'] ?? 'Jakarta',
                'start_date' => $sampleData['event_date'] ?? now()->addDays(30)->toDateTimeString(),
                'poster' => null,
                'organizer_name' => 'Preview Organizer'
            ],
            'buyer' => [
                'name' => $sampleData['attendee_name'] ?? 'John Doe',
                'email' => $sampleData['attendee_email'] ?? '<EMAIL>',
                'phone' => '+62812345678'
            ]
        ];

        $ticketGenerator = new \App\Services\TicketGeneratorService();
        $html = $ticketGenerator->generateTicketHtml($ticketData, $template);

        return response($html);
    })->name('test.ticket-templates.preview');

    Route::post('/test/ticket-templates/pdf', function (\Illuminate\Http\Request $request) {
        $template = \App\Models\TicketTemplate::where('slug', $request->template)->first();
        if (!$template) {
            return response('Template not found', 404);
        }

        $sampleData = $request->sample_data;
        $ticketData = [
            'ticket_number' => $sampleData['ticket_number'] ?? 'TIX-PREVIEW-001',
            'qr_code' => $sampleData['ticket_number'] ?? 'PREVIEW-QR-CODE',
            'price' => $sampleData['price'] ?? 250000,
            'attendee_name' => $sampleData['attendee_name'] ?? 'John Doe',
            'attendee_email' => $sampleData['attendee_email'] ?? '<EMAIL>',
            'attendee_phone' => '+62812345678',
            'event' => [
                'title' => $sampleData['event_title'] ?? 'Preview Event',
                'venue_name' => $sampleData['venue_name'] ?? 'Preview Venue',
                'city' => $sampleData['city'] ?? 'Jakarta',
                'start_date' => $sampleData['event_date'] ?? now()->addDays(30)->toDateTimeString(),
                'poster' => null,
                'organizer_name' => 'Preview Organizer'
            ],
            'buyer' => [
                'name' => $sampleData['attendee_name'] ?? 'John Doe',
                'email' => $sampleData['attendee_email'] ?? '<EMAIL>',
                'phone' => '+62812345678'
            ]
        ];

        $ticketGenerator = new \App\Services\TicketGeneratorService();
        $html = $ticketGenerator->generateTicketHtml($ticketData, $template);

        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($html)
            ->setPaper('A4', 'landscape')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
            ]);

        return $pdf->download("ticket-preview-{$template->slug}.pdf");
    })->name('test.ticket-templates.pdf');
}

// Fallback route for debugging 404 issues
Route::fallback(function () {
    $requestedUrl = request()->fullUrl();
    $requestedPath = request()->path();

    // Check if this is a common misrouted URL with /public/
    if (str_contains($requestedPath, 'public/')) {
        $correctedPath = str_replace('public/', '', $requestedPath);

        // Handle specific routes
        if ($correctedPath === 'tickets/my-tickets') {
            if (auth()->check()) {
                return redirect()->route('tiket-saya');
            } else {
                return redirect()->route('login')->with('intended', route('tiket-saya'));
            }
        }

        if ($correctedPath === 'my-tickets') {
            if (auth()->check()) {
                return redirect()->route('my-tickets');
            } else {
                return redirect()->route('login')->with('intended', route('my-tickets'));
            }
        }

        if (str_starts_with($correctedPath, 'tickets/')) {
            return redirect('/' . $correctedPath);
        }

        // General redirect for other /public/ URLs
        if (!empty($correctedPath)) {
            return redirect('/' . $correctedPath);
        } else {
            return redirect('/');
        }
    }

    // Check for other common URL patterns
    $commonRedirects = [
        'ticket/my-tickets' => 'tickets/my-tickets',
        'event/my-tickets' => 'tickets/my-tickets',
        'tickets/my-tickets' => 'tickets/my-tickets',
        'user/tickets' => 'tickets/my-tickets',
        'profile/tickets' => 'tickets/my-tickets',
    ];

    foreach ($commonRedirects as $pattern => $redirect) {
        if ($requestedPath === $pattern) {
            return redirect('/' . $redirect);
        }
    }

    // Log the 404 for debugging
    \Log::warning('404 Error - Route not found', [
        'url' => $requestedUrl,
        'path' => $requestedPath,
        'method' => request()->method(),
        'user_agent' => request()->userAgent(),
        'ip' => request()->ip(),
        'referer' => request()->header('referer'),
    ]);

    return response()->view('errors.404', [
        'requestedUrl' => $requestedUrl,
        'suggestedRoutes' => [
            'Home' => route('home'),
            'Tickets' => route('tickets.index'),
            'My Tickets' => auth()->check() ? route('tiket-saya') : route('login'),
            'Login' => !auth()->check() ? route('login') : null,
        ]
    ], 404);
});


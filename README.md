# 🎫 TiXara - Event Ticketing System

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-10.x-red.svg)](https://laravel.com)
[![PHP](https://img.shields.io/badge/PHP-8.1+-blue.svg)](https://php.net)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

**TiXara** adalah sistem manajemen tiket event berbasis web yang lengkap dengan fitur auto-generate e-ticket, validasi QR code, dan sistem otorisasi staff yang canggih.

## 🌟 **Fitur Utama**

### 🎨 **12 Template Boarding Pass**
- **Unix Terminal** - Dark theme dengan green accents
- **Minimalist** - Clean dan simple design
- **Professional** - Premium business appearance
- **Elegant** - Sophisticated pink gradient
- **Modern** - Contemporary cyan design
- **Classic** - Traditional amber style
- **Neon** - Dark dengan purple neon effects
- **Retro** - Vintage orange gradient
- **Corporate** - Professional gray theme
- **Festival** - Vibrant green untuk events
- **VIP** - Luxury gold premium design
- **Custom** - Customizable template (Platinum only)

### 🔐 **Sistem Otorisasi Staff**
- **Role-based Access**: Admin, Organizer, Staff, User
- **Staff Assignment**: Staff hanya bisa validasi event dari organizer yang ditugaskan
- **Cross-organizer Protection**: Mencegah akses tidak sah
- **Real-time Authorization**: Check otomatis setiap scan

### 📱 **Scanner & Validation**
- **QR Code Scanner**: Real-time scanning dengan camera
- **Visual Feedback**: Status hijau/merah/kuning dengan icons
- **Authenticity Check**: Verifikasi keaslian tiket tanpa mengubah status
- **Audit Trail**: Log lengkap semua aktivitas validasi

### 🎯 **Auto-Generate E-Tickets**
- **Payment Integration**: Auto-generate setelah pembayaran sukses
- **Template Selection**: Gunakan template yang dipilih organizer
- **Email Delivery**: Kirim otomatis ke pembeli
- **PDF Generation**: High-quality boarding pass PDF

## 🚀 **Quick Start**

### Prerequisites
- PHP 8.1+
- Composer
- Node.js & NPM
- MySQL/PostgreSQL
- Laravel 10.x

### Installation

1. **Clone Repository**
```bash
git clone https://github.com/your-username/tixara.git
cd tixara
```

2. **Install Dependencies**
```bash
composer install
npm install
```

3. **Environment Setup**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Database Configuration**
```bash
# Edit .env file dengan database credentials
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tixara
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

5. **Run Migrations**
```bash
php artisan migrate
php artisan db:seed
```

6. **Build Assets**
```bash
npm run build
```

7. **Start Development Server**
```bash
php artisan serve
```

Visit `http://localhost:8000` untuk mengakses aplikasi.

## 📋 **User Roles & Access**

### 👑 **Admin**
- Manage semua users dan events
- Access ke semua fitur sistem
- Dapat validasi tiket dari semua organizer
- Manage staff assignments

### 🏢 **Organizer (Penjual)**
- Create dan manage events
- Pilih template boarding pass
- View sales analytics
- Manage event settings

### 👮 **Staff**
- Scan dan validasi tiket
- Hanya untuk event dari organizer yang ditugaskan
- View validation history
- Real-time dashboard

### 👤 **User (Customer)**
- Browse dan beli tiket
- Download e-tickets
- View purchase history
- Check ticket authenticity

## 🎫 **Cara Penggunaan**

### **Untuk Organizer**

1. **Create Event**
   - Login ke dashboard organizer
   - Klik "Create New Event"
   - Isi detail event
   - **Pilih template boarding pass** dari 12 pilihan
   - Set auto-generate dan email options
   - Submit form

2. **Manage Templates**
   - Preview semua template tersedia
   - Custom template untuk Platinum users
   - Real-time preview dengan sample data

### **Untuk Staff**

1. **Scanner Access**
   - Login dengan role 'staff'
   - Akses scanner via floating footer
   - Atau kunjungi `/validation/staff`

2. **Validasi Tiket**
   - Scan QR code tiket
   - Sistem check authorization otomatis
   - Jika valid → tampil tombol "Use Ticket"
   - Klik untuk mark as used

### **Untuk Customer**

1. **Pembelian**
   - Browse events yang tersedia
   - Pilih tiket dan lakukan pembayaran
   - Terima email konfirmasi

2. **E-Ticket**
   - Download PDF boarding pass
   - Print atau simpan di mobile
   - Tunjukkan QR code saat masuk event

## 🔧 **Maintenance Commands**

### **Fix Boarding Pass IDs**
```bash
# Dry run - lihat apa yang akan diperbaiki
php artisan tickets:fix-boarding-pass-ids --dry-run

# Fix missing boarding pass IDs
php artisan tickets:fix-boarding-pass-ids
```

### **Test Validation System**
```bash
# Test dengan staff dan ticket tertentu
php artisan tickets:test-validation --staff=1 --ticket=TIK-123

# Test random staff dan ticket
php artisan tickets:test-validation
```

### **Run Tests**
```bash
# Run semua tests
php artisan test

# Run specific test
php artisan test tests/Feature/TicketingSystemTest.php
```

## 🗄️ **Database Schema**

### **Key Tables**

```sql
events:
- boarding_pass_template VARCHAR(50)
- auto_generate_tickets BOOLEAN
- email_tickets_to_buyers BOOLEAN
- template_config JSON

tickets:
- boarding_pass_id VARCHAR(100) UNIQUE
- template_used VARCHAR(50)
- status ENUM('active','used','cancelled','refunded')
- used_at TIMESTAMP
- validated_by INT

staff_organizer_assignments:
- staff_id INT
- organizer_id INT
- is_active BOOLEAN
- assigned_at TIMESTAMP
```

## 🎨 **Template System**

### **Boarding Pass ID Format**
```
Format: BP-{PREFIX}-{YYMMDD}-{RANDOM}
Contoh: BP-UNX-241215-A1B2C3

Prefixes:
UNX=Unix, MIN=Minimalist, PRO=Professional
ELG=Elegant, MOD=Modern, CLS=Classic
NEO=Neon, RET=Retro, CRP=Corporate
FES=Festival, VIP=VIP, CST=Custom
```

### **Template Preview**
Akses preview template di:
- `/templates/unix-preview`
- `/templates/modern-preview`
- `/templates/vip-preview`
- dll.

## 🔒 **Security Features**

- **CSRF Protection**: Semua forms protected
- **Role-based Authorization**: Granular access control
- **Staff Assignment Validation**: Database-level checks
- **Audit Logging**: Complete activity tracking
- **QR Code Encryption**: Secure ticket validation

## 📱 **Mobile Support**

- **PWA Ready**: Progressive Web App support
- **Responsive Design**: Mobile-first approach
- **Touch-friendly**: Optimized untuk mobile scanning
- **Offline Capable**: Basic offline functionality

## 🔧 **Troubleshooting**

### **Boarding Pass ID Hilang**
```bash
# Check dan fix missing boarding pass IDs
php artisan tickets:fix-boarding-pass-ids --dry-run
php artisan tickets:fix-boarding-pass-ids
```

### **Staff Tidak Bisa Scan Tiket**
1. **Check Role**: Pastikan user memiliki role 'staff'
2. **Check Assignment**: Verify di table `staff_organizer_assignments`
3. **Check Event**: Pastikan event milik organizer yang assigned

```sql
-- Check staff assignment
SELECT * FROM staff_organizer_assignments
WHERE staff_id = ? AND is_active = 1;

-- Check event organizer
SELECT organizer_id FROM events WHERE id = ?;
```

### **Template Tidak Muncul**
1. **Check Files**: Pastikan semua template files ada di `resources/views/templates/tickets/`
2. **Check Routes**: Verify preview routes tersedia
3. **Clear Cache**: `php artisan view:clear`

### **E-Ticket Tidak Auto-Generate**
1. **Check Event Settings**: `auto_generate_tickets = true`
2. **Check Webhook**: Pastikan payment webhook berfungsi
3. **Check Logs**: `storage/logs/laravel.log`

## ❓ **FAQ**

### **Q: Bagaimana cara menambah template baru?**
A:
1. Buat file template di `resources/views/templates/tickets/new-template.blade.php`
2. Tambah prefix di `BoardingPassService::TEMPLATE_PREFIXES`
3. Update validation di `EventController`
4. Tambah preview route

### **Q: Bisakah staff validasi tiket dari organizer lain?**
A: Tidak. Staff hanya bisa validasi tiket dari organizer yang ditugaskan. Admin bisa validasi semua tiket.

### **Q: Bagaimana cara assign staff ke organizer?**
A: Melalui admin dashboard atau langsung insert ke table `staff_organizer_assignments`.

### **Q: Apakah tiket bisa di-scan berkali-kali?**
A:
- **Authenticity Check**: Bisa berkali-kali, tidak mengubah status
- **Staff Validation**: Hanya sekali, setelah itu marked as used

### **Q: Format boarding pass ID seperti apa?**
A: `BP-{PREFIX}-{YYMMDD}-{RANDOM}` contoh: `BP-UNX-241215-A1B2C3`

### **Q: Bagaimana cara backup data tiket?**
A:
```bash
# Backup database
mysqldump -u username -p database_name > backup.sql

# Backup uploaded files
tar -czf uploads_backup.tar.gz storage/app/public/
```

## 🤝 **Contributing**

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 **Support**

- **Documentation**: [SISTEM_EVENT_TICKETING_LENGKAP.md](SISTEM_EVENT_TICKETING_LENGKAP.md)
- **Issues**: [GitHub Issues](https://github.com/your-username/tixara/issues)
- **Email**: <EMAIL>

## 🙏 **Acknowledgments**

- Laravel Framework
- Tailwind CSS
- Lucide Icons
- QR Code Libraries
- All contributors

---

**Made with ❤️ by TiXara Team**

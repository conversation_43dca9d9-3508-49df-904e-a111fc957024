<?php

namespace App\Http\Controllers\Staff;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Ticket;
use App\Models\TicketValidation;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Show staff dashboard
     */
    public function index()
    {
        $staff = Auth::user();
        $today = Carbon::today();

        // Get staff statistics
        $stats = $this->getStaffStats($today);

        // Get recent validations
        $recentValidations = $this->getRecentValidations();

        // Get today's events
        $todayEvents = $this->getTodayEvents();

        return view('pages.staff.dashboard', compact(
            'stats',
            'recentValidations',
            'todayEvents'
        ));
    }

    /**
     * Get staff statistics
     */
    private function getStaffStats($today)
    {
        $staff = Auth::user();

        // Today's validations by this staff
        $todayValidations = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->count();

        $validTickets = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->where('status', 'valid')
            ->count();

        $invalidTickets = TicketValidation::where('validated_by', $staff->id)
            ->whereDate('created_at', $today)
            ->where('status', 'invalid')
            ->count();

        // Active events today
        $activeEvents = Event::where('status', 'published')
            ->whereDate('start_date', '<=', $today)
            ->whereDate('end_date', '>=', $today)
            ->count();

        // Upcoming events today
        $upcomingEvents = Event::where('status', 'published')
            ->whereDate('start_date', $today)
            ->where('start_date', '>', now())
            ->count();

        // Performance score calculation
        $totalValidations = $todayValidations;
        $successRate = $totalValidations > 0 ? ($validTickets / $totalValidations) * 100 : 100;
        $performanceScore = min(100, $successRate + ($todayValidations * 2)); // Bonus for volume

        return [
            'today_validations' => $todayValidations,
            'valid_tickets' => $validTickets,
            'invalid_tickets' => $invalidTickets,
            'active_events' => $activeEvents,
            'upcoming_events' => $upcomingEvents,
            'performance_score' => round($performanceScore, 0),
            'success_rate' => round($successRate, 1),
        ];
    }

    /**
     * Get recent validations
     */
    private function getRecentValidations()
    {
        $staff = Auth::user();

        return TicketValidation::with(['ticket.event', 'ticket.user'])
            ->where('validated_by', $staff->id)
            ->latest()
            ->limit(10)
            ->get()
            ->map(function ($validation) {
                return [
                    'id' => $validation->id ?? 0,
                    'ticket_code' => $validation->ticket->ticket_code ?? 'Unknown',
                    'event_title' => $validation->ticket->event->title ?? 'Unknown Event',
                    'customer_name' => $validation->ticket->user->name ?? 'Unknown Customer',
                    'status' => $validation->status ?? 'unknown',
                    'validated_at' => $validation->created_at ? $validation->created_at->format('H:i:s') : 'Unknown',
                    'created_at' => $validation->created_at ?? now(),
                ];
            });
    }

    /**
     * Get today's events (filtered by staff organizer assignments)
     */
    private function getTodayEvents()
    {
        $staff = Auth::user();
        $today = Carbon::today();

        // Get events that staff can validate based on organizer assignments
        $validatableEvents = $staff->getValidatableEvents();

        return $validatableEvents->filter(function ($event) use ($today) {
            // Filter for today's events
            $eventDate = Carbon::parse($event->start_date);
            $eventEndDate = Carbon::parse($event->end_date);

            return $event->status === 'published' && (
                $eventDate->isSameDay($today) ||
                ($eventDate->lte($today) && $eventEndDate->gte($today))
            );
        })
        ->map(function ($event) {
            $now = now();
            $startTime = Carbon::parse($event->start_date);
            $endTime = Carbon::parse($event->end_date);

            $status = 'upcoming';
            if ($now->between($startTime, $endTime)) {
                $status = 'active';
            } elseif ($now->gt($endTime)) {
                $status = 'ended';
            }

            // Get sold tickets count
            $soldTickets = $event->tickets()->where('status', '!=', 'cancelled')->count();

            return [
                'id' => $event->id ?? 0,
                'title' => $event->title ?? 'Unknown Event',
                'venue_name' => $event->venue_name ?? 'Unknown Venue',
                'start_time' => $startTime ? $startTime->format('H:i') : '00:00',
                'end_time' => $endTime ? $endTime->format('H:i') : '00:00',
                'capacity' => $event->total_capacity ?? 0,
                'attendees' => $soldTickets,
                'status' => $status ?? 'upcoming',
                'organizer_name' => $event->organizer->name ?? 'Unknown Organizer',
                'can_validate' => true, // Since we already filtered by assignment
            ];
        })
        ->values(); // Reset array keys
    }

    /**
     * Get dashboard stats API
     */
    public function getStats()
    {
        $today = Carbon::today();
        $stats = $this->getStaffStats($today);

        return response()->json($stats);
    }

    /**
     * Get today's events API
     */
    public function getTodayEventsApi()
    {
        $events = $this->getTodayEvents();
        return response()->json($events);
    }

    /**
     * Get validation history API
     */
    public function getValidationHistory(Request $request)
    {
        $staff = Auth::user();
        $limit = $request->get('limit', 20);

        $validations = TicketValidation::with(['ticket.event', 'ticket.user'])
            ->where('validated_by', $staff->id)
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($validation) {
                return [
                    'id' => $validation->id ?? 0,
                    'ticket_code' => $validation->ticket->ticket_code ?? 'Unknown',
                    'event_title' => $validation->ticket->event->title ?? 'Unknown Event',
                    'customer_name' => $validation->ticket->user->name ?? 'Unknown Customer',
                    'status' => $validation->status ?? 'unknown',
                    'validated_at' => $validation->created_at ? $validation->created_at->diffForHumans() : 'Unknown time',
                    'created_at' => $validation->created_at ?? now(),
                ];
            });

        return response()->json($validations);
    }

    /**
     * Validate ticket
     */
    public function validateTicket(Request $request)
    {
        $request->validate([
            'ticket_code' => 'required|string',
        ]);

        $staff = Auth::user();
        $ticketCode = trim($request->ticket_code);

        // Try to parse JSON if it's a QR code with JSON data
        $qrData = null;
        try {
            $qrData = json_decode($ticketCode, true);
            if ($qrData && isset($qrData['qr_code'])) {
                $ticketCode = $qrData['qr_code'];
            } elseif ($qrData && isset($qrData['ticket_number'])) {
                $ticketCode = $qrData['ticket_number'];
            }
        } catch (Exception $e) {
            // Not JSON, use as is
        }

        // Find ticket by multiple possible identifiers
        $ticket = Ticket::where(function($query) use ($ticketCode) {
            $query->where('ticket_number', $ticketCode)
                  ->orWhere('qr_code', $ticketCode)
                  ->orWhere('id', $ticketCode);
        })
        ->with(['event.organizer', 'buyer', 'validatedBy'])
        ->first();

        if (!$ticket) {
            return response()->json([
                'success' => false,
                'message' => 'Tiket tidak ditemukan dengan kode: ' . $ticketCode,
                'status' => 'not_found',
                'ticket_code' => $ticketCode
            ], 404);
        }

        // Check if event exists
        if (!$ticket->event) {
            return response()->json([
                'success' => false,
                'message' => 'Event tidak ditemukan',
                'status' => 'event_not_found'
            ], 400);
        }

        // STAFF ORGANIZER ASSIGNMENT VALIDATION
        if (!$staff->canValidateTicketFromOrganizer($ticket->event->organizer_id)) {
            return response()->json([
                'success' => false,
                'message' => 'Anda tidak memiliki akses untuk memvalidasi tiket dari organizer ini',
                'status' => 'access_denied',
                'organizer_info' => [
                    'name' => $ticket->event->organizer->name ?? 'Unknown',
                    'event_title' => $ticket->event->title
                ],
                'staff_info' => [
                    'name' => $staff->name,
                    'assigned_organizers_count' => $staff->getAssignedOrganizersCount(),
                    'has_assignments' => $staff->hasOrganizerAssignments()
                ]
            ], 403);
        }

        // Check if ticket is already used/validated
        if ($ticket->status === 'used') {
            return response()->json([
                'success' => false,
                'message' => 'Tiket sudah digunakan',
                'status' => 'already_used',
                'ticket' => [
                    'ticket_number' => $ticket->ticket_number,
                    'event' => $ticket->event->title ?? 'Unknown Event',
                    'buyer' => $ticket->buyer->name ?? 'Unknown Customer',
                    'used_at' => $ticket->used_at ? $ticket->used_at->format('d M Y H:i:s') : 'Unknown',
                    'validated_by' => $ticket->validatedBy->name ?? 'Unknown'
                ]
            ], 400);
        }

        // Check if ticket is cancelled
        if ($ticket->status === 'cancelled') {
            return response()->json([
                'success' => false,
                'message' => 'Tiket telah dibatalkan',
                'status' => 'cancelled',
                'ticket' => [
                    'ticket_number' => $ticket->ticket_number,
                    'event' => $ticket->event->title ?? 'Unknown Event',
                    'buyer' => $ticket->buyer->name ?? 'Unknown Customer'
                ]
            ], 400);
        }

        // Check if event exists
        if (!$ticket->event) {
            return response()->json([
                'success' => false,
                'message' => 'Event tidak ditemukan',
                'status' => 'event_not_found'
            ], 400);
        }

        // Check event date (allow validation on event day)
        $eventDate = Carbon::parse($ticket->event->start_date);
        $today = Carbon::today();

        if ($eventDate->isPast() && !$eventDate->isSameDay($today)) {
            return response()->json([
                'success' => false,
                'message' => 'Event sudah berakhir',
                'status' => 'event_expired',
                'event_date' => $eventDate->format('d M Y')
            ], 400);
        }

        if ($eventDate->isFuture() && !$eventDate->isSameDay($today)) {
            return response()->json([
                'success' => false,
                'message' => 'Event belum dimulai',
                'status' => 'event_not_started',
                'event_date' => $eventDate->format('d M Y')
            ], 400);
        }

        try {
            // Create validation record
            $validation = TicketValidation::create([
                'ticket_id' => $ticket->id,
                'validated_by' => $staff->id,
                'status' => 'valid',
                'validated_at' => now(),
            ]);

            // Update ticket status
            $ticket->update([
                'status' => 'used',
                'used_at' => now(),
                'validated_by' => $staff->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tiket berhasil divalidasi',
                'status' => 'valid',
                'ticket' => [
                    'id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'qr_code' => $ticket->qr_code,
                    'event' => [
                        'id' => $ticket->event->id,
                        'title' => $ticket->event->title,
                        'start_date' => $ticket->event->start_date,
                        'location' => $ticket->event->location ?? 'TBA'
                    ],
                    'buyer' => [
                        'id' => $ticket->buyer->id ?? null,
                        'name' => $ticket->buyer->name ?? 'Unknown Customer',
                        'email' => $ticket->buyer->email ?? 'Unknown'
                    ],
                    'price' => $ticket->price,
                    'validated_at' => $validation->validated_at->format('d M Y H:i:s'),
                    'validated_by' => $staff->name
                ]
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal memvalidasi tiket: ' . $e->getMessage(),
                'status' => 'validation_error'
            ], 500);
        }
    }

    /**
     * Get real-time dashboard metrics
     */
    public function getRealTimeMetrics()
    {
        $staff = Auth::user();
        $today = Carbon::today();

        $metrics = [
            'session_stats' => [
                'start_time' => session('staff_session_start', now()),
                'total_scans' => TicketValidation::where('validated_by', $staff->id)
                    ->whereDate('created_at', $today)
                    ->count(),
                'valid_scans' => TicketValidation::where('validated_by', $staff->id)
                    ->whereDate('created_at', $today)
                    ->where('status', 'valid')
                    ->count(),
                'invalid_scans' => TicketValidation::where('validated_by', $staff->id)
                    ->whereDate('created_at', $today)
                    ->where('status', 'invalid')
                    ->count(),
            ],
            'performance' => [
                'scans_per_minute' => $this->calculateScansPerMinute($staff->id),
                'accuracy_rate' => $this->calculateAccuracyRate($staff->id),
                'peak_hour' => $this->getPeakHour($staff->id),
            ],
            'system_status' => [
                'database_latency' => $this->getDatabaseLatency(),
                'active_staff_count' => $this->getActiveStaffCount(),
                'total_events_today' => Event::whereDate('start_date', $today)->count(),
            ]
        ];

        return response()->json($metrics);
    }

    /**
     * Calculate scans per minute for staff
     */
    private function calculateScansPerMinute($staffId)
    {
        $sessionStart = session('staff_session_start', now()->subHour());
        $minutesActive = now()->diffInMinutes($sessionStart);

        if ($minutesActive == 0) return 0;

        $totalScans = TicketValidation::where('validated_by', $staffId)
            ->where('created_at', '>=', $sessionStart)
            ->count();

        return round($totalScans / $minutesActive, 2);
    }

    /**
     * Calculate accuracy rate for staff
     */
    private function calculateAccuracyRate($staffId)
    {
        $today = Carbon::today();
        $totalScans = TicketValidation::where('validated_by', $staffId)
            ->whereDate('created_at', $today)
            ->count();

        if ($totalScans == 0) return 100;

        $validScans = TicketValidation::where('validated_by', $staffId)
            ->whereDate('created_at', $today)
            ->where('status', 'valid')
            ->count();

        return round(($validScans / $totalScans) * 100, 1);
    }

    /**
     * Get peak hour for staff activity
     */
    private function getPeakHour($staffId)
    {
        $today = Carbon::today();

        $hourlyStats = TicketValidation::where('validated_by', $staffId)
            ->whereDate('created_at', $today)
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->first();

        return $hourlyStats ? $hourlyStats->hour . ':00' : 'N/A';
    }

    /**
     * Get database latency
     */
    private function getDatabaseLatency()
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        $end = microtime(true);

        return round(($end - $start) * 1000, 2); // in milliseconds
    }

    /**
     * Get active staff count
     */
    private function getActiveStaffCount()
    {
        $today = Carbon::today();

        return TicketValidation::whereDate('created_at', $today)
            ->distinct('validated_by')
            ->count('validated_by');
    }

    /**
     * Get staff leaderboard
     */
    public function getStaffLeaderboard()
    {
        $today = Carbon::today();

        $leaderboard = TicketValidation::whereDate('created_at', $today)
            ->selectRaw('validated_by, COUNT(*) as total_validations,
                        SUM(CASE WHEN status = "valid" THEN 1 ELSE 0 END) as valid_count,
                        SUM(CASE WHEN status = "invalid" THEN 1 ELSE 0 END) as invalid_count')
            ->groupBy('validated_by')
            ->orderBy('total_validations', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                $staff = \App\Models\User::find($item->validated_by);
                return [
                    'staff_name' => $staff ? $staff->name : 'Unknown',
                    'total_validations' => $item->total_validations,
                    'valid_count' => $item->valid_count,
                    'invalid_count' => $item->invalid_count,
                    'accuracy_rate' => $item->total_validations > 0 ?
                        round(($item->valid_count / $item->total_validations) * 100, 1) : 0
                ];
            });

        return response()->json($leaderboard);
    }

    /**
     * Get hourly validation statistics
     */
    public function getHourlyStats()
    {
        $staff = Auth::user();
        $today = Carbon::today();

        $hourlyStats = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $validations = TicketValidation::where('validated_by', $staff->id)
                ->whereDate('created_at', $today)
                ->whereRaw('HOUR(created_at) = ?', [$hour])
                ->count();

            $hourlyStats[] = [
                'hour' => sprintf('%02d:00', $hour),
                'validations' => $validations
            ];
        }

        return response()->json($hourlyStats);
    }

    /**
     * Export staff validation report
     */
    public function exportValidationReport(Request $request)
    {
        $staff = Auth::user();
        $startDate = $request->get('start_date', Carbon::today()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::today()->format('Y-m-d'));

        $validations = TicketValidation::with(['ticket.event', 'ticket.buyer'])
            ->where('validated_by', $staff->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->get();

        $reportData = [
            'staff_name' => $staff->name,
            'period' => $startDate . ' to ' . $endDate,
            'total_validations' => $validations->count(),
            'valid_count' => $validations->where('status', 'valid')->count(),
            'invalid_count' => $validations->where('status', 'invalid')->count(),
            'validations' => $validations->map(function ($validation) {
                return [
                    'date_time' => $validation->created_at->format('d/m/Y H:i:s'),
                    'ticket_number' => $validation->ticket->ticket_number ?? 'N/A',
                    'event_title' => $validation->ticket->event->title ?? 'N/A',
                    'customer_name' => $validation->ticket->buyer->name ?? 'N/A',
                    'status' => ucfirst($validation->status),
                    'validation_method' => $validation->validation_method ?? 'manual'
                ];
            })
        ];

        return response()->json($reportData);
    }
}

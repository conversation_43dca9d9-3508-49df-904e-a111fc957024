<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\Event;
use App\Models\Notification;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Payment Methods API
Route::get('/payment-methods/available', [App\Http\Controllers\Api\PaymentMethodController::class, 'getAvailablePaymentMethods']);
Route::get('/payment-methods/{code}', [App\Http\Controllers\Api\PaymentMethodController::class, 'getPaymentMethodDetails']);
Route::post('/payment-methods/calculate-fee', [App\Http\Controllers\Api\PaymentMethodController::class, 'calculateFee']);

// Payment Gateway API (Admin only)
Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    Route::post('/payment-gateways/{gateway}/test', [App\Http\Controllers\Api\PaymentMethodController::class, 'testGatewayConnection']);
});

// Payment Methods API
Route::get('/payment-methods', function () {
    $paymentMethods = \App\Models\PaymentMethod::active()
        ->ordered()
        ->get()
        ->map(function ($method) {
            return [
                'id' => $method->id,
                'name' => $method->name,
                'code' => $method->code,
                'category' => $method->category,
                'category_label' => $method->category_label,
                'type' => $method->type,
                'type_label' => $method->type_label,
                'description' => $method->description,
                'icon' => $method->icon,
                'logo' => $method->logo,
                'formatted_fee' => $method->formatted_fee,
                'formatted_limits' => $method->formatted_limits,
                'is_manual' => $method->is_manual,
                'min_amount' => $method->min_amount,
                'max_amount' => $method->max_amount,
                'instructions' => $method->instructions,
            ];
        });

    return response()->json([
        'success' => true,
        'payment_methods' => $paymentMethods
    ]);
});

// Admin API Routes
Route::middleware(['auth:sanctum', 'admin'])->prefix('admin')->group(function () {
    // Gateway Balances
    Route::get('/gateway-balances', function () {
        $paymentMethods = \App\Models\PaymentMethod::gateway()
            ->active()
            ->get()
            ->map(function ($method) {
                return [
                    'id' => $method->id,
                    'name' => $method->name,
                    'type' => $method->type_label,
                    'balance' => $method->gateway_balance,
                    'formatted_balance' => $method->formatted_gateway_balance,
                    'status' => $method->balance_status,
                    'last_updated' => $method->balance_last_updated?->diffForHumans(),
                ];
            });

        return response()->json([
            'success' => true,
            'balances' => $paymentMethods
        ]);
    });

    // Sync Gateway Balances
    Route::post('/sync-gateway-balances', function () {
        try {
            $results = \App\Models\PaymentMethod::syncAllGatewayBalances();
            $successCount = collect($results)->where('success', true)->count();
            $totalCount = count($results);

            return response()->json([
                'success' => true,
                'message' => "Synced {$successCount}/{$totalCount} gateways successfully",
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync gateway balances: ' . $e->getMessage()
            ]);
        }
    });

    // Security Status
    Route::get('/security-status', function () {
        try {
            $status = \App\Models\CyberGuard\CyberGuardSetting::getSecurityStatus();

            return response()->json([
                'success' => true,
                'status' => $status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load security status: ' . $e->getMessage()
            ]);
        }
    });

    // Badge Statistics
    Route::get('/badge-stats', function () {
        try {
            $stats = \App\Models\UserBadgeLevel::getStatistics();

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load badge statistics: ' . $e->getMessage()
            ]);
        }
    });

    // Auto Upgrade Badges
    Route::post('/auto-upgrade-badges', function () {
        try {
            $upgradedCount = \App\Models\UserBadgeLevel::autoUpgradeUsers();

            return response()->json([
                'success' => true,
                'message' => "Successfully upgraded {$upgradedCount} users",
                'upgraded_count' => $upgradedCount
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to auto-upgrade badges: ' . $e->getMessage()
            ]);
        }
    });
});

// Health check endpoint
Route::get('/health-check', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'service' => 'TiXara API',
        'version' => '1.0.0'
    ]);
});

// API Status endpoint with detailed information
Route::get('/status', function () {
    try {
        // Test database connection
        $dbStatus = 'ok';
        $dbMessage = 'Connected';
        try {
            DB::connection()->getPdo();
            $eventCount = DB::table('events')->count();
            $userCount = DB::table('users')->count();
        } catch (\Exception $e) {
            $dbStatus = 'error';
            $dbMessage = 'Connection failed';
            $eventCount = 0;
            $userCount = 0;
        }

        // Test cache (if available)
        $cacheStatus = 'ok';
        try {
            Cache::put('api_test', 'working', 60);
            $cacheTest = Cache::get('api_test');
            if ($cacheTest !== 'working') {
                $cacheStatus = 'error';
            }
        } catch (\Exception $e) {
            $cacheStatus = 'error';
        }

        return response()->json([
            'status' => 'ok',
            'timestamp' => now()->toISOString(),
            'service' => 'TiXara API',
            'version' => '1.0.0',
            'environment' => app()->environment(),
            'database' => [
                'status' => $dbStatus,
                'message' => $dbMessage,
                'events_count' => $eventCount,
                'users_count' => $userCount
            ],
            'cache' => [
                'status' => $cacheStatus
            ],
            'endpoints' => [
                'search' => '/api/search',
                'notifications' => '/api/notifications/latest',
                'health' => '/api/health-check'
            ]
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'timestamp' => now()->toISOString(),
            'service' => 'TiXara API',
            'version' => '1.0.0',
            'error' => 'System error',
            'message' => 'API temporarily unavailable'
        ], 500);
    }
});

// Search API
Route::get('/search', function (Request $request) {
    $query = $request->get('q', '');

    if (strlen($query) < 2) {
        return response()->json([
            'results' => [],
            'query' => $query,
            'message' => 'Query too short'
        ]);
    }

    try {
        // Search events by title, description, and venue
        $events = Event::where('status', 'published')
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('venue_name', 'LIKE', "%{$query}%")
                  ->orWhere('city', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'title', 'description', 'venue_name', 'city', 'slug', 'poster']);

        $results = $events->map(function ($event) {
            return [
                'id' => $event->id,
                'title' => $event->title,
                'description' => \Str::limit(strip_tags($event->description), 100),
                'venue' => $event->venue_name,
                'city' => $event->city,
                'poster' => $event->poster_url ?? null,
                'url' => route('tickets.show', $event->slug)
            ];
        });

        return response()->json([
            'results' => $results,
            'query' => $query,
            'count' => $results->count(),
            'message' => $results->count() > 0 ? 'Search successful' : 'No events found'
        ]);
    } catch (\Exception $e) {
        \Log::error('Search API Error: ' . $e->getMessage(), [
            'query' => $query,
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'results' => [],
            'query' => $query,
            'error' => 'Search temporarily unavailable',
            'message' => 'Please try again later'
        ], 500);
    }
});

// Notifications API (requires authentication)
Route::middleware('auth:sanctum')->group(function () {
    // Get latest notifications
    Route::get('/notifications/latest', function (Request $request) {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'error' => 'User not authenticated',
                    'notifications' => [],
                    'unread_count' => 0
                ], 401);
            }

            $lastCheck = $request->get('last_check');

            $query = $user->notifications()->latest();

            if ($lastCheck) {
                $query->where('created_at', '>', $lastCheck);
            }

            $notifications = $query->limit(20)->get();
            $unreadCount = $user->unreadNotifications()->count();

            return response()->json([
                'notifications' => $notifications->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'title' => $notification->data['title'] ?? 'Notifikasi',
                        'message' => $notification->data['message'] ?? '',
                        'type' => $notification->data['type'] ?? 'info',
                        'created_at' => $notification->created_at->toISOString(),
                        'read_at' => $notification->read_at?->toISOString(),
                        'action_url' => $notification->data['action_url'] ?? null
                    ];
                }),
                'unread_count' => $unreadCount,
                'has_new' => $lastCheck ? $notifications->count() > 0 : false,
                'message' => $notifications->count() > 0 ? 'Notifications loaded' : 'No notifications found'
            ]);
        } catch (\Exception $e) {
            \Log::error('Notifications API Error: ' . $e->getMessage(), [
                'user_id' => $request->user()?->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to load notifications',
                'notifications' => [],
                'unread_count' => 0,
                'message' => 'Please try again later'
            ], 500);
        }
    });

    // Mark notification as read
    Route::post('/notifications/{id}/read', function (Request $request, $id) {
        try {
            $user = $request->user();
            $notification = $user->notifications()->find($id);

            if ($notification) {
                $notification->markAsRead();
                return response()->json(['success' => true]);
            }

            return response()->json(['error' => 'Notification not found'], 404);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark notification as read'], 500);
        }
    });

    // Mark all notifications as read
    Route::post('/notifications/mark-all-read', function (Request $request) {
        try {
            $user = $request->user();
            $user->unreadNotifications()->update(['read_at' => now()]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark all notifications as read'], 500);
        }
    });
});

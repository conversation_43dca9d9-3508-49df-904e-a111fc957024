<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop badge_level column and related columns if they exist
            if (Schema::hasColumn('users', 'badge_level')) {
                $table->dropColumn('badge_level');
            }
            
            if (Schema::hasColumn('users', 'level_upgraded_at')) {
                $table->dropColumn('level_upgraded_at');
            }
            
            if (Schema::hasColumn('users', 'level_expires_at')) {
                $table->dropColumn('level_expires_at');
            }
            
            if (Schema::hasColumn('users', 'level_benefits')) {
                $table->dropColumn('level_benefits');
            }
            
            if (Schema::hasColumn('users', 'level_notes')) {
                $table->dropColumn('level_notes');
            }
            
            if (Schema::hasColumn('users', 'level_score')) {
                $table->dropColumn('level_score');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Restore badge_level column and related columns
            $table->enum('badge_level', ['bronze', 'star', 'premium'])->default('bronze')->after('bio');
            $table->timestamp('level_upgraded_at')->nullable()->after('badge_level');
            $table->timestamp('level_expires_at')->nullable()->after('level_upgraded_at');
            $table->json('level_benefits')->nullable()->after('level_expires_at');
            $table->text('level_notes')->nullable()->after('level_benefits');
            $table->integer('level_score')->default(0)->after('level_notes');
        });
    }
};

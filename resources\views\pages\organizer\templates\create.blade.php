@extends('layouts.organizer')

@section('title', 'Create Template')

@push('styles')
<style>
.template-type-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.template-type-card {
    border: 2px solid #E5E7EB;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
}

.template-type-card:hover {
    border-color: #3B82F6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.template-type-card.selected {
    border-color: #3B82F6;
    background: linear-gradient(135deg, #EBF8FF 0%, #DBEAFE 100%);
}

.template-type-card.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #F9FAFB;
}

.template-type-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    margin: 0 auto 12px;
}

.type-classic .template-type-icon { background: #059669; }
.type-unix .template-type-icon { background: #000000; }
.type-minimal .template-type-icon { background: #2563EB; }
.type-pro .template-type-icon { background: #7C3AED; }
.type-custom .template-type-icon { background: #F59E0B; }

.config-section {
    background: #F8FAFC;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
}

.color-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #E5E7EB;
    cursor: pointer;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.preview-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    padding: 24px;
    min-height: 400px;
}

.preview-iframe {
    width: 100%;
    height: 350px;
    border: none;
    border-radius: 8px;
    background: #F8FAFC;
}

.badge-requirement-notice {
    background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
    border: 1px solid #F59E0B;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.badge-requirement-notice.error {
    background: linear-gradient(135deg, #FEE2E2 0%, #FECACA 100%);
    border-color: #EF4444;
}

.form-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    padding: 24px;
    margin-bottom: 24px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.custom-config-editor {
    display: none;
}

.custom-config-editor.active {
    display: block;
}

@media (max-width: 768px) {
    .template-type-selector {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Create Template</h1>
            <p class="text-gray-600 dark:text-gray-400">Design your custom ticket template</p>
        </div>
        <a href="{{ route('organizer.templates.index') }}" class="btn btn-secondary">
            <i data-lucide="arrow-left" class="w-4 h-4"></i>
            Back to Templates
        </a>
    </div>

    <form method="POST" action="{{ route('organizer.templates.store') }}" enctype="multipart/form-data" id="templateForm">
        @csrf
        
        <!-- Template Type Selection -->
        <div class="form-section">
            <div class="section-title">
                <i data-lucide="layout-template" class="w-5 h-5"></i>
                Choose Template Type
            </div>
            
            <div class="template-type-selector">
                @foreach($availableTypes as $key => $name)
                    <div class="template-type-card type-{{ $key }} {{ $key === $templateType ? 'selected' : '' }}" 
                         data-type="{{ $key }}" onclick="selectTemplateType('{{ $key }}')">
                        <div class="template-type-icon">
                            @switch($key)
                                @case('classic')
                                    <i data-lucide="ticket"></i>
                                    @break
                                @case('unix')
                                    <i data-lucide="terminal"></i>
                                    @break
                                @case('minimal')
                                    <i data-lucide="minimize-2"></i>
                                    @break
                                @case('pro')
                                    <i data-lucide="crown"></i>
                                    @break
                                @case('custom')
                                    <i data-lucide="palette"></i>
                                    @break
                            @endswitch
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">{{ $name }}</h3>
                        <p class="text-sm text-gray-600">
                            @switch($key)
                                @case('classic')
                                    Traditional ticket design with classic elements
                                    @break
                                @case('unix')
                                    Terminal-style design for tech events
                                    @break
                                @case('minimal')
                                    Clean and simple design
                                    @break
                                @case('pro')
                                    Professional business-style template
                                    @break
                                @case('custom')
                                    Fully customizable with drag-and-drop editor
                                    @break
                            @endswitch
                        </p>
                        @if($key === 'custom')
                            <div class="mt-2">
                                <span class="inline-flex items-center gap-1 text-xs font-medium text-orange-600">
                                    <i data-lucide="lock" class="w-3 h-3"></i>
                                    Requires Platinum Badge
                                </span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
            
            <input type="hidden" name="template_type" id="templateType" value="{{ $templateType }}">
        </div>

        <!-- Basic Information -->
        <div class="form-section">
            <div class="section-title">
                <i data-lucide="info" class="w-5 h-5"></i>
                Basic Information
            </div>
            
            <div class="form-grid">
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Template Name *
                    </label>
                    <input type="text" id="name" name="name" value="{{ old('name') }}" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="Enter template name">
                    @error('name')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="preview_image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Preview Image
                    </label>
                    <input type="file" id="preview_image" name="preview_image" accept="image/*"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    @error('preview_image')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div class="mt-4">
                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description
                </label>
                <textarea id="description" name="description" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Describe your template">{{ old('description') }}</textarea>
                @error('description')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Template Configuration -->
        <div class="form-section" id="configSection">
            <div class="section-title">
                <i data-lucide="settings" class="w-5 h-5"></i>
                Template Configuration
            </div>
            
            <!-- Basic Config -->
            <div id="basicConfig">
                <div class="form-grid">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Primary Color
                        </label>
                        <div class="color-input-group">
                            <input type="color" name="config[primary_color]" id="primaryColor" 
                                   value="{{ $defaultConfig['primary_color'] ?? '#3B82F6' }}"
                                   class="color-preview">
                            <input type="text" value="{{ $defaultConfig['primary_color'] ?? '#3B82F6' }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="document.getElementById('primaryColor').value = this.value">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Secondary Color
                        </label>
                        <div class="color-input-group">
                            <input type="color" name="config[secondary_color]" id="secondaryColor" 
                                   value="{{ $defaultConfig['secondary_color'] ?? '#6B7280' }}"
                                   class="color-preview">
                            <input type="text" value="{{ $defaultConfig['secondary_color'] ?? '#6B7280' }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="document.getElementById('secondaryColor').value = this.value">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Background Color
                        </label>
                        <div class="color-input-group">
                            <input type="color" name="config[background_color]" id="backgroundColor" 
                                   value="{{ $defaultConfig['background_color'] ?? '#FFFFFF' }}"
                                   class="color-preview">
                            <input type="text" value="{{ $defaultConfig['background_color'] ?? '#FFFFFF' }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="document.getElementById('backgroundColor').value = this.value">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Text Color
                        </label>
                        <div class="color-input-group">
                            <input type="color" name="config[text_color]" id="textColor" 
                                   value="{{ $defaultConfig['text_color'] ?? '#1F2937' }}"
                                   class="color-preview">
                            <input type="text" value="{{ $defaultConfig['text_color'] ?? '#1F2937' }}"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="document.getElementById('textColor').value = this.value">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Custom Config (for custom template type) -->
            <div id="customConfig" class="custom-config-editor {{ $templateType === 'custom' ? 'active' : '' }}">
                <div class="badge-requirement-notice {{ auth()->user()->badge_level_id >= 4 ? '' : 'error' }}">
                    <div class="flex items-center gap-2">
                        <i data-lucide="{{ auth()->user()->badge_level_id >= 4 ? 'check-circle' : 'alert-circle' }}" class="w-5 h-5"></i>
                        <div>
                            <h4 class="font-semibold">Custom Template Requirements</h4>
                            <p class="text-sm">
                                @if(auth()->user()->badge_level_id >= 4)
                                    You have access to custom templates with drag-and-drop editor.
                                @else
                                    Custom templates require Platinum badge level or higher. 
                                    <a href="{{ route('organizer.dashboard') }}" class="underline">Upgrade your badge</a> to unlock this feature.
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
                
                @if(auth()->user()->badge_level_id >= 4)
                    <div class="form-grid">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Layout Style
                            </label>
                            <select name="custom_config[layout]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="horizontal">Horizontal</option>
                                <option value="vertical">Vertical</option>
                                <option value="split">Split Layout</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Header Style
                            </label>
                            <select name="custom_config[header_style]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="gradient">Gradient</option>
                                <option value="solid">Solid Color</option>
                                <option value="image">Image Background</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Border Style
                            </label>
                            <select name="custom_config[border_style]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="rounded">Rounded</option>
                                <option value="sharp">Sharp</option>
                                <option value="dashed">Dashed</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Font Family
                            </label>
                            <select name="custom_config[font_family]" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="Inter">Inter</option>
                                <option value="Poppins">Poppins</option>
                                <option value="Roboto">Roboto</option>
                                <option value="Custom">Custom Font</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-900 mb-3">Template Elements</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <label class="flex items-center gap-2">
                                <input type="checkbox" name="custom_config[show_qr_code]" value="1" checked
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm">Show QR Code</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="checkbox" name="custom_config[show_price]" value="1" checked
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm">Show Price</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="checkbox" name="custom_config[show_watermark]" value="1" checked
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm">Show Watermark</span>
                            </label>
                            <label class="flex items-center gap-2">
                                <input type="checkbox" name="custom_config[show_logo]" value="1" checked
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm">Show Logo</span>
                            </label>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Preview Section -->
        <div class="form-section">
            <div class="section-title">
                <i data-lucide="eye" class="w-5 h-5"></i>
                Template Preview
            </div>
            
            <div class="preview-section">
                <iframe id="previewFrame" class="preview-iframe" src="about:blank"></iframe>
                <div class="text-center mt-4">
                    <button type="button" onclick="updatePreview()" class="btn btn-secondary">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        Update Preview
                    </button>
                </div>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="flex justify-end gap-4">
            <a href="{{ route('organizer.templates.index') }}" class="btn btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                <i data-lucide="save" class="w-4 h-4"></i>
                Create Template
            </button>
        </div>
    </form>
</div>

@push('scripts')
<script>
function selectTemplateType(type) {
    // Update UI
    document.querySelectorAll('.template-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('selected');
    
    // Update form
    document.getElementById('templateType').value = type;
    
    // Show/hide custom config
    const customConfig = document.getElementById('customConfig');
    if (type === 'custom') {
        customConfig.classList.add('active');
    } else {
        customConfig.classList.remove('active');
    }
    
    // Update preview
    updatePreview();
}

function updatePreview() {
    const templateType = document.getElementById('templateType').value;
    const previewFrame = document.getElementById('previewFrame');
    
    // Build preview URL with current config
    const params = new URLSearchParams();
    params.append('template_type', templateType);
    
    // Add color config
    const primaryColor = document.getElementById('primaryColor').value;
    const secondaryColor = document.getElementById('secondaryColor').value;
    const backgroundColor = document.getElementById('backgroundColor').value;
    const textColor = document.getElementById('textColor').value;
    
    params.append('primary_color', primaryColor);
    params.append('secondary_color', secondaryColor);
    params.append('background_color', backgroundColor);
    params.append('text_color', textColor);
    
    // Set preview URL
    previewFrame.src = `/templates/${templateType}-preview?${params.toString()}`;
}

// Initialize preview on page load
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
});

// Update preview when colors change
document.querySelectorAll('input[type="color"]').forEach(input => {
    input.addEventListener('change', updatePreview);
});
</script>
@endpush
@endsection

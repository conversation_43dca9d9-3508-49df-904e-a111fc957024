<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentWebhookController extends Controller
{
    /**
     * Handle payment webhook from payment gateways
     */
    public function handle(Request $request)
    {
        try {
            // Log incoming webhook
            Log::info('Payment webhook received', [
                'headers' => $request->headers->all(),
                'body' => $request->all(),
                'ip' => $request->ip()
            ]);

            // Verify webhook signature (implement based on your payment gateway)
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('Invalid webhook signature', [
                    'ip' => $request->ip(),
                    'headers' => $request->headers->all()
                ]);
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            // Parse webhook data
            $webhookData = $this->parseWebhookData($request);
            
            if (!$webhookData) {
                Log::error('Failed to parse webhook data', [
                    'body' => $request->all()
                ]);
                return response()->json(['error' => 'Invalid webhook data'], 400);
            }

            // Find order by reference
            $order = Order::where('payment_reference', $webhookData['reference'])
                ->orWhere('order_number', $webhookData['order_number'] ?? '')
                ->first();

            if (!$order) {
                Log::warning('Order not found for webhook', [
                    'reference' => $webhookData['reference'],
                    'order_number' => $webhookData['order_number'] ?? null
                ]);
                return response()->json(['error' => 'Order not found'], 404);
            }

            // Process payment status update
            $this->processPaymentUpdate($order, $webhookData);

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('Webhook processing error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifyWebhookSignature(Request $request): bool
    {
        // Implement signature verification based on your payment gateway
        // For now, return true for testing
        return true;
        
        // Example for Midtrans:
        // $signature = $request->header('X-Callback-Token');
        // $expectedSignature = hash('sha512', $order->id . $order->status_code . $order->gross_amount . config('midtrans.server_key'));
        // return hash_equals($expectedSignature, $signature);
    }

    /**
     * Parse webhook data from different payment gateways
     */
    private function parseWebhookData(Request $request): ?array
    {
        $data = $request->all();

        // Handle different payment gateway formats
        if (isset($data['transaction_status'])) {
            // Midtrans format
            return [
                'reference' => $data['transaction_id'] ?? $data['order_id'],
                'order_number' => $data['order_id'],
                'status' => $this->mapMidtransStatus($data['transaction_status']),
                'amount' => $data['gross_amount'] ?? 0,
                'payment_method' => $data['payment_type'] ?? 'unknown',
                'gateway' => 'midtrans',
                'raw_data' => $data
            ];
        } elseif (isset($data['status'])) {
            // Tripay format
            return [
                'reference' => $data['reference'],
                'order_number' => $data['merchant_ref'],
                'status' => $this->mapTripayStatus($data['status']),
                'amount' => $data['amount'] ?? 0,
                'payment_method' => $data['payment_method'] ?? 'unknown',
                'gateway' => 'tripay',
                'raw_data' => $data
            ];
        } elseif (isset($data['event'])) {
            // Xendit format
            return [
                'reference' => $data['id'],
                'order_number' => $data['external_id'],
                'status' => $this->mapXenditStatus($data['status']),
                'amount' => $data['amount'] ?? 0,
                'payment_method' => $data['payment_method'] ?? 'unknown',
                'gateway' => 'xendit',
                'raw_data' => $data
            ];
        }

        return null;
    }

    /**
     * Process payment status update
     */
    private function processPaymentUpdate(Order $order, array $webhookData): void
    {
        DB::transaction(function () use ($order, $webhookData) {
            $oldStatus = $order->payment_status;
            $newStatus = $webhookData['status'];

            // Update order payment status
            $order->update([
                'payment_status' => $newStatus,
                'payment_data' => array_merge($order->payment_data ?? [], [
                    'webhook_data' => $webhookData['raw_data'],
                    'updated_at' => now()->toISOString()
                ])
            ]);

            // Handle status changes
            if ($newStatus === 'paid' && $oldStatus !== 'paid') {
                // Payment successful
                $order->update([
                    'paid_at' => now(),
                    'status' => 'confirmed',
                    'confirmed_at' => now()
                ]);

                // Activate tickets
                $order->tickets()->update([
                    'status' => 'active'
                ]);

                // Send confirmation email
                $this->sendPaymentConfirmation($order);

                Log::info('Payment confirmed via webhook', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'amount' => $order->total_amount,
                    'gateway' => $webhookData['gateway']
                ]);

            } elseif ($newStatus === 'failed' && $oldStatus !== 'failed') {
                // Payment failed
                $order->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                    'cancellation_reason' => 'Payment failed'
                ]);

                // Cancel tickets
                $order->tickets()->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                    'cancellation_reason' => 'Payment failed'
                ]);

                // Restore event capacity
                $order->event->increment('available_capacity', $order->quantity);

                Log::info('Payment failed via webhook', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'gateway' => $webhookData['gateway']
                ]);
            }
        });
    }

    /**
     * Send payment confirmation
     */
    private function sendPaymentConfirmation(Order $order): void
    {
        try {
            // Load necessary relationships
            $order->load(['event', 'user', 'tickets']);
            
            // Generate E-Tickets (PDF and QR codes) if not already generated
            $this->generateETickets($order);
            
            // Send email with E-Tickets
            \Mail::to($order->user->email)->send(new \App\Mail\TicketPurchasedMail($order));
            
            Log::info("E-Ticket email sent successfully for order {$order->order_number}", [
                'order_id' => $order->id,
                'user_email' => $order->user->email,
                'tickets_count' => $order->tickets->count()
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to send E-Ticket email for order {$order->order_number}", [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Generate E-Tickets (PDF and QR codes) for the order
     */
    private function generateETickets(Order $order): void
    {
        try {
            foreach ($order->tickets as $ticket) {
                // Generate QR code if not exists
                if (!$ticket->qr_code_path) {
                    $this->generateTicketQRCode($ticket);
                }
                
                // Generate PDF if not exists
                if (!$ticket->pdf_path) {
                    $this->generateTicketPDF($ticket);
                }
            }
        } catch (\Exception $e) {
            Log::error("Failed to generate E-Tickets for order {$order->order_number}", [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate QR code for ticket
     */
    private function generateTicketQRCode($ticket): void
    {
        try {
            // Create QR code data
            $qrData = json_encode([
                'ticket_id' => $ticket->id,
                'ticket_number' => $ticket->ticket_number,
                'event_id' => $ticket->event_id,
                'attendee_name' => $ticket->attendee_name,
                'generated_at' => now()->toISOString()
            ]);
            
            // Generate QR code using SimpleSoftwareIO/simple-qrcode
            $qrCode = \QrCode::format('png')
                ->size(300)
                ->margin(2)
                ->generate($qrData);
            
            // Save QR code to storage
            $qrPath = 'tickets/qr-codes/' . $ticket->ticket_number . '.png';
            \Storage::disk('public')->put($qrPath, $qrCode);
            
            // Update ticket with QR code path
            $ticket->update(['qr_code_path' => $qrPath]);
            
        } catch (\Exception $e) {
            Log::error("Failed to generate QR code for ticket {$ticket->ticket_number}", [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate PDF for ticket
     */
    private function generateTicketPDF($ticket): void
    {
        try {
            // Load ticket with relationships
            $ticket->load(['event', 'order.user']);
            
            // Generate PDF using DomPDF
            $pdf = \PDF::loadView('tickets.pdf.boarding-pass', compact('ticket'));
            
            // Save PDF to storage
            $pdfPath = 'tickets/pdfs/' . $ticket->ticket_number . '.pdf';
            \Storage::disk('public')->put($pdfPath, $pdf->output());
            
            // Update ticket with PDF path
            $ticket->update(['pdf_path' => $pdfPath]);
            
        } catch (\Exception $e) {
            Log::error("Failed to generate PDF for ticket {$ticket->ticket_number}", [
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Map Midtrans status to our status
     */
    private function mapMidtransStatus(string $status): string
    {
        $statusMap = [
            'capture' => 'paid',
            'settlement' => 'paid',
            'pending' => 'pending',
            'deny' => 'failed',
            'cancel' => 'failed',
            'expire' => 'failed',
            'failure' => 'failed'
        ];

        return $statusMap[$status] ?? 'pending';
    }

    /**
     * Map Tripay status to our status
     */
    private function mapTripayStatus(string $status): string
    {
        $statusMap = [
            'PAID' => 'paid',
            'UNPAID' => 'pending',
            'EXPIRED' => 'failed',
            'FAILED' => 'failed'
        ];

        return $statusMap[$status] ?? 'pending';
    }

    /**
     * Map Xendit status to our status
     */
    private function mapXenditStatus(string $status): string
    {
        $statusMap = [
            'PAID' => 'paid',
            'PENDING' => 'pending',
            'EXPIRED' => 'failed',
            'FAILED' => 'failed'
        ];

        return $statusMap[$status] ?? 'pending';
    }
}

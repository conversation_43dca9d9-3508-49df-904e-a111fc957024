@php
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#F59E0B';
    $secondaryColor = $config['secondary_color'] ?? '#374151';
    $backgroundColor = $config['background_color'] ?? '#1F2937';
    $textColor = $config['text_color'] ?? '#ffffff';
@endphp

<div class="ticket-container elegant-style" style="
    background: linear-gradient(135deg, {{ $backgroundColor }} 0%, {{ $secondaryColor }} 100%);
    color: {{ $textColor }};
    font-family: 'Playfair Display', serif;
    width: 800px;
    height: 320px;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
">
    <!-- Gold Accent Border -->
    <div style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border: 2px solid {{ $primaryColor }};
        border-radius: 12px;
        pointer-events: none;
    "></div>
    
    <!-- Decorative Corner Elements -->
    <div style="
        position: absolute;
        top: 16px;
        left: 16px;
        width: 40px;
        height: 40px;
        border-top: 3px solid {{ $primaryColor }};
        border-left: 3px solid {{ $primaryColor }};
    "></div>
    
    <div style="
        position: absolute;
        top: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
        border-top: 3px solid {{ $primaryColor }};
        border-right: 3px solid {{ $primaryColor }};
    "></div>
    
    <div style="
        position: absolute;
        bottom: 16px;
        left: 16px;
        width: 40px;
        height: 40px;
        border-bottom: 3px solid {{ $primaryColor }};
        border-left: 3px solid {{ $primaryColor }};
    "></div>
    
    <div style="
        position: absolute;
        bottom: 16px;
        right: 16px;
        width: 40px;
        height: 40px;
        border-bottom: 3px solid {{ $primaryColor }};
        border-right: 3px solid {{ $primaryColor }};
    "></div>
    
    <!-- Header -->
    <div style="
        padding: 32px 32px 24px 32px;
        text-align: center;
        border-bottom: 1px solid {{ $primaryColor }};
        background: linear-gradient(90deg, transparent 0%, {{ $primaryColor }}20 50%, transparent 100%);
    ">
        <h1 style="
            font-size: 32px;
            font-weight: 700;
            margin: 0 0 8px 0;
            color: {{ $primaryColor }};
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        ">{{ $ticket['event']['title'] ?? 'Event Title' }}</h1>
        <p style="
            font-size: 16px;
            margin: 0;
            color: {{ $textColor }};
            opacity: 0.9;
            font-style: italic;
            letter-spacing: 2px;
            text-transform: uppercase;
        ">Exclusive Invitation</p>
    </div>
    
    <!-- Content -->
    <div style="
        padding: 24px 32px;
        display: flex;
        gap: 32px;
        height: calc(100% - 140px);
    ">
        <!-- Left Section -->
        <div style="flex: 2;">
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 20px;
            ">
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        font-family: 'Inter', sans-serif;
                    ">Event Date</h3>
                    <p style="
                        font-size: 20px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('M j, Y') }}</p>
                    <p style="
                        font-size: 16px;
                        margin: 0;
                        color: rgba(255,255,255,0.8);
                        font-style: italic;
                    ">{{ \Carbon\Carbon::parse($ticket['event']['start_date'] ?? now())->format('l, g:i A') }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        font-family: 'Inter', sans-serif;
                    ">Venue</h3>
                    <p style="
                        font-size: 18px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['event']['venue_name'] ?? 'TBA' }}</p>
                    <p style="
                        font-size: 14px;
                        margin: 0;
                        color: rgba(255,255,255,0.8);
                        font-style: italic;
                    ">{{ $ticket['event']['city'] ?? 'Location TBA' }}</p>
                </div>
            </div>
            
            <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
            ">
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        font-family: 'Inter', sans-serif;
                    ">Distinguished Guest</h3>
                    <p style="
                        font-size: 18px;
                        font-weight: 600;
                        margin: 0 0 4px 0;
                        color: {{ $textColor }};
                    ">{{ $ticket['attendee_name'] ?? $ticket['buyer']['name'] ?? 'Guest' }}</p>
                    <p style="
                        font-size: 13px;
                        margin: 0;
                        color: rgba(255,255,255,0.7);
                        font-family: 'Inter', sans-serif;
                    ">{{ $ticket['attendee_email'] ?? $ticket['buyer']['email'] ?? 'N/A' }}</p>
                </div>
                
                <div>
                    <h3 style="
                        font-size: 14px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        font-family: 'Inter', sans-serif;
                    ">Investment</h3>
                    <p style="
                        font-size: 24px;
                        font-weight: 700;
                        margin: 0;
                        color: {{ $primaryColor }};
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
                    ">Rp {{ number_format($ticket['price'] ?? 0, 0, ',', '.') }}</p>
                </div>
            </div>
            
            <!-- Ticket Number -->
            <div style="
                margin-top: 20px;
                padding: 12px 16px;
                background: linear-gradient(90deg, {{ $primaryColor }}20 0%, transparent 100%);
                border-left: 4px solid {{ $primaryColor }};
                border-radius: 4px;
            ">
                <h3 style="
                    font-size: 12px;
                    font-weight: 600;
                    color: {{ $primaryColor }};
                    margin: 0 0 4px 0;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    font-family: 'Inter', sans-serif;
                ">Exclusive Access Code</h3>
                <p style="
                    font-size: 18px;
                    font-weight: 600;
                    font-family: 'Courier New', monospace;
                    margin: 0;
                    color: {{ $textColor }};
                    letter-spacing: 2px;
                ">{{ $ticket['ticket_number'] ?? 'TIX-000000' }}</p>
            </div>
        </div>
        
        <!-- Right Section - QR Code -->
        <div style="
            width: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-left: 1px solid {{ $primaryColor }};
            padding-left: 32px;
        ">
            <div style="
                background: white;
                padding: 16px;
                border-radius: 8px;
                border: 2px solid {{ $primaryColor }};
                margin-bottom: 16px;
                box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            ">
                {!! QrCode::size(120)->generate($ticket['qr_code'] ?? $ticket['ticket_number'] ?? 'INVALID') !!}
            </div>
            
            <div style="text-align: center;">
                <p style="
                    font-size: 14px;
                    font-weight: 600;
                    color: {{ $primaryColor }};
                    margin: 0 0 4px 0;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                    font-family: 'Inter', sans-serif;
                ">Verification</p>
                <p style="
                    font-size: 12px;
                    color: rgba(255,255,255,0.8);
                    margin: 0;
                    line-height: 1.4;
                    font-style: italic;
                ">Present this code<br>for exclusive entry</p>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12px 32px;
        background: linear-gradient(90deg, transparent 0%, {{ $primaryColor }}10 50%, transparent 100%);
        border-top: 1px solid {{ $primaryColor }};
        text-align: center;
    ">
        <p style="
            font-size: 11px;
            color: rgba(255,255,255,0.7);
            margin: 0;
            font-style: italic;
            letter-spacing: 0.5px;
            font-family: 'Inter', sans-serif;
        ">This exclusive invitation grants single entry • Crafted with elegance by Tixara</p>
    </div>
    
    <!-- Decorative Elements -->
    <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        border: 1px solid {{ $primaryColor }};
        border-radius: 50%;
        opacity: 0.1;
        pointer-events: none;
    "></div>
    
    <div style="
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 150px;
        height: 150px;
        border: 1px solid {{ $primaryColor }};
        border-radius: 50%;
        opacity: 0.05;
        pointer-events: none;
    "></div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@400;500;600&display=swap');

.ticket-container {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
}

@media print {
    .ticket-container {
        width: 800px !important;
        height: 320px !important;
        margin: 0 !important;
        box-shadow: none !important;
    }
}
</style>

<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    /**
     * Show login form
     */
    public function showLogin()
    {
        if (Auth::check()) {
            return $this->redirectBasedOnRole();
        }

        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ], [
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'password.required' => 'Password wajib diisi.',
            'password.min' => 'Password minimal 6 karakter.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $user = Auth::user();

            // Check if user is active
            if (!$user->is_active) {
                Auth::logout();
                return back()->withErrors(['email' => 'Akun Anda tidak aktif. Hubungi administrator.']);
            }

            // Update last login
            $user->updateLastLogin();

            $request->session()->regenerate();

            return $this->redirectBasedOnRole();
        }

        return back()->withErrors([
            'email' => 'Email atau password tidak valid.',
        ])->withInput();
    }

    /**
     * Show register form
     */
    public function showRegister()
    {
        if (Auth::check()) {
            return $this->redirectBasedOnRole();
        }

        return view('auth.register');
    }

    /**
     * Show organizer register form
     */
    public function showOrganizerRegister()
    {
        if (Auth::check()) {
            return $this->redirectBasedOnRole();
        }

        return view('auth.organizer-register');
    }

    /**
     * Handle register request
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => ['required', 'confirmed', Password::min(6)],
            'role' => 'required|in:pembeli,penjual',
            'terms' => 'required|accepted',
        ], [
            'name.required' => 'Nama wajib diisi.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'phone.required' => 'Nomor telepon wajib diisi.',
            'phone.unique' => 'Nomor telepon sudah terdaftar.',
            'password.required' => 'Password wajib diisi.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'password.min' => 'Password minimal 6 karakter.',
            'role.required' => 'Role wajib dipilih.',
            'role.in' => 'Role tidak valid.',
            'terms.required' => 'Anda harus menyetujui syarat dan ketentuan.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'is_active' => true,
        ]);

        // Auto-assign bronze badge to new user
        User::assignDefaultBadge($user);

        // Send OTP for email verification
        $otp = $user->generateOtp();
        $this->sendOtpEmail($user, $otp);

        Auth::login($user);

        return redirect()->route('auth.verify-email')->with('success', 'Registrasi berhasil! Silakan verifikasi email Anda.');
    }

    /**
     * Handle organizer register request
     */
    public function registerOrganizer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'required|string|max:20|unique:users',
            'password' => ['required', 'confirmed', Password::min(6)],
            'company_name' => 'nullable|string|max:255',
            'company_address' => 'nullable|string|max:500',
            'terms' => 'required|accepted',
        ], [
            'name.required' => 'Nama wajib diisi.',
            'email.required' => 'Email wajib diisi.',
            'email.email' => 'Format email tidak valid.',
            'email.unique' => 'Email sudah terdaftar.',
            'phone.required' => 'Nomor telepon wajib diisi.',
            'phone.unique' => 'Nomor telepon sudah terdaftar.',
            'password.required' => 'Password wajib diisi.',
            'password.confirmed' => 'Konfirmasi password tidak cocok.',
            'password.min' => 'Password minimal 6 karakter.',
            'terms.required' => 'Anda harus menyetujui syarat dan ketentuan.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'role' => 'penjual', // Force role to organizer
            'company_name' => $request->company_name,
            'company_address' => $request->company_address,
            'is_active' => true,
        ]);

        // Auto-assign bronze badge to new user
        User::assignDefaultBadge($user);

        // Send OTP for email verification
        $otp = $user->generateOtp();
        $this->sendOtpEmail($user, $otp);

        Auth::login($user);

        return redirect()->route('auth.verify-email')->with('success', 'Registrasi organizer berhasil! Silakan verifikasi email Anda.');
    }

    /**
     * Show email verification form
     */
    public function showVerifyEmail()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        if ($user->email_verified_at) {
            return $this->redirectBasedOnRole();
        }

        return view('auth.verify-email');
    }

    /**
     * Handle email verification
     */
    public function verifyEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|string|size:6',
        ], [
            'otp.required' => 'Kode OTP wajib diisi.',
            'otp.size' => 'Kode OTP harus 6 digit.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $user = Auth::user();

        if ($user->verifyOtp($request->otp)) {
            return $this->redirectBasedOnRole()->with('success', 'Email berhasil diverifikasi!');
        }

        return back()->withErrors(['otp' => 'Kode OTP tidak valid atau sudah kadaluarsa.']);
    }

    /**
     * Resend OTP
     */
    public function resendOtp()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        if ($user->email_verified_at) {
            return $this->redirectBasedOnRole();
        }

        $otp = $user->generateOtp();
        $this->sendOtpEmail($user, $otp);

        return back()->with('success', 'Kode OTP baru telah dikirim ke email Anda.');
    }

    /**
     * Handle logout
     */
    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('home')->with('success', 'Anda telah logout.');
    }

    /**
     * Redirect user based on their role
     */
    private function redirectBasedOnRole()
    {
        $user = Auth::user();

        // Check email verification
        if (!$user->email_verified_at) {
            return redirect()->route('auth.verify-email');
        }

        switch ($user->role) {
            case User::ROLE_ADMIN:
                return redirect()->route('admin.dashboard');
            case User::ROLE_STAFF:
                return redirect()->route('staff.dashboard');
            case User::ROLE_PENJUAL:
                return redirect()->route('organizer.dashboard');
            case User::ROLE_PEMBELI:
            default:
                return redirect()->route('home');
        }
    }

    /**
     * Send OTP email
     */
    private function sendOtpEmail(User $user, string $otp)
    {
        // For now, we'll just log the OTP
        // In production, you should send actual email
        \Log::info("OTP for {$user->email}: {$otp}");

        // TODO: Implement actual email sending
        // Mail::to($user->email)->send(new OtpMail($otp));
    }
}

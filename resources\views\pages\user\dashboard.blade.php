@extends('layouts.user')

@section('title', 'Dashboard')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Welcome back, {{ auth()->user()->name }}! 👋</h1>
                <p class="text-blue-100 text-lg">Discover amazing events and manage your tickets</p>
                <p class="text-sm text-blue-200 mt-2">{{ now()->format('l, d F Y') }}</p>
            </div>
            <div class="hidden md:block">
                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                    <i data-lucide="calendar-heart" class="w-12 h-12 text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="100">
        <!-- Total Orders -->
        <x-stats-card
            title="Total Orders"
            :value="number_format($stats['total_orders'], 0, ',', '.')"
            icon="shopping-cart"
            icon-color="blue"
            aos="fade-up"
            aos-delay="200"
        />

        <!-- Upcoming Events -->
        <x-stats-card
            title="Upcoming Events"
            :value="number_format($stats['upcoming_events'], 0, ',', '.')"
            icon="calendar"
            icon-color="green"
            aos="fade-up"
            aos-delay="300"
        />

        <!-- UangTix Balance -->
        <x-stats-card
            title="UangTix Balance"
            :value="'Rp ' . number_format($stats['uangtix_balance'], 0, ',', '.')"
            icon="wallet"
            icon-color="purple"
            aos="fade-up"
            aos-delay="400"
        />

        <!-- Total Spent -->
        <x-stats-card
            title="Total Spent"
            :value="'Rp ' . number_format($stats['total_spent'], 0, ',', '.')"
            icon="credit-card"
            icon-color="orange"
            aos="fade-up"
            aos-delay="500"
        />
    </div>

    <!-- Recent Orders & Upcoming Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Orders -->
        <x-modern-card title="Recent Orders" icon="shopping-cart" icon-color="blue" aos="fade-up" aos-delay="600">
            @if($recentOrders->count() > 0)
                <div class="space-y-4">
                    @foreach($recentOrders as $order)
                    <div class="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="ticket" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $order->event->title }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</p>
                            <x-badge :variant="$order->status === 'completed' ? 'success' : ($order->status === 'pending' ? 'warning' : 'error')" size="sm">
                                {{ ucfirst($order->status) }}
                            </x-badge>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('orders.index') }}" class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
                        View all orders →
                    </a>
                </div>
            @else
                <x-empty-state
                    icon="shopping-cart"
                    title="No orders yet"
                    description="Your order history will appear here"
                    size="sm"
                />
            @endif
        </x-modern-card>

        <!-- Upcoming Events -->
        <x-modern-card title="Upcoming Events" icon="calendar" icon-color="green" aos="fade-up" aos-delay="700">
            @if($upcomingEvents->count() > 0)
                <div class="space-y-4">
                    @foreach($upcomingEvents as $event)
                    <div class="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="calendar" class="w-5 h-5 text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $event->title }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $event->event_date->format('M d, Y H:i') }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $event->location }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ route('tiket-saya') }}" class="text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 text-sm font-medium">
                        View my tickets →
                    </a>
                </div>
            @else
                <x-empty-state
                    icon="calendar"
                    title="No upcoming events"
                    description="Events you've purchased tickets for will appear here"
                    size="sm"
                />
            @endif
        </x-modern-card>
    </div>

    <!-- Recommended Events -->
    <x-modern-card title="Recommended for You" icon="heart" icon-color="pink" aos="fade-up" aos-delay="800">
        @if($recommendedEvents->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($recommendedEvents as $event)
                <div class="group cursor-pointer" onclick="window.location.href='{{ route('tickets.show', $event) }}'">
                    <div class="bg-gray-100 dark:bg-gray-700 rounded-lg aspect-video mb-3 overflow-hidden">
                        @if($event->image)
                            <img src="{{ asset('storage/' . $event->image) }}" alt="{{ $event->title }}"
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <i data-lucide="image" class="w-12 h-12 text-gray-400"></i>
                            </div>
                        @endif
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {{ $event->title }}
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $event->event_date->format('M d, Y') }}</p>
                    <p class="text-sm font-medium text-blue-600 dark:text-blue-400 mt-2">
                        Rp {{ number_format($event->price, 0, ',', '.') }}
                    </p>
                </div>
                @endforeach
            </div>
            <div class="mt-6 text-center">
                <a href="{{ route('tickets.index') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                    <i data-lucide="search" class="w-4 h-4 mr-2"></i>
                    Explore More Events
                </a>
            </div>
        @else
            <x-empty-state
                icon="heart"
                title="No recommendations yet"
                description="Purchase some tickets to get personalized recommendations"
                action-text="Browse Events"
                action-url="{{ route('tickets.index') }}"
            />
        @endif
    </x-modern-card>
</div>
@endsection

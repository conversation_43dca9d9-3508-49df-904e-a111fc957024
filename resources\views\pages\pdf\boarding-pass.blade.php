<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-Ticket Boarding Pass - {{ $ticket->event->title }}</title>
    <style>
        @page {
            margin: 0;
            size: A4;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.4;
        }
        
        .boarding-pass {
            width: 100%;
            max-width: 800px;
            margin: 40px auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .boarding-pass::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                #ddd 0px,
                #ddd 10px,
                transparent 10px,
                transparent 20px
            );
            transform: translateY(-50%);
            z-index: 10;
        }
        
        .boarding-pass::after {
            content: '';
            position: absolute;
            top: 50%;
            left: -10px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            transform: translateY(-50%);
            z-index: 11;
        }
        
        .boarding-pass .right-circle {
            position: absolute;
            top: 50%;
            right: -10px;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            transform: translateY(-50%);
            z-index: 11;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .ticket-content {
            display: flex;
            min-height: 400px;
        }
        
        .main-section {
            flex: 2;
            padding: 30px;
        }
        
        .qr-section {
            flex: 1;
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-left: 2px dashed #ddd;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .event-title {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 20px;
            line-height: 1.2;
        }
        
        .ticket-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 25px;
        }
        
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .detail-item {
            margin-bottom: 15px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .qr-code {
            width: 150px;
            height: 150px;
            margin: 20px 0;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
        }
        
        .qr-label {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            text-align: center;
        }
        
        .important-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .important-info h4 {
            color: #856404;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .important-info ul {
            font-size: 12px;
            color: #856404;
            padding-left: 15px;
        }
        
        .important-info li {
            margin-bottom: 3px;
        }
        
        .footer {
            background: #2d3748;
            color: white;
            padding: 20px 30px;
            text-align: center;
            font-size: 12px;
        }
        
        .footer .logo {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-badge {
            background: #d4edda;
            color: #155724;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
        }
        
        .organizer-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .organizer-info h4 {
            color: #2d3748;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 60px;
            color: rgba(102, 126, 234, 0.05);
            font-weight: bold;
            z-index: 1;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="boarding-pass">
        <div class="right-circle"></div>
        <div class="watermark">TIXARA</div>
        
        <!-- Header -->
        <div class="header">
            <h1>🎫 E-TICKET BOARDING PASS</h1>
            <div class="subtitle">Tiket Resmi TiXara</div>
        </div>
        
        <!-- Main Content -->
        <div class="ticket-content">
            <!-- Main Section -->
            <div class="main-section">
                <div class="event-title">{{ $ticket->event->title }}</div>
                
                <div class="ticket-number">{{ $ticket->ticket_number }}</div>
                
                <div class="details-grid">
                    <div>
                        <div class="detail-item">
                            <div class="detail-label">📅 Tanggal & Waktu</div>
                            <div class="detail-value">
                                {{ \Carbon\Carbon::parse($ticket->event->start_date)->format('d M Y') }}<br>
                                <small>{{ \Carbon\Carbon::parse($ticket->event->start_date)->format('H:i') }} WIB</small>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">📍 Lokasi</div>
                            <div class="detail-value">{{ $ticket->event->venue_name }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">👤 Nama Peserta</div>
                            <div class="detail-value">{{ $ticket->attendee_name }}</div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="detail-item">
                            <div class="detail-label">📧 Email</div>
                            <div class="detail-value">{{ $ticket->attendee_email }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">💰 Harga</div>
                            <div class="detail-value">Rp {{ number_format($ticket->price, 0, ',', '.') }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">✅ Status</div>
                            <div class="detail-value">
                                <span class="status-badge">AKTIF</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Organizer Info -->
                <div class="organizer-info">
                    <h4>🏢 Penyelenggara</h4>
                    <div style="font-size: 14px; color: #4a5568;">
                        {{ $ticket->event->organizer->name }}<br>
                        <small>{{ $ticket->event->organizer->email }}</small>
                    </div>
                </div>
                
                <!-- Important Information -->
                <div class="important-info">
                    <h4>⚠️ Informasi Penting:</h4>
                    <ul>
                        <li>Tunjukkan QR Code ini saat masuk event</li>
                        <li>Bawa identitas diri yang sesuai</li>
                        <li>Datang 30 menit sebelum acara</li>
                        <li>Tiket tidak dapat dipindahtangankan</li>
                        <li>Simpan tiket ini dengan baik</li>
                    </ul>
                </div>
            </div>
            
            <!-- QR Section -->
            <div class="qr-section">
                <div style="font-weight: bold; margin-bottom: 10px; color: #2d3748;">SCAN UNTUK VALIDASI</div>
                
                @if($ticket->qr_code_path && \Storage::disk('public')->exists($ticket->qr_code_path))
                    <img src="{{ public_path('storage/' . $ticket->qr_code_path) }}" alt="QR Code" class="qr-code">
                @else
                    <div class="qr-code" style="display: flex; align-items: center; justify-content: center; background: #f1f1f1; color: #666;">
                        QR Code<br>Not Available
                    </div>
                @endif
                
                <div class="qr-label">
                    Tunjukkan QR Code ini<br>
                    kepada petugas saat<br>
                    masuk event
                </div>
                
                <div style="margin-top: 20px; font-size: 12px; color: #666;">
                    <strong>Diterbitkan:</strong><br>
                    {{ $ticket->created_at->format('d M Y H:i') }}
                </div>
                
                <div style="margin-top: 15px; font-size: 12px; color: #666;">
                    <strong>Order:</strong><br>
                    {{ $ticket->order->order_number }}
                </div>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="logo">TiXara</div>
            <div>Platform Tiket Event Terpercaya</div>
            <div style="margin-top: 10px; opacity: 0.8;">
                www.tixara.my.id | <EMAIL><br>
                © {{ date('Y') }} TiXara. All rights reserved.
            </div>
        </div>
    </div>
</body>
</html>

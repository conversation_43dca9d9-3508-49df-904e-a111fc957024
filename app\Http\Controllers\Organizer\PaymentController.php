<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        $query = Payment::with(['order.user', 'order.event'])
            ->whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            });

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by event
        if ($request->filled('event_id')) {
            $query->whereHas('order', function($q) use ($request) {
                $q->where('event_id', $request->event_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('payment_id', 'like', "%{$search}%")
                  ->orWhere('external_id', 'like', "%{$search}%")
                  ->orWhereHas('order.user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        $payments = $query->latest()->paginate(15);

        // Get organizer's events for filter dropdown
        $events = Event::where('organizer_id', Auth::id())
            ->select('id', 'title')
            ->get();

        // Statistics
        $stats = [
            'total_payments' => Payment::whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->count(),
            'completed_payments' => Payment::whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'completed')->count(),
            'pending_payments' => Payment::whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'pending')->count(),
            'total_amount' => Payment::whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            })->where('status', 'completed')->sum('amount'),
        ];

        return view('pages.organizer.payments.index', compact('payments', 'events', 'stats'));
    }

    public function show(Payment $payment)
    {
        // Ensure the payment belongs to organizer's event
        if ($payment->order->event->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        $payment->load(['order.user', 'order.event']);

        return view('pages.organizer.payments.show', compact('payment'));
    }

    public function export(Request $request)
    {
        $query = Payment::with(['order.user', 'order.event'])
            ->whereHas('order.event', function($q) {
                $q->where('organizer_id', Auth::id());
            });

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('event_id')) {
            $query->whereHas('order', function($q) use ($request) {
                $q->where('event_id', $request->event_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $payments = $query->get();

        $csvData = [];
        $csvData[] = [
            'Payment ID',
            'External ID',
            'Customer Name',
            'Customer Email',
            'Event Title',
            'Amount',
            'Payment Method',
            'Status',
            'Payment Date',
            'Completed Date'
        ];

        foreach ($payments as $payment) {
            $csvData[] = [
                $payment->payment_id,
                $payment->external_id,
                $payment->order->user->name,
                $payment->order->user->email,
                $payment->order->event->title,
                'Rp ' . number_format($payment->amount, 0, ',', '.'),
                ucfirst($payment->payment_method),
                ucfirst($payment->status),
                $payment->created_at->format('Y-m-d H:i:s'),
                $payment->paid_at ? $payment->paid_at->format('Y-m-d H:i:s') : 'N/A'
            ];
        }

        $filename = 'payments_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

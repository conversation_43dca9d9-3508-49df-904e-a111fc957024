<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserBadgeLevel;

class AssignDefaultBadges extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'badges:assign-default {--force : Force assign even if user already has badge}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign default Bronze badge to all users without badges';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting default badge assignment...');

        // Get or create Bronze badge
        $bronzeBadge = UserBadgeLevel::where('name', 'Bronze')->first();
        
        if (!$bronzeBadge) {
            $bronzeBadge = UserBadgeLevel::create([
                'name' => 'Bronze',
                'description' => 'Default badge for all new users',
                'color' => '#CD7F32',
                'icon' => 'fas fa-medal',
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'benefits' => [
                    'basic_support' => true,
                    'newsletter' => true,
                    'event_notifications' => true,
                ],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]);
            
            $this->info('Created Bronze badge.');
        }

        // Get users without badges
        $query = User::query();
        
        if ($this->option('force')) {
            $query = User::query(); // All users
            $this->info('Force mode: Assigning to ALL users...');
        } else {
            $query = User::whereNull('badge_level_id'); // Only users without badges
            $this->info('Normal mode: Assigning to users without badges...');
        }

        $users = $query->get();
        $assignedCount = 0;

        foreach ($users as $user) {
            if ($this->option('force') || !$user->badge_level_id) {
                $user->assignBadge($bronzeBadge);
                $assignedCount++;
                
                $this->line("✓ Assigned Bronze badge to: {$user->name} ({$user->email})");
            }
        }

        $this->info("Successfully assigned Bronze badge to {$assignedCount} users.");
        
        return Command::SUCCESS;
    }
}

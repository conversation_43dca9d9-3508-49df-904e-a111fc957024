<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Notification;
use Carbon\Carbon;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('No users found. Please run UserSeeder first.');
            return;
        }

        $this->command->info('Creating sample notifications...');

        foreach ($users as $user) {
            // Create welcome notification
            Notification::create([
                'user_id' => $user->id,
                'title' => 'Selamat Datang di TiXara!',
                'message' => 'Terima kasih telah bergabung dengan tixara. Jelajahi berbagai event menarik dan dapatkan tiket favorit Anda!',
                'type' => 'system',
                'priority' => 'normal',
                'data' => [
                    'welcome' => true,
                    'user_role' => $user->role
                ],
                'action_url' => route('tickets.index'),
                'created_at' => Carbon::now()->subDays(rand(1, 7)),
            ]);

            // Create event notifications for buyers
            if ($user->role === 'pembeli') {
                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Event Baru: Jakarta Music Festival',
                    'message' => 'Event musik terbesar tahun ini telah tersedia! Jangan sampai terlewat, dapatkan tiket Anda sekarang.',
                    'type' => 'event',
                    'priority' => 'high',
                    'data' => [
                        'event_id' => 1,
                        'event_name' => 'Jakarta Music Festival',
                        'discount' => '20%'
                    ],
                    'action_url' => route('tickets.index'),
                    'created_at' => Carbon::now()->subHours(rand(1, 24)),
                ]);

                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Pembayaran Berhasil',
                    'message' => 'Pembayaran Anda untuk tiket Digital Marketing Workshop telah berhasil diproses. Tiket akan dikirim ke email Anda.',
                    'type' => 'payment',
                    'priority' => 'normal',
                    'data' => [
                        'order_id' => 'ORD-' . rand(1000, 9999),
                        'amount' => 150000,
                        'payment_method' => 'Bank Transfer'
                    ],
                    'action_url' => route('tiket-saya'),
                    'read_at' => rand(0, 1) ? Carbon::now()->subMinutes(rand(30, 120)) : null,
                    'created_at' => Carbon::now()->subDays(rand(1, 3)),
                ]);
            }

            // Create organizer notifications
            if ($user->role === 'penjual' || $user->role === 'admin') {
                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Event Anda Telah Disetujui',
                    'message' => 'Event "Bali Food Festival" telah disetujui dan sekarang dapat dilihat oleh pembeli. Selamat!',
                    'type' => 'event',
                    'priority' => 'high',
                    'data' => [
                        'event_id' => 2,
                        'event_name' => 'Bali Food Festival',
                        'status' => 'approved'
                    ],
                    'action_url' => route('organizer.dashboard'),
                    'created_at' => Carbon::now()->subHours(rand(2, 12)),
                ]);

                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Tiket Terjual',
                    'message' => '5 tiket untuk event "Tech Conference 2024" telah terjual. Total penjualan hari ini: Rp 750.000',
                    'type' => 'order',
                    'priority' => 'normal',
                    'data' => [
                        'tickets_sold' => 5,
                        'total_sales' => 750000,
                        'event_name' => 'Tech Conference 2024'
                    ],
                    'action_url' => route('organizer.dashboard'),
                    'read_at' => rand(0, 1) ? Carbon::now()->subMinutes(rand(15, 60)) : null,
                    'created_at' => Carbon::now()->subHours(rand(1, 6)),
                ]);
            }

            // Create admin notifications
            if ($user->role === 'admin') {
                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Event Baru Menunggu Persetujuan',
                    'message' => 'Ada 3 event baru yang menunggu persetujuan Anda. Silakan review dan setujui event tersebut.',
                    'type' => 'system',
                    'priority' => 'urgent',
                    'data' => [
                        'pending_tickets' => 3,
                        'action_required' => true
                    ],
                    'action_url' => '/admin/events',
                    'created_at' => Carbon::now()->subMinutes(rand(30, 180)),
                ]);

                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Laporan Harian',
                    'message' => 'Laporan aktivitas hari ini: 25 tiket terjual, 5 event baru, 12 user baru terdaftar.',
                    'type' => 'system',
                    'priority' => 'low',
                    'data' => [
                        'tickets_sold' => 25,
                        'new_tickets' => 5,
                        'new_users' => 12,
                        'report_date' => Carbon::today()->toDateString()
                    ],
                    'action_url' => '/admin',
                    'read_at' => Carbon::now()->subMinutes(rand(10, 30)),
                    'created_at' => Carbon::now()->subHours(rand(1, 4)),
                ]);
            }

            // Create some random unread notifications
            if (rand(0, 1)) {
                Notification::create([
                    'user_id' => $user->id,
                    'title' => 'Update Sistem',
                    'message' => 'Sistem TiXara telah diperbarui dengan fitur-fitur baru. Cek apa saja yang baru!',
                    'type' => 'system',
                    'priority' => 'low',
                    'data' => [
                        'version' => '2.1.0',
                        'features' => ['Dark Mode', 'Better Search', 'Mobile Optimization']
                    ],
                    'action_url' => '/',
                    'created_at' => Carbon::now()->subMinutes(rand(5, 30)),
                ]);
            }
        }

        $totalNotifications = Notification::count();
        $this->command->info("Created {$totalNotifications} sample notifications for {$users->count()} users.");
    }
}

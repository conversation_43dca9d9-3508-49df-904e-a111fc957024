<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corporate Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Arial:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: white;
            border: 2px solid #475569;
            box-shadow: 0 10px 15px -3px rgba(71, 85, 105, 0.1), 0 4px 6px -2px rgba(71, 85, 105, 0.05);
            position: relative;
        }

        .corporate-header {
            background: linear-gradient(135deg, #475569 0%, #334155 100%);
            color: white;
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid #64748b;
        }

        .header-left {
            flex: 1;
        }

        .header-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 4px;
            letter-spacing: 2px;
        }

        .header-subtitle {
            font-size: 14px;
            font-weight: 400;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .corporate-logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            color: #475569;
            border: 2px solid #64748b;
        }

        .boarding-body {
            padding: 40px;
            background: #fefefe;
        }

        .event-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 24px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-left: 4px solid #475569;
        }

        .event-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-category {
            display: inline-block;
            background: #475569;
            color: white;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            align-items: start;
        }

        .details-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 32px;
            border-left: 4px solid #475569;
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: #475569;
            margin-bottom: 24px;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 8px;
        }

        .details-grid {
            display: grid;
            gap: 20px;
        }

        .detail-pair {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            background: white;
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-left: 3px solid #64748b;
        }

        .detail-label {
            font-size: 11px;
            font-weight: 600;
            color: #475569;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 6px;
        }

        .detail-value {
            font-size: 15px;
            font-weight: 600;
            color: #1e293b;
            line-height: 1.3;
        }

        .detail-value.primary {
            font-size: 17px;
            color: #475569;
            font-weight: 700;
        }

        .qr-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 32px;
            text-align: center;
            border-left: 4px solid #475569;
        }

        .qr-header {
            margin-bottom: 24px;
        }

        .qr-title {
            font-size: 16px;
            font-weight: 700;
            color: #475569;
            margin-bottom: 4px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .qr-subtitle {
            font-size: 12px;
            color: #64748b;
            font-weight: 500;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: linear-gradient(135deg, #1e293b, #475569);
            border: 2px solid #64748b;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 20px;
            box-shadow: 0 4px 6px -1px rgba(71, 85, 105, 0.1);
        }

        .boarding-id {
            background: white;
            border: 2px solid #475569;
            padding: 12px 16px;
            font-size: 14px;
            font-weight: 700;
            color: #475569;
            font-family: 'Courier New', monospace;
            margin-top: 16px;
            letter-spacing: 1px;
        }

        .corporate-footer {
            background: #f8fafc;
            padding: 24px 40px;
            border-top: 2px solid #e2e8f0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            align-items: center;
        }

        .footer-item {
            text-align: center;
            padding: 12px;
            background: white;
            border: 1px solid #e2e8f0;
        }

        .footer-label {
            font-size: 10px;
            color: #475569;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .footer-value {
            font-size: 12px;
            font-weight: 600;
            color: #1e293b;
        }

        .tixara-corporate {
            font-size: 18px;
            font-weight: 700;
            color: #475569;
            letter-spacing: 1px;
            text-align: center;
            grid-column: 1 / -1;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e2e8f0;
        }

        .security-strip {
            background: #475569;
            color: white;
            padding: 8px 40px;
            text-align: center;
            font-size: 10px;
            font-weight: 600;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            font-weight: 100;
            color: rgba(71, 85, 105, 0.03);
            pointer-events: none;
            z-index: 1;
        }

        .content-wrapper {
            position: relative;
            z-index: 2;
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-pair {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .corporate-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
            
            .boarding-body {
                padding: 24px;
            }
            
            .corporate-footer {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 2px solid #000;
            }
            
            .watermark {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="watermark">CORPORATE</div>
        
        <div class="content-wrapper">
            <div class="corporate-header">
                <div class="header-left">
                    <div class="header-title">CORPORATE</div>
                    <div class="header-subtitle">Business Professional Boarding Pass</div>
                </div>
                <div class="corporate-logo">
                    CORP
                </div>
            </div>

            <div class="boarding-body">
                <div class="event-header">
                    <div class="event-title">{{ $ticket->event->title }}</div>
                    <div class="event-category">{{ $ticket->event->category->name ?? 'Corporate Event' }}</div>
                </div>

                <div class="boarding-content">
                    <div class="details-section">
                        <div class="section-title">Event Details</div>
                        <div class="details-grid">
                            <div class="detail-pair">
                                <div class="detail-item">
                                    <div class="detail-label">Event Date</div>
                                    <div class="detail-value">{{ $ticket->event->start_date->format('l, d F Y') }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Event Time</div>
                                    <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-label">Venue Location</div>
                                <div class="detail-value">{{ $ticket->event->location }}</div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-label">Attendee Name</div>
                                <div class="detail-value primary">{{ $ticket->attendee_name }}</div>
                            </div>

                            <div class="detail-pair">
                                <div class="detail-item">
                                    <div class="detail-label">Ticket Number</div>
                                    <div class="detail-value">{{ $ticket->ticket_number }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Seat Assignment</div>
                                    <div class="detail-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                                </div>
                            </div>

                            <div class="detail-pair">
                                <div class="detail-item">
                                    <div class="detail-label">Order Reference</div>
                                    <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Registration Fee</div>
                                    <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="qr-section">
                        <div class="qr-header">
                            <div class="qr-title">Validation QR</div>
                            <div class="qr-subtitle">Corporate Security Code</div>
                        </div>
                        
                        <div class="qr-code">
                            CORPORATE<br>
                            QR CODE<br>
                            VALIDATION
                        </div>
                        
                        <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-CRP-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                    </div>
                </div>
            </div>

            <div class="security-strip">
                SECURE CORPORATE BOARDING PASS • AUTHORIZED PERSONNEL ONLY • TIXARA BUSINESS SOLUTIONS
            </div>

            <div class="corporate-footer">
                <div class="footer-item">
                    <div class="footer-label">Issue Date</div>
                    <div class="footer-value">{{ $ticket->order->created_at->format('d M Y') ?? now()->format('d M Y') }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Issue Time</div>
                    <div class="footer-value">{{ $ticket->order->created_at->format('H:i') ?? now()->format('H:i') }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Status</div>
                    <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Category</div>
                    <div class="footer-value">{{ $ticket->event->category->name ?? 'Corporate' }}</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">Version</div>
                    <div class="footer-value">v1.0</div>
                </div>
                <div class="tixara-corporate">TiXara Corporate Solutions</div>
            </div>
        </div>
    </div>
</body>
</html>

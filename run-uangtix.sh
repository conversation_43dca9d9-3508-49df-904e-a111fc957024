#!/bin/bash

echo "========================================"
echo "   UangTix - Digital Wallet Setup"
echo "   GoPay/OVO Style Implementation"
echo "========================================"
echo

echo "[1/5] Checking Laravel installation..."
if [ ! -f "artisan" ]; then
    echo "ERROR: Laravel not found! Please run this script from Laravel root directory."
    exit 1
fi

echo "[2/5] Installing/Updating dependencies..."
composer install --no-dev --optimize-autoloader
if [ $? -ne 0 ]; then
    echo "ERROR: Composer install failed!"
    exit 1
fi

echo "[3/5] Building frontend assets..."
npm install
npm run build
if [ $? -ne 0 ]; then
    echo "ERROR: NPM build failed!"
    exit 1
fi

echo "[4/5] Running database migrations..."
php artisan migrate --force
if [ $? -ne 0 ]; then
    echo "ERROR: Migration failed!"
    exit 1
fi

echo "[5/5] Seeding UangTix data..."
php artisan db:seed --class=UangTixSeeder --force
if [ $? -ne 0 ]; then
    echo "WARNING: Seeder failed, but continuing..."
fi

echo
echo "========================================"
echo "   Setup Complete! Starting Server..."
echo "========================================"
echo
echo "UangTix Features:"
echo "- GoPay/OVO Style Interface"
echo "- Responsive Design (Desktop/Mobile)"
echo "- PWA Support"
echo "- Floating Footer Navigation"
echo "- Real-time Balance Updates"
echo "- Top Up, Withdrawal, Transfer"
echo "- Transaction History"
echo
echo "Access UangTix at: http://localhost:8000/uangtix"
echo
echo "Press Ctrl+C to stop the server"
echo "========================================"

php artisan serve --host=0.0.0.0 --port=8000

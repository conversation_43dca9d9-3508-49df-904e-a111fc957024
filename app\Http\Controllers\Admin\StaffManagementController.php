<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class StaffManagementController extends Controller
{
    /**
     * Display staff management page
     */
    public function index()
    {
        // Get all staff members with their assigned organizers
        $staffMembers = User::where('role', User::ROLE_STAFF)
            ->orderBy('name')
            ->get()
            ->map(function ($staff) {
                $assignedOrganizers = $staff->assignedOrganizerUsers();

                return [
                    'id' => $staff->id,
                    'name' => $staff->name,
                    'email' => $staff->email,
                    'phone' => $staff->phone,
                    'assigned_organizers_count' => $staff->getAssignedOrganizersCount(),
                    'assigned_organizers' => $assignedOrganizers->map(function ($organizer) {
                        return [
                            'id' => $organizer->id,
                            'name' => $organizer->name,
                            'email' => $organizer->email,
                            'events_count' => $organizer->events()->count()
                        ];
                    }),
                    'is_active' => $staff->is_active,
                    'last_login_at' => $staff->last_login_at,
                    'created_at' => $staff->created_at
                ];
            });

        // Get all organizers
        $organizers = User::where('role', User::ROLE_PENJUAL)
            ->orderBy('name')
            ->get()
            ->map(function ($organizer) {
                return [
                    'id' => $organizer->id,
                    'name' => $organizer->name,
                    'email' => $organizer->email,
                    'events_count' => $organizer->events()->count(),
                    'active_events_count' => $organizer->events()
                        ->where('status', 'published')
                        ->whereDate('start_date', '>=', Carbon::today())
                        ->count(),
                    'assigned_staff_count' => User::where('role', User::ROLE_STAFF)
                        ->whereJsonContains('assigned_organizers', $organizer->id)
                        ->count()
                ];
            });

        return view('admin.staff-management.index', compact('staffMembers', 'organizers'));
    }

    /**
     * Show staff assignment form
     */
    public function showAssignmentForm($staffId)
    {
        $staff = User::where('role', User::ROLE_STAFF)->findOrFail($staffId);

        $organizers = User::where('role', User::ROLE_PENJUAL)
            ->orderBy('name')
            ->get()
            ->map(function ($organizer) use ($staff) {
                return [
                    'id' => $organizer->id,
                    'name' => $organizer->name,
                    'email' => $organizer->email,
                    'events_count' => $organizer->events()->count(),
                    'active_events_count' => $organizer->events()
                        ->where('status', 'published')
                        ->whereDate('start_date', '>=', Carbon::today())
                        ->count(),
                    'is_assigned' => $staff->isAssignedToOrganizer($organizer->id)
                ];
            });

        return view('admin.staff-management.assignment-form', compact('staff', 'organizers'));
    }

    /**
     * Assign organizers to staff
     */
    public function assignOrganizers(Request $request, $staffId)
    {
        $request->validate([
            'organizer_ids' => 'required|array',
            'organizer_ids.*' => 'exists:users,id'
        ]);

        $staff = User::where('role', User::ROLE_STAFF)->findOrFail($staffId);

        // Validate that all selected IDs are actually organizers
        $validOrganizerIds = User::whereIn('id', $request->organizer_ids)
            ->where('role', User::ROLE_PENJUAL)
            ->pluck('id')
            ->toArray();

        if (count($validOrganizerIds) !== count($request->organizer_ids)) {
            return response()->json([
                'success' => false,
                'message' => 'Beberapa ID yang dipilih bukan organizer yang valid'
            ], 400);
        }

        try {
            $staff->assignOrganizers($validOrganizerIds);

            return response()->json([
                'success' => true,
                'message' => 'Organizer berhasil ditugaskan ke staff',
                'data' => [
                    'staff_name' => $staff->name,
                    'assigned_organizers_count' => count($validOrganizerIds),
                    'assigned_organizers' => User::whereIn('id', $validOrganizerIds)->pluck('name')->toArray()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menugaskan organizer: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove organizer assignment from staff
     */
    public function removeOrganizerAssignment(Request $request, $staffId, $organizerId)
    {
        $staff = User::where('role', User::ROLE_STAFF)->findOrFail($staffId);
        $organizer = User::where('role', User::ROLE_PENJUAL)->findOrFail($organizerId);

        try {
            $staff->removeOrganizer($organizerId);

            return response()->json([
                'success' => true,
                'message' => "Organizer {$organizer->name} berhasil dihapus dari staff {$staff->name}",
                'data' => [
                    'staff_name' => $staff->name,
                    'organizer_name' => $organizer->name,
                    'remaining_assignments' => $staff->getAssignedOrganizersCount()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all organizer assignments for staff
     */
    public function clearAllAssignments($staffId)
    {
        $staff = User::where('role', User::ROLE_STAFF)->findOrFail($staffId);

        try {
            $previousCount = $staff->getAssignedOrganizersCount();
            $staff->clearOrganizerAssignments();

            return response()->json([
                'success' => true,
                'message' => "Semua assignment organizer untuk {$staff->name} berhasil dihapus",
                'data' => [
                    'staff_name' => $staff->name,
                    'previous_assignments_count' => $previousCount,
                    'current_assignments_count' => 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal menghapus semua assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get staff assignment statistics
     */
    public function getAssignmentStats()
    {
        $stats = [
            'total_staff' => User::where('role', User::ROLE_STAFF)->count(),
            'assigned_staff' => User::where('role', User::ROLE_STAFF)
                ->whereNotNull('assigned_organizers')
                ->whereJsonLength('assigned_organizers', '>', 0)
                ->count(),
            'unassigned_staff' => User::where('role', User::ROLE_STAFF)
                ->where(function($query) {
                    $query->whereNull('assigned_organizers')
                          ->orWhereJsonLength('assigned_organizers', 0);
                })
                ->count(),
            'total_organizers' => User::where('role', User::ROLE_PENJUAL)->count(),
            'organizers_with_staff' => $this->getOrganizersWithStaffCount(),
            'organizers_without_staff' => $this->getOrganizersWithoutStaffCount()
        ];

        return response()->json($stats);
    }

    /**
     * Get detailed assignment report
     */
    public function getAssignmentReport()
    {
        $report = [
            'staff_assignments' => User::where('role', User::ROLE_STAFF)
                ->get()
                ->map(function ($staff) {
                    return [
                        'staff_id' => $staff->id,
                        'staff_name' => $staff->name,
                        'staff_email' => $staff->email,
                        'assigned_organizers_count' => $staff->getAssignedOrganizersCount(),
                        'assigned_organizers' => $staff->assignedOrganizerUsers()->map(function ($organizer) {
                            return [
                                'organizer_id' => $organizer->id,
                                'organizer_name' => $organizer->name,
                                'organizer_email' => $organizer->email,
                                'events_count' => $organizer->events()->count()
                            ];
                        }),
                        'validatable_events_count' => $staff->getValidatableEvents()->count()
                    ];
                }),
            'organizer_coverage' => User::where('role', User::ROLE_PENJUAL)
                ->get()
                ->map(function ($organizer) {
                    $assignedStaff = User::where('role', User::ROLE_STAFF)
                        ->whereJsonContains('assigned_organizers', $organizer->id)
                        ->get();

                    return [
                        'organizer_id' => $organizer->id,
                        'organizer_name' => $organizer->name,
                        'organizer_email' => $organizer->email,
                        'events_count' => $organizer->events()->count(),
                        'active_events_count' => $organizer->events()
                            ->where('status', 'published')
                            ->whereDate('start_date', '>=', Carbon::today())
                            ->count(),
                        'assigned_staff_count' => $assignedStaff->count(),
                        'assigned_staff' => $assignedStaff->map(function ($staff) {
                            return [
                                'staff_id' => $staff->id,
                                'staff_name' => $staff->name,
                                'staff_email' => $staff->email
                            ];
                        })
                    ];
                })
        ];

        return response()->json($report);
    }

    /**
     * Bulk assign organizers to multiple staff
     */
    public function bulkAssignOrganizers(Request $request)
    {
        $request->validate([
            'staff_ids' => 'required|array',
            'staff_ids.*' => 'exists:users,id',
            'organizer_ids' => 'required|array',
            'organizer_ids.*' => 'exists:users,id',
            'assignment_mode' => 'required|in:add,replace'
        ]);

        $staffMembers = User::whereIn('id', $request->staff_ids)
            ->where('role', User::ROLE_STAFF)
            ->get();

        $organizers = User::whereIn('id', $request->organizer_ids)
            ->where('role', User::ROLE_PENJUAL)
            ->get();

        if ($staffMembers->count() !== count($request->staff_ids)) {
            return response()->json([
                'success' => false,
                'message' => 'Beberapa staff ID tidak valid'
            ], 400);
        }

        if ($organizers->count() !== count($request->organizer_ids)) {
            return response()->json([
                'success' => false,
                'message' => 'Beberapa organizer ID tidak valid'
            ], 400);
        }

        try {
            DB::transaction(function () use ($staffMembers, $request) {
                foreach ($staffMembers as $staff) {
                    if ($request->assignment_mode === 'replace') {
                        $staff->assignOrganizers($request->organizer_ids);
                    } else { // add mode
                        $currentOrganizers = $staff->getAssignedOrganizers();
                        $newOrganizers = array_unique(array_merge($currentOrganizers, $request->organizer_ids));
                        $staff->assignOrganizers($newOrganizers);
                    }
                }
            });

            return response()->json([
                'success' => true,
                'message' => 'Bulk assignment berhasil dilakukan',
                'data' => [
                    'affected_staff_count' => $staffMembers->count(),
                    'assigned_organizers_count' => count($request->organizer_ids),
                    'assignment_mode' => $request->assignment_mode
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal melakukan bulk assignment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get count of organizers with assigned staff
     */
    private function getOrganizersWithStaffCount()
    {
        try {
            // Check if assigned_organizers column exists
            if (!Schema::hasColumn('users', 'assigned_organizers')) {
                return 0;
            }

            return User::where('role', User::ROLE_PENJUAL)
                ->whereHas('events')
                ->whereIn('id', function($query) {
                    $query->select(DB::raw('JSON_UNQUOTE(JSON_EXTRACT(assigned_organizers, "$[*]"))'))
                          ->from('users')
                          ->where('role', User::ROLE_STAFF)
                          ->whereNotNull('assigned_organizers');
                })
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get count of organizers without assigned staff
     */
    private function getOrganizersWithoutStaffCount()
    {
        try {
            // Check if assigned_organizers column exists
            if (!Schema::hasColumn('users', 'assigned_organizers')) {
                return User::where('role', User::ROLE_PENJUAL)->count();
            }

            return User::where('role', User::ROLE_PENJUAL)
                ->whereNotIn('id', function($query) {
                    $query->select(DB::raw('JSON_UNQUOTE(JSON_EXTRACT(assigned_organizers, "$[*]"))'))
                          ->from('users')
                          ->where('role', User::ROLE_STAFF)
                          ->whereNotNull('assigned_organizers');
                })
                ->count();
        } catch (\Exception $e) {
            return User::where('role', User::ROLE_PENJUAL)->count();
        }
    }
}

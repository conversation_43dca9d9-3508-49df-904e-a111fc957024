<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use Illuminate\Support\Str;

class TestETicketSeeder extends Seeder
{
    public function run()
    {
        $this->command->info('=== CREATING TEST DATA FOR E-TICKET SYSTEM ===');

        // 1. Create test users
        $this->command->info('1. Creating test users...');

        $organizer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Organizer',
                'password' => bcrypt('password'),
                'role' => 'penjual',
                'badge_level_id' => 4,
                'email_verified_at' => now()
            ]
        );

        $customer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Customer',
                'password' => bcrypt('password'),
                'role' => 'pembeli',
                'badge_level_id' => 1,
                'email_verified_at' => now()
            ]
        );

        $staff = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Staff',
                'password' => bcrypt('password'),
                'role' => 'staff',
                'badge_level_id' => 3,
                'email_verified_at' => now()
            ]
        );

        $this->command->info('✓ Users created successfully');

        // 2. Create test category
        $this->command->info('2. Creating test category...');

        $category = Category::firstOrCreate(
            ['name' => 'Test Category'],
            [
                'description' => 'Category for testing purposes',
                'slug' => 'test-category',
                'is_active' => true
            ]
        );

        $this->command->info("✓ Category created: {$category->name} (ID: {$category->id})");

        // 3. Create test events with different templates
        $this->command->info('3. Creating test events with different templates...');

        $templates = ['unix', 'minimalist', 'pro', 'custom'];
        $events = [];
        $tickets = [];

        foreach ($templates as $index => $template) {
            // Create event
            $event = Event::create([
                'title' => "Test Event - {$template} Template " . now()->format('YmdHis'),
                'description' => "Testing event untuk {$template} boarding pass template",
                'category_id' => $category->id,
                'venue_name' => 'Jakarta Convention Center',
                'venue_address' => 'Jl. Gatot Subroto, Jakarta',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'start_date' => now()->addDays(7),
                'end_date' => now()->addDays(7)->addHours(3),
                'price' => 150000 + ($index * 50000),
                'total_capacity' => 100,
                'available_capacity' => 100,
                'organizer_id' => $organizer->id,
                'boarding_pass_template' => $template,
                'auto_generate_tickets' => true,
                'email_tickets_to_buyers' => true,
                'status' => 'published'
            ]);

            $events[$template] = $event;
            $this->command->info("✓ Event created: {$event->title} (ID: {$event->id})");

            // Create order
            $order = Order::create([
                'order_number' => 'ORD-' . now()->format('YmdHis') . '-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'event_id' => $event->id,
                'quantity' => 1,
                'unit_price' => $event->price,
                'subtotal' => $event->price,
                'admin_fee' => 0,
                'total_amount' => $event->price,
                'customer_name' => $customer->name,
                'customer_email' => $customer->email,
                'customer_phone' => '081234567890',
                'status' => 'completed',
                'payment_status' => 'paid'
            ]);

            // Create ticket
            $ticket = Ticket::create([
                'ticket_number' => 'TIK-' . now()->format('YmdHis') . '-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'boarding_pass_id' => 'BP' . now()->format('ymd') . strtoupper(substr($template, 0, 3)) . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
                'template_used' => $template,
                'generated_at' => now(),
                'qr_code' => 'QR-' . strtoupper(substr($template, 0, 3)) . '-' . Str::random(8),
                'event_id' => $event->id,
                'buyer_id' => $customer->id,
                'user_id' => $customer->id,
                'order_id' => $order->id,
                'attendee_name' => $customer->name,
                'attendee_email' => $customer->email,
                'price' => $event->price,
                'total_paid' => $event->price,
                'status' => 'active'
            ]);

            $tickets[$template] = $ticket;
            $this->command->info("✓ Ticket created: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})");
        }

        $this->command->info('');
        $this->command->info('=== TEST DATA CREATED SUCCESSFULLY ===');
        $this->command->info('');
        $this->command->info('Test URLs:');
        $this->command->info('- Home: http://localhost:8000');
        $this->command->info('- Authenticity Checker: http://localhost:8000/authenticity-check');
        $this->command->info('- Staff Validation: http://localhost:8000/validation');
        $this->command->info('');
        $this->command->info('Login Credentials:');
        $this->command->info('- Organizer: <EMAIL> / password');
        $this->command->info('- Customer: <EMAIL> / password');
        $this->command->info('- Staff: <EMAIL> / password');
        $this->command->info('');
        $this->command->info('Template Preview URLs:');
        foreach ($templates as $template) {
            $this->command->info("- {$template}: http://localhost:8000/organizer/events/templates/boarding-pass/{$template}");
        }
        $this->command->info('');
        $this->command->info('Test Ticket Numbers for Authenticity Check:');
        foreach ($tickets as $template => $ticket) {
            $this->command->info("- {$template}: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})");
        }
        $this->command->info('');
        $this->command->info('You can now test the complete E-Ticket system!');
    }
}

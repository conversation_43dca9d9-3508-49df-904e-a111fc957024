@extends('layouts.organizer')

@section('title', 'Analytics Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-line text-primary me-2"></i>Analytics Dashboard
            </h1>
            <p class="mb-0 text-muted">Comprehensive insights into your event performance</p>
        </div>
        <div class="d-flex gap-2">
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-calendar me-2"></i>{{ $dateRange ?? 'Last 30 Days' }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="?range=7">Last 7 Days</a>
                    <a class="dropdown-item" href="?range=30">Last 30 Days</a>
                    <a class="dropdown-item" href="?range=90">Last 90 Days</a>
                    <a class="dropdown-item" href="?range=365">Last Year</a>
                </div>
            </div>
            <div class="dropdown">
                <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>Export
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="{{ route('organizer.analytics.export', ['type' => 'revenue', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Revenue Report (CSV)
                    </a>
                    <a class="dropdown-item" href="{{ route('organizer.analytics.export', ['type' => 'events', 'format' => 'csv']) }}">
                        <i class="fas fa-file-csv me-2"></i>Events Report (CSV)
                    </a>
                    <a class="dropdown-item" href="{{ route('organizer.analytics.export', ['type' => 'complete', 'format' => 'pdf']) }}">
                        <i class="fas fa-file-pdf me-2"></i>Complete Report (PDF)
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <!-- Total Revenue -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Revenue
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rp {{ number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.') }}
                            </div>
                            @if(isset($revenueData['growth_percentage']) && $revenueData['growth_percentage'] != 0)
                                <div class="text-xs {{ $revenueData['growth_percentage'] > 0 ? 'text-success' : 'text-danger' }}">
                                    <i class="fas fa-arrow-{{ $revenueData['growth_percentage'] > 0 ? 'up' : 'down' }} me-1"></i>
                                    {{ abs($revenueData['growth_percentage']) }}% from previous period
                                </div>
                            @endif
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Events -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Events
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($eventData['total_events'] ?? 0) }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($eventData['published_events'] ?? 0) }} published
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Tickets Sold -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Tickets Sold
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($salesData['total_tickets_sold'] ?? 0) }}
                            </div>
                            <div class="text-xs text-muted">
                                {{ number_format($salesData['total_orders'] ?? 0) }} orders
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-ticket-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Conversion Rate -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Conversion Rate
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $salesData['conversion_rate'] ?? 0 }}%
                            </div>
                            <div class="text-xs text-muted">
                                Avg Order: Rp {{ number_format($revenueData['average_order_value'] ?? 0, 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Revenue Trend Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue Trend</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="{{ route('organizer.analytics.revenue') }}">View Details</a>
                            <a class="dropdown-item" href="{{ route('organizer.analytics.export', ['type' => 'revenue']) }}">Export Data</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Performance -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performing Events</h6>
                    <a href="{{ route('organizer.analytics.events') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        @forelse($topEvents ?? [] as $event)
                            <div class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                <div>
                                    <h6 class="mb-1">{{ Str::limit($event->title ?? 'Event Title', 25) }}</h6>
                                    <small class="text-muted">{{ $event->tickets_sold ?? 0 }} tickets sold</small>
                                </div>
                                <div class="text-right">
                                    <span class="font-weight-bold">Rp {{ number_format($event->revenue ?? 0, 0, ',', '.') }}</span>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No event data available</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Detailed Analytics</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="{{ route('organizer.analytics.revenue') }}" class="text-decoration-none">
                                <div class="card border-left-primary h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                        <h5 class="card-title">Revenue Analytics</h5>
                                        <p class="card-text text-muted">Detailed revenue analysis and trends</p>
                                        <div class="mt-3">
                                            <span class="badge badge-primary">
                                                Total: Rp {{ number_format($revenueData['total_revenue'] ?? 0, 0, ',', '.') }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-4 mb-3">
                            <a href="{{ route('organizer.analytics.events') }}" class="text-decoration-none">
                                <div class="card border-left-success h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-calendar-check fa-3x text-success mb-3"></i>
                                        <h5 class="card-title">Event Performance</h5>
                                        <p class="card-text text-muted">Individual event analytics and metrics</p>
                                        <div class="mt-3">
                                            <span class="badge badge-success">
                                                {{ number_format($eventData['total_events'] ?? 0) }} Events
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="col-md-4 mb-3">
                            <a href="{{ route('organizer.reports.index') }}" class="text-decoration-none">
                                <div class="card border-left-info h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-file-alt fa-3x text-info mb-3"></i>
                                        <h5 class="card-title">Reports</h5>
                                        <p class="card-text text-muted">Comprehensive reports and exports</p>
                                        <div class="mt-3">
                                            <span class="badge badge-info">
                                                Export Available
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Sales Performance</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="salesChart"></canvas>
                    </div>
                    <div class="mt-4 text-center small">
                        <span class="mr-2">
                            <i class="fas fa-circle text-primary"></i> Completed Orders
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-success"></i> Pending Orders
                        </span>
                        <span class="mr-2">
                            <i class="fas fa-circle text-info"></i> Cancelled Orders
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Key Performance Indicators</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 border-right">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-primary">
                                    Rp {{ number_format($revenueData['average_order_value'] ?? 0, 0, ',', '.') }}
                                </div>
                                <div class="text-muted">Average Order Value</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-success">
                                    {{ $salesData['conversion_rate'] ?? 0 }}%
                                </div>
                                <div class="text-muted">Conversion Rate</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6 border-right">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-info">
                                    {{ number_format($eventData['average_attendance'] ?? 0) }}
                                </div>
                                <div class="text-muted">Avg Attendance</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 font-weight-bold text-warning">
                                    {{ number_format($eventData['upcoming_events'] ?? 0) }}
                                </div>
                                <div class="text-muted">Upcoming Events</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.chart-area {
    position: relative;
    height: 300px;
}

.chart-pie {
    position: relative;
    height: 250px;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
            label: 'Revenue',
            data: [12000000, 19000000, 15000000, 25000000, 22000000, 30000000],
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return 'Rp ' + (value / 1000000) + 'M';
                    }
                }
            }
        }
    }
});

// Sales Chart
const salesCtx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(salesCtx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'Pending', 'Cancelled'],
        datasets: [{
            data: [{{ $salesData['completed_orders'] ?? 75 }}, {{ $salesData['pending_orders'] ?? 20 }}, {{ $salesData['cancelled_orders'] ?? 5 }}],
            backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc'],
            hoverBackgroundColor: ['#2e59d9', '#17a673', '#2c9faf'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        cutout: '80%',
    },
});
</script>
@endpush
@endsection

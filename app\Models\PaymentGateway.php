<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PaymentGateway extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'provider',
        'config',
        'is_active',
        'is_production',
        'sort_order'
    ];

    protected $casts = [
        'config' => 'array',
        'is_active' => 'boolean',
        'is_production' => 'boolean',
        'sort_order' => 'integer'
    ];

    /**
     * Gateway provider constants
     */
    const PROVIDER_XENDIT = 'xendit';
    const PROVIDER_MIDTRANS = 'midtrans';
    const PROVIDER_TRIPAY = 'tripay';
    const PROVIDER_MANUAL = 'manual';

    /**
     * Get the payment methods for the gateway
     */
    public function paymentMethods(): HasMany
    {
        return $this->hasMany(PaymentMethod::class, 'gateway_id');
    }

    /**
     * Scope for active gateways
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for production gateways
     */
    public function scopeProduction($query)
    {
        return $query->where('is_production', true);
    }

    /**
     * Get gateway configuration for specific environment
     */
    public function getEnvironmentConfig(): array
    {
        $config = $this->config ?? [];
        
        if ($this->is_production) {
            return [
                'environment' => 'production',
                'api_key' => $config['production_api_key'] ?? $config['api_key'] ?? null,
                'secret_key' => $config['production_secret_key'] ?? $config['secret_key'] ?? null,
                'merchant_id' => $config['production_merchant_id'] ?? $config['merchant_id'] ?? null,
                'base_url' => $config['production_base_url'] ?? $config['base_url'] ?? null,
            ];
        }
        
        return [
            'environment' => 'sandbox',
            'api_key' => $config['sandbox_api_key'] ?? $config['api_key'] ?? null,
            'secret_key' => $config['sandbox_secret_key'] ?? $config['secret_key'] ?? null,
            'merchant_id' => $config['sandbox_merchant_id'] ?? $config['merchant_id'] ?? null,
            'base_url' => $config['sandbox_base_url'] ?? $config['base_url'] ?? null,
        ];
    }

    /**
     * Test gateway connection
     */
    public function testConnection(): array
    {
        try {
            switch ($this->code) {
                case self::PROVIDER_XENDIT:
                    return $this->testXenditConnection();
                case self::PROVIDER_MIDTRANS:
                    return $this->testMidtransConnection();
                case self::PROVIDER_TRIPAY:
                    return $this->testTripayConnection();
                default:
                    return [
                        'success' => true,
                        'message' => 'Manual gateway - no connection test needed'
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Xendit connection
     */
    private function testXenditConnection(): array
    {
        $config = $this->getEnvironmentConfig();
        $apiKey = $config['secret_key'];

        if (!$apiKey) {
            return [
                'success' => false,
                'message' => 'API key not configured'
            ];
        }

        $response = \Http::withBasicAuth($apiKey, '')
            ->timeout(10)
            ->get('https://api.xendit.co/balance');

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => $response->json()
            ];
        }

        return [
            'success' => false,
            'message' => 'Connection failed: ' . $response->body()
        ];
    }

    /**
     * Test Midtrans connection
     */
    private function testMidtransConnection(): array
    {
        $config = $this->getEnvironmentConfig();
        $serverKey = $config['secret_key'];

        if (!$serverKey) {
            return [
                'success' => false,
                'message' => 'Server key not configured'
            ];
        }

        $baseUrl = $this->is_production 
            ? 'https://api.midtrans.com' 
            : 'https://api.sandbox.midtrans.com';

        $response = \Http::withBasicAuth($serverKey, '')
            ->timeout(10)
            ->get($baseUrl . '/v2/ping');

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => $response->json()
            ];
        }

        return [
            'success' => false,
            'message' => 'Connection failed: ' . $response->body()
        ];
    }

    /**
     * Test Tripay connection
     */
    private function testTripayConnection(): array
    {
        $config = $this->getEnvironmentConfig();
        $apiKey = $config['api_key'];

        if (!$apiKey) {
            return [
                'success' => false,
                'message' => 'API key not configured'
            ];
        }

        $baseUrl = $this->is_production 
            ? 'https://tripay.co.id/api' 
            : 'https://tripay.co.id/api-sandbox';

        $response = \Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey
        ])->timeout(10)->get($baseUrl . '/merchant/payment-channel');

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => $response->json()
            ];
        }

        return [
            'success' => false,
            'message' => 'Connection failed: ' . $response->body()
        ];
    }

    /**
     * Get available payment channels for this gateway
     */
    public function getAvailableChannels(): array
    {
        switch ($this->code) {
            case self::PROVIDER_XENDIT:
                return $this->getXenditChannels();
            case self::PROVIDER_MIDTRANS:
                return $this->getMidtransChannels();
            case self::PROVIDER_TRIPAY:
                return $this->getTripayChannels();
            default:
                return [];
        }
    }

    /**
     * Get Xendit payment channels
     */
    private function getXenditChannels(): array
    {
        return [
            'credit_card' => 'Credit Card',
            'bca_va' => 'BCA Virtual Account',
            'bni_va' => 'BNI Virtual Account',
            'bri_va' => 'BRI Virtual Account',
            'mandiri_va' => 'Mandiri Virtual Account',
            'ovo' => 'OVO',
            'dana' => 'DANA',
            'linkaja' => 'LinkAja',
            'shopeepay' => 'ShopeePay'
        ];
    }

    /**
     * Get Midtrans payment channels
     */
    private function getMidtransChannels(): array
    {
        return [
            'credit_card' => 'Credit Card',
            'bank_transfer' => 'Bank Transfer',
            'echannel' => 'Mandiri Bill',
            'bca_va' => 'BCA Virtual Account',
            'bni_va' => 'BNI Virtual Account',
            'bri_va' => 'BRI Virtual Account',
            'gopay' => 'GoPay',
            'shopeepay' => 'ShopeePay'
        ];
    }

    /**
     * Get Tripay payment channels
     */
    private function getTripayChannels(): array
    {
        return [
            'MYBVA' => 'Maybank Virtual Account',
            'PERMATAVA' => 'Permata Virtual Account',
            'BNIVA' => 'BNI Virtual Account',
            'BRIVA' => 'BRI Virtual Account',
            'MANDIRIVA' => 'Mandiri Virtual Account',
            'BCAVA' => 'BCA Virtual Account',
            'SMSVA' => 'SMS Virtual Account',
            'MUAMALATVA' => 'Muamalat Virtual Account',
            'CIMBVA' => 'CIMB Virtual Account',
            'BSIVA' => 'BSI Virtual Account',
            'ALFAMART' => 'Alfamart',
            'INDOMARET' => 'Indomaret',
            'OVO' => 'OVO',
            'DANA' => 'DANA',
            'SHOPEEPAY' => 'ShopeePay',
            'LINKAJA' => 'LinkAja',
            'QRIS' => 'QRIS'
        ];
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (is_null($model->sort_order)) {
                $model->sort_order = static::max('sort_order') + 1;
            }
        });
    }
}

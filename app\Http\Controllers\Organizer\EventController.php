<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use App\Services\ImageService;

class EventController extends Controller
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->middleware(['auth', 'organizer']);
        $this->imageService = $imageService;
    }

    /**
     * Display organizer's Events
     */
    public function index(Request $request)
    {
        $query = auth()->user()->organizedEvents()->with('category');

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('venue_name', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        $events = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        // Get statistics
        $stats = [
            'total' => auth()->user()->organizedEvents()->count(),
            'total_events' => auth()->user()->organizedEvents()->count(),
            'published' => auth()->user()->organizedEvents()->where('status', 'published')->count(),
            'draft' => auth()->user()->organizedEvents()->where('status', 'draft')->count(),
            'completed' => auth()->user()->organizedEvents()->where('status', 'completed')->count(),
            'total_revenue' => auth()->user()->organizedEvents()
                ->selectRaw('SUM(price * (total_capacity - available_capacity)) as revenue')
                ->value('revenue') ?? 0,
            'total_tickets_sold' => auth()->user()->organizedEvents()
                ->selectRaw('SUM(total_capacity - available_capacity) as sold')
                ->value('sold') ?? 0,
        ];

        return view('pages.organizer.events.index', compact('events', 'stats'));
    }

    /**
     * Show create event form
     */
    public function create()
    {
        $categories = Category::orderBy('name')->get();
        return view('organizer.events.create', compact('categories'));
    }

    /**
     * Store new event
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'province' => 'required|string|max:100',
            'start_date' => 'required|date|after:now',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'required|image|mimes:jpeg,png,jpg|max:2048',
            'gallery.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'boarding_pass_template' => 'required|in:unix,minimalist,pro,custom,elegant,modern,classic,neon,retro,corporate,festival,vip',
            'auto_generate_tickets' => 'boolean',
            'email_tickets_to_buyers' => 'boolean',
            'is_free' => 'boolean',
            'requires_approval' => 'boolean',
            'sale_start_date' => 'nullable|date|before:start_date',
            'sale_end_date' => 'nullable|date|before:start_date|after:sale_start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ], [
            'title.required' => 'Judul event wajib diisi.',
            'description.required' => 'Deskripsi event wajib diisi.',
            'category_id.required' => 'Kategori event wajib dipilih.',
            'venue_name.required' => 'Nama venue wajib diisi.',
            'venue_address.required' => 'Alamat venue wajib diisi.',
            'start_date.required' => 'Tanggal mulai wajib diisi.',
            'start_date.after' => 'Tanggal mulai harus setelah hari ini.',
            'end_date.required' => 'Tanggal selesai wajib diisi.',
            'end_date.after' => 'Tanggal selesai harus setelah tanggal mulai.',
            'price.required' => 'Harga tiket wajib diisi.',
            'total_capacity.required' => 'Kapasitas total wajib diisi.',
            'poster.required' => 'Poster event wajib diupload.',
            'poster.image' => 'File poster harus berupa gambar.',
            'poster.max' => 'Ukuran poster maksimal 2MB.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if user can use custom template (platinum badge only)
        if ($request->boarding_pass_template === 'custom') {
            $user = auth()->user();
            if (!$user->hasActiveBadge('platinum')) {
                return back()->with('error', 'Custom templates are only available for Platinum badge users. Please select a different template.')
                    ->withInput();
            }
        }

        try {
            // Handle poster upload
            $posterPath = $this->uploadPoster($request->file('poster'));

            // Handle gallery upload
            $galleryPaths = [];
            if ($request->hasFile('gallery')) {
                foreach ($request->file('gallery') as $file) {
                    $galleryPaths[] = $this->uploadGalleryImage($file);
                }
            }

            // Process tags
            $tags = [];
            if ($request->filled('tags')) {
                $tags = array_map('trim', explode(',', $request->tags));
            }

            // Create event
            $event = Event::create([
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'category_id' => $request->category_id,
                'organizer_id' => auth()->id(),
                'venue_name' => $request->venue_name,
                'venue_address' => $request->venue_address,
                'city' => $request->city,
                'province' => $request->province,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'price' => $request->is_free ? 0 : $request->price,
                'total_capacity' => $request->total_capacity,
                'available_capacity' => $request->total_capacity,
                'poster' => $posterPath,
                'gallery' => $galleryPaths,
                'is_free' => $request->boolean('is_free'),
                'requires_approval' => $request->boolean('requires_approval'),
                'boarding_pass_template' => $request->boarding_pass_template,
                'auto_generate_tickets' => $request->boolean('auto_generate_tickets', true),
                'email_tickets_to_buyers' => $request->boolean('email_tickets_to_buyers', true),
                'template_config' => $this->getTemplateConfig($request->boarding_pass_template),
                'sale_start_date' => $request->sale_start_date,
                'sale_end_date' => $request->sale_end_date,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
                'tags' => $tags,
                'status' => 'draft',
            ]);

            return redirect()->route('organizer.events.show', $event)
                ->with('success', 'Event berhasil dibuat! Silakan review dan publish event Anda.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat membuat event: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show event details for organizer
     */
    public function show(Event $event)
    {
        $this->authorize('view', $event);

        $event->load(['category', 'tickets.buyer']);

        // Get event statistics
        $stats = [
            'total_tickets' => $event->tickets()->count(),
            'active_tickets' => $event->tickets()->where('status', 'active')->count(),
            'used_tickets' => $event->tickets()->where('status', 'used')->count(),
            'cancelled_tickets' => $event->tickets()->where('status', 'cancelled')->count(),
            'total_revenue' => $event->tickets()->where('status', '!=', 'cancelled')->sum('total_paid'),
            'view_count' => $event->view_count ?? 0,
        ];

        return view('organizer.events.show', compact('event', 'stats'));
    }

    /**
     * Show edit form
     */
    public function edit(Event $event)
    {
        $this->authorize('update', $event);

        $categories = Category::orderBy('name')->get();
        return view('organizer.events.edit', compact('event', 'categories'));
    }

    /**
     * Update event
     */
    public function update(Request $request, Event $event)
    {
        $this->authorize('update', $event);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'venue_name' => 'required|string|max:255',
            'venue_address' => 'required|string',
            'city' => 'required|string|max:100',
            'province' => 'required|string|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'price' => 'required|numeric|min:0',
            'total_capacity' => 'required|integer|min:1',
            'poster' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
            'gallery.*' => 'image|mimes:jpeg,png,jpg|max:2048',
            'is_free' => 'boolean',
            'requires_approval' => 'boolean',
            'sale_start_date' => 'nullable|date|before:start_date',
            'sale_end_date' => 'nullable|date|before:start_date|after:sale_start_date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $updateData = [
                'title' => $request->title,
                'slug' => Str::slug($request->title),
                'description' => $request->description,
                'category_id' => $request->category_id,
                'venue_name' => $request->venue_name,
                'venue_address' => $request->venue_address,
                'city' => $request->city,
                'province' => $request->province,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'price' => $request->is_free ? 0 : $request->price,
                'total_capacity' => $request->total_capacity,
                'is_free' => $request->boolean('is_free'),
                'requires_approval' => $request->boolean('requires_approval'),
                'sale_start_date' => $request->sale_start_date,
                'sale_end_date' => $request->sale_end_date,
                'meta_title' => $request->meta_title,
                'meta_description' => $request->meta_description,
            ];

            // Handle poster upload
            if ($request->hasFile('poster')) {
                // Delete old poster
                if ($event->poster) {
                    Storage::disk('public')->delete($event->poster);
                }
                $updateData['poster'] = $this->uploadPoster($request->file('poster'));
            }

            // Handle gallery upload
            if ($request->hasFile('gallery')) {
                // Delete old gallery images
                if ($event->gallery) {
                    foreach ($event->gallery as $imagePath) {
                        Storage::disk('public')->delete($imagePath);
                    }
                }

                $galleryPaths = [];
                foreach ($request->file('gallery') as $file) {
                    $galleryPaths[] = $this->uploadGalleryImage($file);
                }
                $updateData['gallery'] = $galleryPaths;
            }

            // Process tags
            if ($request->filled('tags')) {
                $updateData['tags'] = array_map('trim', explode(',', $request->tags));
            }

            $event->update($updateData);

            return redirect()->route('organizer.events.show', $event)
                ->with('success', 'Event berhasil diperbarui!');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat memperbarui event: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Publish event
     */
    public function publish(Event $event)
    {
        $this->authorize('update', $event);

        if ($event->status !== 'draft') {
            return back()->with('error', 'Event sudah dipublish atau tidak dapat dipublish.');
        }

        $event->update(['status' => 'published']);

        return back()->with('success', 'Event berhasil dipublish dan sekarang dapat dilihat oleh publik!');
    }

    /**
     * Unpublish event
     */
    public function unpublish(Event $event)
    {
        $this->authorize('update', $event);

        if ($event->status !== 'published') {
            return back()->with('error', 'Event tidak dalam status published.');
        }

        $event->update(['status' => 'draft']);

        return back()->with('success', 'Event berhasil di-unpublish dan tidak lagi terlihat oleh publik.');
    }

    /**
     * Update event ticket template
     */
    public function updateTemplate(Request $request, Event $event)
    {
        $this->authorize('update', $event);

        $request->validate([
            'boarding_pass_template' => 'required|in:unix,minimalist,pro,custom',
            'auto_generate_tickets' => 'boolean',
            'email_tickets_to_buyers' => 'boolean'
        ]);

        // Check if user can use custom template (platinum badge only)
        if ($request->boarding_pass_template === 'custom') {
            $user = auth()->user();
            if (!$user->hasActiveBadge('platinum')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Custom templates are only available for Platinum badge users.'
                ], 403);
            }
        }

        $event->update([
            'boarding_pass_template' => $request->boarding_pass_template,
            'auto_generate_tickets' => $request->boolean('auto_generate_tickets', true),
            'email_tickets_to_buyers' => $request->boolean('email_tickets_to_buyers', true),
            'template_config' => $this->getTemplateConfig($request->boarding_pass_template)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'E-Ticket template updated successfully!'
        ]);
    }

    /**
     * Get template configuration based on template type
     */
    private function getTemplateConfig($template)
    {
        $configs = [
            'unix' => [
                'font_family' => 'JetBrains Mono, monospace',
                'background_color' => '#0d1117',
                'primary_color' => '#58a6ff',
                'secondary_color' => '#c9d1d9',
                'text_color' => '#c9d1d9',
                'accent_color' => '#58a6ff',
                'border_style' => 'solid',
                'theme' => 'terminal',
                'terminal_style' => true
            ],
            'minimalist' => [
                'font_family' => 'Inter, sans-serif',
                'background_color' => '#ffffff',
                'primary_color' => '#3b82f6',
                'secondary_color' => '#6b7280',
                'text_color' => '#1f2937',
                'accent_color' => '#3b82f6',
                'border_style' => 'minimal',
                'theme' => 'clean',
                'clean_design' => true
            ],
            'pro' => [
                'font_family' => 'Poppins, sans-serif',
                'background_color' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'primary_color' => '#7c3aed',
                'secondary_color' => '#a855f7',
                'text_color' => '#1f2937',
                'accent_color' => '#8b5cf6',
                'border_style' => 'professional',
                'theme' => 'business',
                'premium_style' => true
            ],
            'custom' => [
                'font_family' => 'Inter, sans-serif',
                'background_color' => '#f8fafc',
                'primary_color' => '#10b981',
                'secondary_color' => '#6b7280',
                'text_color' => '#1f2937',
                'accent_color' => '#10b981',
                'border_style' => 'custom',
                'theme' => 'branded',
                'customizable' => true
            ],
            'elegant' => [
                'font_family' => 'Playfair Display, serif',
                'background_color' => 'linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%)',
                'primary_color' => '#ec4899',
                'secondary_color' => '#f9a8d4',
                'text_color' => '#831843',
                'accent_color' => '#ec4899',
                'border_style' => 'elegant',
                'theme' => 'luxury',
                'sophisticated' => true
            ],
            'modern' => [
                'font_family' => 'Roboto, sans-serif',
                'background_color' => 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',
                'primary_color' => '#0891b2',
                'secondary_color' => '#67e8f9',
                'text_color' => '#164e63',
                'accent_color' => '#0891b2',
                'border_style' => 'modern',
                'theme' => 'contemporary',
                'trendy' => true
            ],
            'classic' => [
                'font_family' => 'Times New Roman, serif',
                'background_color' => 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                'primary_color' => '#d97706',
                'secondary_color' => '#f59e0b',
                'text_color' => '#92400e',
                'accent_color' => '#d97706',
                'border_style' => 'classic',
                'theme' => 'traditional',
                'timeless' => true
            ],
            'neon' => [
                'font_family' => 'Orbitron, monospace',
                'background_color' => '#0f0f23',
                'primary_color' => '#8b5cf6',
                'secondary_color' => '#a78bfa',
                'text_color' => '#e5e7eb',
                'accent_color' => '#8b5cf6',
                'border_style' => 'neon',
                'theme' => 'cyberpunk',
                'glow_effect' => true
            ],
            'retro' => [
                'font_family' => 'Courier New, monospace',
                'background_color' => 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',
                'primary_color' => '#ea580c',
                'secondary_color' => '#fb923c',
                'text_color' => '#9a3412',
                'accent_color' => '#ea580c',
                'border_style' => 'retro',
                'theme' => 'vintage',
                'vintage_style' => true
            ],
            'corporate' => [
                'font_family' => 'Arial, sans-serif',
                'background_color' => 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
                'primary_color' => '#475569',
                'secondary_color' => '#64748b',
                'text_color' => '#1e293b',
                'accent_color' => '#475569',
                'border_style' => 'corporate',
                'theme' => 'business',
                'formal' => true
            ],
            'festival' => [
                'font_family' => 'Fredoka One, cursive',
                'background_color' => 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)',
                'primary_color' => '#059669',
                'secondary_color' => '#34d399',
                'text_color' => '#064e3b',
                'accent_color' => '#059669',
                'border_style' => 'festival',
                'theme' => 'fun',
                'colorful' => true
            ],
            'vip' => [
                'font_family' => 'Montserrat, sans-serif',
                'background_color' => 'linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%)',
                'primary_color' => '#d97706',
                'secondary_color' => '#f59e0b',
                'text_color' => '#92400e',
                'accent_color' => '#d97706',
                'border_style' => 'vip',
                'theme' => 'exclusive',
                'premium' => true
            ]
        ];

        return $configs[$template] ?? $configs['unix'];
    }

    /**
     * Delete event
     */
    public function destroy(Event $event)
    {
        $this->authorize('delete', $event);

        // Check if event has tickets
        if ($event->tickets()->count() > 0) {
            return back()->with('error', 'Event tidak dapat dihapus karena sudah memiliki tiket yang terjual.');
        }

        try {
            // Delete poster and gallery images
            if ($event->poster) {
                Storage::disk('public')->delete($event->poster);
            }

            if ($event->gallery) {
                foreach ($event->gallery as $imagePath) {
                    Storage::disk('public')->delete($imagePath);
                }
            }

            $event->delete();

            return redirect()->route('organizer.events.index')
                ->with('success', 'Event berhasil dihapus.');

        } catch (\Exception $e) {
            return back()->with('error', 'Terjadi kesalahan saat menghapus event: ' . $e->getMessage());
        }
    }

    /**
     * Upload and process poster image
     */
    private function uploadPoster($file)
    {
        return $this->imageService->uploadPoster($file);
    }

    /**
     * Upload and process gallery image
     */
    private function uploadGalleryImage($file)
    {
        return $this->imageService->uploadGallery($file);
    }

    /**
     * Download QR Code for event
     */
    public function downloadQRCode(Event $event)
    {
        $this->authorize('view', $event);

        try {
            // Generate QR code data for event
            $qrData = json_encode([
                'type' => 'event',
                'event_id' => $event->id,
                'event_slug' => $event->slug,
                'event_title' => $event->title,
                'event_url' => route('tickets.show', $event->slug),
                'organizer' => $event->organizer->name,
                'generated_at' => now()->toISOString(),
            ]);

            // Generate QR code using QRCodeService
            $qrCodeService = app(\App\Services\QRCodeService::class);
            $qrCodeSvg = $qrCodeService->generateEventQRCode($event, $qrData);

            // Set headers for download
            $filename = "qr-code-{$event->slug}.svg";

            return response($qrCodeSvg)
                ->header('Content-Type', 'image/svg+xml')
                ->header('Content-Disposition', "attachment; filename=\"{$filename}\"")
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            \Log::error('QR Code generation failed', [
                'event_id' => $event->id,
                'error' => $e->getMessage()
            ]);

            return back()->with('error', 'Failed to generate QR Code: ' . $e->getMessage());
        }
    }
}

@php
    // Configuration with fallbacks
    $config = $config ?? [];
    $primaryColor = $config['primary_color'] ?? '#7C3AED';
    $secondaryColor = $config['secondary_color'] ?? '#F3F4F6';
    $backgroundColor = $config['background_color'] ?? '#FFFFFF';
    $textColor = $config['text_color'] ?? '#111827';
    $accentColor = $config['accent_color'] ?? '#F59E0B';
    
    // Helper functions
    function getTicketValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket[$key] ?? $default;
        } elseif (is_object($ticket)) {
            return $ticket->{$key} ?? $default;
        }
        return $default;
    }
    
    function getEventValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['event'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->event)) {
            return $ticket->event->{$key} ?? $default;
        }
        return $default;
    }
    
    function getBuyerValue($ticket, $key, $default = null) {
        if (is_array($ticket)) {
            return $ticket['buyer'][$key] ?? $default;
        } elseif (is_object($ticket) && isset($ticket->buyer)) {
            return $ticket->buyer->{$key} ?? $default;
        }
        return $default;
    }
@endphp

<div class="ticket-container pro-ticket" style="
    background: linear-gradient(135deg, {{ $backgroundColor }} 0%, {{ $secondaryColor }} 100%);
    color: {{ $textColor }};
    font-family: 'Poppins', sans-serif;
    width: 700px;
    height: 350px;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid rgba(255, 255, 255, 0.2);
">
    <!-- Background Pattern -->
    <div style="
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
            radial-gradient(circle at 25% 25%, {{ $primaryColor }}10 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, {{ $accentColor }}10 0%, transparent 50%);
        opacity: 0.5;
    "></div>

    <!-- Header with Gradient -->
    <div style="
        background: linear-gradient(135deg, {{ $primaryColor }} 0%, {{ $accentColor }} 100%);
        padding: 24px;
        position: relative;
        overflow: hidden;
    ">
        <!-- Header Background Pattern -->
        <div style="
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');
            animation: float 20s ease-in-out infinite;
        "></div>

        <div style="position: relative; z-index: 2;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h1 style="
                        font-size: 28px;
                        font-weight: 700;
                        margin: 0 0 8px 0;
                        color: white;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                        letter-spacing: -0.025em;
                    ">{{ getEventValue($ticket, 'title', 'Event Title') }}</h1>
                    <div style="
                        display: inline-flex;
                        align-items: center;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 6px 12px;
                        border-radius: 20px;
                        backdrop-filter: blur(10px);
                    ">
                        <div style="
                            width: 8px;
                            height: 8px;
                            background: #10B981;
                            border-radius: 50%;
                            margin-right: 8px;
                            animation: pulse 2s infinite;
                        "></div>
                        <span style="
                            color: white;
                            font-size: 12px;
                            font-weight: 600;
                            text-transform: uppercase;
                            letter-spacing: 0.05em;
                        ">Premium Ticket</span>
                    </div>
                </div>
                <div style="
                    width: 64px;
                    height: 64px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                ">
                    <svg width="32" height="32" fill="white" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div style="
        padding: 32px;
        position: relative;
        z-index: 2;
        height: calc(100% - 140px);
    ">
        <div style="display: flex; gap: 32px; height: 100%;">
            <!-- Left Section -->
            <div style="flex: 1;">
                <div style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 24px;
                    margin-bottom: 24px;
                ">
                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 20px;
                        border-radius: 16px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    ">
                        <h3 style="
                            font-size: 12px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 8px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">Event Date</h3>
                        @php
                            $startDate = getEventValue($ticket, 'start_date', now());
                            $parsedDate = \Carbon\Carbon::parse($startDate);
                        @endphp
                        <p style="
                            font-size: 18px;
                            font-weight: 700;
                            margin: 0 0 4px 0;
                            color: {{ $textColor }};
                        ">{{ $parsedDate->format('M j, Y') }}</p>
                        <p style="
                            font-size: 14px;
                            margin: 0;
                            color: #6B7280;
                            font-weight: 500;
                        ">{{ $parsedDate->format('l, g:i A') }}</p>
                    </div>

                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 20px;
                        border-radius: 16px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                    ">
                        <h3 style="
                            font-size: 12px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 8px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">Venue</h3>
                        <p style="
                            font-size: 16px;
                            font-weight: 700;
                            margin: 0 0 4px 0;
                            color: {{ $textColor }};
                        ">{{ getEventValue($ticket, 'venue_name', 'TBA') }}</p>
                        <p style="
                            font-size: 14px;
                            margin: 0;
                            color: #6B7280;
                            font-weight: 500;
                        ">{{ getEventValue($ticket, 'city', 'Location TBA') }}</p>
                    </div>
                </div>

                <div style="
                    background: rgba(255, 255, 255, 0.9);
                    padding: 24px;
                    border-radius: 16px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                ">
                    <div>
                        <h3 style="
                            font-size: 12px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 8px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">Attendee</h3>
                        <p style="
                            font-size: 18px;
                            font-weight: 700;
                            margin: 0 0 4px 0;
                            color: {{ $textColor }};
                        ">{{ getTicketValue($ticket, 'attendee_name', getBuyerValue($ticket, 'name', 'Guest')) }}</p>
                        <p style="
                            font-size: 14px;
                            margin: 0;
                            color: #6B7280;
                            font-weight: 500;
                        ">{{ getTicketValue($ticket, 'attendee_email', getBuyerValue($ticket, 'email', 'N/A')) }}</p>
                    </div>
                    <div style="text-align: right;">
                        <h3 style="
                            font-size: 12px;
                            font-weight: 600;
                            color: {{ $primaryColor }};
                            margin: 0 0 8px 0;
                            text-transform: uppercase;
                            letter-spacing: 0.1em;
                        ">Price</h3>
                        @php
                            $price = getTicketValue($ticket, 'price', 0);
                        @endphp
                        <p style="
                            font-size: 24px;
                            font-weight: 800;
                            margin: 0;
                            color: {{ $accentColor }};
                        ">Rp {{ number_format($price, 0, ',', '.') }}</p>
                    </div>
                </div>
            </div>

            <!-- Right Section - QR Code -->
            <div style="
                width: 200px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            ">
                <div style="
                    background: rgba(255, 255, 255, 0.95);
                    padding: 24px;
                    border-radius: 20px;
                    backdrop-filter: blur(20px);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                    margin-bottom: 16px;
                ">
                    @php
                        $qrData = getTicketValue($ticket, 'qr_code', 
                            getTicketValue($ticket, 'ticket_number', 
                                getTicketValue($ticket, 'ticket_code', 'INVALID')
                            )
                        );
                    @endphp
                    @if(class_exists('SimpleSoftwareIO\QrCode\Facades\QrCode'))
                        {!! QrCode::size(140)->generate($qrData) !!}
                    @else
                        <div style="
                            width: 140px;
                            height: 140px;
                            background: #f8fafc;
                            border: 2px dashed #cbd5e1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 14px;
                            color: #64748b;
                            text-align: center;
                            border-radius: 12px;
                        ">
                            QR Code<br>{{ substr($qrData, 0, 8) }}
                        </div>
                    @endif
                </div>

                <div style="text-align: center;">
                    <p style="
                        font-size: 14px;
                        font-weight: 600;
                        color: {{ $primaryColor }};
                        margin: 0 0 8px 0;
                    ">Scan to Verify</p>
                    <p style="
                        font-size: 11px;
                        color: #6B7280;
                        margin: 0 0 8px 0;
                        line-height: 1.4;
                    ">Present this QR code at the venue entrance</p>
                    <div style="
                        background: rgba(255, 255, 255, 0.8);
                        padding: 8px 12px;
                        border-radius: 8px;
                        backdrop-filter: blur(10px);
                    ">
                        <p style="
                            font-size: 10px;
                            font-weight: 600;
                            font-family: 'JetBrains Mono', monospace;
                            margin: 0;
                            color: {{ $textColor }};
                            letter-spacing: 1px;
                        ">{{ getTicketValue($ticket, 'ticket_number', getTicketValue($ticket, 'ticket_code', 'TIX-000000')) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Decorative Elements -->
    <div style="
        position: absolute;
        top: 20px;
        right: 20px;
        width: 100px;
        height: 100px;
        background: linear-gradient(45deg, {{ $primaryColor }}20, {{ $accentColor }}20);
        border-radius: 50%;
        filter: blur(40px);
    "></div>

    <div style="
        position: absolute;
        bottom: 20px;
        left: 20px;
        width: 80px;
        height: 80px;
        background: linear-gradient(45deg, {{ $accentColor }}20, {{ $primaryColor }}20);
        border-radius: 50%;
        filter: blur(30px);
    "></div>
</div>

<style>
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

.pro-ticket {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
    box-sizing: border-box;
}

.pro-ticket * {
    box-sizing: border-box;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pro-ticket {
        width: 100% !important;
        max-width: 700px !important;
        height: auto !important;
        min-height: 350px !important;
    }
}

@media print {
    .pro-ticket {
        width: 700px !important;
        height: 350px !important;
        margin: 0 !important;
        page-break-inside: avoid;
    }
}
</style>

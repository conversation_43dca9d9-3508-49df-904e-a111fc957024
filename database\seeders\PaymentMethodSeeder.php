<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            // Manual Bank Transfer Methods
            [
                'name' => 'Transfer Bank BCA',
                'type' => PaymentMethod::TYPE_MANUAL,
                'code' => 'bank_bca',
                'category' => PaymentMethod::CATEGORY_BANK_TRANSFER,
                'description' => 'Transfer ke rekening Bank BCA',
                'icon' => 'fas fa-university',
                'logo' => '/images/banks/bca.png',
                'fee_percentage' => 0,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => true,
                'sort_order' => 1,
                'manual_config' => [
                    'bank_name' => 'Bank Central Asia (BCA)',
                    'account_number' => '**********',
                    'account_name' => 'PT TiXara Indonesia',
                    'bank_code' => 'BCA',
                ],
                'instructions' => 'Transfer ke rekening BCA di atas, lalu upload bukti transfer.',
            ],
            [
                'name' => 'Transfer Bank Mandiri',
                'type' => PaymentMethod::TYPE_MANUAL,
                'code' => 'bank_mandiri',
                'category' => PaymentMethod::CATEGORY_BANK_TRANSFER,
                'description' => 'Transfer ke rekening Bank Mandiri',
                'icon' => 'fas fa-university',
                'logo' => '/images/banks/mandiri.png',
                'fee_percentage' => 0,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => true,
                'sort_order' => 2,
                'manual_config' => [
                    'bank_name' => 'Bank Mandiri',
                    'account_number' => '**********',
                    'account_name' => 'PT TiXara Indonesia',
                    'bank_code' => 'MANDIRI',
                ],
                'instructions' => 'Transfer ke rekening Mandiri di atas, lalu upload bukti transfer.',
            ],
            [
                'name' => 'Transfer Bank BNI',
                'type' => PaymentMethod::TYPE_MANUAL,
                'code' => 'bank_bni',
                'category' => PaymentMethod::CATEGORY_BANK_TRANSFER,
                'description' => 'Transfer ke rekening Bank BNI',
                'icon' => 'fas fa-university',
                'logo' => '/images/banks/bni.png',
                'fee_percentage' => 0,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => true,
                'sort_order' => 3,
                'manual_config' => [
                    'bank_name' => 'Bank Negara Indonesia (BNI)',
                    'account_number' => '**********',
                    'account_name' => 'PT TiXara Indonesia',
                    'bank_code' => 'BNI',
                ],
                'instructions' => 'Transfer ke rekening BNI di atas, lalu upload bukti transfer.',
            ],

            // Gateway Methods (Inactive by default - need configuration)
            [
                'name' => 'GoPay',
                'type' => PaymentMethod::TYPE_TRIPAY,
                'code' => 'gopay',
                'category' => PaymentMethod::CATEGORY_E_WALLET,
                'description' => 'Bayar dengan GoPay',
                'icon' => 'fab fa-google-pay',
                'logo' => '/images/ewallet/gopay.png',
                'fee_percentage' => 2.5,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => false, // Inactive until configured
                'sort_order' => 10,
            ],
            [
                'name' => 'OVO',
                'type' => PaymentMethod::TYPE_TRIPAY,
                'code' => 'ovo',
                'category' => PaymentMethod::CATEGORY_E_WALLET,
                'description' => 'Bayar dengan OVO',
                'icon' => 'fas fa-wallet',
                'logo' => '/images/ewallet/ovo.png',
                'fee_percentage' => 2.5,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 11,
            ],
            [
                'name' => 'DANA',
                'type' => PaymentMethod::TYPE_TRIPAY,
                'code' => 'dana',
                'category' => PaymentMethod::CATEGORY_E_WALLET,
                'description' => 'Bayar dengan DANA',
                'icon' => 'fas fa-mobile-alt',
                'logo' => '/images/ewallet/dana.png',
                'fee_percentage' => 2.5,
                'fee_fixed' => 0,
                'min_amount' => 10000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 12,
            ],
            [
                'name' => 'QRIS',
                'type' => PaymentMethod::TYPE_MIDTRANS,
                'code' => 'qris',
                'category' => PaymentMethod::CATEGORY_QRIS,
                'description' => 'Bayar dengan QRIS (Scan QR)',
                'icon' => 'fas fa-qrcode',
                'logo' => '/images/qris.png',
                'fee_percentage' => 0.7,
                'fee_fixed' => 0,
                'min_amount' => 1000,
                'max_amount' => 2000000,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 20,
            ],
            [
                'name' => 'Virtual Account BCA',
                'type' => PaymentMethod::TYPE_XENDIT,
                'code' => 'va_bca',
                'category' => PaymentMethod::CATEGORY_VIRTUAL_ACCOUNT,
                'description' => 'Virtual Account Bank BCA',
                'icon' => 'fas fa-credit-card',
                'logo' => '/images/banks/bca.png',
                'fee_percentage' => 1.5,
                'fee_fixed' => 4000,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 30,
            ],
            [
                'name' => 'Virtual Account Mandiri',
                'type' => PaymentMethod::TYPE_XENDIT,
                'code' => 'va_mandiri',
                'category' => PaymentMethod::CATEGORY_VIRTUAL_ACCOUNT,
                'description' => 'Virtual Account Bank Mandiri',
                'icon' => 'fas fa-credit-card',
                'logo' => '/images/banks/mandiri.png',
                'fee_percentage' => 1.5,
                'fee_fixed' => 4000,
                'min_amount' => 10000,
                'max_amount' => ********,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 31,
            ],
            [
                'name' => 'Kartu Kredit/Debit',
                'type' => PaymentMethod::TYPE_MIDTRANS,
                'code' => 'credit_card',
                'category' => PaymentMethod::CATEGORY_CREDIT_CARD,
                'description' => 'Bayar dengan Kartu Kredit atau Debit',
                'icon' => 'fas fa-credit-card',
                'logo' => '/images/credit-card.png',
                'fee_percentage' => 2.9,
                'fee_fixed' => 2000,
                'min_amount' => 10000,
                'max_amount' => *********,
                'is_manual' => false,
                'is_active' => false,
                'sort_order' => 40,
            ],
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['code' => $method['code']],
                $method
            );
        }

        $this->command->info('Payment methods seeded successfully!');
    }
}

<?php

namespace App\Mail;

use App\Models\Order;
use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class TicketPurchasedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $order;
    public $tickets;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order)
    {
        $this->order = $order->load(['event', 'user', 'tickets']);
        $this->tickets = $order->tickets;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'E-Ticket Boarding Pass - ' . $this->order->event->title,
            from: config('mail.from.address', '<EMAIL>'),
            replyTo: config('mail.from.address', '<EMAIL>'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ticket-purchased',
            with: [
                'order' => $this->order,
                'tickets' => $this->tickets,
                'event' => $this->order->event,
                'user' => $this->order->user,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments(): array
    {
        $attachments = [];

        // Attach PDF tickets if they exist
        foreach ($this->tickets as $ticket) {
            if ($ticket->pdf_path && Storage::disk('public')->exists($ticket->pdf_path)) {
                $attachments[] = Attachment::fromStorageDisk('public', $ticket->pdf_path)
                    ->as("E-Ticket-{$ticket->ticket_number}.pdf")
                    ->withMime('application/pdf');
            }
        }

        return $attachments;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'category_id',
        'organizer_id',
        'venue_name',
        'venue_address',
        'city',
        'province',
        'latitude',
        'longitude',
        'start_date',
        'end_date',
        'timezone',
        'price',
        'original_price',
        'total_capacity',
        'available_capacity',
        'min_purchase',
        'max_purchase',
        'poster',
        'gallery',
        'status',
        'is_featured',
        'is_free',
        'requires_approval',
        'meta_title',
        'meta_description',
        'tags',
        'sale_start_date',
        'sale_end_date',
        'view_count',
        'ticket_template_id',
        'ticket_template',
        'template_settings',
        'boarding_pass_template',
        'template_config',
        'auto_generate_tickets',
        'email_tickets_to_buyers',
        'custom_template_id',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'sale_start_date' => 'datetime',
        'sale_end_date' => 'datetime',
        'price' => 'decimal:2',
        'original_price' => 'decimal:2',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'total_capacity' => 'integer',
        'available_capacity' => 'integer',
        'min_purchase' => 'integer',
        'max_purchase' => 'integer',
        'view_count' => 'integer',
        'is_featured' => 'boolean',
        'is_free' => 'boolean',
        'requires_approval' => 'boolean',
        'gallery' => 'array',
        'tags' => 'array',
        'template_settings' => 'array',
        'template_config' => 'array',
        'auto_generate_tickets' => 'boolean',
        'email_tickets_to_buyers' => 'boolean',
    ];

    /**
     * Status constants
     */
    public const STATUS_DRAFT = 'draft';
    public const STATUS_PUBLISHED = 'published';
    public const STATUS_CANCELLED = 'cancelled';
    public const STATUS_COMPLETED = 'completed';

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
            if ($event->available_capacity === null) {
                $event->available_capacity = $event->total_capacity;
            }
            if ($event->view_count === null) {
                $event->view_count = 0;
            }
        });

        static::updating(function ($event) {
            if ($event->isDirty('title') && empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });
    }

    /**
     * Category relationship
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Organizer relationship
     */
    public function organizer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    /**
     * Orders relationship
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Tickets relationship
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Ticket template relationship
     */
    public function ticketTemplate(): BelongsTo
    {
        return $this->belongsTo(TicketTemplate::class, 'ticket_template_id');
    }

    /**
     * Get poster URL
     */
    public function getPosterUrlAttribute(): string
    {
        if ($this->poster && Storage::disk('public')->exists($this->poster)) {
            return Storage::disk('public')->url($this->poster);
        }

        // Default poster with event title
        $title = urlencode($this->title);
        return "https://via.placeholder.com/600x400/A8D5BA/FFFFFF?text={$title}";
    }

    /**
     * Get gallery URLs
     */
    public function getGalleryUrlsAttribute(): array
    {
        if (!$this->gallery) {
            return [];
        }

        return collect($this->gallery)->map(function ($image) {
            if (Storage::disk('public')->exists($image)) {
                return Storage::disk('public')->url($image);
            }
            // Log missing image for debugging
            \Log::warning("Missing gallery image: {$image} for event: {$this->title} (ID: {$this->id})");
            return null;
        })->filter()->values()->toArray();
    }

    /**
     * Clean up gallery by removing missing images
     */
    public function cleanupGallery(): void
    {
        if (!$this->gallery) {
            return;
        }

        $existingImages = collect($this->gallery)->filter(function ($image) {
            return Storage::disk('public')->exists($image);
        })->values()->toArray();

        if (count($existingImages) !== count($this->gallery)) {
            $this->update(['gallery' => $existingImages]);
            \Log::info("Cleaned up gallery for event: {$this->title} (ID: {$this->id})");
        }
    }

    /**
     * Check if event is published
     */
    public function isPublished(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * Check if event is sold out
     */
    public function isSoldOut(): bool
    {
        return $this->available_capacity <= 0;
    }

    /**
     * Check if event sale is active
     */
    public function isSaleActive(): bool
    {
        $now = now();

        if ($this->sale_start_date && $now->isBefore($this->sale_start_date)) {
            return false;
        }

        if ($this->sale_end_date && $now->isAfter($this->sale_end_date)) {
            return false;
        }

        return true;
    }

    /**
     * Check if event has started
     */
    public function hasStarted(): bool
    {
        return now()->isAfter($this->start_date);
    }

    /**
     * Check if event has ended
     */
    public function hasEnded(): bool
    {
        return now()->isAfter($this->end_date);
    }

    /**
     * Get tickets sold count
     */
    public function getTicketsSoldAttribute(): int
    {
        return $this->total_capacity - $this->available_capacity;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->is_free) {
            return 'Gratis';
        }

        return 'Rp ' . number_format($this->price, 0, ',', '.');
    }

    /**
     * Get discount percentage
     */
    public function getDiscountPercentageAttribute(): ?int
    {
        if (!$this->original_price || $this->original_price <= $this->price) {
            return null;
        }

        return round((($this->original_price - $this->price) / $this->original_price) * 100);
    }

    /**
     * Scope for published tickets
     */
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    /**
     * Scope for featured tickets
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for upcoming tickets
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now());
    }

    /**
     * Scope for tickets in specific city
     */
    public function scopeInCity($query, string $city)
    {
        return $query->where('city', 'like', "%{$city}%");
    }

    /**
     * Scope for search
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('venue_name', 'like', "%{$search}%")
              ->orWhere('city', 'like', "%{$search}%");
        });
    }

    /**
     * Get route key name for model binding
     */
    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Wishlist relationship (many-to-many with users)
     */
    public function wishlistedBy(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_wishlist', 'event_id', 'user_id')
            ->withTimestamps();
    }

    /**
     * Get event status badge color
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'gray',
            'published' => 'green',
            'cancelled' => 'red',
            'completed' => 'blue',
            default => 'gray'
        };
    }

    /**
     * Get event availability status
     */
    public function getAvailabilityStatusAttribute(): string
    {
        if ($this->isSoldOut()) {
            return 'sold_out';
        }

        if (!$this->isSaleActive()) {
            return 'sale_not_active';
        }

        if ($this->hasStarted()) {
            return 'started';
        }

        if ($this->available_capacity <= 10) {
            return 'limited';
        }

        return 'available';
    }

    /**
     * Get SEO meta title
     */
    public function getSeoTitleAttribute(): string
    {
        return $this->meta_title ?: $this->title . ' - TiXara';
    }

    /**
     * Get SEO meta description
     */
    public function getSeoDescriptionAttribute(): string
    {
        return $this->meta_description ?: Str::limit(strip_tags($this->description), 160);
    }

    /**
     * Get event duration in hours
     */
    public function getDurationInHoursAttribute(): float
    {
        return $this->start_date->diffInHours($this->end_date);
    }

    /**
     * Check if user can purchase tickets
     */
    public function canPurchaseTickets(?User $user = null): bool
    {
        if (!$this->isPublished()) {
            return false;
        }

        if ($this->isSoldOut()) {
            return false;
        }

        if (!$this->isSaleActive()) {
            return false;
        }

        if ($this->hasStarted()) {
            return false;
        }

        return true;
    }

    /**
     * Check if event is in user's wishlist
     */
    public function isInWishlist(?User $user = null): bool
    {
        if (!$user) {
            $user = auth()->user();
        }

        if (!$user) {
            return false;
        }

        return $this->wishlistedBy()->where('user_id', $user->id)->exists();
    }

    /**
     * Get wishlist count for this event
     */
    public function getWishlistCountAttribute(): int
    {
        return $this->wishlistedBy()->count();
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'position',
        'image_url',
        'click_url',
        'targeting',
        'cost_per_click',
        'cost_per_impression',
        'daily_budget',
        'total_budget',
        'spent_amount',
        'impressions',
        'clicks',
        'priority',
        'is_active',
        'is_approved',
        'start_date',
        'end_date',
        'advertiser_id',
        'event_id',
        'status',
        'admin_notes',
    ];

    protected $casts = [
        'targeting' => 'array',
        'cost_per_click' => 'decimal:2',
        'cost_per_impression' => 'decimal:2',
        'daily_budget' => 'decimal:2',
        'total_budget' => 'decimal:2',
        'spent_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
    ];

    /**
     * Get the advertiser (user) that owns the advertisement
     */
    public function advertiser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'advertiser_id');
    }

    /**
     * Get the event associated with the advertisement
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scope for active advertisements
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for approved advertisements
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope for current advertisements (within date range)
     */
    public function scopeCurrent($query)
    {
        $now = now();
        return $query->where(function($q) use ($now) {
            $q->where('start_date', '<=', $now)
              ->where('end_date', '>=', $now);
        });
    }

    /**
     * Scope for advertisements by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for advertisements by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Get formatted budget
     */
    public function getFormattedDailyBudgetAttribute()
    {
        return 'Rp ' . number_format($this->daily_budget, 0, ',', '.');
    }

    /**
     * Get formatted total budget
     */
    public function getFormattedTotalBudgetAttribute()
    {
        return 'Rp ' . number_format($this->total_budget, 0, ',', '.');
    }

    /**
     * Get formatted spent amount
     */
    public function getFormattedSpentAmountAttribute()
    {
        return 'Rp ' . number_format($this->spent_amount, 0, ',', '.');
    }

    /**
     * Get click-through rate
     */
    public function getCtrAttribute()
    {
        if ($this->impressions == 0) {
            return 0;
        }
        return round(($this->clicks / $this->impressions) * 100, 2);
    }

    /**
     * Get cost per click
     */
    public function getCpcAttribute()
    {
        if ($this->clicks == 0) {
            return 0;
        }
        return round($this->spent_amount / $this->clicks, 2);
    }

    /**
     * Get cost per mille (1000 impressions)
     */
    public function getCpmAttribute()
    {
        if ($this->impressions == 0) {
            return 0;
        }
        return round(($this->spent_amount / $this->impressions) * 1000, 2);
    }

    /**
     * Check if advertisement is currently running
     */
    public function getIsRunningAttribute()
    {
        $now = now();
        return $this->is_active &&
               $this->is_approved &&
               $this->start_date <= $now &&
               $this->end_date >= $now &&
               $this->status === 'approved';
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'pending' => 'yellow',
            'approved' => 'green',
            'rejected' => 'red',
            'paused' => 'gray',
            'completed' => 'blue',
            default => 'gray'
        };
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute()
    {
        return match($this->type) {
            'banner' => 'Banner Ad',
            'sponsored_event' => 'Sponsored Event',
            'popup' => 'Popup Ad',
            'sidebar' => 'Sidebar Ad',
            default => ucfirst($this->type)
        };
    }

    /**
     * Get position label
     */
    public function getPositionLabelAttribute()
    {
        return match($this->position) {
            'top' => 'Top of Page',
            'bottom' => 'Bottom of Page',
            'sidebar' => 'Sidebar',
            'popup' => 'Popup',
            'between_events' => 'Between Events',
            default => ucfirst($this->position)
        };
    }

    /**
     * Record impression
     */
    public function recordImpression()
    {
        $this->increment('impressions');

        // Calculate cost for impression
        if ($this->cost_per_impression > 0) {
            $this->increment('spent_amount', $this->cost_per_impression);
        }
    }

    /**
     * Record click
     */
    public function recordClick()
    {
        $this->increment('clicks');

        // Calculate cost for click
        if ($this->cost_per_click > 0) {
            $this->increment('spent_amount', $this->cost_per_click);
        }
    }

    /**
     * Check if budget is exceeded
     */
    public function isBudgetExceeded()
    {
        return $this->spent_amount >= $this->total_budget;
    }

    /**
     * Get remaining budget
     */
    public function getRemainingBudgetAttribute()
    {
        return max(0, $this->total_budget - $this->spent_amount);
    }

    /**
     * Get budget usage percentage
     */
    public function getBudgetUsagePercentageAttribute()
    {
        if ($this->total_budget == 0) {
            return 0;
        }
        return round(($this->spent_amount / $this->total_budget) * 100, 1);
    }
}

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Services\TicketGeneratorService;
use App\Services\TicketValidationService;
use Illuminate\Support\Str;

class TestETicketSystem extends Command
{
    protected $signature = 'test:eticket-system';
    protected $description = 'Test the complete E-Ticket Boarding Pass system';

    public function handle()
    {
        $this->info('=== TESTING E-TICKET BOARDING PASS SYSTEM ===');
        $this->newLine();

        // 1. Create test users
        $this->info('1. Creating test users...');
        $organizer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Organizer',
                'password' => bcrypt('password'),
                'role' => 'penjual',
                'badge_level_id' => 4,
                'email_verified_at' => now()
            ]
        );

        $customer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Customer',
                'password' => bcrypt('password'),
                'role' => 'pembeli',
                'badge_level_id' => 1,
                'email_verified_at' => now()
            ]
        );

        $staff = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Staff',
                'password' => bcrypt('password'),
                'role' => 'staff',
                'badge_level_id' => 3,
                'email_verified_at' => now()
            ]
        );

        $this->info('✓ Users created successfully');
        $this->newLine();

        // 2. Create test category
        $this->info('2. Creating test category...');
        $category = \App\Models\Category::firstOrCreate(
            ['name' => 'Test Category'],
            [
                'description' => 'Category for testing purposes',
                'slug' => 'test-category',
                'is_active' => true
            ]
        );
        $this->info("✓ Category created: {$category->name} (ID: {$category->id})");

        // 3. Create test events with different templates
        $this->info('3. Creating test events with different templates...');

        $templates = ['unix', 'minimalist', 'pro', 'custom'];
        $events = [];

        foreach ($templates as $template) {
            $event = Event::create([
                'title' => "Test Event - {$template} Template",
                'description' => "Testing event untuk {$template} boarding pass template",
                'category_id' => $category->id,
                'venue_name' => 'Jakarta Convention Center',
                'venue_address' => 'Jl. Gatot Subroto, Jakarta',
                'city' => 'Jakarta',
                'province' => 'DKI Jakarta',
                'start_date' => now()->addDays(7),
                'end_date' => now()->addDays(7)->addHours(3),
                'price' => 150000 + (array_search($template, $templates) * 50000),
                'total_capacity' => 100,
                'available_capacity' => 100,
                'organizer_id' => $organizer->id,
                'boarding_pass_template' => $template,
                'auto_generate_tickets' => true,
                'email_tickets_to_buyers' => true,
                'status' => 'published'
            ]);

            $events[$template] = $event;
            $this->info("✓ Event created: {$event->title} (ID: {$event->id})");
        }

        $this->newLine();

        // 4. Create orders and tickets
        $this->info('4. Creating orders and generating tickets...');

        $ticketGenerator = new TicketGeneratorService();
        $tickets = [];

        foreach ($events as $template => $event) {
            // Create order
            $order = Order::create([
                'order_number' => 'ORD-' . now()->format('Ymd') . '-' . str_pad(array_search($template, $templates) + 1, 3, '0', STR_PAD_LEFT),
                'user_id' => $customer->id,
                'event_id' => $event->id,
                'total_amount' => $event->price,
                'status' => 'completed',
                'payment_status' => 'paid'
            ]);

            // Create ticket
            $ticket = Ticket::create([
                'ticket_number' => 'TIK-' . now()->format('Ymd') . '-' . str_pad(array_search($template, $templates) + 1, 3, '0', STR_PAD_LEFT),
                'qr_code' => 'QR-' . strtoupper(substr($template, 0, 3)) . '-' . Str::random(8),
                'event_id' => $event->id,
                'buyer_id' => $customer->id,
                'user_id' => $customer->id,
                'order_id' => $order->id,
                'attendee_name' => $customer->name,
                'attendee_email' => $customer->email,
                'price' => $event->price,
                'total_paid' => $event->price,
                'status' => 'active'
            ]);

            // Generate boarding pass ID and PDF
            try {
                $pdfPath = $ticketGenerator->generateTicketPdfFromEvent($ticket);
                $tickets[$template] = $ticket->fresh();
                $this->info("✓ Ticket generated: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})");
                $this->line("  PDF: {$pdfPath}");
            } catch (\Exception $e) {
                $this->error("✗ Error generating ticket: {$e->getMessage()}");
            }
        }

        $this->newLine();

        // 5. Test authenticity checker
        $this->info('5. Testing Authenticity Checker...');

        $validationService = new TicketValidationService();

        foreach ($tickets as $template => $ticket) {
            $this->line("Testing {$template} template ticket:");

            // Test with ticket number
            $result = $validationService->checkAuthenticity($ticket->ticket_number);
            $this->line("  Ticket Number Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED'));

            // Test with boarding pass ID
            $result = $validationService->checkAuthenticity($ticket->boarding_pass_id);
            $this->line("  Boarding Pass ID Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED'));

            // Test with QR code
            $result = $validationService->checkAuthenticity($ticket->qr_code);
            $this->line("  QR Code Check: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED'));

            $this->newLine();
        }

        // 6. Test validation (marking as used)
        $this->info('6. Testing ticket validation (marking as used)...');

        $testTicket = $tickets['unix']; // Use unix template ticket for validation test
        $result = $validationService->validateByTicketNumber($testTicket->ticket_number, $staff);

        $this->line("Validation Result: " . ($result['success'] ? '✓ SUCCESS' : '✗ FAILED'));
        $this->line("Message: {$result['message']}");

        // Test authenticity check on used ticket
        $this->line("\nTesting authenticity check on used ticket:");
        $result = $validationService->checkAuthenticity($testTicket->ticket_number);
        $this->line("Used Ticket Authenticity: " . ($result['success'] ? '✓ AUTHENTIC' : '✗ FAILED'));
        $this->line("Status: {$result['data']['ticket_status']}");

        $this->newLine();

        // 7. Test fake ticket
        $this->info('7. Testing fake ticket detection...');

        $fakeTicketNumber = 'TIK-20250127-FAKE';
        $result = $validationService->checkAuthenticity($fakeTicketNumber);
        $this->line("Fake Ticket Check: " . ($result['success'] ? '✗ FAILED (Should be fake)' : '✓ CORRECTLY DETECTED AS FAKE'));
        $this->line("Message: {$result['message']}");

        $this->newLine();

        // 8. Display test URLs
        $this->info('8. Test URLs for manual testing:');
        $this->line('Home: http://localhost:8000');
        $this->line('Login (Organizer): http://localhost:8000/login (<EMAIL> / password)');
        $this->line('Login (Customer): http://localhost:8000/login (<EMAIL> / password)');
        $this->line('Login (Staff): http://localhost:8000/login (<EMAIL> / password)');
        $this->line('Authenticity Checker: http://localhost:8000/authenticity-check');
        $this->line('Staff Validation: http://localhost:8000/validation');

        $this->newLine();
        $this->info('Template Preview URLs:');
        foreach ($templates as $template) {
            $this->line("- {$template}: http://localhost:8000/organizer/events/templates/boarding-pass/{$template}");
        }

        $this->newLine();
        $this->info('Test Ticket Numbers for Authenticity Check:');
        foreach ($tickets as $template => $ticket) {
            $this->line("- {$template}: {$ticket->ticket_number} (Boarding Pass: {$ticket->boarding_pass_id})");
        }

        $this->newLine();
        $this->info('=== TESTING COMPLETED ===');
        $this->line('You can now test the system manually using the URLs above.');

        return 0;
    }
}

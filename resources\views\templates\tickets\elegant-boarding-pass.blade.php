<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elegant Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800&family=Inter:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
            color: #831843;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .boarding-container {
            max-width: 900px;
            width: 100%;
            background: white;
            border-radius: 0;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(131, 24, 67, 0.25);
            border: 3px solid #ec4899;
            position: relative;
        }

        .elegant-border {
            position: absolute;
            top: 12px;
            left: 12px;
            right: 12px;
            bottom: 12px;
            border: 1px solid #f9a8d4;
            pointer-events: none;
        }

        .boarding-header {
            background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .boarding-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="elegant-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M20 0L40 20L20 40L0 20Z" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="200" height="200" fill="url(%23elegant-pattern)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .header-title {
            font-family: 'Playfair Display', serif;
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 8px;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header-subtitle {
            font-size: 14px;
            font-weight: 300;
            opacity: 0.95;
            letter-spacing: 3px;
            text-transform: uppercase;
        }

        .ornament {
            width: 60px;
            height: 2px;
            background: white;
            margin: 16px auto;
            position: relative;
        }

        .ornament::before,
        .ornament::after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            top: -3px;
        }

        .ornament::before { left: -12px; }
        .ornament::after { right: -12px; }

        .boarding-body {
            padding: 48px;
            background: linear-gradient(135deg, #fefefe 0%, #fdf2f8 100%);
        }

        .event-showcase {
            text-align: center;
            margin-bottom: 48px;
            padding: 32px;
            background: white;
            border: 2px solid #f9a8d4;
            position: relative;
        }

        .event-showcase::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #fce7f3;
        }

        .event-title {
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 700;
            color: #831843;
            margin-bottom: 16px;
            line-height: 1.2;
            position: relative;
            z-index: 2;
        }

        .event-category {
            display: inline-block;
            background: linear-gradient(135deg, #ec4899, #be185d);
            color: white;
            padding: 8px 24px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            z-index: 2;
        }

        .boarding-main {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 48px;
            align-items: start;
        }

        .details-panel {
            background: white;
            padding: 40px;
            border: 2px solid #f9a8d4;
            position: relative;
        }

        .details-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #fce7f3;
        }

        .panel-title {
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 700;
            color: #ec4899;
            margin-bottom: 32px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .details-grid {
            display: grid;
            gap: 24px;
            position: relative;
            z-index: 2;
        }

        .detail-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
        }

        .detail-item {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #fce7f3;
        }

        .detail-label {
            font-size: 11px;
            font-weight: 600;
            color: #ec4899;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 8px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 500;
            color: #831843;
            line-height: 1.3;
        }

        .detail-value.highlight {
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 700;
            color: #ec4899;
        }

        .qr-panel {
            background: white;
            padding: 40px;
            text-align: center;
            border: 2px solid #f9a8d4;
            position: relative;
        }

        .qr-panel::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid #fce7f3;
        }

        .qr-title {
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            font-weight: 700;
            color: #ec4899;
            margin-bottom: 24px;
            position: relative;
            z-index: 2;
        }

        .qr-code {
            width: 160px;
            height: 160px;
            background: linear-gradient(135deg, #831843, #be185d);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            text-align: center;
            line-height: 1.4;
            margin: 0 auto 24px;
            position: relative;
            z-index: 2;
            border: 3px solid #f9a8d4;
        }

        .qr-code::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border: 1px solid #fce7f3;
        }

        .boarding-id {
            font-size: 16px;
            font-weight: 600;
            color: #ec4899;
            font-family: 'Playfair Display', serif;
            background: #fdf2f8;
            padding: 16px 20px;
            border: 2px solid #f9a8d4;
            margin-top: 20px;
            position: relative;
            z-index: 2;
        }

        .elegant-footer {
            background: linear-gradient(135deg, #fdf2f8, #fce7f3);
            padding: 24px 48px;
            border-top: 2px solid #f9a8d4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-label {
            font-size: 10px;
            color: #be185d;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
        }

        .footer-value {
            font-size: 13px;
            font-weight: 500;
            color: #831843;
        }

        .tixara-elegant {
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 800;
            color: #ec4899;
            letter-spacing: 1px;
        }

        .decorative-corner {
            position: absolute;
            width: 20px;
            height: 20px;
            border: 2px solid #f9a8d4;
        }

        .decorative-corner.top-left {
            top: 24px;
            left: 24px;
            border-right: none;
            border-bottom: none;
        }

        .decorative-corner.top-right {
            top: 24px;
            right: 24px;
            border-left: none;
            border-bottom: none;
        }

        .decorative-corner.bottom-left {
            bottom: 24px;
            left: 24px;
            border-right: none;
            border-top: none;
        }

        .decorative-corner.bottom-right {
            bottom: 24px;
            right: 24px;
            border-left: none;
            border-top: none;
        }

        @media (max-width: 768px) {
            .boarding-main {
                grid-template-columns: 1fr;
                gap: 32px;
            }
            
            .detail-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-body {
                padding: 32px 24px;
            }
            
            .elegant-footer {
                flex-direction: column;
                text-align: center;
                padding: 24px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 3px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="elegant-border"></div>
        <div class="decorative-corner top-left"></div>
        <div class="decorative-corner top-right"></div>
        <div class="decorative-corner bottom-left"></div>
        <div class="decorative-corner bottom-right"></div>

        <div class="boarding-header">
            <div class="header-content">
                <div class="header-title">ELEGANT</div>
                <div class="ornament"></div>
                <div class="header-subtitle">Sophisticated Boarding Pass</div>
            </div>
        </div>

        <div class="boarding-body">
            <div class="event-showcase">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Elegant Event' }}</div>
            </div>

            <div class="boarding-main">
                <div class="details-panel">
                    <div class="panel-title">Event Details</div>
                    <div class="details-grid">
                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Event Date</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('l') }}<br>{{ $ticket->event->start_date->format('d F Y') }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Event Time</div>
                                <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }}<br>WIB</div>
                            </div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Venue Location</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>

                        <div class="detail-item">
                            <div class="detail-label">Distinguished Guest</div>
                            <div class="detail-value highlight">{{ $ticket->attendee_name }}</div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Ticket Reference</div>
                                <div class="detail-value">{{ $ticket->ticket_number }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Seating</div>
                                <div class="detail-value">{{ $ticket->seat_number ?? 'Premium' }}</div>
                            </div>
                        </div>

                        <div class="detail-row">
                            <div class="detail-item">
                                <div class="detail-label">Order Reference</div>
                                <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">Investment</div>
                                <div class="detail-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="qr-panel">
                    <div class="qr-title">Validation Code</div>
                    <div class="qr-code">
                        ELEGANT<br>
                        QR CODE<br>
                        VALIDATION
                    </div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-ELG-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="elegant-footer">
            <div class="footer-info">
                <div class="footer-label">Issued</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y H:i') ?? now()->format('d M Y H:i') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Category</div>
                <div class="footer-value">{{ $ticket->event->category->name ?? 'Elegant' }}</div>
            </div>
            <div class="tixara-elegant">TiXara Elegant</div>
        </div>
    </div>
</body>
</html>

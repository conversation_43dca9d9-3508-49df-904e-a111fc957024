<?php

namespace App\Http\Controllers\Organizer;

use App\Http\Controllers\Controller;
use App\Models\Voucher;
use App\Models\VoucherUsage;
use App\Models\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class VoucherController extends Controller
{
    /**
     * Display organizer's vouchers
     */
    public function index(Request $request)
    {
        $query = Voucher::where('organizer_id', Auth::id())
                       ->with(['event', 'usages']);

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by event
        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        $vouchers = $query->latest()->paginate(15);

        // Get organizer's events for filter
        $events = Event::where('organizer_id', Auth::id())
                      ->select('id', 'title')
                      ->get();

        // Statistics
        $stats = [
            'total_vouchers' => Voucher::where('organizer_id', Auth::id())->count(),
            'active_vouchers' => Voucher::where('organizer_id', Auth::id())->where('is_active', true)->count(),
            'total_usage' => VoucherUsage::whereHas('voucher', function($q) {
                $q->where('organizer_id', Auth::id());
            })->count(),
            'total_discount' => VoucherUsage::whereHas('voucher', function($q) {
                $q->where('organizer_id', Auth::id());
            })->sum('discount_amount'),
        ];

        return view('pages.organizer.vouchers.index', compact('vouchers', 'events', 'stats'));
    }

    /**
     * Show create voucher form
     */
    public function create()
    {
        $events = Event::where('organizer_id', Auth::id())
                      ->where('status', 'published')
                      ->select('id', 'title')
                      ->get();

        return view('pages.organizer.vouchers.create', compact('events'));
    }

    /**
     * Store new voucher
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:vouchers,code',
            'description' => 'nullable|string|max:500',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'minimum_purchase' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'event_id' => 'nullable|exists:events,id',
            'is_active' => 'boolean',
        ]);

        // Validate event ownership
        if ($request->event_id) {
            $event = Event::where('id', $request->event_id)
                         ->where('organizer_id', Auth::id())
                         ->first();
            if (!$event) {
                return back()->withErrors(['event_id' => 'Invalid event selected.']);
            }
        }

        $voucher = Voucher::create([
            'organizer_id' => Auth::id(),
            'name' => $request->name,
            'code' => strtoupper($request->code),
            'description' => $request->description,
            'type' => $request->type,
            'value' => $request->value,
            'minimum_purchase' => $request->minimum_purchase,
            'maximum_discount' => $request->maximum_discount,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_user' => $request->usage_limit_per_user,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'event_id' => $request->event_id,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('organizer.vouchers.show', $voucher)
                        ->with('success', 'Voucher created successfully!');
    }

    /**
     * Show voucher details
     */
    public function show(Voucher $voucher)
    {
        // Ensure voucher belongs to organizer
        if ($voucher->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this voucher.');
        }

        $voucher->load(['event', 'usages.user', 'usages.order']);

        // Usage statistics
        $usageStats = [
            'total_usage' => $voucher->usages->count(),
            'unique_users' => $voucher->usages->unique('user_id')->count(),
            'total_discount' => $voucher->usages->sum('discount_amount'),
            'average_discount' => $voucher->usages->avg('discount_amount'),
            'usage_percentage' => $voucher->usage_limit ? 
                round(($voucher->usages->count() / $voucher->usage_limit) * 100, 1) : 0,
        ];

        return view('pages.organizer.vouchers.show', compact('voucher', 'usageStats'));
    }

    /**
     * Show edit voucher form
     */
    public function edit(Voucher $voucher)
    {
        // Ensure voucher belongs to organizer
        if ($voucher->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this voucher.');
        }

        $events = Event::where('organizer_id', Auth::id())
                      ->where('status', 'published')
                      ->select('id', 'title')
                      ->get();

        return view('pages.organizer.vouchers.edit', compact('voucher', 'events'));
    }

    /**
     * Update voucher
     */
    public function update(Request $request, Voucher $voucher)
    {
        // Ensure voucher belongs to organizer
        if ($voucher->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this voucher.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:vouchers,code,' . $voucher->id,
            'description' => 'nullable|string|max:500',
            'type' => 'required|in:percentage,fixed',
            'value' => 'required|numeric|min:0',
            'minimum_purchase' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'event_id' => 'nullable|exists:events,id',
            'is_active' => 'boolean',
        ]);

        // Validate event ownership
        if ($request->event_id) {
            $event = Event::where('id', $request->event_id)
                         ->where('organizer_id', Auth::id())
                         ->first();
            if (!$event) {
                return back()->withErrors(['event_id' => 'Invalid event selected.']);
            }
        }

        $voucher->update([
            'name' => $request->name,
            'code' => strtoupper($request->code),
            'description' => $request->description,
            'type' => $request->type,
            'value' => $request->value,
            'minimum_purchase' => $request->minimum_purchase,
            'maximum_discount' => $request->maximum_discount,
            'usage_limit' => $request->usage_limit,
            'usage_limit_per_user' => $request->usage_limit_per_user,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'event_id' => $request->event_id,
            'is_active' => $request->boolean('is_active'),
        ]);

        return redirect()->route('organizer.vouchers.show', $voucher)
                        ->with('success', 'Voucher updated successfully!');
    }

    /**
     * Delete voucher
     */
    public function destroy(Voucher $voucher)
    {
        // Ensure voucher belongs to organizer
        if ($voucher->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this voucher.');
        }

        // Check if voucher has been used
        if ($voucher->usages()->count() > 0) {
            return back()->with('error', 'Cannot delete voucher that has been used.');
        }

        $voucher->delete();

        return redirect()->route('organizer.vouchers.index')
                        ->with('success', 'Voucher deleted successfully!');
    }

    /**
     * Toggle voucher status
     */
    public function toggleStatus(Voucher $voucher)
    {
        // Ensure voucher belongs to organizer
        if ($voucher->organizer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this voucher.');
        }

        $voucher->update([
            'is_active' => !$voucher->is_active
        ]);

        $status = $voucher->is_active ? 'activated' : 'deactivated';

        return response()->json([
            'success' => true,
            'message' => "Voucher {$status} successfully!",
            'is_active' => $voucher->is_active
        ]);
    }

    /**
     * Generate voucher code
     */
    public function generateCode()
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (Voucher::where('code', $code)->exists());

        return response()->json([
            'success' => true,
            'code' => $code
        ]);
    }

    /**
     * Export vouchers
     */
    public function export(Request $request)
    {
        $query = Voucher::where('organizer_id', Auth::id())
                       ->with(['event', 'usages']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        $vouchers = $query->get();

        $csvData = [];
        $csvData[] = [
            'Voucher Code',
            'Name',
            'Type',
            'Value',
            'Event',
            'Usage Count',
            'Usage Limit',
            'Total Discount',
            'Status',
            'Start Date',
            'End Date'
        ];

        foreach ($vouchers as $voucher) {
            $csvData[] = [
                $voucher->code,
                $voucher->name,
                ucfirst($voucher->type),
                $voucher->type === 'percentage' ? $voucher->value . '%' : 'Rp ' . number_format($voucher->value, 0, ',', '.'),
                $voucher->event ? $voucher->event->title : 'All Events',
                $voucher->usages->count(),
                $voucher->usage_limit ?? 'Unlimited',
                'Rp ' . number_format($voucher->usages->sum('discount_amount'), 0, ',', '.'),
                $voucher->is_active ? 'Active' : 'Inactive',
                $voucher->start_date->format('Y-m-d'),
                $voucher->end_date->format('Y-m-d')
            ];
        }

        $filename = 'vouchers_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

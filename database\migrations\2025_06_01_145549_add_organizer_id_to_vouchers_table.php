<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vouchers', function (Blueprint $table) {
            $table->foreignId('organizer_id')->nullable()->after('created_by_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('event_id')->nullable()->after('organizer_id')->constrained('events')->onDelete('cascade');
            $table->decimal('minimum_purchase', 12, 2)->nullable()->after('event_id');
            $table->decimal('maximum_discount', 12, 2)->nullable()->after('minimum_purchase');
            $table->date('start_date')->nullable()->after('maximum_discount');
            $table->date('end_date')->nullable()->after('start_date');

            $table->index(['organizer_id']);
            $table->index(['event_id']);
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vouchers', function (Blueprint $table) {
            $table->dropForeign(['organizer_id']);
            $table->dropForeign(['event_id']);
            $table->dropIndex(['organizer_id']);
            $table->dropIndex(['event_id']);
            $table->dropColumn(['organizer_id', 'event_id', 'minimum_purchase', 'maximum_discount', 'start_date', 'end_date']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if table exists before trying to modify it
        if (Schema::hasTable('user_badge_level')) {
            Schema::table('user_badge_level', function (Blueprint $table) {
                if (!Schema::hasColumn('user_badge_level', 'is_default')) {
                    $table->boolean('is_default')->default(false)->after('is_active');
                }
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        if (Schema::hasTable('user_badge_level') && Schema::hasColumn('user_badge_level', 'is_default')) {
            Schema::table('user_badge_level', function (Blueprint $table) {
                $table->dropColumn('is_default');
            });
        }
    }
};

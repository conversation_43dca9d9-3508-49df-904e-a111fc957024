@extends('layouts.organizer')

@section('title', 'Template Management')

@push('styles')
<style>
.template-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #3B82F6;
}

.template-card.default {
    border-color: #10B981;
    background: linear-gradient(135deg, #F0FDF4 0%, #ECFDF5 100%);
}

.template-preview {
    height: 200px;
    background: #F8FAFC;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    overflow: hidden;
    position: relative;
}

.template-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-preview .placeholder {
    color: #9CA3AF;
    font-size: 48px;
}

.template-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-item {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
}

.template-type-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.type-classic { background: #FEF3C7; color: #92400E; }
.type-unix { background: #D1FAE5; color: #065F46; }
.type-minimal { background: #DBEAFE; color: #1E40AF; }
.type-pro { background: #E0E7FF; color: #5B21B6; }
.type-custom { background: #FDE68A; color: #92400E; }

.default-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: #10B981;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.btn-template {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.btn-edit {
    background: #F59E0B;
    color: white;
    border-color: #F59E0B;
}

.btn-edit:hover {
    background: #D97706;
    color: white;
}

.btn-view {
    background: #6366F1;
    color: white;
    border-color: #6366F1;
}

.btn-view:hover {
    background: #4F46E5;
    color: white;
}

.btn-duplicate {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.btn-duplicate:hover {
    background: #7C3AED;
    color: white;
}

.global-templates {
    background: linear-gradient(135deg, #F3F4F6 0%, #E5E7EB 100%);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 32px;
}

.global-template-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    border: 1px solid #E5E7EB;
    transition: all 0.2s;
}

.global-template-item:hover {
    border-color: #3B82F6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.global-template-preview {
    width: 80px;
    height: 60px;
    background: #F8FAFC;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9CA3AF;
    font-size: 20px;
}

.badge-requirement {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(239, 68, 68, 0.1);
    color: #DC2626;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.badge-requirement.met {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

@media (max-width: 768px) {
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .template-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Template Management</h1>
            <p class="text-gray-600 dark:text-gray-400">Create and manage your ticket templates</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('organizer.templates.create') }}" class="btn btn-primary">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Create Template
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="template-stats">
        <div class="stat-item">
            <div class="stat-value">{{ $stats['total_templates'] }}</div>
            <div class="stat-label">Total Templates</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['active_templates'] }}</div>
            <div class="stat-label">Active Templates</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">{{ $stats['total_usage'] }}</div>
            <div class="stat-label">Total Usage</div>
        </div>
        <div class="stat-item">
            <div class="stat-value">
                @if($stats['can_create_custom'])
                    <span class="text-green-600">✓</span>
                @else
                    <span class="text-red-600">✗</span>
                @endif
            </div>
            <div class="stat-label">Custom Templates</div>
        </div>
    </div>

    <!-- Global Templates -->
    @if($globalTemplates->count() > 0)
        <div class="global-templates">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i data-lucide="globe" class="w-5 h-5 inline mr-2"></i>
                Global Templates
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Templates created by admin, available for all organizers</p>
            
            @foreach($globalTemplates as $template)
                <div class="global-template-item">
                    <div class="global-template-preview">
                        @if($template->preview_image)
                            <img src="{{ $template->preview_url }}" alt="{{ $template->name }}">
                        @else
                            <i data-lucide="image"></i>
                        @endif
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 dark:text-white">{{ $template->name }}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $template->description }}</p>
                        <div class="flex items-center gap-2 mt-2">
                            <span class="template-type-badge type-{{ $template->template_type }}">
                                {{ $template->template_type }}
                            </span>
                            @if($template->template_type === 'custom' && !$stats['can_create_custom'])
                                <span class="badge-requirement">
                                    <i data-lucide="lock" class="w-3 h-3"></i>
                                    Requires Platinum Badge
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <a href="{{ route('organizer.templates.preview', $template) }}" 
                           class="btn-template btn-view" target="_blank">
                            <i data-lucide="eye" class="w-3 h-3"></i>
                            Preview
                        </a>
                        @if($template->template_type !== 'custom' || $stats['can_create_custom'])
                            <form method="POST" action="{{ route('organizer.templates.duplicate', $template) }}" class="inline">
                                @csrf
                                <button type="submit" class="btn-template btn-duplicate">
                                    <i data-lucide="copy" class="w-3 h-3"></i>
                                    Use Template
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Search and Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="Search templates..." 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div>
                <select name="type" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Types</option>
                    @foreach($availableTypes as $key => $name)
                        <option value="{{ $key }}" {{ request('type') === $key ? 'selected' : '' }}>
                            {{ $name }}
                        </option>
                    @endforeach
                </select>
            </div>
            <div>
                <select name="status" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">
                <i data-lucide="search" class="w-4 h-4"></i>
                Search
            </button>
        </form>
    </div>

    <!-- Templates Grid -->
    @if($templates->count() > 0)
        <div class="template-grid">
            @foreach($templates as $template)
                <div class="template-card {{ $template->is_default ? 'default' : '' }} bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    @if($template->is_default)
                        <div class="default-badge">Default</div>
                    @endif
                    
                    <div class="template-type-badge type-{{ $template->template_type }}">
                        {{ $template->template_type }}
                    </div>

                    <!-- Template Preview -->
                    <div class="template-preview">
                        @if($template->preview_image)
                            <img src="{{ $template->preview_url }}" alt="{{ $template->name }}">
                        @else
                            <div class="placeholder">
                                <i data-lucide="image"></i>
                            </div>
                        @endif
                    </div>

                    <!-- Template Info -->
                    <div class="mb-4">
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">{{ $template->name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ $template->description }}</p>

                        <!-- Usage Stats -->
                        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                            <div class="grid grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400">
                                <div>Usage: {{ $template->usage_count ?? 0 }}x</div>
                                <div>Status: 
                                    <span class="font-semibold {{ $template->is_active ? 'text-green-600' : 'text-red-600' }}">
                                        {{ $template->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </div>
                                @if($template->last_used_at)
                                    <div class="col-span-2">Last used: {{ $template->last_used_at->diffForHumans() }}</div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="action-buttons">
                        <a href="{{ route('organizer.templates.show', $template) }}" class="btn-template btn-view">
                            <i data-lucide="eye" class="w-3 h-3"></i>
                            View
                        </a>

                        <a href="{{ route('organizer.templates.edit', $template) }}" class="btn-template btn-edit">
                            <i data-lucide="edit" class="w-3 h-3"></i>
                            Edit
                        </a>

                        <form method="POST" action="{{ route('organizer.templates.duplicate', $template) }}" class="inline">
                            @csrf
                            <button type="submit" class="btn-template btn-duplicate">
                                <i data-lucide="copy" class="w-3 h-3"></i>
                                Duplicate
                            </button>
                        </form>

                        @if(!$template->is_default)
                            <form method="POST" action="{{ route('organizer.templates.set-default', $template) }}" class="inline">
                                @csrf
                                <button type="submit" class="btn-template" style="background: #10B981; color: white;">
                                    <i data-lucide="star" class="w-3 h-3"></i>
                                    Set Default
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($templates->hasPages())
            <div class="flex justify-center">
                {{ $templates->links() }}
            </div>
        @endif
    @else
        <div class="text-center py-12">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i data-lucide="layout-template" class="w-8 h-8 text-gray-400"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Templates Found</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">Get started by creating your first template</p>
            <a href="{{ route('organizer.templates.create') }}" class="btn btn-primary">
                <i data-lucide="plus" class="w-4 h-4"></i>
                Create Template
            </a>
        </div>
    @endif
</div>
@endsection

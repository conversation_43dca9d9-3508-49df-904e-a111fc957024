<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimalist Boarding Pass - {{ $ticket->event->title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            color: #1f2937;
            padding: 20px;
            line-height: 1.5;
        }

        .boarding-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .boarding-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .header-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
            letter-spacing: 2px;
        }

        .header-subtitle {
            font-size: 14px;
            font-weight: 300;
            opacity: 0.9;
        }

        .boarding-body {
            padding: 32px;
        }

        .event-info {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .event-title {
            font-size: 28px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .event-category {
            font-size: 14px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }

        .boarding-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 32px;
            align-items: start;
        }

        .details-section {
            display: grid;
            gap: 20px;
        }

        .detail-group {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .detail-label {
            font-size: 12px;
            font-weight: 500;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .detail-value {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .detail-value.large {
            font-size: 18px;
        }

        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            padding: 24px;
            background: #f9fafb;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
        }

        .qr-code {
            width: 140px;
            height: 140px;
            background: #1f2937;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            line-height: 1.3;
            margin-bottom: 16px;
        }

        .qr-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .boarding-id {
            font-size: 14px;
            font-weight: 600;
            color: #3b82f6;
            font-family: 'JetBrains Mono', monospace;
        }

        .boarding-footer {
            background: #f9fafb;
            padding: 20px 32px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .footer-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .footer-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer-value {
            font-size: 13px;
            font-weight: 500;
            color: #1f2937;
        }

        .tixara-logo {
            font-size: 16px;
            font-weight: 700;
            color: #3b82f6;
            letter-spacing: 1px;
        }

        .perforated-line {
            position: relative;
            margin: 24px 0;
        }

        .perforated-line::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: repeating-linear-gradient(
                to right,
                #d1d5db 0px,
                #d1d5db 8px,
                transparent 8px,
                transparent 16px
            );
        }

        .perforated-line::after {
            content: '✂';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            padding: 0 8px;
            color: #9ca3af;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .boarding-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }
            
            .detail-group {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .boarding-footer {
                flex-direction: column;
                text-align: center;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .boarding-container {
                box-shadow: none;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="boarding-container">
        <div class="boarding-header">
            <div class="header-title">BOARDING PASS</div>
            <div class="header-subtitle">Minimalist Design</div>
        </div>

        <div class="boarding-body">
            <div class="event-info">
                <div class="event-title">{{ $ticket->event->title }}</div>
                <div class="event-category">{{ $ticket->event->category->name ?? 'Event' }}</div>
            </div>

            <div class="boarding-content">
                <div class="details-section">
                    <div class="detail-group">
                        <div class="detail-item">
                            <div class="detail-label">Event Date</div>
                            <div class="detail-value">{{ $ticket->event->start_date->format('d M Y') }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Event Time</div>
                            <div class="detail-value">{{ $ticket->event->start_date->format('H:i') }} WIB</div>
                        </div>
                    </div>

                    <div class="detail-group">
                        <div class="detail-item">
                            <div class="detail-label">Venue</div>
                            <div class="detail-value">{{ $ticket->event->location }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Seat</div>
                            <div class="detail-value">{{ $ticket->seat_number ?? 'General Admission' }}</div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">Attendee Name</div>
                        <div class="detail-value large">{{ $ticket->attendee_name }}</div>
                    </div>

                    <div class="perforated-line"></div>

                    <div class="detail-group">
                        <div class="detail-item">
                            <div class="detail-label">Ticket Number</div>
                            <div class="detail-value">{{ $ticket->ticket_number }}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Order Number</div>
                            <div class="detail-value">{{ $ticket->order->order_number ?? 'N/A' }}</div>
                        </div>
                    </div>
                </div>

                <div class="qr-section">
                    <div class="qr-code">
                        QR CODE<br>
                        SCAN FOR<br>
                        VALIDATION
                    </div>
                    <div class="qr-label">Scan to Validate</div>
                    <div class="boarding-id">{{ $ticket->boarding_pass_id ?? 'BP-MIN-' . date('ymd') . '-' . strtoupper(substr(md5($ticket->id), 0, 6)) }}</div>
                </div>
            </div>
        </div>

        <div class="boarding-footer">
            <div class="footer-info">
                <div class="footer-label">Issued Date</div>
                <div class="footer-value">{{ $ticket->order->created_at->format('d M Y H:i') ?? now()->format('d M Y H:i') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Price</div>
                <div class="footer-value">Rp {{ number_format($ticket->order->unit_price ?? 0, 0, ',', '.') }}</div>
            </div>
            <div class="footer-info">
                <div class="footer-label">Status</div>
                <div class="footer-value">{{ ucfirst($ticket->status ?? 'Active') }}</div>
            </div>
            <div class="tixara-logo">TiXara</div>
        </div>
    </div>
</body>
</html>

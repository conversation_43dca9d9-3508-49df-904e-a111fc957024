@extends('layouts.app')

@section('title', 'Badge Debug')

@section('content')
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Badge Level Debug</h1>
    
    @auth
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Current User Debug</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-medium mb-2">User Information</h3>
                    <ul class="space-y-1 text-sm">
                        <li><strong>ID:</strong> {{ auth()->user()->id }}</li>
                        <li><strong>Name:</strong> {{ auth()->user()->name }}</li>
                        <li><strong>Email:</strong> {{ auth()->user()->email }}</li>
                        <li><strong>Role:</strong> {{ auth()->user()->role }}</li>
                        <li><strong>Badge Level ID:</strong> {{ auth()->user()->badge_level_id ?? 'NULL' }}</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Badge Level Data</h3>
                    @if(auth()->user()->badgelevel)
                        <ul class="space-y-1 text-sm">
                            <li><strong>Badge ID:</strong> {{ auth()->user()->badgeLevel->id }}</li>
                            <li><strong>Name:</strong> {{ auth()->user()->badgeLevel->name }}</li>
                            <li><strong>Color:</strong> {{ auth()->user()->badgeLevel->color }}</li>
                            <li><strong>Icon:</strong> {{ auth()->user()->badgeLevel->icon }}</li>
                            <li><strong>Description:</strong> {{ auth()->user()->badgeLevel->description }}</li>
                            <li><strong>Is Active:</strong> {{ auth()->user()->badgeLevel->is_active ? 'Yes' : 'No' }}</li>
                        </ul>
                    @else
                        <p class="text-red-500">No badge level found!</p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Badge Component Tests</h2>
            
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium mb-2">Size XS</h3>
                    <x-badge-level :user="auth()->user()" size="xs" />
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Size SM</h3>
                    <x-badge-level :user="auth()->user()" size="sm" />
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Size MD (Default)</h3>
                    <x-badge-level :user="auth()->user()" />
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Size LG</h3>
                    <x-badge-level :user="auth()->user()" size="lg" />
                </div>
                
                <div>
                    <h3 class="font-medium mb-2">Without Tooltip</h3>
                    <x-badge-level :user="auth()->user()" :showTooltip="false" />
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Manual Badge HTML</h2>
            
            @if(auth()->user()->badgelevel)
                @php
                    $badge = auth()->user()->badgeLevel;
                @endphp
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-medium mb-2">Manual Implementation</h3>
                        <div class="inline-flex items-center px-3 py-1 text-sm rounded-full font-semibold text-white shadow-sm"
                             style="background: {{ $badge->color }};">
                            <i class="{{ $badge->icon ?? 'fas fa-medal' }} w-4 h-4 mr-2"></i>
                            <span>{{ $badge->name }}</span>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="font-medium mb-2">Using getBadgeHtml Method</h3>
                        {!! auth()->user()->getBadgeLevelHtml() !!}
                    </div>
                </div>
            @else
                <p class="text-red-500">No badge to display manually</p>
            @endif
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">All Available Badge Level</h2>
            
            @php
                $allBadges = \App\Models\UserBadgeLevel::all();
            @endphp
            
            @if($allBadges->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($allBadges as $badge)
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <div class="inline-flex items-center px-3 py-1 text-sm rounded-full font-semibold text-white shadow-sm"
                                     style="background: {{ $badge->color }};">
                                    <i class="{{ $badge->icon ?? 'fas fa-medal' }} w-4 h-4 mr-2"></i>
                                    <span>{{ $badge->name }}</span>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                <p><strong>ID:</strong> {{ $badge->id }}</p>
                                <p><strong>Color:</strong> {{ $badge->color }}</p>
                                <p><strong>Icon:</strong> {{ $badge->icon }}</p>
                                <p><strong>Active:</strong> {{ $badge->is_active ? 'Yes' : 'No' }}</p>
                                <p><strong>Default:</strong> {{ $badge->is_default ? 'Yes' : 'No' }}</p>
                                <p><strong>Users:</strong> {{ $badge->users()->count() }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-red-500">No badge level found in database!</p>
            @endif
        </div>
        
    @else
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Please login to debug badge level.
        </div>
    @endauth
</div>
@endsection

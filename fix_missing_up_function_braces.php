<?php

echo "🔧 Fixing missing up() function closing braces...\n";

$migrationPath = 'database/migrations/';
$migrationFiles = glob($migrationPath . '*.php');

$fixedCount = 0;
$successCount = 0;

foreach ($migrationFiles as $file) {
    $filename = basename($file);
    $content = file_get_contents($file);
    $originalContent = $content;
    
    // Check if file has the "unexpected token public" issue
    $output = [];
    $returnCode = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
    
    if ($returnCode !== 0 && strpos(implode(' ', $output), 'unexpected token "public"') !== false) {
        echo "Fixing {$filename}...\n";
        
        // Find the pattern where up() function is missing closing brace
        // Look for: });        }    }    /**     * Reverse the migrations.     */ public function down()
        
        $pattern = '/(\s+}\);\s+}\s+}\s+\/\*\*\s+\*\s+Reverse\s+the\s+migrations\.\s+\*\/\s+)(public\s+function\s+down)/s';
        $replacement = '$1' . "\n\n    $2";
        
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            echo "  🔧 Fixed pattern 1 in {$filename}\n";
        }
        
        // Alternative pattern: });        }    }    public function down()
        $pattern2 = '/(\s+}\);\s+}\s+}\s+)(public\s+function\s+down)/s';
        $replacement2 = '$1' . "\n\n    /**\n     * Reverse the migrations.\n     */\n    $2";
        
        if (preg_match($pattern2, $content)) {
            $content = preg_replace($pattern2, $replacement2, $content);
            echo "  🔧 Fixed pattern 2 in {$filename}\n";
        }
        
        // Pattern for missing closing brace before comment
        $pattern3 = '/(\s+}\);\s+}\s+\/\*\*\s+\*\s+Reverse\s+the\s+migrations\.\s+\*\/\s+)(public\s+function\s+down)/s';
        $replacement3 = '$1' . "\n    $2";
        
        if (preg_match($pattern3, $content)) {
            $content = preg_replace($pattern3, $replacement3, $content);
            echo "  🔧 Fixed pattern 3 in {$filename}\n";
        }
        
        // Pattern for completely missing closing brace for up() function
        $pattern4 = '/(\s+}\);\s+}\s+)(\/\*\*\s+\*\s+Reverse\s+the\s+migrations\.\s+\*\/\s+public\s+function\s+down)/s';
        $replacement4 = '$1' . "\n    }\n\n    $2";
        
        if (preg_match($pattern4, $content)) {
            $content = preg_replace($pattern4, $replacement4, $content);
            echo "  🔧 Fixed pattern 4 in {$filename}\n";
        }
        
        // Write the fixed content
        if ($content !== $originalContent) {
            file_put_contents($file, $content);
            $fixedCount++;
            echo "  ✅ Fixed {$filename}\n";
        }
    }
    
    // Validate syntax after fix
    $output = [];
    $returnCode = 0;
    exec("php -l \"{$file}\" 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        $successCount++;
    } else {
        echo "  ❌ Still has error: {$filename} - " . implode(' ', $output) . "\n";
    }
}

echo "\n📊 Summary:\n";
echo "  Files processed: " . count($migrationFiles) . "\n";
echo "  Files fixed: {$fixedCount}\n";
echo "  Files with valid syntax: {$successCount}\n";
echo "  Files with errors: " . (count($migrationFiles) - $successCount) . "\n";

if ($successCount === count($migrationFiles)) {
    echo "\n🎉 All migration files are now syntax-error free!\n";
} else {
    echo "\n⚠️  Some files still have syntax errors.\n";
}

echo "\n✅ Migration syntax fix completed!\n";

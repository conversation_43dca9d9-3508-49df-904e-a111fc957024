<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BoardingPassService;

class FixBoardingPassIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tickets:fix-boarding-pass-ids 
                            {--dry-run : Show what would be fixed without making changes}
                            {--template= : Force specific template for all tickets}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing boarding pass IDs for existing tickets';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎫 Fixing missing boarding pass IDs...');
        
        $boardingPassService = app(BoardingPassService::class);
        
        if ($this->option('dry-run')) {
            $this->info('🔍 DRY RUN MODE - No changes will be made');
            
            $ticketsWithoutBoardingPass = \App\Models\Ticket::whereNull('boarding_pass_id')
                ->orWhere('boarding_pass_id', '')
                ->with('event')
                ->get();
            
            $this->info("Found {$ticketsWithoutBoardingPass->count()} tickets without boarding pass IDs:");
            
            foreach ($ticketsWithoutBoardingPass as $ticket) {
                $template = $this->option('template') ?? $ticket->event->boarding_pass_template ?? 'unix';
                $prefix = $boardingPassService->getTemplatePrefix($template);
                
                $this->line("  - Ticket #{$ticket->ticket_number} (Event: {$ticket->event->title}) -> Would get BP-{$prefix}-XXXXXX-XXXXXX");
            }
            
            return 0;
        }
        
        // Actual fix
        $results = $boardingPassService->fixMissingBoardingPassIds();
        
        $this->info("📊 Results:");
        $this->info("  Total processed: {$results['total_processed']}");
        $this->info("  ✅ Successful: {$results['successful']}");
        $this->info("  ❌ Failed: {$results['failed']}");
        
        if ($results['failed'] > 0) {
            $this->warn("\n⚠️  Some tickets failed to be fixed:");
            foreach ($results['details'] as $detail) {
                if (!$detail['success']) {
                    $this->error("  - Ticket #{$detail['ticket_number']}: {$detail['error']}");
                }
            }
        }
        
        if ($results['successful'] > 0) {
            $this->info("\n✅ Successfully fixed boarding pass IDs:");
            foreach ($results['details'] as $detail) {
                if ($detail['success']) {
                    $this->line("  - Ticket #{$detail['ticket_number']} -> {$detail['boarding_pass_id']} ({$detail['template']})");
                }
            }
        }
        
        $this->info("\n🎉 Boarding pass ID fix completed!");
        
        return 0;
    }
}

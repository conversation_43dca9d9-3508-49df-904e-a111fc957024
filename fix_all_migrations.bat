@echo off
echo Fixing all migration syntax errors...

REM Create a PowerShell script to fix the migrations
echo $files = Get-ChildItem "database\migrations\*.php" > fix_migrations.ps1
echo foreach ($file in $files) { >> fix_migrations.ps1
echo     $content = Get-Content $file.FullName -Raw >> fix_migrations.ps1
echo     $originalContent = $content >> fix_migrations.ps1
echo     # Fix missing closing braces for Schema::create >> fix_migrations.ps1
echo     $content = $content -replace '(\s+\$table-^>timestamp\([^)]+\)-^>useCurrent\(\);\s+)\s*}\s*}\s*', '$1            });        }    }' >> fix_migrations.ps1
echo     # Fix missing closing parenthesis and brace for Schema::create >> fix_migrations.ps1
echo     $content = $content -replace '(\s+\$table-^>[^;]+;\s+)\s*}\s*}\s*', '$1            });        }    }' >> fix_migrations.ps1
echo     # Fix general missing closing braces >> fix_migrations.ps1
echo     $content = $content -replace '(\s+\$table-^>[^;]+;\s+)\s*}\s*}\s*(/\*\*)', '$1            });        }    }    $2' >> fix_migrations.ps1
echo     if ($content -ne $originalContent) { >> fix_migrations.ps1
echo         Set-Content $file.FullName $content >> fix_migrations.ps1
echo         Write-Host "Fixed: $($file.Name)" >> fix_migrations.ps1
echo     } >> fix_migrations.ps1
echo } >> fix_migrations.ps1

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File fix_migrations.ps1

REM Clean up
del fix_migrations.ps1

echo Done fixing migrations!

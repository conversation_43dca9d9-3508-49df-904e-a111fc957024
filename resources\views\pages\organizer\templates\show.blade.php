@extends('layouts.organizer')

@section('title', $template->name)

@push('styles')
<style>
.template-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 32px;
    color: white;
    margin-bottom: 24px;
}

.template-preview-large {
    height: 400px;
    background: #F8FAFC;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.template-preview-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-preview-large .placeholder {
    color: #9CA3AF;
    font-size: 64px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    padding: 24px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    margin: 0 auto 12px;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 4px;
}

.stat-label {
    color: #6B7280;
    font-size: 14px;
    font-weight: 500;
}

.info-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    padding: 24px;
    margin-bottom: 24px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #F3F4F6;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #6B7280;
    font-size: 14px;
}

.info-value {
    color: #1F2937;
    font-size: 14px;
    font-weight: 500;
}

.template-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.type-classic { background: #FEF3C7; color: #92400E; }
.type-unix { background: #D1FAE5; color: #065F46; }
.type-minimal { background: #DBEAFE; color: #1E40AF; }
.type-pro { background: #E0E7FF; color: #5B21B6; }
.type-custom { background: #FDE68A; color: #92400E; }

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.status-active {
    background: #D1FAE5;
    color: #065F46;
}

.status-inactive {
    background: #FEE2E2;
    color: #DC2626;
}

.default-badge {
    background: #10B981;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn-template {
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.btn-edit {
    background: #F59E0B;
    color: white;
    border-color: #F59E0B;
}

.btn-edit:hover {
    background: #D97706;
    color: white;
}

.btn-preview {
    background: #6366F1;
    color: white;
    border-color: #6366F1;
}

.btn-preview:hover {
    background: #4F46E5;
    color: white;
}

.btn-duplicate {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.btn-duplicate:hover {
    background: #7C3AED;
    color: white;
}

.btn-default {
    background: #10B981;
    color: white;
    border-color: #10B981;
}

.btn-default:hover {
    background: #059669;
    color: white;
}

.btn-danger {
    background: #EF4444;
    color: white;
    border-color: #EF4444;
}

.btn-danger:hover {
    background: #DC2626;
    color: white;
}

.events-list {
    max-height: 300px;
    overflow-y: auto;
}

.event-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #F3F4F6;
}

.event-item:last-child {
    border-bottom: none;
}

.event-info {
    flex: 1;
}

.event-title {
    font-weight: 500;
    color: #1F2937;
    margin-bottom: 4px;
}

.event-meta {
    font-size: 12px;
    color: #6B7280;
}

.event-status {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.status-published {
    background: #D1FAE5;
    color: #065F46;
}

.status-draft {
    background: #FEF3C7;
    color: #92400E;
}

.config-preview {
    background: #F8FAFC;
    border-radius: 8px;
    padding: 16px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #374151;
    max-height: 200px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .template-header {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        justify-content: center;
    }
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Template Header -->
    <div class="template-header">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold mb-2">{{ $template->name }}</h1>
                <p class="text-blue-100 mb-4">{{ $template->description }}</p>
                <div class="flex items-center gap-3">
                    <span class="template-type-badge type-{{ $template->template_type }}">
                        {{ ucfirst($template->template_type) }}
                    </span>
                    <span class="status-badge {{ $template->is_active ? 'status-active' : 'status-inactive' }}">
                        <i data-lucide="{{ $template->is_active ? 'check-circle' : 'x-circle' }}" class="w-3 h-3"></i>
                        {{ $template->is_active ? 'Active' : 'Inactive' }}
                    </span>
                    @if($template->is_default)
                        <span class="default-badge">
                            <i data-lucide="star" class="w-3 h-3"></i>
                            Default
                        </span>
                    @endif
                </div>
            </div>
            <div class="action-buttons">
                <a href="{{ route('organizer.templates.preview', $template) }}" target="_blank" class="btn-template btn-preview">
                    <i data-lucide="eye" class="w-4 h-4"></i>
                    Preview
                </a>
                <a href="{{ route('organizer.templates.edit', $template) }}" class="btn-template btn-edit">
                    <i data-lucide="edit" class="w-4 h-4"></i>
                    Edit
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon" style="background: #3B82F6;">
                <i data-lucide="calendar"></i>
            </div>
            <div class="stat-value">{{ $usageStats['total_events'] }}</div>
            <div class="stat-label">Total Events</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="background: #10B981;">
                <i data-lucide="check-circle"></i>
            </div>
            <div class="stat-value">{{ $usageStats['active_events'] }}</div>
            <div class="stat-label">Active Events</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="background: #F59E0B;">
                <i data-lucide="ticket"></i>
            </div>
            <div class="stat-value">{{ $usageStats['total_tickets'] }}</div>
            <div class="stat-label">Total Tickets</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="background: #8B5CF6;">
                <i data-lucide="activity"></i>
            </div>
            <div class="stat-value">{{ $template->usage_count ?? 0 }}</div>
            <div class="stat-label">Usage Count</div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Template Preview -->
        <div class="info-section">
            <div class="section-title">
                <i data-lucide="image" class="w-5 h-5"></i>
                Template Preview
            </div>
            
            <div class="template-preview-large">
                @if($template->preview_image)
                    <img src="{{ $template->preview_url }}" alt="{{ $template->name }}">
                @else
                    <div class="placeholder">
                        <i data-lucide="image"></i>
                    </div>
                @endif
            </div>
            
            <div class="mt-4 text-center">
                <a href="{{ route('organizer.templates.preview', $template) }}" target="_blank" 
                   class="btn-template btn-preview">
                    <i data-lucide="external-link" class="w-4 h-4"></i>
                    Open Full Preview
                </a>
            </div>
        </div>

        <!-- Template Information -->
        <div class="info-section">
            <div class="section-title">
                <i data-lucide="info" class="w-5 h-5"></i>
                Template Information
            </div>
            
            <div class="space-y-4">
                <div class="info-item">
                    <span class="info-label">Template ID</span>
                    <span class="info-value">#{{ $template->id }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Slug</span>
                    <span class="info-value">{{ $template->slug }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Created</span>
                    <span class="info-value">{{ $template->created_at->format('M d, Y H:i') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Last Updated</span>
                    <span class="info-value">{{ $template->updated_at->format('M d, Y H:i') }}</span>
                </div>
                @if($template->last_used_at)
                    <div class="info-item">
                        <span class="info-label">Last Used</span>
                        <span class="info-value">{{ $template->last_used_at->diffForHumans() }}</span>
                    </div>
                @endif
                @if($template->creator)
                    <div class="info-item">
                        <span class="info-label">Created By</span>
                        <span class="info-value">{{ $template->creator->name }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Template Configuration -->
    @if($template->config || $template->custom_config)
        <div class="info-section">
            <div class="section-title">
                <i data-lucide="settings" class="w-5 h-5"></i>
                Template Configuration
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                @if($template->config)
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Basic Configuration</h4>
                        <div class="config-preview">
                            {{ json_encode($template->config, JSON_PRETTY_PRINT) }}
                        </div>
                    </div>
                @endif
                
                @if($template->custom_config)
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Custom Configuration</h4>
                        <div class="config-preview">
                            {{ json_encode($template->custom_config, JSON_PRETTY_PRINT) }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Events Using This Template -->
    <div class="info-section">
        <div class="section-title">
            <i data-lucide="calendar" class="w-5 h-5"></i>
            Events Using This Template ({{ $template->events->count() }})
        </div>
        
        @if($template->events->count() > 0)
            <div class="events-list">
                @foreach($template->events as $event)
                    <div class="event-item">
                        <div class="event-info">
                            <div class="event-title">{{ $event->title }}</div>
                            <div class="event-meta">
                                {{ $event->start_date->format('M d, Y') }} • {{ $event->venue_name }}
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="event-status {{ $event->status === 'published' ? 'status-published' : 'status-draft' }}">
                                {{ ucfirst($event->status) }}
                            </span>
                            <a href="{{ route('organizer.events.show', $event) }}" class="text-blue-600 hover:text-blue-800">
                                <i data-lucide="external-link" class="w-4 h-4"></i>
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i data-lucide="calendar" class="w-6 h-6 text-gray-400"></i>
                </div>
                <p class="text-gray-500">No events are using this template yet</p>
            </div>
        @endif
    </div>

    <!-- Actions -->
    <div class="info-section">
        <div class="section-title">
            <i data-lucide="zap" class="w-5 h-5"></i>
            Template Actions
        </div>
        
        <div class="action-buttons">
            <a href="{{ route('organizer.templates.edit', $template) }}" class="btn-template btn-edit">
                <i data-lucide="edit" class="w-4 h-4"></i>
                Edit Template
            </a>
            
            <form method="POST" action="{{ route('organizer.templates.duplicate', $template) }}" class="inline">
                @csrf
                <button type="submit" class="btn-template btn-duplicate">
                    <i data-lucide="copy" class="w-4 h-4"></i>
                    Duplicate
                </button>
            </form>
            
            @if(!$template->is_default)
                <form method="POST" action="{{ route('organizer.templates.set-default', $template) }}" class="inline">
                    @csrf
                    <button type="submit" class="btn-template btn-default">
                        <i data-lucide="star" class="w-4 h-4"></i>
                        Set as Default
                    </button>
                </form>
            @endif
            
            @if($template->events->count() === 0)
                <form method="POST" action="{{ route('organizer.templates.destroy', $template) }}" class="inline"
                      onsubmit="return confirm('Are you sure you want to delete this template? This action cannot be undone.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn-template btn-danger">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                        Delete Template
                    </button>
                </form>
            @endif
        </div>
        
        @if($template->events->count() > 0)
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center gap-2 text-yellow-800">
                    <i data-lucide="alert-triangle" class="w-4 h-4"></i>
                    <span class="text-sm font-medium">
                        This template cannot be deleted because it's being used by {{ $template->events->count() }} event(s).
                    </span>
                </div>
            </div>
        @endif
    </div>

    <!-- Back to Templates -->
    <div class="text-center">
        <a href="{{ route('organizer.templates.index') }}" class="btn btn-secondary">
            <i data-lucide="arrow-left" class="w-4 h-4"></i>
            Back to Templates
        </a>
    </div>
</div>
@endsection

@extends('layouts.admin')

@section('title', 'Manajemen Level Pengguna')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Manajemen Level Pengguna</h1>
            <p class="text-muted">Kelola level dan status pengguna penjual & admin</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" onclick="autoUpgradeUsers()">
                <i class="fas fa-magic"></i> Auto Upgrade
            </button>
            <button type="button" class="btn btn-info" onclick="exportData()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>

    <!-- Level Statistics -->
    <div class="row mb-4">
        @foreach($level as $levelKey => $levelConfig)
            <div class="col-md-3 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-uppercase mb-1" 
                                     style="color: {{ $levelConfig['color'] }}">
                                    {{ $levelConfig['display_name'] }}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    {{ $leveltats[$levelKey] ?? 0 }}
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="{{ $levelConfig['icon'] }} fa-2x" style="color: {{ $levelConfig['color'] }}"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.user-level.index') }}" class="row">
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">Pencarian</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Nama, email, organisasi...">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="role" class="form-label">Role</label>
                    <select class="form-control" id="role" name="role">
                        <option value="">Semua Role</option>
                        <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                        <option value="penjual" {{ request('role') == 'penjual' ? 'selected' : '' }}>Penjual</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="level" class="form-label">Level</label>
                    <select class="form-control" id="level" name="level">
                        <option value="">Semua Level</option>
                        @foreach($level as $levelKey => $levelConfig)
                            <option value="{{ $levelKey }}" {{ request('level') == $levelKey ? 'selected' : '' }}>
                                {{ $levelConfig['display_name'] }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="{{ route('admin.user-level.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Daftar Pengguna</h6>
            <div>
                <button type="button" class="btn btn-sm btn-warning" onclick="bulkUpdateLevel()">
                    <i class="fas fa-edit"></i> Bulk Update
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th>Pengguna</th>
                            <th>Role</th>
                            <th>Level Saat Ini</th>
                            <th>Organisasi</th>
                            <th>Statistik</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                            <tr>
                                <td>
                                    <input type="checkbox" class="user-checkbox" value="{{ $user->id }}">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" 
                                             class="rounded-circle me-2" width="40" height="40">
                                        <div>
                                            <div class="font-weight-bold">{{ $user->name }}</div>
                                            <div class="text-muted small">{{ $user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-{{ $user->role == 'admin' ? 'danger' : 'primary' }}">
                                        {{ ucfirst($user->role) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge px-3 py-2 rounded-pill text-white me-2" 
                                              style="background: {{ $user->getLevelBackground() }}">
                                            <i class="{{ $user->getLevelConfig()['icon'] ?? 'fas fa-star' }} me-1"></i>
                                            {{ $user->getLevelDisplayName() }}
                                        </span>
                                    </div>
                                </td>
                                <td>{{ $user->organization ?? '-' }}</td>
                                <td>
                                    <div class="small">
                                        <div><strong>Events:</strong> {{ $user->organizedEvents->count() }}</div>
                                        <div><strong>Orders:</strong> {{ $user->orders->count() }}</div>
                                    </div>
                                </td>
                                <td>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                                data-bs-toggle="dropdown">
                                            Ubah Level
                                        </button>
                                        <ul class="dropdown-menu">
                                            @foreach($level as $levelKey => $levelConfig)
                                                @if($levelKey != $user->badge_level)
                                                    <li>
                                                        <a class="dropdown-item" href="#" 
                                                           onclick="updateUserLevel({{ $user->id }}, '{{ $levelKey }}')">
                                                            <i class="{{ $levelConfig['icon'] }} me-2" 
                                                               style="color: {{ $levelConfig['color'] }}"></i>
                                                            {{ $levelConfig['display_name'] }}
                                                        </a>
                                                    </li>
                                                @endif
                                            @endforeach
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>Tidak ada pengguna yang ditemukan</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($users->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $users->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Bulk Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Update Level</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Pilih level baru untuk <span id="selectedCount">0</span> pengguna yang dipilih:</p>
                <select class="form-control" id="bulkLevel">
                    @foreach($level as $levelKey => $levelConfig)
                        <option value="{{ $levelKey }}">{{ $levelConfig['display_name'] }}</option>
                    @endforeach
                </select>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="confirmBulkUpdate()">Update</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Select all functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selected = document.querySelectorAll('.user-checkbox:checked');
    document.getElementById('selectedCount').textContent = selected.length;
}

// Listen for individual checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('user-checkbox')) {
        updateSelectedCount();
    }
});

// Update single user level
async function updateUserLevel(userId, level) {
    if (!confirm('Apakah Anda yakin ingin mengubah level pengguna ini?')) {
        return;
    }

    try {
        const response = await fetch(`/admin/user-level/${userId}/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ badge_level: level })
        });

        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message || 'Terjadi kesalahan');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

// Bulk update
function bulkUpdateLevel() {
    const selected = document.querySelectorAll('.user-checkbox:checked');
    if (selected.length === 0) {
        showAlert('warning', 'Pilih minimal satu pengguna');
        return;
    }
    
    updateSelectedCount();
    new bootstrap.Modal(document.getElementById('bulkUpdateModal')).show();
}

async function confirmBulkUpdate() {
    const selected = Array.from(document.querySelectorAll('.user-checkbox:checked')).map(cb => cb.value);
    const level = document.getElementById('bulkLevel').value;

    try {
        const response = await fetch('/admin/user-level/bulk-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({ 
                user_ids: selected,
                badge_level: level 
            })
        });

        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('bulkUpdateModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message || 'Terjadi kesalahan');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

// Auto upgrade users
async function autoUpgradeUsers() {
    if (!confirm('Apakah Anda yakin ingin melakukan auto-upgrade berdasarkan performa pengguna?')) {
        return;
    }

    try {
        const response = await fetch('/admin/user-level/auto-upgrade', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();
        
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message || 'Terjadi kesalahan');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

// Export data
async function exportData() {
    try {
        const response = await fetch('/admin/user-level/export', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });

        const data = await response.json();
        
        if (data.success) {
            // Download JSON file
            const blob = new Blob([JSON.stringify(data.data, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = data.filename;
            a.click();
            window.URL.revokeObjectURL(url);
            
            showAlert('success', 'Data berhasil diexport');
        } else {
            showAlert('error', 'Gagal export data');
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
}

// Show alert function
function showAlert(type, message) {
    // You can implement your preferred alert system here
    // For now, using simple alert
    alert(message);
}
</script>
@endpush

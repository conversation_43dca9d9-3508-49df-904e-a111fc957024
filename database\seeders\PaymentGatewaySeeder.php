<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentGateway;
use App\Models\PaymentMethod;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Payment Gateways
        $gateways = [
            [
                'code' => 'xendit',
                'name' => 'Xendit',
                'description' => 'Payment gateway by Xendit for Indonesia',
                'provider' => 'xendit',
                'config' => [
                    'sandbox_api_key' => '',
                    'sandbox_secret_key' => '',
                    'production_api_key' => '',
                    'production_secret_key' => '',
                    'webhook_token' => '',
                    'callback_url' => url('/webhooks/xendit'),
                ],
                'is_active' => false,
                'is_production' => false,
                'sort_order' => 1
            ],
            [
                'code' => 'midtrans',
                'name' => 'Midtrans',
                'description' => 'Payment gateway by Midtrans for Indonesia',
                'provider' => 'midtrans',
                'config' => [
                    'sandbox_server_key' => '',
                    'sandbox_client_key' => '',
                    'production_server_key' => '',
                    'production_client_key' => '',
                    'merchant_id' => '',
                    'callback_url' => url('/webhooks/midtrans'),
                    'finish_url' => url('/payment/success'),
                    'error_url' => url('/payment/error'),
                    'unfinish_url' => url('/payment/pending'),
                ],
                'is_active' => false,
                'is_production' => false,
                'sort_order' => 2
            ],
            [
                'code' => 'tripay',
                'name' => 'TriPay',
                'description' => 'Payment gateway by TriPay for Indonesia',
                'provider' => 'tripay',
                'config' => [
                    'sandbox_api_key' => '',
                    'sandbox_private_key' => '',
                    'sandbox_merchant_code' => '',
                    'production_api_key' => '',
                    'production_private_key' => '',
                    'production_merchant_code' => '',
                    'callback_url' => url('/webhooks/tripay'),
                ],
                'is_active' => false,
                'is_production' => false,
                'sort_order' => 3
            ],
            [
                'code' => 'manual',
                'name' => 'Manual Payment',
                'description' => 'Manual payment processing (Bank Transfer, Cash)',
                'provider' => 'manual',
                'config' => [
                    'bank_accounts' => [
                        [
                            'bank_name' => 'Bank BCA',
                            'account_number' => '**********',
                            'account_name' => 'TiXara Indonesia',
                        ],
                        [
                            'bank_name' => 'Bank Mandiri',
                            'account_number' => '**********',
                            'account_name' => 'TiXara Indonesia',
                        ]
                    ]
                ],
                'is_active' => true,
                'is_production' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($gateways as $gateway) {
            PaymentGateway::updateOrCreate(
                ['code' => $gateway['code']],
                $gateway
            );
        }

        // Create Payment Methods
        $xenditGateway = PaymentGateway::where('code', 'xendit')->first();
        $midtransGateway = PaymentGateway::where('code', 'midtrans')->first();
        $tripayGateway = PaymentGateway::where('code', 'tripay')->first();
        $manualGateway = PaymentGateway::where('code', 'manual')->first();

        $paymentMethods = [
            // Xendit Methods
            [
                'code' => 'xendit_credit_card',
                'name' => 'Kartu Kredit/Debit',
                'description' => 'Visa, Mastercard, JCB, American Express',
                'icon_class' => 'fas fa-credit-card',
                'icon_bg_class' => 'bg-purple-100 text-purple-600',
                'gateway_id' => $xenditGateway->id,
                'fee' => 2.9,
                'fee_type' => 'percentage',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Pembayaran akan diproses secara otomatis setelah kartu kredit/debit diverifikasi.',
                'is_active' => false,
                'sort_order' => 1
            ],
            [
                'code' => 'xendit_ovo',
                'name' => 'OVO',
                'description' => 'Bayar dengan OVO',
                'icon_class' => 'fas fa-mobile-alt',
                'icon_bg_class' => 'bg-purple-100 text-purple-600',
                'gateway_id' => $xenditGateway->id,
                'fee' => 1.5,
                'fee_type' => 'percentage',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Buka aplikasi OVO dan scan QR code atau masukkan kode pembayaran.',
                'is_active' => false,
                'sort_order' => 2
            ],
            [
                'code' => 'xendit_dana',
                'name' => 'DANA',
                'description' => 'Bayar dengan DANA',
                'icon_class' => 'fas fa-mobile-alt',
                'icon_bg_class' => 'bg-blue-100 text-blue-600',
                'gateway_id' => $xenditGateway->id,
                'fee' => 1.5,
                'fee_type' => 'percentage',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Buka aplikasi DANA dan scan QR code atau masukkan kode pembayaran.',
                'is_active' => false,
                'sort_order' => 3
            ],

            // Midtrans Methods
            [
                'code' => 'midtrans_gopay',
                'name' => 'GoPay',
                'description' => 'Bayar dengan GoPay',
                'icon_class' => 'fas fa-mobile-alt',
                'icon_bg_class' => 'bg-green-100 text-green-600',
                'gateway_id' => $midtransGateway->id,
                'fee' => 2.0,
                'fee_type' => 'percentage',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Buka aplikasi Gojek/GoPay dan scan QR code.',
                'is_active' => false,
                'sort_order' => 4
            ],
            [
                'code' => 'midtrans_bca_va',
                'name' => 'BCA Virtual Account',
                'description' => 'Transfer melalui BCA Virtual Account',
                'icon_class' => 'fas fa-university',
                'icon_bg_class' => 'bg-blue-100 text-blue-600',
                'gateway_id' => $midtransGateway->id,
                'fee' => 4000,
                'fee_type' => 'fixed',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => '1-3 hours',
                'instructions' => 'Transfer ke nomor Virtual Account yang diberikan melalui ATM, mobile banking, atau internet banking BCA.',
                'is_active' => false,
                'sort_order' => 5
            ],

            // TriPay Methods
            [
                'code' => 'tripay_qris',
                'name' => 'QRIS',
                'description' => 'Bayar dengan QRIS (semua e-wallet)',
                'icon_class' => 'fas fa-qrcode',
                'icon_bg_class' => 'bg-indigo-100 text-indigo-600',
                'gateway_id' => $tripayGateway->id,
                'fee' => 0.7,
                'fee_type' => 'percentage',
                'min_amount' => 1500,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Scan QR code dengan aplikasi e-wallet atau mobile banking yang mendukung QRIS.',
                'is_active' => false,
                'sort_order' => 6
            ],
            [
                'code' => 'tripay_shopeepay',
                'name' => 'ShopeePay',
                'description' => 'Bayar dengan ShopeePay',
                'icon_class' => 'fas fa-mobile-alt',
                'icon_bg_class' => 'bg-orange-100 text-orange-600',
                'gateway_id' => $tripayGateway->id,
                'fee' => 1.5,
                'fee_type' => 'percentage',
                'min_amount' => 10000,
                'max_amount' => ********,
                'processing_time' => 'Instant',
                'instructions' => 'Buka aplikasi Shopee dan pilih ShopeePay untuk melakukan pembayaran.',
                'is_active' => false,
                'sort_order' => 7
            ],

            // Manual Methods
            [
                'code' => 'bank_transfer',
                'name' => 'Transfer Bank',
                'description' => 'Transfer manual ke rekening bank',
                'icon_class' => 'fas fa-university',
                'icon_bg_class' => 'bg-blue-100 text-blue-600',
                'gateway_id' => $manualGateway->id,
                'fee' => 0,
                'fee_type' => 'fixed',
                'min_amount' => 10000,
                'max_amount' => null,
                'processing_time' => '1-24 hours',
                'instructions' => 'Transfer ke rekening bank yang tertera dan upload bukti transfer.',
                'is_active' => true,
                'sort_order' => 8
            ],
            [
                'code' => 'cash',
                'name' => 'Bayar di Tempat',
                'description' => 'Bayar saat check-in event',
                'icon_class' => 'fas fa-money-bill-wave',
                'icon_bg_class' => 'bg-green-100 text-green-600',
                'gateway_id' => $manualGateway->id,
                'fee' => 0,
                'fee_type' => 'fixed',
                'min_amount' => 0,
                'max_amount' => null,
                'processing_time' => 'At event',
                'instructions' => 'Bayar langsung saat check-in di lokasi event.',
                'is_active' => true,
                'sort_order' => 9
            ]
        ];

        foreach ($paymentMethods as $method) {
            PaymentMethod::updateOrCreate(
                ['code' => $method['code']],
                $method
            );
        }
    }
}

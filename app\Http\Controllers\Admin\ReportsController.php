<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\User;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportsController extends Controller
{
    public function index()
    {
        // Get date range from request or default to last 30 days
        $startDate = request('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = request('end_date', Carbon::now()->format('Y-m-d'));
        
        // Convert to Carbon instances
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        // Revenue Analytics
        $revenueData = $this->getRevenueAnalytics($start, $end);
        
        // Event Analytics
        $eventData = $this->getEventAnalytics($start, $end);
        
        // User Analytics
        $userData = $this->getUserAnalytics($start, $end);
        
        // Category Analytics
        $categoryData = $this->getCategoryAnalytics($start, $end);
        
        // Geographic Analytics
        $geographicData = $this->getGeographicAnalytics($start, $end);
        
        // Recent Activities
        $recentActivities = $this->getRecentActivities();

        return view('admin.reports.index', compact(
            'revenueData',
            'eventData', 
            'userData',
            'categoryData',
            'geographicData',
            'recentActivities',
            'startDate',
            'endDate'
        ));
    }

    private function getRevenueAnalytics($start, $end)
    {
        $totalRevenue = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$start, $end])
            ->sum('total_amount');

        $previousPeriod = $start->copy()->subDays($end->diffInDays($start));
        $previousRevenue = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$previousPeriod, $start])
            ->sum('total_amount');

        $revenueGrowth = $previousRevenue > 0 
            ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 
            : 0;

        // Daily revenue chart data
        $dailyRevenue = Order::where('payment_status', 'paid')
            ->whereBetween('created_at', [$start, $end])
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total' => $totalRevenue,
            'growth' => $revenueGrowth,
            'daily' => $dailyRevenue,
            'average_order' => $totalRevenue > 0 ? $totalRevenue / Order::where('payment_status', 'paid')->whereBetween('created_at', [$start, $end])->count() : 0
        ];
    }

    private function getEventAnalytics($start, $end)
    {
        $totalEvents = Event::whereBetween('created_at', [$start, $end])->count();
        $publishedEvents = Event::where('status', 'published')->whereBetween('created_at', [$start, $end])->count();
        $draftEvents = Event::where('status', 'draft')->whereBetween('created_at', [$start, $end])->count();

        // Events by category
        $eventsByCategory = Event::join('categories', 'events.category_id', '=', 'categories.id')
            ->whereBetween('events.created_at', [$start, $end])
            ->selectRaw('categories.name, COUNT(*) as count')
            ->groupBy('categories.name')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();

        return [
            'total' => $totalEvents,
            'published' => $publishedEvents,
            'draft' => $draftEvents,
            'by_category' => $eventsByCategory
        ];
    }

    private function getUserAnalytics($start, $end)
    {
        $totalUsers = User::whereBetween('created_at', [$start, $end])->count();
        $organizers = User::where('role', 'penjual')->whereBetween('created_at', [$start, $end])->count();
        $customers = User::where('role', 'pembeli')->whereBetween('created_at', [$start, $end])->count();

        // User registration trend
        $userRegistrations = User::whereBetween('created_at', [$start, $end])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total' => $totalUsers,
            'organizers' => $organizers,
            'customers' => $customers,
            'registrations' => $userRegistrations
        ];
    }

    private function getCategoryAnalytics($start, $end)
    {
        $categoryStats = Category::leftJoin('events', 'categories.id', '=', 'events.category_id')
            ->leftJoin('orders', function($join) use ($start, $end) {
                $join->on('events.id', '=', 'orders.event_id')
                     ->where('orders.payment_status', 'paid')
                     ->whereBetween('orders.created_at', [$start, $end]);
            })
            ->selectRaw('
                categories.name,
                COUNT(DISTINCT events.id) as events_count,
                COUNT(DISTINCT orders.id) as orders_count,
                COALESCE(SUM(orders.total_amount), 0) as revenue
            ')
            ->groupBy('categories.id', 'categories.name')
            ->orderBy('revenue', 'desc')
            ->get();

        return $categoryStats;
    }

    private function getGeographicAnalytics($start, $end)
    {
        $cityStats = Event::join('orders', 'events.id', '=', 'orders.event_id')
            ->where('orders.payment_status', 'paid')
            ->whereBetween('orders.created_at', [$start, $end])
            ->selectRaw('events.city, COUNT(*) as orders_count, SUM(orders.total_amount) as revenue')
            ->groupBy('events.city')
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        return $cityStats;
    }

    private function getRecentActivities()
    {
        $activities = collect();

        // Recent orders
        $recentOrders = Order::with(['event', 'user'])
            ->where('payment_status', 'paid')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($order) {
                return [
                    'type' => 'order',
                    'message' => "New order for {$order->event->title}",
                    'user' => $order->user->name,
                    'amount' => $order->total_amount,
                    'created_at' => $order->created_at
                ];
            });

        // Recent events
        $recentEvents = Event::with(['organizer'])
            ->where('status', 'published')
            ->latest()
            ->limit(5)
            ->get()
            ->map(function($event) {
                return [
                    'type' => 'event',
                    'message' => "New event published: {$event->title}",
                    'user' => $event->organizer->name,
                    'amount' => null,
                    'created_at' => $event->created_at
                ];
            });

        // Recent users
        $recentUsers = User::latest()
            ->limit(5)
            ->get()
            ->map(function($user) {
                return [
                    'type' => 'user',
                    'message' => "New user registered",
                    'user' => $user->name,
                    'amount' => null,
                    'created_at' => $user->created_at
                ];
            });

        return $activities->merge($recentOrders)
                         ->merge($recentEvents)
                         ->merge($recentUsers)
                         ->sortByDesc('created_at')
                         ->take(15);
    }

    public function export(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->subDays(30)->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));
        $type = $request->get('type', 'revenue');

        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();

        switch ($type) {
            case 'revenue':
                return $this->exportRevenueReport($start, $end);
            case 'events':
                return $this->exportEventsReport($start, $end);
            case 'users':
                return $this->exportUsersReport($start, $end);
            default:
                return $this->exportRevenueReport($start, $end);
        }
    }

    private function exportRevenueReport($start, $end)
    {
        $orders = Order::with(['event', 'user'])
            ->where('payment_status', 'paid')
            ->whereBetween('created_at', [$start, $end])
            ->get();

        $filename = 'revenue_report_' . $start->format('Y-m-d') . '_to_' . $end->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($orders) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Order ID', 'Event', 'Customer', 'Amount', 'Date', 'Payment Method']);

            foreach ($orders as $order) {
                fputcsv($file, [
                    $order->id,
                    $order->event->title,
                    $order->user->name,
                    $order->total_amount,
                    $order->created_at->format('Y-m-d H:i:s'),
                    $order->payment_method
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportEventsReport($start, $end)
    {
        $events = Event::with(['organizer', 'category'])
            ->whereBetween('created_at', [$start, $end])
            ->get();

        $filename = 'events_report_' . $start->format('Y-m-d') . '_to_' . $end->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($events) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Event ID', 'Title', 'Organizer', 'Category', 'Status', 'Price', 'Capacity', 'Created Date']);

            foreach ($events as $event) {
                fputcsv($file, [
                    $event->id,
                    $event->title,
                    $event->organizer->name,
                    $event->category->name,
                    $event->status,
                    $event->price,
                    $event->total_capacity,
                    $event->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportUsersReport($start, $end)
    {
        $users = User::whereBetween('created_at', [$start, $end])->get();

        $filename = 'users_report_' . $start->format('Y-m-d') . '_to_' . $end->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['User ID', 'Name', 'Email', 'Role', 'Registration Date', 'Email Verified']);

            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->role,
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->email_verified_at ? 'Yes' : 'No'
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}

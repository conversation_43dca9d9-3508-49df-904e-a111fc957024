<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BadgeLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing badge level
        DB::table('user_badge_level')->truncate();

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create badge level
        $badgelevel = [
            [
                'name' => 'Bronze',
                'slug' => 'bronze',
                'description' => 'Entry level badge for new users',
                'color' => '#cd7f32',
                'icon' => 'fas fa-medal',
                'badge_image' => null,
                'min_spent_amount' => 0,
                'min_uangtix_balance' => 0,
                'min_transactions' => 0,
                'min_events_attended' => 0,
                'discount_percentage' => 0,
                'cashback_percentage' => 1,
                'benefits' => json_encode([
                    'basic_support' => true,
                    'template_access' => ['classic', 'unix', 'minimal']
                ]),
                'requirements' => json_encode(['verified_email' => true]),
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Silver',
                'slug' => 'silver',
                'description' => 'Intermediate level badge',
                'color' => '#c0c0c0',
                'icon' => 'fas fa-medal',
                'badge_image' => null,
                'min_spent_amount' => 500000,
                'min_uangtix_balance' => 100000,
                'min_transactions' => 5,
                'min_events_attended' => 3,
                'discount_percentage' => 5,
                'cashback_percentage' => 2,
                'benefits' => json_encode([
                    'priority_support' => true,
                    'early_access' => true,
                    'template_access' => ['classic', 'unix', 'minimal', 'pro']
                ]),
                'requirements' => json_encode(['verified_email' => true, 'account_age_days' => 30]),
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Gold',
                'slug' => 'gold',
                'description' => 'Premium level badge',
                'color' => '#ffd700',
                'icon' => 'fas fa-crown',
                'badge_image' => null,
                'min_spent_amount' => 2000000,
                'min_uangtix_balance' => 500000,
                'min_transactions' => 15,
                'min_events_attended' => 10,
                'discount_percentage' => 10,
                'cashback_percentage' => 5,
                'benefits' => json_encode([
                    'vip_support' => true,
                    'exclusive_events' => true,
                    'free_shipping' => true,
                    'template_access' => ['classic', 'unix', 'minimal', 'pro']
                ]),
                'requirements' => json_encode(['verified_email' => true, 'account_age_days' => 90]),
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Platinum',
                'slug' => 'platinum',
                'description' => 'Elite level badge',
                'color' => '#e5e4e2',
                'icon' => 'fas fa-gem',
                'badge_image' => null,
                'min_spent_amount' => 5000000,
                'min_uangtix_balance' => 1000000,
                'min_transactions' => 30,
                'min_events_attended' => 25,
                'discount_percentage' => 15,
                'cashback_percentage' => 10,
                'benefits' => json_encode([
                    'concierge_service' => true,
                    'unlimited_access' => true,
                    'personal_manager' => true,
                    'custom_templates' => true,
                    'template_access' => ['classic', 'unix', 'minimal', 'pro', 'custom']
                ]),
                'requirements' => json_encode(['verified_email' => true, 'account_age_days' => 180]),
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Diamond',
                'slug' => 'diamond',
                'description' => 'Elite level badge with maximum benefits',
                'color' => '#b9f2ff',
                'icon' => 'fas fa-gem',
                'badge_image' => null,
                'min_spent_amount' => ********,
                'min_uangtix_balance' => 2000000,
                'min_transactions' => 50,
                'min_events_attended' => 20,
                'discount_percentage' => 20,
                'cashback_percentage' => 15,
                'benefits' => json_encode([
                    'concierge_service' => true,
                    'unlimited_access' => true,
                    'personal_manager' => true,
                    'custom_templates' => true,
                    'vip_events' => true,
                    'priority_support' => true,
                    'template_access' => ['classic', 'unix', 'minimal', 'pro', 'custom']
                ]),
                'requirements' => json_encode(['verified_email' => true, 'verified_phone' => true, 'kyc_verified' => true, 'account_age_days' => 365]),
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        foreach ($badgelevel as $level) {
            DB::table('user_badge_level')->insert($level);
        }

        $this->command->info('Badge level seeded successfully!');
    }
}

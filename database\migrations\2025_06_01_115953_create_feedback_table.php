<?php

/**
 * Create Feedback Table Migration
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: <PERSON><PERSON><PERSON> Nazula P
 * Instagram: @seehai.dhafa
 *
 * All rights reserved.
 */

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('feedback')) {
            Schema::create('feedback', function (Blueprint $table) {
                $table->id();
                $table->string('feedback_number')->unique();
                $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
                $table->enum('type', ['suggestion', 'complaint', 'compliment', 'feature_request', 'bug_report', 'other']);
                $table->enum('category', ['ui_ux', 'performance', 'feature', 'content', 'service', 'technical', 'other']);
                $table->string('title');
                $table->text('message');
                $table->tinyInteger('rating')->nullable()->comment('1-5 stars');
                $table->string('contact_email')->nullable();
                $table->string('contact_phone')->nullable();
                $table->boolean('is_anonymous')->default(false);
                $table->enum('status', ['pending', 'reviewed', 'responded', 'implemented', 'rejected'])->default('pending');
                $table->ipAddress('ip_address')->nullable();
                $table->text('user_agent')->nullable();
                $table->text('admin_response')->nullable();
                $table->timestamp('responded_at')->nullable();
                $table->foreignId('responded_by')->nullable()->constrained('users')->onDelete('set null');
                $table->timestamps();

                $table->index(['user_id', 'status']);
                $table->index(['type', 'category']);
                $table->index('created_at');
                $table->index('rating');
        
            });
        }

    
    }

    /**
     * Reverse the migrations.
     */
    
    public function down(): void
    {
        Schema::dropIfExists('feedback');
    }
};

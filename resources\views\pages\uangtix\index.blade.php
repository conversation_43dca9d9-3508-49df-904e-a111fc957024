{{--
/**
 * UangTix Dashboard Page - GoPay/OVO Style
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: <PERSON><PERSON><PERSON> P
 * Instagram: @seehai.dhafa
 *
 * All rights reserved.
 */
--}}

@extends('layouts.app')

@section('title', 'UangTix - Dompet Digital')

@push('styles')
<!-- FontAwesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- Bootstrap for modals -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<!-- UangTix Custom CSS -->
<link rel="stylesheet" href="{{ asset('css/uangtix.css') }}?v={{ time() }}">
<style>
/* UangTix Core Styles - Inline to ensure loading */
.uangtix-container {
    --uangtix-primary-blue: #0066CC !important;
    --uangtix-primary-green: #00AA5B !important;
    --uangtix-secondary-blue: #E6F3FF !important;
    --uangtix-text-dark: #1A1A1A !important;
    --uangtix-text-gray: #666666 !important;
    --uangtix-bg-light: #F8FAFC !important;
    --uangtix-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    --uangtix-shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
    --uangtix-border-radius: 16px !important;

    background: var(--uangtix-bg-light) !important;
    min-height: 100vh !important;
    padding-bottom: 100px !important;
    font-family: 'Poppins', 'DM Sans', sans-serif !important;
}

.uangtix-container .wallet-header {
    background: linear-gradient(135deg, var(--uangtix-primary-blue) 0%, var(--uangtix-primary-green) 100%) !important;
    padding: 20px 16px 40px !important;
    position: relative !important;
    overflow: hidden !important;
}

.uangtix-container .balance-card {
    background: rgba(255, 255, 255, 0.15) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: var(--uangtix-border-radius) !important;
    padding: 24px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.uangtix-container .quick-actions {
    padding: 24px 16px !important;
    background: white !important;
    margin: -20px 16px 0 !important;
    border-radius: var(--uangtix-border-radius) !important;
    box-shadow: var(--uangtix-shadow-light) !important;
    position: relative !important;
    z-index: 3 !important;
}

.uangtix-container .actions-grid {
    display: grid !important;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 16px !important;
}

.uangtix-container .action-icon {
    width: 48px !important;
    height: 48px !important;
    border-radius: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-bottom: 8px !important;
    font-size: 20px !important;
    color: white !important;
}

.uangtix-container .action-icon.deposit {
    background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
}

.uangtix-container .action-icon.withdraw {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%) !important;
}

.uangtix-container .action-icon.transfer {
    background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%) !important;
}

.uangtix-container .action-icon.history {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%) !important;
}

/* Modal Enhancements for UangTix */
.modal-content {
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
}

.modal-header {
    border-bottom: 1px solid #E5E7EB !important;
    padding: 20px 24px !important;
}

.modal-body {
    padding: 24px !important;
}

.modal-title {
    font-weight: 700 !important;
    color: #1A1A1A !important;
}

.form-control {
    border-radius: 12px !important;
    border: 2px solid #E5E7EB !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
}

.form-control:focus {
    border-color: #0066CC !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25) !important;
}

.form-select {
    border-radius: 12px !important;
    border: 2px solid #E5E7EB !important;
    padding: 12px 16px !important;
}

.summary-card {
    background: #F8FAFC !important;
    border-radius: 12px !important;
    padding: 16px !important;
    margin-bottom: 20px !important;
}

.summary-title {
    font-weight: 700 !important;
    margin-bottom: 12px !important;
    color: #1A1A1A !important;
}

.summary-row {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 8px !important;
}

.summary-label {
    color: #666666 !important;
    font-size: 14px !important;
}

.summary-value {
    font-weight: 600 !important;
    color: #1A1A1A !important;
}

.btn-uangtix {
    background: #0066CC !important;
    color: white !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn-uangtix:hover {
    background: #0052A3 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 16px rgba(0, 102, 204, 0.3) !important;
}

.btn-uangtix.secondary {
    background: #F59E0B !important;
}

.btn-uangtix.secondary:hover {
    background: #D97706 !important;
}

/* Loading state */
.btn-uangtix:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* Toast notification styles */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    max-width: 300px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.toast-success {
    background: #10B981;
}

.toast-error {
    background: #EF4444;
}

.toast-info {
    background: #3B82F6;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
@endpush

@section('content')
<div class="uangtix-container">
    <!-- Wallet Header -->
    <div class="wallet-header">
        <div class="header-content">
            <!-- Header Top -->
            <div class="header-top">
                <div>
                    <div class="greeting">Selamat datang,</div>
                    <div class="user-name">{{ auth()->user()->name ?? 'Pengguna' }}</div>
                </div>
                <div class="header-actions"
                    <button class="header-btn" onclick="showQRCode()">
                        <i class="fas fa-qrcode"></i>
                    </button>
                </div>
            </div>

            <!-- Balance Card -->
            <div class="balance-card">
                <div class="balance-label">Saldo UangTix</div>
                <div class="balance-amount">
                    {{ number_format($balance->balance ?? 0, 0, ',', '.') }}
                    <span class="balance-currency">UTX</span>
                </div>
                <div class="balance-idr">
                    ≈ Rp {{ number_format(($balance->balance ?? 0) * ($exchangeRate->rate_uangtix_to_idr ?? 1), 0, ',', '.') }}
                </div>

                <div class="balance-actions">
                    <button class="balance-btn" onclick="openDepositModal()">
                        <i class="fas fa-plus"></i>
                        Top Up
                    </button>
                    <button class="balance-btn secondary" onclick="openWithdrawModal()">
                        <i class="fas fa-minus"></i>
                        Tarik
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <div class="quick-actions-title">Layanan Utama</div>
        <div class="actions-grid">
            <a href="#" class="action-item" onclick="openDepositModal()">
                <div class="action-icon deposit">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="action-label">Top Up</div>
            </a>

            <a href="#" class="action-item" onclick="openWithdrawModal()">
                <div class="action-icon withdraw">
                    <i class="fas fa-minus"></i>
                </div>
                <div class="action-label">Tarik Dana</div>
            </a>

            <a href="#" class="action-item" onclick="openTransferModal()">
                <div class="action-icon transfer">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <div class="action-label">Transfer</div>
            </a>

            <a href="{{ route('uangtix.transactions') }}" class="action-item">
                <div class="action-icon history">
                    <i class="fas fa-history"></i>
                </div>
                <div class="action-label">Riwayat</div>
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-section">
        <div class="stats-title">Statistik</div>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{{ number_format($stats['total_earned'] ?? 0, 0, ',', '.') }}</div>
                <div class="stat-label">Total Earned</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ number_format($stats['total_deposited'] ?? 0, 0, ',', '.') }}</div>
                <div class="stat-label">Total Deposit</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ number_format($stats['total_withdrawn'] ?? 0, 0, ',', '.') }}</div>
                <div class="stat-label">Total Tarik</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $stats['transactions_this_month'] ?? 0 }}</div>
                <div class="stat-label">Transaksi Bulan Ini</div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="transactions-section">
        <div class="section-header">
            <div class="section-title">Transaksi Terbaru</div>
            <a href="{{ route('uangtix.transactions') }}" class="view-all-btn">Lihat Semua</a>
        </div>

        @if(isset($recentTransactions) && $recentTransactions->count() > 0)
            @foreach($recentTransactions as $transaction)
            <div class="transaction-item">
                <div class="transaction-icon {{ $transaction->type }}">
                    @if($transaction->type === 'deposit')
                        <i class="fas fa-arrow-down"></i>
                    @elseif($transaction->type === 'withdrawal')
                        <i class="fas fa-arrow-up"></i>
                    @elseif($transaction->type === 'transfer_in')
                        <i class="fas fa-arrow-down"></i>
                    @elseif($transaction->type === 'transfer_out')
                        <i class="fas fa-arrow-up"></i>
                    @else
                        <i class="fas fa-exchange-alt"></i>
                    @endif
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">
                        @if($transaction->type === 'deposit') Deposit
                        @elseif($transaction->type === 'withdrawal') Penarikan
                        @elseif($transaction->type === 'transfer_in') Transfer Masuk
                        @elseif($transaction->type === 'transfer_out') Transfer Keluar
                        @else Transaksi
                        @endif
                    </div>
                    <div class="transaction-subtitle">
                        {{ $transaction->created_at->format('d M Y, H:i') }}
                    </div>
                </div>
                <div class="transaction-amount {{ in_array($transaction->type, ['deposit', 'transfer_in']) ? 'positive' : 'negative' }}">
                    {{ in_array($transaction->type, ['deposit', 'transfer_in']) ? '+' : '-' }}{{ number_format($transaction->amount ?? 0, 0, ',', '.') }} UTX
                    <div class="transaction-date">
                        {{ $transaction->status ?? 'pending' }}
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="transaction-item">
                <div class="transaction-icon" style="background: #E5E7EB;">
                    <i class="fas fa-inbox" style="color: #9CA3AF;"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">Belum ada transaksi</div>
                    <div class="transaction-subtitle">Mulai dengan melakukan top up</div>
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Modals -->
<!-- Deposit Modal -->
<div class="modal fade" id="depositModal" tabindex="-1" aria-labelledby="depositModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="depositModalLabel">Top Up UangTix</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="depositForm" onsubmit="submitDeposit(event)">
                    <div class="mb-3">
                        <label for="depositAmount" class="form-label" style="font-weight: 600;">Jumlah Deposit (IDR)</label>
                        <input type="number" class="form-control" id="depositAmount" name="amount_idr"
                               min="{{ $exchangeRate->min_deposit_idr ?? 10000 }}"
                               max="{{ $exchangeRate->max_deposit_idr ?? 10000000 }}"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan jumlah deposit" required>
                        <div class="form-text">Minimum: Rp {{ number_format($exchangeRate->min_deposit_idr ?? 10000, 0, ',', '.') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label" style="font-weight: 600;">Metode Pembayaran</label>
                        <div id="paymentMethodsContainer" style="display: grid; gap: 12px;">
                            <!-- Payment methods will be loaded here -->
                        </div>
                        <input type="hidden" id="selectedPaymentMethod" name="payment_method" required>
                    </div>

                    <div class="summary-card" style="background: #F8FAFC; border-radius: 12px; padding: 16px; margin-bottom: 20px;">
                        <div class="summary-title" style="font-weight: 700; margin-bottom: 12px;">Ringkasan Deposit</div>
                        <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="summary-label">Jumlah Deposit:</span>
                            <span class="summary-value" id="depositSummaryAmount">Rp 0</span>
                        </div>
                        <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="summary-label">Fee ({{ $exchangeRate->deposit_fee_percentage ?? 0 }}%):</span>
                            <span class="summary-value" id="depositSummaryFee">Rp 0</span>
                        </div>
                        <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="summary-label">Jumlah Bersih:</span>
                            <span class="summary-value" id="depositSummaryNet">Rp 0</span>
                        </div>
                        <div class="summary-row" style="display: flex; justify-content: space-between;">
                            <span class="summary-label">UangTix yang Diterima:</span>
                            <span class="summary-value" id="depositSummaryUTX" style="color: #10B981; font-weight: 700;">0 UTX</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-uangtix">
                            <i class="fas fa-plus"></i>
                            Lanjutkan Pembayaran
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1" aria-labelledby="withdrawModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="withdrawModalLabel">Tarik Dana UangTix</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="withdrawForm" onsubmit="submitWithdraw(event)">
                    <div class="mb-3">
                        <label for="withdrawAmount" class="form-label" style="font-weight: 600;">Jumlah Penarikan (UTX)</label>
                        <input type="number" class="form-control" id="withdrawAmount" name="amount_uangtix"
                               min="{{ $exchangeRate->min_withdrawal_uangtix ?? 10 }}"
                               max="{{ $balance->balance ?? 0 }}"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan jumlah penarikan" required>
                        <div class="form-text">Saldo tersedia: {{ number_format($balance->balance ?? 0, 0, ',', '.') }} UTX</div>
                    </div>

                    <div class="mb-3">
                        <label for="bankName" class="form-label" style="font-weight: 600;">Nama Bank</label>
                        <select class="form-select" id="bankName" name="bank_name"
                                style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;" required>
                            <option value="">Pilih bank</option>
                            <option value="BCA">BCA</option>
                            <option value="Mandiri">Mandiri</option>
                            <option value="BNI">BNI</option>
                            <option value="BRI">BRI</option>
                            <option value="CIMB">CIMB Niaga</option>
                            <option value="Permata">Permata</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="accountNumber" class="form-label" style="font-weight: 600;">Nomor Rekening</label>
                        <input type="text" class="form-control" id="accountNumber" name="bank_account_number"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan nomor rekening" required>
                    </div>

                    <div class="mb-3">
                        <label for="accountName" class="form-label" style="font-weight: 600;">Nama Pemilik Rekening</label>
                        <input type="text" class="form-control" id="accountName" name="bank_account_name"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan nama pemilik rekening" required>
                    </div>

                    <div class="summary-card" style="background: #F8FAFC; border-radius: 12px; padding: 16px; margin-bottom: 20px;">
                        <div class="summary-title" style="font-weight: 700; margin-bottom: 12px;">Ringkasan Penarikan</div>
                        <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="summary-label">Jumlah Penarikan:</span>
                            <span class="summary-value" id="withdrawSummaryAmount">0 UTX</span>
                        </div>
                        <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span class="summary-label">Fee ({{ $exchangeRate->withdrawal_fee_percentage ?? 2.5 }}%):</span>
                            <span class="summary-value" id="withdrawSummaryFee">0 UTX</span>
                        </div>
                        <div class="summary-row" style="display: flex; justify-content: space-between;">
                            <span class="summary-label">Jumlah Diterima:</span>
                            <span class="summary-value" id="withdrawSummaryNet" style="color: #10B981; font-weight: 700;">Rp 0</span>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-uangtix secondary">
                            <i class="fas fa-minus"></i>
                            Ajukan Penarikan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transferModalLabel">Transfer UangTix</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="transferForm" onsubmit="submitTransfer(event)">
                    <div class="mb-3">
                        <label for="recipientEmail" class="form-label" style="font-weight: 600;">Email Penerima</label>
                        <input type="email" class="form-control" id="recipientEmail" name="to_user_email"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan email penerima" required>
                    </div>

                    <div class="mb-3">
                        <label for="transferAmount" class="form-label" style="font-weight: 600;">Jumlah Transfer (UTX)</label>
                        <input type="number" class="form-control" id="transferAmount" name="amount"
                               min="1" max="{{ $balance->balance ?? 0 }}"
                               style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                               placeholder="Masukkan jumlah transfer" required>
                        <div class="form-text">Saldo tersedia: {{ number_format($balance->balance ?? 0, 0, ',', '.') }} UTX</div>
                    </div>

                    <div class="mb-3">
                        <label for="transferNote" class="form-label" style="font-weight: 600;">Catatan (Opsional)</label>
                        <textarea class="form-control" id="transferNote" name="description" rows="3"
                                  style="border-radius: 12px; border: 2px solid #E5E7EB; padding: 12px 16px;"
                                  placeholder="Tambahkan catatan untuk transfer ini"></textarea>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-uangtix">
                            <i class="fas fa-exchange-alt"></i>
                            Kirim Transfer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
/*
 * UangTix JavaScript Functions - GoPay/OVO Style
 *
 * Copyright (c) 2024 BintangCode
 * Sub Holding CV Bintang Gumilang Group
 *
 * Developer: Dhafa Nazula P
 * Instagram: @seehai.dhafa
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips and other Bootstrap components
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Real-time calculation for deposit
    const depositAmountInput = document.getElementById('depositAmount');
    if (depositAmountInput) {
        depositAmountInput.addEventListener('input', function() {
            calculateDepositSummary();
        });
    }

    // Real-time calculation for withdrawal
    const withdrawAmountInput = document.getElementById('withdrawAmount');
    if (withdrawAmountInput) {
        withdrawAmountInput.addEventListener('input', function() {
            calculateWithdrawSummary();
        });
    }

    // Auto-refresh balance every 30 seconds
    setInterval(refreshBalance, 30000);
});

// Modal Functions
function openDepositModal() {
    const modal = new bootstrap.Modal(document.getElementById('depositModal'));
    modal.show();
}

function openWithdrawModal() {
    const modal = new bootstrap.Modal(document.getElementById('withdrawModal'));
    modal.show();
}

function openTransferModal() {
    const modal = new bootstrap.Modal(document.getElementById('transferModal'));
    modal.show();
}

// Header Functions
function showNotifications() {
    // Show notifications (you can implement this based on your notification system)
    showToast('Fitur notifikasi akan segera hadir!', 'info');
}

function showQRCode() {
    // Generate and show QR code for receiving payments
    showToast('Fitur QR Code akan segera hadir!', 'info');
}

// Calculation Functions
function calculateDepositSummary() {
    const amount = parseFloat(document.getElementById('depositAmount').value) || 0;
    const exchangeRate = {{ $exchangeRate->rate_idr_to_uangtix ?? 0.001 }};
    const feePercentage = {{ $exchangeRate->deposit_fee_percentage ?? 0 }};
    const minDeposit = {{ $exchangeRate->min_deposit_idr ?? 10000 }};

    if (amount >= minDeposit) {
        const fee = amount * (feePercentage / 100);
        const netAmount = amount - fee;
        const uangTixAmount = netAmount * exchangeRate;

        document.getElementById('depositSummaryAmount').textContent = 'Rp ' + formatNumber(amount);
        document.getElementById('depositSummaryFee').textContent = 'Rp ' + formatNumber(fee);
        document.getElementById('depositSummaryNet').textContent = 'Rp ' + formatNumber(netAmount);
        document.getElementById('depositSummaryUTX').textContent = formatNumber(uangTixAmount) + ' UTX';
    } else {
        document.getElementById('depositSummaryAmount').textContent = 'Rp 0';
        document.getElementById('depositSummaryFee').textContent = 'Rp 0';
        document.getElementById('depositSummaryNet').textContent = 'Rp 0';
        document.getElementById('depositSummaryUTX').textContent = '0 UTX';
    }
}

function calculateWithdrawSummary() {
    const amount = parseFloat(document.getElementById('withdrawAmount').value) || 0;
    const exchangeRate = {{ $exchangeRate->rate_uangtix_to_idr ?? 1 }};
    const feePercentage = {{ $exchangeRate->withdrawal_fee_percentage ?? 2.5 }};

    if (amount > 0) {
        const fee = amount * (feePercentage / 100);
        const netAmount = amount - fee;
        const idrAmount = netAmount * exchangeRate;

        document.getElementById('withdrawSummaryAmount').textContent = formatNumber(amount) + ' UTX';
        document.getElementById('withdrawSummaryFee').textContent = formatNumber(fee) + ' UTX';
        document.getElementById('withdrawSummaryNet').textContent = 'Rp ' + formatNumber(idrAmount);
    } else {
        document.getElementById('withdrawSummaryAmount').textContent = '0 UTX';
        document.getElementById('withdrawSummaryFee').textContent = '0 UTX';
        document.getElementById('withdrawSummaryNet').textContent = 'Rp 0';
    }
}

// Form Submission Functions
function submitDeposit(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';

    fetch('{{ route("uangtix.deposit") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Permintaan deposit berhasil dibuat!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
            form.reset();
            calculateDepositSummary();
            // Redirect to payment page if needed
            if (data.payment_url) {
                window.location.href = data.payment_url;
            }
        } else {
            showToast(data.message || 'Terjadi kesalahan saat memproses deposit', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Terjadi kesalahan jaringan', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-plus"></i> Lanjutkan Pembayaran';
    });
}

function submitWithdraw(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';

    fetch('{{ route("uangtix.withdraw") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Permintaan penarikan berhasil dibuat!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
            form.reset();
            calculateWithdrawSummary();
            refreshBalance();
        } else {
            showToast(data.message || 'Terjadi kesalahan saat memproses penarikan', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Terjadi kesalahan jaringan', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-minus"></i> Ajukan Penarikan';
    });
}

function submitTransfer(event) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Memproses...';

    fetch('{{ route("uangtix.transfer") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Transfer berhasil dikirim!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
            form.reset();
            refreshBalance();
        } else {
            showToast(data.message || 'Terjadi kesalahan saat memproses transfer', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Terjadi kesalahan jaringan', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-exchange-alt"></i> Kirim Transfer';
    });
}

// Utility Functions
function formatNumber(number) {
    return new Intl.NumberFormat('id-ID').format(number);
}

function refreshBalance() {
    fetch('{{ route("uangtix.balance") }}', {
        method: 'GET',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update balance display
            const balanceElement = document.querySelector('.balance-amount');
            if (balanceElement) {
                balanceElement.innerHTML = formatNumber(data.data.balance) + ' <span class="balance-currency">UTX</span>';
            }

            const balanceIdrElement = document.querySelector('.balance-idr');
            if (balanceIdrElement) {
                balanceIdrElement.textContent = '≈ Rp ' + formatNumber(data.data.balance_idr);
            }
        }
    })
    .catch(error => {
        console.error('Error refreshing balance:', error);
    });
}

function showToast(message, type = 'info') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => toast.remove());

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#3B82F6'};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        z-index: 9999;
        font-weight: 600;
        max-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;

    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;
    document.head.appendChild(style);

    document.body.appendChild(toast);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.animation = 'slideInRight 0.3s ease-out reverse';
            setTimeout(() => toast.remove(), 300);
        }
    }, 5000);
}

// PWA Functions
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(error) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// Add to home screen prompt
let deferredPrompt;
window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;

    // Show install button or banner
    const installBanner = document.createElement('div');
    installBanner.innerHTML = `
        <div style="position: fixed; bottom: 80px; left: 16px; right: 16px; background: white; padding: 16px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.15); z-index: 1000;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
                <div>
                    <div style="font-weight: 600; margin-bottom: 4px;">Install UangTix</div>
                    <div style="font-size: 14px; color: #666;">Akses lebih cepat dari home screen</div>
                </div>
                <button onclick="installApp()" style="background: var(--primary-blue); color: white; border: none; padding: 8px 16px; border-radius: 8px; font-weight: 600;">Install</button>
            </div>
            <button onclick="this.parentElement.remove()" style="position: absolute; top: 8px; right: 8px; background: none; border: none; font-size: 18px; color: #999;">&times;</button>
        </div>
    `;
    document.body.appendChild(installBanner);
});

function installApp() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            }
            deferredPrompt = null;
        });
    }
}

// Load Payment Methods
function loadPaymentMethods() {
    fetch('/api/payment-methods')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderPaymentMethods(data.payment_methods);
            } else {
                console.error('Failed to load payment methods:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading payment methods:', error);
        });
}

function renderPaymentMethods(paymentMethods) {
    const container = document.getElementById('paymentMethodsContainer');
    if (!container) return;

    container.innerHTML = '';

    // Group by category
    const grouped = paymentMethods.reduce((acc, method) => {
        if (!acc[method.category]) {
            acc[method.category] = [];
        }
        acc[method.category].push(method);
        return acc;
    }, {});

    // Render each category
    Object.keys(grouped).forEach(category => {
        const categoryDiv = document.createElement('div');
        categoryDiv.innerHTML = `
            <div style="font-size: 14px; font-weight: 600; color: #374151; margin-bottom: 8px; text-transform: capitalize;">
                ${category.replace('_', ' ')}
            </div>
        `;

        const methodsGrid = document.createElement('div');
        methodsGrid.style.cssText = 'display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; margin-bottom: 16px;';

        grouped[category].forEach(method => {
            const methodCard = document.createElement('div');
            methodCard.className = 'payment-method-card';
            methodCard.style.cssText = `
                border: 2px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
                cursor: pointer;
                transition: all 0.3s ease;
                background: white;
            `;

            methodCard.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: #F3F4F6; border-radius: 8px;">
                        ${method.logo ?
                            `<img src="${method.logo}" alt="${method.name}" style="width: 100%; height: 100%; object-fit: contain; border-radius: 6px;">` :
                            `<i class="${method.icon || 'fas fa-credit-card'}" style="font-size: 18px; color: #6B7280;"></i>`
                        }
                    </div>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; color: #1A1A1A; font-size: 14px;">${method.name}</div>
                        <div style="font-size: 12px; color: #6B7280;">${method.formatted_fee}</div>
                        ${method.is_manual ? '<div style="font-size: 11px; color: #F59E0B; font-weight: 600;">Manual</div>' : ''}
                    </div>
                </div>
            `;

            methodCard.addEventListener('click', () => selectPaymentMethod(method, methodCard));
            methodCard.addEventListener('mouseenter', () => {
                methodCard.style.borderColor = '#0066CC';
                methodCard.style.background = '#F8FAFC';
            });
            methodCard.addEventListener('mouseleave', () => {
                if (!methodCard.classList.contains('selected')) {
                    methodCard.style.borderColor = '#E5E7EB';
                    methodCard.style.background = 'white';
                }
            });

            methodsGrid.appendChild(methodCard);
        });

        categoryDiv.appendChild(methodsGrid);
        container.appendChild(categoryDiv);
    });
}

function selectPaymentMethod(method, cardElement) {
    // Remove selection from all cards
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.classList.remove('selected');
        card.style.borderColor = '#E5E7EB';
        card.style.background = 'white';
    });

    // Select current card
    cardElement.classList.add('selected');
    cardElement.style.borderColor = '#0066CC';
    cardElement.style.background = '#E6F3FF';

    // Set hidden input value
    document.getElementById('selectedPaymentMethod').value = method.code;

    // Update deposit summary if amount is entered
    const amountInput = document.getElementById('depositAmount');
    if (amountInput && amountInput.value) {
        updateDepositSummary();
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    loadPaymentMethods();
});
</script>
@endpush

@extends('layouts.main')

@section('title', 'Test User level')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">User Level System Test</h1>
            <p class="text-lg text-gray-600">Testing user level badges and functionality</p>
        </div>

        <!-- Level Configuration Display -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Available Level</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                @foreach(config('badge_level.level') as $levelKey => $levelConfig)
                    <div class="border rounded-lg p-4" style="border-color: {{ $levelConfig['color'] }}">
                        <div class="text-center">
                            <div class="mb-3">
                                <span class="inline-flex items-center px-3 py-1 rounded-full font-semibold text-white"
                                      style="background: {{ $levelConfig['background'] }}">
                                    <i class="{{ $levelConfig['icon'] }} mr-1"></i>
                                    {{ $levelConfig['display_name'] }}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mb-3">{{ $levelConfig['description'] }}</p>

                            <div class="text-xs text-gray-500">
                                <div><strong>Requirements:</strong></div>
                                @foreach($levelConfig['requirements'] as $key => $value)
                                    <div>{{ ucfirst(str_replace('_', ' ', $key)) }}: {{ $value }}</div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Current User Level -->
        @auth
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Your Current Level</h2>
                <div class="flex items-center space-x-4">
                    <img src="{{ auth()->user()->avatar_url }}" alt="{{ auth()->user()->name }}"
                         class="w-16 h-16 rounded-full object-cover">
                    <div>
                        <h3 class="text-lg font-semibold">{{ auth()->user()->name }}</h3>
                        <div class="flex items-center space-x-2 mt-1">
                            <span class="text-sm text-gray-600">{{ ucfirst(auth()->user()->role) }}</span>
                            @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                                <x-badge-level :user="auth()->user()" size="sm" />
                            @endif
                        </div>
                        <div class="text-sm text-gray-500 mt-2">
                            <div>Events Created: {{ auth()->user()->total_events_created }}</div>
                            <div>Tickets Sold: {{ auth()->user()->total_tickets_sold }}</div>
                            <div>Total Revenue: Rp {{ number_format(auth()->user()->total_revenue, 0, ',', '.') }}</div>
                            <div>Level Score: {{ auth()->user()->level_score }}</div>
                        </div>
                    </div>
                </div>
            </div>
        @endauth

        <!-- All Users with Level -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-4">All Users with Level</h2>
            <div class="space-y-4">
                @foreach(\App\Models\User::whereIn('role', ['admin', 'penjual'])->get() as $user)
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center space-x-4">
                            <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}"
                                 class="w-12 h-12 rounded-full object-cover">
                            <div>
                                <h4 class="font-semibold">{{ $user->name }}</h4>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">{{ ucfirst($user->role) }}</span>
                                    <x-badge-level :user="$user" size="xs" />
                                </div>
                                @if($user->organization)
                                    <p class="text-xs text-gray-500">{{ $user->organization }}</p>
                                @endif
                            </div>
                        </div>
                        <div class="text-right text-sm text-gray-500">
                            <div>Events: {{ $user->total_events_created }}</div>
                            <div>Tickets: {{ $user->total_tickets_sold }}</div>
                            <div>Score: {{ $user->level_score }}</div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Level Badge Sizes Demo -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Badge Size Variations</h2>
            @auth
                @if(auth()->user()->isPenjual() || auth()->user()->isAdmin())
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <span class="w-16 text-sm text-gray-600">XS:</span>
                            <x-badge-level :user="auth()->user()" size="xs" />
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="w-16 text-sm text-gray-600">SM:</span>
                            <x-badge-level :user="auth()->user()" size="sm" />
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="w-16 text-sm text-gray-600">MD:</span>
                            <x-badge-level :user="auth()->user()" size="md" />
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="w-16 text-sm text-gray-600">LG:</span>
                            <x-badge-level :user="auth()->user()" size="lg" />
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="w-16 text-sm text-gray-600">XL:</span>
                            <x-badge-level :user="auth()->user()" size="xl" />
                        </div>
                    </div>
                @else
                    <p class="text-gray-600">Level badges are only available for penjual and admin users.</p>
                @endif
            @else
                <p class="text-gray-600">Please login to see badge size variations.</p>
            @endauth
        </div>

        <!-- Admin Level Management Link -->
        @auth
            @if(auth()->user()->isAdmin())
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-bold text-gray-900 mb-4">Admin Actions</h2>
                    <div class="space-y-2">
                        <a href="{{ route('admin.user-level.index') }}"
                           class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                            <i class="fas fa-users-cog mr-2"></i>
                            Manage User Level
                        </a>
                        <a href="{{ route('admin.users.index') }}"
                           class="inline-flex items-center px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary/90 transition-colors ml-2">
                            <i class="fas fa-users mr-2"></i>
                            Manage Users
                        </a>
                    </div>
                </div>
            @endif
        @endauth
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endpush

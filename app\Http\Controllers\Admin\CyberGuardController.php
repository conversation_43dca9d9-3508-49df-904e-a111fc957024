<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CyberGuard\CyberGuardSetting;
use App\Models\CyberGuard\CyberGuardLog;
use Illuminate\Http\Request;

class CyberGuardController extends Controller
{
    /**
     * Display CyberGuard dashboard
     */
    public function index()
    {
        // Get security status
        $securityStatus = CyberGuardSetting::getSecurityStatus();

        // Get recent logs
        $recentLogs = CyberGuardLog::recent(24)->orderByDesc('detected_at')->limit(10)->get();

        // Get statistics
        $stats = CyberGuardLog::getStatistics(24);

        // Get threat level
        $threatLevel = CyberGuardLog::getThreatLevel();

        // Get settings overview
        $settingsOverview = [
            'total_settings' => CyberGuardSetting::count(),
            'active_settings' => CyberGuardSetting::where('is_active', true)->count(),
            'categories' => CyberGuardSetting::distinct('category')->pluck('category')->count(),
        ];

        return view('pages.admin.cyber-guard.index', compact(
            'securityStatus',
            'recentLogs',
            'stats',
            'threatLevel',
            'settingsOverview'
        ));
    }

    /**
     * Display security settings
     */
    public function settings(Request $request)
    {
        // Mock settings data
        $settings = [
            'protection_enabled' => true,
            'protection_level' => 'medium',
            'log_retention_days' => 30,
            'auto_block_threats' => true,
            'rate_limiting_enabled' => true,
            'max_requests_per_minute' => 60,
            'rate_limit_window' => 60,
            'rate_limit_action' => 'block',
            'sql_injection_protection' => true,
            'xss_protection' => true,
            'brute_force_protection' => true,
            'ddos_protection' => true,
            'malware_scanning' => false,
            'threat_sensitivity' => 'medium',
            'email_notifications' => true,
            'notification_email' => '<EMAIL>',
            'notification_threshold' => 'medium',
            'slack_notifications' => false,
            'slack_webhook' => '',
            'whitelist_ips' => "127.0.0.1\n192.168.1.0/24",
            'blacklist_ips' => '',
            'blocked_countries' => '',
            'maintenance_mode' => false,
            'debug_mode' => false
        ];

        $stats = [
            'active_rules' => 15,
            'threats_today' => 8
        ];

        return view('pages.admin.cyber-guard.settings', compact('settings', 'stats'));
    }

    /**
     * Update security settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'settings' => 'required|array',
        ]);

        try {
            foreach ($request->settings as $key => $value) {
                $setting = CyberGuardSetting::where('key', $key)->first();
                if ($setting) {
                    $setting->setTypedValue($value);
                    $setting->save();
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Security settings updated successfully!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display security logs
     */
    public function logs(Request $request)
    {
        // Mock data for logs
        $logs = collect([
            [
                'id' => 1,
                'severity' => 'critical',
                'type' => 'sql_injection',
                'ip_address' => '*************',
                'country' => 'Unknown',
                'description' => 'SQL injection attempt detected',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'request_url' => '/admin/users?id=1\' OR 1=1--',
                'is_resolved' => false,
                'resolved_at' => null,
                'detected_at' => '2024-01-15 14:30:25',
                'time_ago' => '2 hours ago'
            ],
            [
                'id' => 2,
                'severity' => 'high',
                'type' => 'brute_force',
                'ip_address' => '*********',
                'country' => 'Indonesia',
                'description' => 'Multiple failed login attempts',
                'user_agent' => 'curl/7.68.0',
                'request_url' => '/login',
                'is_resolved' => true,
                'resolved_at' => '2024-01-15 13:45:00',
                'detected_at' => '2024-01-15 13:15:10',
                'time_ago' => '3 hours ago'
            ],
            [
                'id' => 3,
                'severity' => 'medium',
                'type' => 'suspicious',
                'ip_address' => '***********',
                'country' => 'Singapore',
                'description' => 'Suspicious user agent detected',
                'user_agent' => 'Bot/1.0',
                'request_url' => '/api/events',
                'is_resolved' => false,
                'resolved_at' => null,
                'detected_at' => '2024-01-15 12:20:15',
                'time_ago' => '4 hours ago'
            ]
        ]);

        $stats = [
            'total_events' => 156,
            'critical_events' => 12,
            'unresolved_events' => 45,
            'unique_ips' => 89
        ];

        return view('pages.admin.cyber-guard.logs', compact('logs', 'stats'));
    }

    /**
     * Show log details
     */
    public function showLog(CyberGuardLog $log)
    {
        return view('pages.admin.cyber-guard.log-details', compact('log'));
    }

    /**
     * Mark log as resolved
     */
    public function resolveLog(Request $request, CyberGuardLog $log)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        $log->markResolved($request->resolution_notes);

        return response()->json([
            'success' => true,
            'message' => 'Log marked as resolved successfully!'
        ]);
    }

    /**
     * Display blocked IPs
     */
    public function blockedIps(Request $request)
    {
        // This would show blocked IPs management
        // Implementation depends on your blocked IPs model
        return view('pages.admin.cyber-guard.blocked-ips');
    }

    /**
     * Display firewall rules
     */
    public function firewallRules(Request $request)
    {
        // Mock data for firewall rules
        $rules = collect([
            [
                'id' => 1,
                'name' => 'Block Malicious IPs',
                'type' => 'block',
                'target' => '*************',
                'priority' => 'high',
                'hits' => 1250,
                'is_active' => true,
                'description' => 'Block known malicious IP addresses',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => 'Rate Limit API',
                'type' => 'rate_limit',
                'target' => '/api/*',
                'priority' => 'medium',
                'hits' => 850,
                'is_active' => true,
                'description' => 'Limit API requests to prevent abuse',
                'created_at' => '2024-01-14 14:20:00'
            ],
            [
                'id' => 3,
                'name' => 'Allow Admin IPs',
                'type' => 'allow',
                'target' => '10.0.0.0/8',
                'priority' => 'high',
                'hits' => 2100,
                'is_active' => true,
                'description' => 'Always allow admin network access',
                'created_at' => '2024-01-13 09:15:00'
            ]
        ]);

        $stats = [
            'total_rules' => $rules->count(),
            'active_rules' => $rules->where('is_active', true)->count(),
            'blocked_ips' => 45,
            'threats_blocked' => 1250
        ];

        return view('pages.admin.cyber-guard.firewall-rules', compact('rules', 'stats'));
    }

    /**
     * Run security scan
     */
    public function runSecurityScan()
    {
        try {
            // Implement security scan logic
            $results = $this->performSecurityScan();

            return response()->json([
                'success' => true,
                'message' => 'Security scan completed successfully!',
                'results' => $results
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Security scan failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Perform security scan
     */
    private function performSecurityScan()
    {
        $results = [
            'vulnerabilities_found' => 0,
            'security_score' => 0,
            'recommendations' => [],
            'scan_time' => now(),
        ];

        // Check security settings
        $securityStatus = CyberGuardSetting::getSecurityStatus();
        $totalProtected = 0;
        $totalCategories = count($securityStatus);

        foreach ($securityStatus as $category => $status) {
            if ($status['status'] === 'protected') {
                $totalProtected++;
            } elseif ($status['status'] === 'vulnerable') {
                $results['vulnerabilities_found']++;
                $results['recommendations'][] = "Enable {$category} protection";
            }
        }

        $results['security_score'] = round(($totalProtected / $totalCategories) * 100);

        // Check recent threats
        $recentThreats = CyberGuardLog::recent(24)->count();
        if ($recentThreats > 50) {
            $results['recommendations'][] = "High threat activity detected in last 24 hours";
        }

        // Check critical logs
        $criticalLogs = CyberGuardLog::recent(24)
                                   ->where('severity', CyberGuardLog::SEVERITY_CRITICAL)
                                   ->count();
        if ($criticalLogs > 0) {
            $results['vulnerabilities_found'] += $criticalLogs;
            $results['recommendations'][] = "Review {$criticalLogs} critical security events";
        }

        return $results;
    }

    /**
     * Export security report
     */
    public function exportReport(Request $request)
    {
        $request->validate([
            'format' => 'required|in:pdf,csv,json',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
        ]);

        try {
            // Generate report based on format
            $reportData = $this->generateReportData($request->date_from, $request->date_to);

            switch ($request->format) {
                case 'pdf':
                    return $this->generatePdfReport($reportData);
                case 'csv':
                    return $this->generateCsvReport($reportData);
                case 'json':
                    return response()->json($reportData);
                default:
                    throw new \Exception('Invalid format');
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate report: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate report data
     */
    private function generateReportData($dateFrom, $dateTo)
    {
        $logs = CyberGuardLog::whereBetween('detected_at', [$dateFrom, $dateTo])
                            ->orderByDesc('detected_at')
                            ->get();

        return [
            'period' => [
                'from' => $dateFrom,
                'to' => $dateTo,
            ],
            'summary' => [
                'total_events' => $logs->count(),
                'by_severity' => $logs->groupBy('severity')->map->count(),
                'by_type' => $logs->groupBy('type')->map->count(),
                'top_ips' => $logs->groupBy('ip_address')->map->count()->sortDesc()->take(10),
            ],
            'events' => $logs->toArray(),
            'generated_at' => now(),
        ];
    }

    /**
     * Initialize CyberGuard system
     */
    public function initialize()
    {
        try {
            $created = CyberGuardSetting::initializeDefaults();

            return response()->json([
                'success' => true,
                'message' => "CyberGuard initialized successfully! Created {$created} default settings.",
                'created_settings' => $created
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to initialize CyberGuard: ' . $e->getMessage()
            ]);
        }
    }
}

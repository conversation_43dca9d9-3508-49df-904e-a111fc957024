@extends('layouts.admin')

@section('title', 'Badge Level Details - ' . $badgeLevel->name)

@push('styles')
<style>
.badge-header {
    background: linear-gradient(135deg, {{ $badgeLevel->color }}20 0%, {{ $badgeLevel->color }}10 100%);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    border: 2px solid {{ $badgeLevel->color }}40;
}

.badge-icon-large {
    width: 80px;
    height: 80px;
    background: {{ $badgeLevel->color }};
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    box-shadow: 0 8px 24px {{ $badgeLevel->color }}40;
    margin-bottom: 24px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card-title {
    font-size: 14px;
    font-weight: 600;
    color: #6B7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.info-card-value {
    font-size: 18px;
    font-weight: 700;
    color: #1F2937;
}

.users-table {
    background: white;
    border-radius: 12px;
    border: 1px solid #E5E7EB;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
    background: #F9FAFB;
    padding: 16px 20px;
    border-bottom: 1px solid #E5E7EB;
}

.benefit-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #10B981;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
}

.requirement-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: #F59E0B;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: #6B7280;
    border: 1px solid rgba(107, 114, 128, 0.2);
}
</style>
@endpush

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Badge Level Details</h1>
            <p class="text-gray-600 dark:text-gray-400">View and manage badge level information</p>
        </div>
        <div class="flex gap-3">
            <a href="{{ route('admin.badge-level.edit', $badgelevel) }}" class="btn btn-primary">
                <i data-lucide="edit" class="w-4 h-4"></i>
                Edit Badge Level
            </a>
            <a href="{{ route('admin.badge-level.index') }}" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4"></i>
                Back to Badge Level
            </a>
        </div>
    </div>

    <!-- Badge Header -->
    <div class="badge-header">
        <div class="flex items-start justify-between">
            <div class="flex items-center gap-6">
                <div class="badge-icon-large">
                    @if($badgeLevel->badge_image)
                        <img src="{{ asset($badgeLevel->badge_image) }}" alt="{{ $badgeLevel->name }}" 
                             class="w-full h-full object-cover rounded-2xl">
                    @else
                        <i class="{{ $badgeLevel->icon ?? 'fas fa-medal' }}"></i>
                    @endif
                </div>
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ $badgeLevel->name }}</h2>
                    <p class="text-gray-600 text-lg mb-4">{{ $badgeLevel->description }}</p>
                    <div class="flex items-center gap-4">
                        <span class="status-badge {{ $badgeLevel->is_active ? 'status-active' : 'status-inactive' }}">
                            <i data-lucide="{{ $badgeLevel->is_active ? 'check-circle' : 'x-circle' }}" class="w-3 h-3"></i>
                            {{ $badgeLevel->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        <span class="text-sm text-gray-500">Sort Order: {{ $badgeLevel->sort_order }}</span>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold text-gray-900">{{ $badgeLevel->users->count() }}</div>
                <div class="text-sm text-gray-600">Users with this badge</div>
            </div>
        </div>
    </div>

    <!-- Information Grid -->
    <div class="info-grid">
        <!-- Requirements -->
        <div class="info-card">
            <div class="info-card-title">Minimum Spent Amount</div>
            <div class="info-card-value">Rp {{ number_format($badgeLevel->min_spent_amount, 0, ',', '.') }}</div>
        </div>

        <div class="info-card">
            <div class="info-card-title">Minimum UangTix Balance</div>
            <div class="info-card-value">Rp {{ number_format($badgeLevel->min_uangtix_balance, 0, ',', '.') }}</div>
        </div>

        <div class="info-card">
            <div class="info-card-title">Minimum Transactions</div>
            <div class="info-card-value">{{ number_format($badgeLevel->min_transactions) }}</div>
        </div>

        <div class="info-card">
            <div class="info-card-title">Minimum Events Attended</div>
            <div class="info-card-value">{{ number_format($badgeLevel->min_events_attended) }}</div>
        </div>

        <!-- Benefits -->
        <div class="info-card">
            <div class="info-card-title">Discount Percentage</div>
            <div class="info-card-value">{{ $badgeLevel->discount_percentage }}%</div>
        </div>

        <div class="info-card">
            <div class="info-card-title">Cashback Percentage</div>
            <div class="info-card-value">{{ $badgeLevel->cashback_percentage }}%</div>
        </div>
    </div>

    <!-- Benefits & Requirements -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Benefits -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i data-lucide="gift" class="w-5 h-5 inline mr-2 text-green-600"></i>
                Benefits
            </h3>
            <div class="space-y-3">
                <div class="benefit-badge">
                    <i data-lucide="percent" class="w-3 h-3"></i>
                    {{ $badgeLevel->discount_percentage }}% Discount
                </div>
                <div class="benefit-badge">
                    <i data-lucide="coins" class="w-3 h-3"></i>
                    {{ $badgeLevel->cashback_percentage }}% Cashback
                </div>
                
                @if($badgeLevel->benefits)
                    @foreach($badgeLevel->benefits as $benefit => $value)
                        @if($value === true || $value === 1 || $value === '1')
                            <div class="benefit-badge">
                                <i data-lucide="check" class="w-3 h-3"></i>
                                {{ ucfirst(str_replace('_', ' ', $benefit)) }}
                            </div>
                        @endif
                    @endforeach
                @endif
            </div>
        </div>

        <!-- Requirements -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i data-lucide="shield-check" class="w-5 h-5 inline mr-2 text-orange-600"></i>
                Requirements
            </h3>
            <div class="space-y-3">
                @if($badgeLevel->requirements)
                    @foreach($badgeLevel->requirements as $requirement => $value)
                        @if($value === true || $value === 1 || $value === '1')
                            <div class="requirement-badge">
                                <i data-lucide="check-circle" class="w-3 h-3"></i>
                                {{ ucfirst(str_replace('_', ' ', $requirement)) }}
                            </div>
                        @endif
                    @endforeach
                @else
                    <p class="text-gray-500 text-sm">No additional requirements</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Users with this Badge -->
    <div class="users-table">
        <div class="table-header">
            <h3 class="text-lg font-semibold text-gray-900">Users with {{ $badgeLevel->name }} Badge</h3>
        </div>
        
        @if($badgeLevel->users->count() > 0)
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Achieved Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($badgeLevel->users->take(20) as $user)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                            @if($user->avatar)
                                                <img src="{{ asset($user->avatar) }}" alt="{{ $user->name }}" 
                                                     class="w-10 h-10 rounded-full object-cover">
                                            @else
                                                <span class="text-gray-600 font-medium">{{ substr($user->name, 0, 1) }}</span>
                                            @endif
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                            <div class="text-sm text-gray-500">ID: {{ $user->id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ $user->email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ $user->badge_achieved_at ? $user->badge_achieved_at->format('M d, Y') : 'N/A' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        Rp {{ number_format($user->total_spent_amount ?? 0, 0, ',', '.') }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.users.show', $user) }}" 
                                       class="text-blue-600 hover:text-blue-900">View User</a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            @if($badgeLevel->users->count() > 20)
                <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
                    <p class="text-sm text-gray-600">
                        Showing 20 of {{ $badgeLevel->users->count() }} users. 
                        <a href="{{ route('admin.users.index', ['badge_level' => $badgeLevel->id]) }}" 
                           class="text-blue-600 hover:text-blue-900">View all users with this badge</a>
                    </p>
                </div>
            @endif
        @else
            <div class="text-center py-12">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i data-lucide="users" class="w-8 h-8 text-gray-400"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No users yet</h3>
                <p class="text-gray-500">No users have achieved this badge level yet.</p>
            </div>
        @endif
    </div>

    <!-- Actions -->
    <div class="flex justify-between items-center bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Badge Level Actions</h3>
            <p class="text-gray-600">Manage this badge level and its users</p>
        </div>
        <div class="flex gap-3">
            <form method="POST" action="{{ route('admin.badge-level.toggle-status', $badgelevel) }}" class="inline">
                @csrf
                <button type="submit" class="btn {{ $badgeLevel->is_active ? 'btn-warning' : 'btn-success' }}">
                    <i data-lucide="{{ $badgeLevel->is_active ? 'eye-off' : 'eye' }}" class="w-4 h-4"></i>
                    {{ $badgeLevel->is_active ? 'Deactivate' : 'Activate' }}
                </button>
            </form>
            
            <a href="{{ route('admin.badge-level.edit', $badgelevel) }}" class="btn btn-primary">
                <i data-lucide="edit" class="w-4 h-4"></i>
                Edit Badge Level
            </a>
            
            @if($badgeLevel->users->count() === 0)
                <form method="POST" action="{{ route('admin.badge-level.destroy', $badgelevel) }}" 
                      onsubmit="return confirm('Are you sure you want to delete this badge level?')" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                        Delete
                    </button>
                </form>
            @endif
        </div>
    </div>
</div>
@endsection

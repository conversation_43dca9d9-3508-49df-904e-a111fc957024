<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UangTixBalance;
use App\Models\UangTixTransaction;
use App\Models\UangTixRequest;
use App\Models\UangTixExchangeRate;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UangTixController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display UangTix dashboard for users
     */
    public function index()
    {
        $user = auth()->user();
        $balance = $user->getUangTixBalance();
        $exchangeRate = UangTixExchangeRate::current();

        // Get recent transactions
        $recentTransactions = $balance->getRecentTransactions(10);

        // Get pending requests
        $pendingRequests = UangTixRequest::where('user_id', $user->id)
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->get();

        // Statistics for current user
        $stats = [
            'current_balance' => $balance->balance,
            'total_earned' => $balance->total_earned,
            'total_spent' => $balance->total_spent,
            'total_deposited' => $balance->total_deposited,
            'total_withdrawn' => $balance->total_withdrawn,
            'transactions_this_month' => $balance->transactions()
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        // Check if user is admin for additional features
        $isAdmin = $user->role === 'admin';

        // If admin, show admin dashboard, otherwise show user dashboard
        if ($isAdmin) {
            return $this->adminDashboard();
        }

        return view('pages.uangtix.index', compact(
            'balance',
            'exchangeRate',
            'recentTransactions',
            'pendingRequests',
            'stats'
        ));
    }

    /**
     * Admin dashboard (redirect to admin controller)
     */
    private function adminDashboard()
    {
        $query = UangTixBalance::with(['user'])
            ->where('balance', '>', 0)
            ->orWhere('total_earned', '>', 0)
            ->orWhere('total_spent', '>', 0);

        $balances = $query->orderBy('balance', 'desc')->paginate(20);

        // Statistics
        $stats = [
            'total_balances' => UangTixBalance::sum('balance'),
            'total_users' => UangTixBalance::where('balance', '>', 0)->count(),
            'total_transactions_today' => UangTixTransaction::today()->count(),
            'total_volume_today' => UangTixTransaction::today()->sum(DB::raw('ABS(amount)')),
            'pending_deposits' => UangTixRequest::pending()->deposits()->count(),
            'pending_withdrawals' => UangTixRequest::pending()->withdrawals()->count(),
        ];

        $exchangeRate = UangTixExchangeRate::current();

        return view('pages.admin.uangtix.index', compact('balances', 'stats', 'exchangeRate'));
    }

    /**
     * Show user balance details
     */
    public function balance(Request $request)
    {
        $user = auth()->user();
        $balance = $user->getUangTixBalance();
        $exchangeRate = UangTixExchangeRate::current();

        // If AJAX request, return JSON
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'balance' => $balance->balance,
                    'balance_idr' => $balance->balance_in_idr,
                    'formatted_balance' => $balance->formatted_balance,
                    'formatted_balance_idr' => $balance->formatted_balance_idr,
                    'total_earned' => $balance->total_earned,
                    'total_spent' => $balance->total_spent,
                    'total_deposited' => $balance->total_deposited,
                    'total_withdrawn' => $balance->total_withdrawn,
                ]
            ]);
        }

        // Get monthly statistics for view
        $monthlyStats = $balance->getMonthlyStats();

        return view('pages.uangtix.balance', compact('balance', 'exchangeRate', 'monthlyStats'));
    }

    /**
     * Show user transactions
     */
    public function transactions(Request $request)
    {
        $user = auth()->user();
        $balance = $user->getUangTixBalance();
        $query = $balance->transactions();

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by description or transaction number
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);
        $balance = $user->getUangTixBalance();
        $exchangeRate = UangTixExchangeRate::current();

        // Transaction type options for filter
        $transactionTypes = [
            'deposit' => 'Deposit',
            'withdrawal' => 'Penarikan',
            'transfer_in' => 'Transfer Masuk',
            'transfer_out' => 'Transfer Keluar',
            'earning' => 'Pendapatan',
            'spending' => 'Pengeluaran',
            'admin_add' => 'Penambahan Admin',
            'admin_deduct' => 'Pengurangan Admin',
        ];

        return view('pages.uangtix.transactions', compact('transactions', 'balance', 'exchangeRate', 'transactionTypes'));
    }

    /**
     * Create deposit request
     */
    public function deposit(Request $request)
    {
        $request->validate([
            'amount_idr' => 'required|numeric|min:10000|max:10000000',
            'payment_method' => 'required|string',
        ]);

        // Get payment method
        $paymentMethod = \App\Models\PaymentMethod::where('code', $request->payment_method)
            ->where('is_active', true)
            ->first();

        if (!$paymentMethod) {
            return response()->json([
                'success' => false,
                'message' => 'Metode pembayaran tidak valid atau tidak aktif'
            ], 400);
        }

        // Validate amount against payment method limits
        if (!$paymentMethod->isAmountValid($request->amount_idr)) {
            return response()->json([
                'success' => false,
                'message' => "Jumlah harus antara {$paymentMethod->formatted_limits}"
            ], 400);
        }

        $exchangeRate = UangTixExchangeRate::current();

        // Validate deposit amount
        $validation = $exchangeRate->validateDepositAmount($request->amount_idr);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => implode(', ', $validation['errors'])
            ], 400);
        }

        // Calculate amounts including payment method fees
        $amounts = $exchangeRate->getNetDepositAmount($request->amount_idr);
        $paymentFee = $paymentMethod->calculateFee($request->amount_idr);

        // Add payment method fee to total
        $amounts['payment_fee'] = $paymentFee['total_fee'];
        $amounts['total_amount'] = $request->amount_idr + $paymentFee['total_fee'];

        try {
            $depositRequest = UangTixRequest::create([
                'request_number' => UangTixRequest::generateRequestNumber('deposit'),
                'user_id' => auth()->id(),
                'type' => 'deposit',
                'amount_idr' => $amounts['gross_idr'],
                'amount_uangtix' => $amounts['uangtix_amount'],
                'fee_amount' => $amounts['fee_idr'] + $paymentFee['total_fee'],
                'final_amount' => $amounts['uangtix_amount'],
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'payment_data' => array_merge($request->payment_data ?? [], [
                    'payment_method_id' => $paymentMethod->id,
                    'payment_fee' => $paymentFee,
                ]),
            ]);

            // If manual payment, return instructions
            if ($paymentMethod->is_manual) {
                return response()->json([
                    'success' => true,
                    'message' => 'Permintaan deposit berhasil dibuat',
                    'request_id' => $depositRequest->id,
                    'amounts' => $amounts,
                    'payment_method' => $paymentMethod,
                    'instructions' => $paymentMethod->getInstructions(),
                    'account_info' => $paymentMethod->getAccountInfo(),
                ]);
            }

            // For gateway payments, process through payment gateway
            return response()->json([
                'success' => true,
                'message' => 'Permintaan deposit berhasil dibuat',
                'request_id' => $depositRequest->id,
                'amounts' => $amounts,
                'payment_method' => $paymentMethod,
                'redirect_url' => route('uangtix.payment', $depositRequest->id),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat permintaan deposit: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create withdrawal request
     */
    public function withdraw(Request $request)
    {
        $request->validate([
            'amount_uangtix' => 'required|numeric|min:10',
            'bank_name' => 'required|string|max:100',
            'bank_account_number' => 'required|string|max:50',
            'bank_account_name' => 'required|string|max:100',
        ]);

        $user = auth()->user();
        $balance = $user->getUangTixBalance();
        $exchangeRate = UangTixExchangeRate::current();

        // Validate withdrawal amount
        $validation = $exchangeRate->validateWithdrawalAmount($request->amount_uangtix);
        if (!$validation['valid']) {
            return response()->json([
                'success' => false,
                'message' => implode(', ', $validation['errors'])
            ], 400);
        }

        // Check if user has sufficient balance
        if (!$balance->canSpend($request->amount_uangtix)) {
            return response()->json([
                'success' => false,
                'message' => 'Saldo UangTix tidak mencukupi'
            ], 400);
        }

        // Calculate amounts
        $amounts = $exchangeRate->getNetWithdrawalAmount($request->amount_uangtix);

        try {
            $withdrawalRequest = UangTixRequest::create([
                'request_number' => UangTixRequest::generateRequestNumber('withdrawal'),
                'user_id' => $user->id,
                'type' => 'withdrawal',
                'amount_uangtix' => $amounts['gross_uangtix'],
                'fee_amount' => $amounts['fee_uangtix'],
                'final_amount' => $amounts['idr_amount'],
                'status' => 'pending',
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_account_name' => $request->bank_account_name,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Permintaan penarikan berhasil dibuat',
                'request_id' => $withdrawalRequest->id,
                'amounts' => $amounts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat permintaan penarikan: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Transfer UangTix to another user
     */
    public function transfer(Request $request)
    {
        $request->validate([
            'to_user_email' => 'required|email|exists:users,email',
            'amount' => 'required|numeric|min:1',
            'description' => 'nullable|string|max:255',
        ]);

        $fromUser = auth()->user();
        $toUser = User::where('email', $request->to_user_email)->first();

        if ($fromUser->id === $toUser->id) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak dapat transfer ke diri sendiri'
            ], 400);
        }

        $exchangeRate = UangTixExchangeRate::current();
        if (!$exchangeRate->transfers_enabled) {
            return response()->json([
                'success' => false,
                'message' => 'Transfer UangTix sedang tidak tersedia'
            ], 400);
        }

        try {
            $transactions = $fromUser->transferUangTix(
                $toUser,
                $request->amount,
                $request->description ?? 'Transfer UangTix'
            );

            // Create notifications
            \App\Models\Notification::create([
                'user_id' => $fromUser->id,
                'title' => 'Transfer UangTix Berhasil',
                'message' => "Transfer {$request->amount} UTX ke {$toUser->name} berhasil.",
                'type' => 'uangtix',
                'data' => [
                    'transaction_id' => $transactions['out_transaction']->id,
                    'amount' => $request->amount,
                    'to_user' => $toUser->name,
                ]
            ]);

            \App\Models\Notification::create([
                'user_id' => $toUser->id,
                'title' => 'Menerima Transfer UangTix',
                'message' => "Anda menerima {$request->amount} UTX dari {$fromUser->name}.",
                'type' => 'uangtix',
                'data' => [
                    'transaction_id' => $transactions['in_transaction']->id,
                    'amount' => $request->amount,
                    'from_user' => $fromUser->name,
                ]
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Transfer berhasil',
                'new_balance' => $fromUser->getUangTixBalance()->fresh()->formatted_balance
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get current exchange rate
     */
    public function exchangeRate()
    {
        $exchangeRate = UangTixExchangeRate::current();

        return response()->json([
            'success' => true,
            'data' => [
                'rates' => $exchangeRate->formatted_rates,
                'limits' => $exchangeRate->formatted_limits,
                'fees' => $exchangeRate->formatted_fees,
                'services' => [
                    'deposits_enabled' => $exchangeRate->deposits_enabled,
                    'withdrawals_enabled' => $exchangeRate->withdrawals_enabled,
                    'transfers_enabled' => $exchangeRate->transfers_enabled,
                ]
            ]
        ]);
    }
}

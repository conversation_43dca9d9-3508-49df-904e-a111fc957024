@extends('layouts.admin')

@section('title', 'Security Logs - CyberGuard')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-file-alt text-primary me-2"></i>Security Logs
            </h1>
            <p class="mb-0 text-muted">Monitor security events and threats</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
            <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-download me-2"></i>Export
            </button>
            <button type="button" class="btn btn-danger" onclick="clearLogs()">
                <i class="fas fa-trash me-2"></i>Clear Logs
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Events
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['total_events'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Critical Events
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['critical_events'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Unresolved
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['unresolved_events'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Unique IPs
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['unique_ips'] ?? 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-globe fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filter Logs</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.cyber-guard.logs') }}">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ request('search') }}" placeholder="IP, User Agent...">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="severity">Severity</label>
                            <select class="form-control" id="severity" name="severity">
                                <option value="">All Severities</option>
                                <option value="critical" {{ request('severity') == 'critical' ? 'selected' : '' }}>Critical</option>
                                <option value="high" {{ request('severity') == 'high' ? 'selected' : '' }}>High</option>
                                <option value="medium" {{ request('severity') == 'medium' ? 'selected' : '' }}>Medium</option>
                                <option value="low" {{ request('severity') == 'low' ? 'selected' : '' }}>Low</option>
                                <option value="info" {{ request('severity') == 'info' ? 'selected' : '' }}>Info</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">All Types</option>
                                <option value="sql_injection" {{ request('type') == 'sql_injection' ? 'selected' : '' }}>SQL Injection</option>
                                <option value="xss" {{ request('type') == 'xss' ? 'selected' : '' }}>XSS</option>
                                <option value="brute_force" {{ request('type') == 'brute_force' ? 'selected' : '' }}>Brute Force</option>
                                <option value="ddos" {{ request('type') == 'ddos' ? 'selected' : '' }}>DDoS</option>
                                <option value="malware" {{ request('type') == 'malware' ? 'selected' : '' }}>Malware</option>
                                <option value="suspicious" {{ request('type') == 'suspicious' ? 'selected' : '' }}>Suspicious</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="resolved" {{ request('status') == 'resolved' ? 'selected' : '' }}>Resolved</option>
                                <option value="unresolved" {{ request('status') == 'unresolved' ? 'selected' : '' }}>Unresolved</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="{{ request('date_to') }}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                        <a href="{{ route('admin.cyber-guard.logs') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear
                        </a>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="per_page">Per Page</label>
                            <select class="form-control" id="per_page" name="per_page" onchange="this.form.submit()">
                                <option value="25" {{ request('per_page') == '25' ? 'selected' : '' }}>25</option>
                                <option value="50" {{ request('per_page') == '50' ? 'selected' : '' }}>50</option>
                                <option value="100" {{ request('per_page') == '100' ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Security Events</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-cog me-1"></i>Actions
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="#" onclick="bulkAction('resolve')">
                        <i class="fas fa-check me-2"></i>Mark as Resolved
                    </a>
                    <a class="dropdown-item" href="#" onclick="bulkAction('block_ip')">
                        <i class="fas fa-ban me-2"></i>Block IPs
                    </a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                        <i class="fas fa-trash me-2"></i>Delete Selected
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th width="30">
                                <input type="checkbox" id="selectAll">
                            </th>
                            <th>Time</th>
                            <th>Severity</th>
                            <th>Type</th>
                            <th>IP Address</th>
                            <th>Details</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($logs ?? [] as $log)
                            <tr class="{{ $log['severity'] == 'critical' ? 'table-danger' : ($log['severity'] == 'high' ? 'table-warning' : '') }}">
                                <td>
                                    <input type="checkbox" class="log-checkbox" value="{{ $log['id'] }}">
                                </td>
                                <td>
                                    <div class="small">
                                        <div>{{ $log['detected_at'] }}</div>
                                        <span class="text-muted">{{ $log['time_ago'] }}</span>
                                    </div>
                                </td>
                                <td>
                                    @switch($log['severity'])
                                        @case('critical')
                                            <span class="badge badge-danger">Critical</span>
                                            @break
                                        @case('high')
                                            <span class="badge badge-warning">High</span>
                                            @break
                                        @case('medium')
                                            <span class="badge badge-info">Medium</span>
                                            @break
                                        @case('low')
                                            <span class="badge badge-secondary">Low</span>
                                            @break
                                        @case('info')
                                            <span class="badge badge-light">Info</span>
                                            @break
                                        @default
                                            <span class="badge badge-secondary">{{ ucfirst($log['severity']) }}</span>
                                    @endswitch
                                </td>
                                <td>
                                    <span class="badge badge-primary">{{ ucfirst(str_replace('_', ' ', $log['type'])) }}</span>
                                </td>
                                <td>
                                    <div>
                                        <code>{{ $log['ip_address'] }}</code>
                                        @if($log['country'])
                                            <br><small class="text-muted">{{ $log['country'] }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="small">
                                        <div class="font-weight-bold">{{ $log['description'] }}</div>
                                        @if($log['user_agent'])
                                            <div class="text-muted">{{ Str::limit($log['user_agent'], 50) }}</div>
                                        @endif
                                        @if($log['request_url'])
                                            <div class="text-info">{{ Str::limit($log['request_url'], 40) }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($log['is_resolved'])
                                        <span class="badge badge-success">Resolved</span>
                                        @if($log['resolved_at'])
                                            <br><small class="text-muted">{{ $log['resolved_at'] }}</small>
                                        @endif
                                    @else
                                        <span class="badge badge-warning">Unresolved</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewDetails({{ $log['id'] }})" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if(!$log['is_resolved'])
                                            <button type="button" class="btn btn-sm btn-outline-success" 
                                                    onclick="resolveLog({{ $log['id'] }})" title="Mark as Resolved">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        @endif
                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                onclick="blockIP('{{ $log['ip_address'] }}')" title="Block IP">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteLog({{ $log['id'] }})" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-shield-alt fa-3x mb-3"></i>
                                        <p>No security events found</p>
                                        <small>This is good news! Your system is secure.</small>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if(isset($logs) && method_exists($logs, 'hasPages') && $logs->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $logs->appends(request()->query())->links() }}
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export Security Logs</h5>
                <button type="button" class="close" data-bs-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form action="{{ route('admin.cyber-guard.logs.export') }}" method="GET">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Date Range</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="date" class="form-control" name="start_date" required>
                            </div>
                            <div class="col-6">
                                <input type="date" class="form-control" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Severity</label>
                        <select class="form-control" name="severity">
                            <option value="">All Severities</option>
                            <option value="critical">Critical</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Format</label>
                        <select class="form-control" name="format">
                            <option value="csv">CSV</option>
                            <option value="json">JSON</option>
                            <option value="pdf">PDF Report</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Export</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Auto-refresh logs every 30 seconds
setInterval(function() {
    if (document.getElementById('auto-refresh').checked) {
        refreshLogs();
    }
}, 30000);

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.log-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

function refreshLogs() {
    location.reload();
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all logs? This action cannot be undone.')) {
        fetch('/admin/cyber-guard/logs/clear', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function viewDetails(logId) {
    // Implementation for viewing log details
    window.open(`/admin/cyber-guard/logs/${logId}`, '_blank');
}

function resolveLog(logId) {
    if (confirm('Mark this event as resolved?')) {
        fetch(`/admin/cyber-guard/logs/${logId}/resolve`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function blockIP(ipAddress) {
    if (confirm(`Block IP address ${ipAddress}?`)) {
        fetch('/admin/cyber-guard/firewall-rules', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: `Block ${ipAddress}`,
                type: 'block',
                target: ipAddress,
                priority: 'high',
                description: 'Auto-generated rule from security log',
                is_active: true
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('IP address blocked successfully!');
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function deleteLog(logId) {
    if (confirm('Are you sure you want to delete this log entry?')) {
        fetch(`/admin/cyber-guard/logs/${logId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}

function bulkAction(action) {
    const selectedIds = Array.from(document.querySelectorAll('.log-checkbox:checked')).map(cb => cb.value);
    
    if (selectedIds.length === 0) {
        alert('Please select at least one log entry.');
        return;
    }
    
    if (confirm(`Are you sure you want to ${action.replace('_', ' ')} ${selectedIds.length} log entries?`)) {
        fetch(`/admin/cyber-guard/logs/bulk-${action}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ log_ids: selectedIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }
}
</script>
@endpush
@endsection

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, ensure the user_badge_level table exists with correct name
        if (!Schema::hasTable('user_badge_level')) {
            // Check if badge_level table exists and rename it
            if (Schema::hasTable('badge_level')) {
                Schema::rename('badge_level', 'user_badge_level');
            } else {
                // Create the table if it doesn't exist
                Schema::create('user_badge_level', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('color', 7)->default('#6b7280');
                $table->string('icon')->nullable();
                $table->string('badge_image')->nullable();
                $table->decimal('min_spent_amount', 15, 2)->default(0);
                $table->decimal('min_uangtix_balance', 15, 2)->default(0);
                $table->integer('min_transactions')->default(0);
                $table->integer('min_events_attended')->default(0);
                $table->decimal('discount_percentage', 5, 2)->default(0);
                $table->decimal('cashback_percentage', 5, 2)->default(0);
                $table->json('benefits')->nullable();
                $table->json('requirements')->nullable();
                $table->boolean('is_active')->default(true);
                $table->boolean('is_default')->default(false);
                $table->integer('sort_order')->default(0);
                $table->timestamps();
                });
            }
        }

        // Now fix the users table badge columns
        Schema::table('users', function (Blueprint $table) {
            $columns = Schema::getColumnListing('users');

            // Add badge_level_id if it doesn't exist
            if (!in_array('badge_level_id', $columns)) {
                $table->unsignedBigInteger('badge_level_id')->nullable()->after('role');
            }

            // Add badge_assigned_at if it doesn't exist
            if (!in_array('badge_assigned_at', $columns)) {
                $table->timestamp('badge_assigned_at')->nullable()->after('badge_level_id');
            }

            // Add badge_expires_at if it doesn't exist
            if (!in_array('badge_expires_at', $columns)) {
                $table->timestamp('badge_expires_at')->nullable()->after('badge_assigned_at');
            }

            // Add badge_duration_days if it doesn't exist
            if (!in_array('badge_duration_days', $columns)) {
                $table->integer('badge_duration_days')->nullable()->comment('Duration in days, null for permanent')->after('badge_expires_at');
            }

            // Add badge_auto_renew if it doesn't exist
            if (!in_array('badge_auto_renew', $columns)) {
                $table->boolean('badge_auto_renew')->default(false)->after('badge_duration_days');
            }
        });

        // Add foreign key constraint if it doesn't exist
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->foreign('badge_level_id')->references('id')->on('user_badge_level')->onDelete('set null');
            });
        } catch (Exception $e) {
            // Foreign key might already exist, ignore the error
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraint
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropForeign(['badge_level_id']);
            });
        } catch (Exception $e) {
            // Ignore if foreign key doesn't exist
        }

        // Remove badge columns from users table
        Schema::table('users', function (Blueprint $table) {
            $columns = Schema::getColumnListing('users');
            $columnsToRemove = [];

            if (in_array('badge_auto_renew', $columns)) {
                $columnsToRemove[] = 'badge_auto_renew';
            }
            if (in_array('badge_duration_days', $columns)) {
                $columnsToRemove[] = 'badge_duration_days';
            }
            if (in_array('badge_expires_at', $columns)) {
                $columnsToRemove[] = 'badge_expires_at';
            }
            if (in_array('badge_assigned_at', $columns)) {
                $columnsToRemove[] = 'badge_assigned_at';
            }
            if (in_array('badge_level_id', $columns)) {
                $columnsToRemove[] = 'badge_level_id';
            }

            if (!empty($columnsToRemove)) {
                $table->dropColumn($columnsToRemove);
            }
        });

        // Drop user_badge_level table
        Schema::dropIfExists('user_badge_level');
    }
};

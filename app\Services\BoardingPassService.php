<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\Event;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BoardingPassService
{
    /**
     * Template prefixes for boarding pass IDs
     */
    private const TEMPLATE_PREFIXES = [
        'unix' => 'UNX',
        'minimalist' => 'MIN',
        'pro' => 'PRO',
        'custom' => 'CST',
        'elegant' => 'ELG',
        'modern' => 'MOD',
        'classic' => 'CLS',
        'neon' => 'NEO',
        'retro' => 'RET',
        'corporate' => 'CRP',
        'festival' => 'FES',
        'vip' => 'VIP'
    ];

    /**
     * Generate unique boarding pass ID based on template
     */
    public function generateBoardingPassId(string $template = 'unix'): string
    {
        $prefix = self::TEMPLATE_PREFIXES[$template] ?? 'UNX';
        $timestamp = date('ymd');
        
        // Generate unique ID with retry mechanism
        $maxRetries = 10;
        $attempt = 0;
        
        do {
            $random = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 6));
            $boardingPassId = "BP-{$prefix}-{$timestamp}-{$random}";
            
            // Check if ID already exists
            $exists = Ticket::where('boarding_pass_id', $boardingPassId)->exists();
            $attempt++;
            
        } while ($exists && $attempt < $maxRetries);
        
        if ($exists) {
            // Fallback with microseconds if still not unique
            $microtime = substr(str_replace('.', '', microtime(true)), -6);
            $boardingPassId = "BP-{$prefix}-{$timestamp}-{$microtime}";
        }
        
        Log::info('Boarding pass ID generated', [
            'boarding_pass_id' => $boardingPassId,
            'template' => $template,
            'attempts' => $attempt
        ]);
        
        return $boardingPassId;
    }

    /**
     * Generate boarding pass IDs for multiple tickets
     */
    public function generateBulkBoardingPassIds(array $tickets, string $template = 'unix'): array
    {
        $results = [];
        
        foreach ($tickets as $ticket) {
            if ($ticket instanceof Ticket) {
                $boardingPassId = $this->generateBoardingPassId($template);
                $ticket->update(['boarding_pass_id' => $boardingPassId]);
                
                $results[] = [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'boarding_pass_id' => $boardingPassId,
                    'template' => $template,
                    'success' => true
                ];
            }
        }
        
        return $results;
    }

    /**
     * Validate boarding pass ID format
     */
    public function validateBoardingPassId(string $boardingPassId): bool
    {
        // Format: BP-XXX-YYMMDD-XXXXXX
        $pattern = '/^BP-[A-Z]{3}-\d{6}-[A-Z0-9]{6}$/';
        return preg_match($pattern, $boardingPassId) === 1;
    }

    /**
     * Extract template from boarding pass ID
     */
    public function extractTemplateFromBoardingPassId(string $boardingPassId): ?string
    {
        if (!$this->validateBoardingPassId($boardingPassId)) {
            return null;
        }
        
        $parts = explode('-', $boardingPassId);
        if (count($parts) !== 4) {
            return null;
        }
        
        $prefix = $parts[1];
        
        // Find template by prefix
        foreach (self::TEMPLATE_PREFIXES as $template => $templatePrefix) {
            if ($templatePrefix === $prefix) {
                return $template;
            }
        }
        
        return null;
    }

    /**
     * Get boarding pass statistics
     */
    public function getBoardingPassStats(): array
    {
        $stats = [];
        
        foreach (self::TEMPLATE_PREFIXES as $template => $prefix) {
            $count = Ticket::where('boarding_pass_id', 'LIKE', "BP-{$prefix}-%")->count();
            $stats[$template] = [
                'template' => $template,
                'prefix' => $prefix,
                'count' => $count
            ];
        }
        
        return $stats;
    }

    /**
     * Regenerate boarding pass ID for ticket
     */
    public function regenerateBoardingPassId(Ticket $ticket, ?string $template = null): string
    {
        // Use ticket's event template or provided template
        $template = $template ?? $ticket->event->boarding_pass_template ?? 'unix';
        
        $newBoardingPassId = $this->generateBoardingPassId($template);
        
        $ticket->update([
            'boarding_pass_id' => $newBoardingPassId,
            'template_used' => $template
        ]);
        
        Log::info('Boarding pass ID regenerated', [
            'ticket_id' => $ticket->id,
            'old_boarding_pass_id' => $ticket->getOriginal('boarding_pass_id'),
            'new_boarding_pass_id' => $newBoardingPassId,
            'template' => $template
        ]);
        
        return $newBoardingPassId;
    }

    /**
     * Fix missing boarding pass IDs for existing tickets
     */
    public function fixMissingBoardingPassIds(): array
    {
        $ticketsWithoutBoardingPass = Ticket::whereNull('boarding_pass_id')
            ->orWhere('boarding_pass_id', '')
            ->with('event')
            ->get();
        
        $results = [
            'total_processed' => $ticketsWithoutBoardingPass->count(),
            'successful' => 0,
            'failed' => 0,
            'details' => []
        ];
        
        foreach ($ticketsWithoutBoardingPass as $ticket) {
            try {
                $template = $ticket->event->boarding_pass_template ?? 'unix';
                $boardingPassId = $this->generateBoardingPassId($template);
                
                $ticket->update([
                    'boarding_pass_id' => $boardingPassId,
                    'template_used' => $template
                ]);
                
                $results['successful']++;
                $results['details'][] = [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'boarding_pass_id' => $boardingPassId,
                    'template' => $template,
                    'success' => true
                ];
                
            } catch (\Exception $e) {
                $results['failed']++;
                $results['details'][] = [
                    'ticket_id' => $ticket->id,
                    'ticket_number' => $ticket->ticket_number,
                    'error' => $e->getMessage(),
                    'success' => false
                ];
                
                Log::error('Failed to fix boarding pass ID', [
                    'ticket_id' => $ticket->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        Log::info('Boarding pass ID fix completed', $results);
        
        return $results;
    }

    /**
     * Get available templates
     */
    public function getAvailableTemplates(): array
    {
        return array_keys(self::TEMPLATE_PREFIXES);
    }

    /**
     * Get template prefix
     */
    public function getTemplatePrefix(string $template): string
    {
        return self::TEMPLATE_PREFIXES[$template] ?? 'UNX';
    }
}

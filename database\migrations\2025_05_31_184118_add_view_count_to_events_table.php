<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('events', function (Blueprint $table) {
            // Add view_count column to track event page views
            $table->unsignedBigInteger('view_count')->default(0)->after('sale_end_date');

            // Add index for better performance when sorting by view count
            $table->index('view_count');
        });
    }

    /**
        }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('events', function (Blueprint $table) {
            // Drop the view_count column and its index
            $table->dropIndex(['view_count']);
            $table->dropColumn('view_count');
        });
    }
};
